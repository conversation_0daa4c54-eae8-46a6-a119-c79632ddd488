<template>
    <div class="component-wrap">
        <el-form @submit.prevent :model="queryParams" ref="queryFormRef" size="default" :inline="true"
            v-show="showSearch" label-width="48px">
            <el-form-item label="名称" prop="componentName">
                <el-input v-model="queryParams.componentName" placeholder="请输入名称" clearable
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="handleResetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb10" :justify="'space-between'">
          <div>
            <el-button type="primary" size="default" @click="handleAdd"
                v-auths="['scada:component:add']"><el-icon><ele-Plus /></el-icon>新增</el-button>
            <el-button type="success" size="default" :disabled="single" @click="handleUpdate"
                v-auths="['scada:component:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
            <el-button type="danger" size="default" :disabled="multiple" @click="handleDelete"
                v-auths="['scada:component:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
            <el-button type="warning" size="default" @click="handleExport"
                v-auths="['scada:component:export']"><el-icon><ele-Download /></el-icon>导出</el-button>
          </div>
          <div class="flex">
            <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                           @queryTable="getList"></right-toolbar>
            <div class="ml8">
              <el-tooltip effect="dark" content="切换卡片/列表" placement="top" style="float: right;margin-right: 5px;">
                <el-button size="default" circle @click="handleChangeShowType">
                  <el-icon><ele-Grid /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </div>
        </el-row>

        <div v-if="showType == 'card'">
            <el-row :gutter="15" v-loading="loading">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" style="margin-top: 7.5px; margin-bottom: 7.5px"
                        v-for="item in componentList" :key="item.id">
                        <el-card class="card-wrap" :body-style="{ padding: '10px' }">
                            <div class="img-wrap">
                                <el-image style="width: 100%; height: 100%; border-radius: 5px" lazy
                                    :src="baseUrl + item.componentImage" fit="cover"
                                    @click="goToDetail(item)"></el-image>
                            </div>
                            <div class="tag-wrap">
                                <span>{{ item.isShare === 0 ? '私有' : '公有' }}</span>
                            </div>
                            <div class="name-wrap">
                                <span>{{ item.componentName }}</span>
                            </div>
                            <div class="tools-wrap">
                              <el-checkbox-group v-model="ids" @change="checkboxChange">
                                <el-checkbox class="checkbox" :value="item.id" :key="item.id"><span
                                    v-show="false">占位符</span></el-checkbox>
                              </el-checkbox-group>
                                <el-button-group>
                                    <el-button size="default" type="primary" link @click="goToDetail(item)"
                                        v-auths="['scada:component:query']"><el-icon><ele-View /></el-icon>详情</el-button>
                                    <el-button style="margin-left: 10px; color: #f56c6c" size="default" type="primary" link @click="handleDelete(item)"
                                        v-auths="['scada:component:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                </el-button-group>
                            </div>
                        </el-card>
                    </el-col>

            </el-row>
            <el-empty description="暂无数据" v-if="total == 0"></el-empty>
          <el-pagination v-show="total > 0"  @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                         style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                         v-model:current-page="state.tableData.param.pageNum" background
                         v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                         :total="state.tableData.total">
          </el-pagination>
        </div>
        <div v-if="showType == 'list'">
            <el-table v-loading="loading" :data="componentList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="id" align="center" prop="id" width="100" />
                <el-table-column label="名称" align="center" prop="componentName" width="300"/>
                <el-table-column label="类型" align="center" prop="isShare" width="120">
                    <template #default="scope">
                        <el-tag type="info">{{ scope.row.isShare === 0 ? '私有' : '公有' }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="缩略图" align="center" prop="componentImage" width="200">
                    <template #default="scope">
                        <image-preview :src="baseUrl + scope.row.componentImage" :width="50" :height="50" />
                    </template>
                </el-table-column>
                <el-table-column label="更新时间" align="center" prop="updateTime" width="250" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" type="primary" link @click="goToDetail(scope.row)"
                            v-auths="['scada:component:query']"><el-icon><ele-View /></el-icon>详情</el-button>
                        <el-button style="color: #f56c6c" size="default" type="primary" link @click="handleDelete(scope.row)"
                                   v-auths="['scada:component:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
          <el-pagination v-show="total > 0"  @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                         style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                         v-model:current-page="state.tableData.param.pageNum" background
                         v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                         :total="state.tableData.total">
          </el-pagination>
        </div>

        <!-- 添加或修改组件管理对话框 -->
        <el-dialog :title="dialog.title" v-model="dialog.open" width="400px" append-to-body>
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="dialogFormRef" :model="dialog.form" :rules="dialog.rules" label-width="58px">
                <el-form-item label="图片" prop="componentImage">
                    <image-upload v-model="dialog.form.componentImage" :multiple="false"
                        :class="{ disable: uploadDisabled }" />
                </el-form-item>
                <el-form-item label="名称" prop="componentName">
                    <el-input v-model="dialog.form.componentName" placeholder="请输入名称" clearable />
                </el-form-item>
                <el-form-item label="类型" prop="isShare">
                    <el-radio-group v-model="dialog.form.isShare">
                        <el-radio :label="0">私有</el-radio>
                        <el-radio :label="1">公有</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
                    <el-button @click="handleDialogCancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { listComponent, getComponent, delComponent, addComponent, updateComponent } from '/@/api/scada/component';
import {download} from "@/utils/request";

// 定义组件名称
defineOptions({
    name: 'Component'
});

// 路由
const router = useRouter();

// 响应式数据
const loading = ref(true); // 遮罩层
const single = ref(true); // 非单个禁用
const multiple = ref(true); // 非多个禁用
const showSearch = ref(true); // 显示搜索条件

// 查询参数
const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    componentName: null,
    componentTemplate: null,
    componentStyle: null,
    componentScript: null,
    componentImage: null,
    tenantId: null,
    tenantName: null,
});

const componentList = ref<any[]>([]); // 组件管理表格数据
const total = ref(0); // 总条数
const ids = ref<any[]>([]); // 选中数组
const showType = ref('card');
const baseUrl = import.meta.env.VITE_APP_BASE_API; // 根路径

const dialog = reactive({
    open: false, // 弹出层标题
    title: '', // 对话框标题
    // 表单参数
    form: {
        id: null,
        componentImage: '',
        componentName: '',
        isShare: 1,
    },
    // 表单校验
    rules: {
        componentName: [{ required: true, message: '请输入名称', trigger: 'change' }],
        isShare: [{ required: true, message: '请选择类型', trigger: 'change' }],
    },
});

// 计算属性
const uploadDisabled = computed(() => {
    return dialog.form.componentImage !== '';
});

// 引用
const queryFormRef = ref();
const dialogFormRef = ref();

// 表单重置
const reset = () => {
    dialog.form = {
        id: null,
        componentImage: '',
        componentName: '',
        isShare: 1,
    };
    dialogFormRef.value?.resetFields();
};

// 查询组件管理列表
const getList = () => {
    loading.value = true;
    listComponent(queryParams).then((response) => {
      response = response.data
        if (response.code === 200) {
            componentList.value = response.rows;
            total.value = response.total;
        }
        loading.value = false;
    });
};
interface TableDataItem {
  id: string; // 假设 id 是 string 类型，如果是其他类型，请调整
  pageResolution?: string; // 可选字段，表示分辨率
  pageName: string;
  pageImage: string;
}
const state = reactive({
  tableData: {
    data: [] as TableDataItem[],
    total: 0,
    loading: false,
    param: {
      pageNum: 1,
      pageSize: 10,
      pageName: undefined //分类名称
    },
  },
});
// 分页改变
const onHandleSizeChange = (val: number) => {
  state.tableData.param.pageSize = val;
  getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
  state.tableData.param.pageNum = val;
  getList();
};
// 搜索按钮操作
const handleQuery = () => {
    queryParams.pageNum = 1;
    getList();
};

// 重置按钮操作
const handleResetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
};

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
};

// 新增按钮操作
const handleAdd = () => {
    reset();
    dialog.open = true;
    dialog.title = '添加组件管理';
};

// 提交按钮
const handleDialogSubmit = () => {
    dialogFormRef.value?.validate((valid: boolean) => {
        if (valid) {
            if (dialog.form.id != null) {
                updateComponent(dialog.form).then((res) => {
                  res = res.data
                    if (res.code === 200) {
                        ElMessage.success('修改成功');
                        dialog.open = false;
                        getList();
                    }
                });
            } else {
                addComponent(dialog.form).then((res) => {
                  res = res.data
                    if (res.code === 200) {
                        ElMessage.success('新增成功');
                        dialog.open = false;
                        getList();
                    }
                });
            }
        }
    });
};

// 取消按钮
const handleDialogCancel = () => {
    dialog.open = false;
};

// 修改按钮操作
const handleUpdate = (row: any) => {
    dialog.title = '修改组件管理';
    const id = row.id || ids.value;
    getComponent(id).then((res) => {
      res = res.data
        if (res.code === 200) {
            dialog.form = res.data;
            dialog.open = true;
        }
    });
};

// 删除按钮操作
const handleDelete = (row: any) => {
    const deleteIds = row.id || ids.value;
    ElMessageBox.confirm('是否确认删除组件编号为"' + deleteIds + '"的数据项？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        return delComponent(deleteIds);
    }).then(() => {
        getList();
        ElMessage.success('删除成功');
    }).catch(() => {});
};

// 导出按钮操作
const handleExport = () => {
  download('scada/component/export', {
    ...queryParams
  }, `组件${new Date().getTime()}.xlsx`)
};
// 切换显示方式
const handleChangeShowType = () => {
    ids.value = [];
    showType.value = showType.value == 'card' ? 'list' : 'card';
};

// 跳转详情页
const goToDetail = (row: any) => {
    router.push({
        path: '/scada/component/detail',
        query: {
            id: row.id,
        },
    });
};

// 卡片选择
const checkboxChange = (selection: any[]) => {
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

// 生命周期
onMounted(() => {
    getList();
});
</script>
<style lang="scss" scoped>
.component-wrap {
    padding: 20px;

    .card-wrap {
        position: relative;
        border-radius: 5px;

        .img-wrap {
            height: 200px;
            width: 100%;
            // transition: transform 0.3s ease;

            // &:hover {
            //     cursor: pointer;
            //     transform: scale(1.1);
            // }
        }

        .tag-wrap {
            position: absolute;
            top: 0;
            left: 0;
            background-color: #1890ff;
            border-top-left-radius: 5px;
            border-bottom-right-radius: 5px;
            padding: 5px 15px;
            font-size: 12px;
            color: #fff;
        }

        .name-wrap {
            height: 20px;
            line-height: 20px;
            margin-top: 10px;
            font-size: 14px;
        }

        .tools-wrap {
            margin-top: 10px;
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
        }
    }
}

.disable {
    ::v-deep .el-upload--picture-card {
        display: none !important;
    }
}
</style>
