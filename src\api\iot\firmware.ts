import request from '/@/utils/request'

// 定义接口类型
interface FirmwareQuery {
  // 你可以根据实际查询条件定义类型
  page: number;
  pageSize: number;
  searchQuery?: string;
}

interface FirmwareData {
  // 定义固件数据的类型
  firmwareId: string;
  name: string;
  version: string;
  description: string;
  // 其他固件属性...
}

interface FirmwareResponse {
  // 假设接口返回的固件列表数据
  data: FirmwareData[];
  total: number;
}

// 查询产品固件列表
export function listFirmware(query: any): Promise<any> {
  return request({
    url: '/iot/firmware/list',
    method: 'get',
    params: query
  })
}

// 查询待升级固件版本列表
export function upGradeVersionList(query: FirmwareQuery): Promise<FirmwareResponse> {
  return request({
    url: '/iot/firmware/upGradeVersionList',
    method: 'get',
    params: query
  })
}

// 查询设备最新固件
export function getLatestFirmware(deviceId: string): Promise<FirmwareData> {
  return request({
    url: `/iot/firmware/getLatest/${deviceId}`,
    method: 'get'
  })
}

// 查询产品固件详细
export function getFirmware(firmwareId: string): Promise<any> {
  return request({
    url: `/iot/firmware/${firmwareId}`,
    method: 'get'
  })
}

// 新增产品固件
export function addFirmware(data: any): Promise<any> {
  return request({
    url: '/iot/firmware',
    method: 'post',
    data: data
  })
}

// 修改产品固件
export function updateFirmware(data: any): Promise<any> {
  return request({
    url: '/iot/firmware',
    method: 'put',
    data: data
  })
}

// 删除产品固件
export function delFirmware(firmwareId: string): Promise<void> {
  return request({
    url: `/iot/firmware/${firmwareId}`,
    method: 'delete'
  })
}
