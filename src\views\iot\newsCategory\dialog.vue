<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="500">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
                size="default" label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="分类名称" prop="categoryName">
                            <el-input v-model="state.ruleForm.categoryName" placeholder="请输入新闻分类标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="显示顺序" prop="orderNum">
                            <el-input v-model="state.ruleForm.orderNum" placeholder="请输入新闻分类标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="备注">
                        <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
                            maxlength="150"></el-input>
                    </el-form-item>
                </el-col>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { addNewsCategory, getNewsCategory, updateNewsCategory } from '/@/api/iot/newsCategory';

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        categoryName: '', // 新闻分类标题
        orderNum: '', // 新闻分类类型
        remark: '', // 状态
        categoryId:''
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    categoryName: [{
        required: true,
        message: "分类名字不能为空",
        trigger: "blur"
    }],
    orderNum: [{
        required: true,
        message: "显示顺序不能为空",
        trigger: "blur"
    }],

})
// 打开弹窗
const openDialog = (type: string, row: RowNewsCategoryType, categoryId: string) => {
    if (type === 'edit') {

        if (row != undefined) {
            getNewsCategory(row.categoryId).then(response => {
                state.ruleForm = response.data.data

            });

        } else {
            getNewsCategory(categoryId).then(response => {
                state.ruleForm = response.data.data

            });
        }
        state.dialog.title = '修改新闻分类';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '新增新闻分类';
        state.dialog.submitTxt = '新 增';
    }
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.categoryId != '') {
                updateNewsCategory(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addNewsCategory(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
