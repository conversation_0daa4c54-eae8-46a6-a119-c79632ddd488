// api/dept.ts
import request from '/@/utils/request';

// 定义部门相关数据类型
interface Dept {
  deptId: string;
  deptName: string;
  parentId?: string;
  // 根据实际字段补充其他部门属性
}

interface DeptQuery {
  deptName?: string;
  status?: string;
  parentId?: string;
  [key: string]: any; // 用于接收其他查询条件
}

// 查询部门列表
export function listDept(query: DeptQuery) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query,
  });
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId: any) {
  return request({
    url: `/system/dept/list/exclude/${deptId}`,
    method: 'get',
  });
}

// 查询部门详细
export function getDept(deptId: number) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'get',
  });
}

// 新增部门
export function addDept(data: Dept) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data,
  });
}

// 修改部门
export function updateDept(data: Dept) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data,
  });
}

// 删除部门
export function delDept(deptId: number) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'delete',
  });
}
