<template>
    <div class="system-user-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="字典名称" prop="dictName">
                        <el-select v-model="state.tableData.param.dictType" placeholder="用户状态" clearable
                            style="width: 240px">
                            <el-option v-for="dict in typeOptions" :key="dict.dictId" :label="dict.dictName"
                                :value="dict.dictType" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="字典标签" prop="dictLabel">
                        <el-input v-model="state.tableData.param.dictLabel" clearable size="default"
                            placeholder="请输入字典标签" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="state.tableData.param.status" placeholder="数据状态" clearable
                            style="width: 240px">
                            <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['system:dict:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add',state.tableData.param.dictType)">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['system:dict:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="handleUpdate('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['system:dict:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="handleDelete">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['system:dict:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                        <el-button v-auths="['system:dict:refresh']" size="default" type="info" class="ml10"
                            @click="handleClose">
                            <el-icon><ele-Close /></el-icon>
                            关闭
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <div class="app-container">
                <el-table :data="state.tableData.data" v-loading="state.tableData.loading" ref="tableRef"
                    @selection-change="handleSelectionChange" border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column label="字典编码" align="center" prop="dictCode" />
                    <el-table-column label="字典标签" align="center" prop="dictLabel">
                        <template #default="scope">
                            <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{
                                scope.row.dictLabel }}</span>
                            <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{
                                scope.row.dictLabel }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column label="字典键值" align="center" prop="dictValue" :show-overflow-tooltip="true" />
                    <el-table-column label="字典排序" align="center" prop="dictSort" :show-overflow-tooltip="true" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="statuslist" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
                    <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <!-- <template #default="scope">
                            <el-button size="default" text type="primary" icon="el-icon-delete"
                                @click="cancelAuthUser(scope.row)"
                                v-auths="['system:role:remove']"><el-icon><ele-CircleCloseFilled /></el-icon>取消授权</el-button>
                        </template> -->
                        <template #default="scope">
                            <el-button size="default" text type="primary" @click="handleUpdate('edit', scope.row)"
                                v-auths="['system:dict:edit']">
                                <el-icon><ele-EditPen /></el-icon>修改</el-button>
                            <el-button size="default" text type="primary" @click="handleDelete(scope.row)"
                                v-auths="['system:dict:remove']">
                                <el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                    style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>
            </div>
        </el-card>
        <dicObjDialog ref="dicObjDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemUser">

import { reactive, onMounted, ref, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { useRoute } from 'vue-router';
import router from '/@/router';
import { optionselect as getDictOptionselect, getType } from "/@/api/system/dict/type";
import { allocatedUserList, authUserCancel, authUserCancelAll } from '/@/api/system/role';
import { delData, listData } from '/@/api/system/dict/data';
import { download } from '/@/utils/request';
const dictStore = useDictStore();  // 使用 Pinia store
// 引入组件
const dicObjDialog = defineAsyncComponent(() => import('/@/views/system/dict/objdialog.vue'));
// 定义变量内容
const dicObjDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            dictId: '' as any,
            dictName: undefined,
            dictLabel: undefined,
            dictType: undefined,
            status: ''
        },
    },
});
const typeOptions = ref()
const defaultDictType = ref()
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

const ids = ref() //dictId
const tableRef = ref();  // 引用表格组件实例
// 初始化表格数据
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const response = await listData(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 查询字典类型详细 */
const getTypes = (dictId: any) => {
    getType(dictId).then(response => {
        state.tableData.param.dictType = response.data.data.dictType;
        defaultDictType.value = response.data.data.dictType;
        getTableData()
    });
}
/** 查询字典类型列表 */
const getTypeList = () => {
    getDictOptionselect().then(response => {
        typeOptions.value = response.data.data;
    });
}
// 打开新增新闻弹窗
const onOpenAddDic = (type: string,dictType:any) => {
    dicObjDialogRef.value.openDialog(type,dictType);
};
// 打开修改字典弹窗
const handleUpdate = (type: string, row: any | undefined) => {
    var dictId = ''
    if (!row) {
        dictId = ids.value
    } else {
        dictId = row.dictCode
    }
    dicObjDialogRef.value.openDialog(type, row, dictId);
};
// 删除字典
const handleDelete = (row: any) => {
    const dictIds = row.dictCode || ids.value;
    ElMessageBox.confirm(`此操作将永久删除字典编码为“${dictIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delData(dictIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
                // 清除字典缓存
                dictStore.clearDictCache('dict/cleanDict');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('system/dict/data/export', {
        ...state.tableData.param
    }, `data_${new Date().getTime()}.xlsx`)
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param.pageNum = 1
    state.tableData.param.pageSize = 10
    state.tableData.param.dictId = undefined
    state.tableData.param.dictName = undefined
    state.tableData.param.dictLabel = undefined
    state.tableData.param.dictType = undefined
    state.tableData.param.status = ''

}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_normal_disable')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { dictCode: string; }) => item.dictCode);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 关闭按钮 */
const handleClose = () => {
    const obj = { path: "/system/dict" };
    router.push(obj);
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
}
// 页面加载时
onMounted(() => {
    const route = useRoute();
    const dictId = route.params.dictId;
    getTypes(dictId);
    getTypeList()
    getdictdata()
});
</script>