import request from '/@/utils/request'

// 定义接口返回的数据类型
interface Post {
  id: string | number;
  name: string;
  status: boolean;
  rows: any;
  total:string
  // 其他字段可以根据需要补充
}

// 查询岗位列表
export function listPost(query: Record<string, any>): Promise<{ data: any}> {
  return request({
    url: '/system/post/list',
    method: 'get',
    params: query
  }) as Promise<{ data: any }>;
}

// 查询岗位详细
export function getPost(postId: string | number): Promise<any> {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  }) as Promise<any>;
}

// 新增岗位
export function addPost(data: any): Promise<Post> {
  return request({
    url: '/system/post',
    method: 'post',
    data: data
  }) as Promise<Post>;
}

// 修改岗位
export function updatePost(data: any): Promise<Post> {
  return request({
    url: '/system/post',
    method: 'put',
    data: data
  }) as Promise<Post>;
}

// 删除岗位
export function delPost(postId: string | number): Promise<void> {
  return request({
    url: '/system/post/' + postId,
    method: 'delete'
  }) as Promise<void>;
}
