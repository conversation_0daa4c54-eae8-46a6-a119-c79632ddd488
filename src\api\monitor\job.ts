import request from '/@/utils/request'

// 定义 `QueryParams` 类型，假设它是一个对象
interface QueryParams {
  [key: string]: any; // 可根据实际需求定义更具体的类型
}

// 定义 `JobData` 类型，表示任务数据的结构
interface JobData {
  jobId: string;
  jobGroup?: string;
  [key: string]: any; // 其他可能的字段，可以根据实际情况扩展
}

// 查询定时任务调度列表
export function listJob(query: QueryParams) {
  return request({
    url: '/monitor/job/list',
    method: 'get',
    params: query
  })
}

// 查询定时任务调度详细
export function getJob(jobId: any) {
  return request({
    url: `/monitor/job/${jobId}`,
    method: 'get'
  })
}

// 新增定时任务调度
export function addJob(data: any) {
  return request({
    url: '/monitor/job',
    method: 'post',
    data: data
  })
}

// 修改定时任务调度
export function updateJob(data: any) {
  return request({
    url: '/monitor/job',
    method: 'put',
    data: data
  })
}

// 删除定时任务调度
export function delJob(jobId: string) {
  return request({
    url: `/monitor/job/${jobId}`,
    method: 'delete'
  })
}

// 任务状态修改
export function changeJobStatus(jobId: string, status: string) {
  const data = {
    jobId,
    status
  }
  return request({
    url: '/monitor/job/changeStatus',
    method: 'put',
    data: data
  })
}

// 定时任务立即执行一次
export function runJob(jobId: string, jobGroup: string) {
  const data = {
    jobId,
    jobGroup
  }
  return request({
    url: '/monitor/job/run',
    method: 'put',
    data: data
  })
}
