<template>
  <div :key="key">
    <ScrollBoard
        :config="{
        rowNum: detail.style.rowNum,
        data: detail.style.data,
        header: detail.style.header,
        headerBGC: detail.style.headerBGC,
        oddRowBGC: detail.style.oddRowBGC,
        evenRowBGC: detail.style.evenRowBGC,
        waitTime: detail.style.waitTime,
        headerHeight: detail.style.headerHeight,
        columnWidth: detail.style.columnWidth.split(','),
        align: detail.style.align,
        index: detail.style.index,
        indexHeader: detail.style.indexHeader,
        carousel: detail.style.carousel,
      }"
        :style="{
        width: detail.style.position.w + 'px',
        height: detail.style.position.h + 'px',
        color: detail.style.foreColor,
      }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { listAlertLog } from '@/api/iot/alertLog'

// 使用 base.json 中维修记录的配置
const detail = ref({
  style: {
    rowNum: 5,
    data: [
      ['2024-01-15 10:30:00', '温度传感器异常', '设备001', '一级', '已维修完成'],
      ['2024-01-15 09:15:00', '压力超限报警', '设备002', '二级', '正在处理中'],
      ['2024-01-15 08:45:00', '通信故障', '设备003', '三级', '已恢复正常'],
      ['2024-01-15 07:20:00', '电源电压异常', '设备004', '一级', '已更换电源'],
      ['2024-01-15 06:10:00', '流量计故障', '设备005', '二级', '维修中']
    ],
    header: ['时间', '告警名称', '设备名称', '告警级别', '处理内容'],
    headerBGC: '#00BAFF',
    oddRowBGC: '#003B51',
    evenRowBGC: '#0A2732',
    waitTime: 2000,
    headerHeight: 35,
    columnWidth: '160,160,110,110,160',
    align: ['center'],
    index: true,
    indexHeader: '序号',
    carousel: 'single',
    foreColor: '#fff',
    position: {
      w: 568,
      h: 300
    }
  }
})

// 响应式 key 控制组件刷新
const key = ref(0)

// 字典模拟数据（可替换为全局挂载或 API 获取）
const dict = ref({
  type: {
    iot_alert_level: [
      { value: 1, label: '一级', raw: { listClass: 'danger' } },
      { value: 2, label: '二级', raw: { listClass: 'warning' } },
      { value: 3, label: '三级', raw: { listClass: 'primary' } },
      { value: 4, label: '四级', raw: { listClass: 'success' } }
    ],
    iot_process_status: [
      { value: 0, label: '未处理', raw: { listClass: 'danger' } },
      { value: 1, label: '处理中', raw: { listClass: 'warning' } },
      { value: 2, label: '已处理', raw: { listClass: 'success' } }
    ]
  }
})

// 定时器
let timer: NodeJS.Timeout | null = null

// 获取数据方法
const getList = () => {
  const params = {
    status: 3,
    pageNum: 1,
    pageSize: 9999
  }

  listAlertLog(params).then((res: any) => {
    res = res.data
    if (res.code === 200) {
      const data: any[] = []
      res.rows.forEach((item: any) => {
        const mdata = [
          item.createTime,
          item.alertName,
          item.deviceName,
          getSpecifiedElement(dict.value.type.iot_alert_level, item.alertLevel),
          '维修完成' // 处理内容，可以根据实际数据调整
        ]
        data.push(mdata)
      })

      // 更新数据
      detail.value.style.data = data
      key.value = Date.now() // 强制刷新组件
    }
  })
}

// 获取指定字典值
const getSpecifiedElement = (dictList, val) => {
  const obj = dictList.find(item => item.value == val)
  if (obj && (obj.raw.listClass === 'default' || !obj.raw.listClass)) {
    return obj.label
  } else {
    return `<span style="color:${getColor(obj.raw.listClass)};">${obj.label}</span>`
  }
}

// 根据类型返回颜色
const getColor = (type) => {
  switch (type) {
    case 'primary':
      return '#1890ff'
    case 'success':
      return '#13ce66'
    case 'warning':
      return '#ffba00'
    case 'danger':
      return '#ff4949'
    default:
      return '#fff'
  }
}

// 页面加载后首次获取数据
onMounted(() => {
  getList()
  timer = setInterval(() => {
    getList()
  }, 60000 * 5)
})

// 组件卸载前清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style lang="scss" scoped></style>