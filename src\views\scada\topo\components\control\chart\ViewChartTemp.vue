<template>
  <div class="view-chart-temp" ref="chartView" :id="detail.identifier">
    <!-- 隐藏的依赖项，确保响应式更新 -->
    <div v-show="false">{{ height }}{{ width }}{{ chartsValue }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import * as echarts from 'echarts';
import topoUtil from '/@/utils/topo/topo-util';
import { useCounterStore } from '/@/stores/counterStore';

// 定义组件名称
defineOptions({
  name: 'ViewChartTemp'
});

// Props
interface Props {
  editMode?: boolean;
  selected?: boolean;
  detail: any;
}
const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  selected: false
});

// Composables
const counterStore = useCounterStore();
const { mqttData } = storeToRefs(counterStore);

// Refs
const chartView = ref<HTMLElement>();
const myChart = ref<any>(null);
const option = ref<any>({});
const timer = ref<any>(null);
const paramValue = ref(0);

const width = computed(() => {
  return props.detail.style?.position?.w || 200;
});

const height = computed(() => {
  return props.detail.style?.position?.h || 200;
});

// 监听尺寸变化并调整图表大小
watch([width, height], ([newWidth, newHeight], [oldWidth, oldHeight]) => {
  if (myChart.value && (newWidth !== oldWidth || newHeight !== oldHeight)) {
    nextTick(() => {
      try {
        myChart.value.resize();
      } catch (error) {
        console.error('❌ 调整温度计图表尺寸失败:', error);
      }
    });
  }
}, { immediate: false });
const chartsValue = computed(() => {
  console.log('🌡️ 温度计 chartsValue 计算属性执行');
  console.log('  MQTT数据:', mqttData.value);
  console.log('  数据绑定配置:', props.detail.dataBind);

  let paramVal = 0;
  let foreColor = '';

  // 检查 MQTT 数据绑定
  if (props.detail.dataBind?.identifier && mqttData.value && mqttData.value.serialNumber) {
    console.log('    组件标识符:', props.detail.dataBind.identifier);
    console.log('    组件序列号:', props.detail.dataBind.serialNumber);
    console.log('    MQTT序列号:', mqttData.value.serialNumber);

    if (mqttData.value.serialNumber == props.detail.dataBind.serialNumber) {

      const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataBind.identifier);
      if (message) {
        paramVal = message.value;
        if (isNaN(paramVal)) {
          paramVal = 0;
        }
        paramValue.value = paramVal;

        // 处理状态列表，确定颜色
        if (props.detail.dataBind.stateList) {
          props.detail.dataBind.stateList.forEach((element: any) => {
            let isSure = topoUtil.judgeSize(element.paramCondition, paramVal, element.paramData);
            console.log(`  🔍 状态判断: ${paramVal} ${element.paramCondition} ${element.paramData} = ${isSure}`);
            if (isSure) {
              foreColor = element.foreColor;
              console.log('  🎨 设置颜色:', foreColor);
            }
          });
        }
      } else {
        console.log('  ❌ 未找到匹配的消息');
      }
    } else {
      console.log('  ❌ 序列号不匹配');
    }
  } else {
    console.log('  ❌ 数据绑定条件不满足');
    // 使用默认值
    paramVal = paramValue.value || props.detail.dataBind?.modelValue || 0;
  }

  // 更新颜色
  if (foreColor) {
    props.detail.style.foreColor = foreColor;
  }
  // 准备图表配置参数
  let max = props.detail.dataBind?.paramMax || 100,
      min = props.detail.dataBind?.paramMin || 0,
      temp = paramVal,
      tempColor = props.detail.style?.foreColor || '#ff0000',
      interval = props.detail.dataBind?.interval || 10,
      unit = props.detail.dataBind?.unit || '';

  console.log('  📊 图表配置参数:', { max, min, temp, tempColor, interval, unit });
  option.value = {
    grid: {
      right: '50%',
      left: '50%',
    },
    title: {
      bottom: 'bottom',
      left: 'center',
      textStyle: { fontSize: 14 },
    },
    xAxis: [
      {
        type: 'category',
        show: false,
        axisTick: {
          alignWithLabel: true,
        },
        axisLine: {
          onZero: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: min,
        max: max,
        interval: interval,
        axisTick: {
          show: true,
          length: 5,
          lineStyle: {
            // color: 'white',
          },
        },
        minorTick: {
          show: true,
        },
        nameTextStyle: {
          color: 'white',
        },
        axisLine: {
          // onZero:false,
        },
        splitLine: {
          show: false,
        },
        offset: 10,
        axisLabel: {
          margin: 20,
          // color: '#fff',
          fontSize: 12,
        },
      },
    ],
    series: [
      {
        name: '透明框',
        type: 'bar',
        // xAxisIndex: 2,
        data: [max],
        barWidth: 6,
        itemStyle: {
          color: 'transparent',
          barBorderRadius: 0,
          borderWidth: 20,
          borderType: 'solid',
          borderColor: 'grey',
        },
        z: 1,
      },
      {
        z: 19,
        barGap: '-100%',
        type: 'bar',
        barWidth: 6,
        stack: 'Total',
        label: {
          show: true,
          formatter: function (p: any) {
            return '  {temp|' + p.value + unit + '}';
          },
          position: [10, -20],
          rich: {
            temp: {
              // color: 'white',
              lineHeight: 30,
              padding: [0, 0, 0, 3],
              fontSize: 18,
              verticalAlign: 'middle',
              align: 'center',
              height: 30,
            },
          },
        },
        itemStyle: {
          borderWidth: 3,
          borderColor: tempColor,
        },
        showBackground: true,
        backgroundStyle: {
          color: '#cccccc',
        },
        data: [temp],
      },
      {
        name: '圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [min],
        symbolSize: 58,
        itemStyle: {
          normal: {
            color: 'grey',
            opacity: 1,
          },
        },
        z: 12,
      },
      {
        name: '白圆',
        type: 'scatter',
        hoverAnimation: false,
        data: [min],
        symbolSize: 30,
        itemStyle: {
          normal: {
            color: tempColor,
            opacity: 1,
          },
        },
        z: 18,
      },
    ],
  };

  nextTick(() => {
    setOption(option.value);
  });

  return paramVal;
});
// Methods
const setOption = (optionData: any) => {
  if (myChart.value) {
    myChart.value.setOption(optionData);
  }
};

// 监听 MQTT 数据变化
watch(
  mqttData,
  (newData, oldData) => {
    // 详细分析序列号变化
    if (oldData && newData) {
      // 只有当序列号匹配时才处理
      if (newData.serialNumber === props.detail.dataBind?.serialNumber) {
        // 检查消息中是否有匹配的标识符
        const hasMatchingMessage = newData.message?.some((item: any) =>
          item.id === props.detail.dataBind?.identifier
        );
        if (hasMatchingMessage) {
          nextTick(() => {
            chartsValue.value;
          });
        }
      }
    } else if (newData && newData.serialNumber) {
      if (newData.serialNumber === props.detail.dataBind?.serialNumber) {
        nextTick(() => {
          chartsValue.value;
        });
      }
    }
  },
  { deep: true, immediate: true }
);

// 添加 paramValue 监听器
watch(
  paramValue,
  (newValue, oldValue) => {
    // 当数值变化时，强制更新图表
    if (myChart.value && newValue !== oldValue) {
      nextTick(() => {
        try {
          console.log('🔄 强制更新温度计图表配置');
          // 触发 chartsValue 重新计算
          chartsValue.value;
        } catch (error) {
          console.error('❌ 更新温度计图表失败:', error);
        }
      });
    }
  },
  { immediate: true }
);

// Lifecycle
onMounted(() => {
  const dom = document.getElementById(props.detail.identifier);
  if (dom) {
    // 确保容器样式正确
    dom.style.width = '100%';
    dom.style.height = '100%';
    dom.style.position = 'relative';
    myChart.value = echarts.init(dom);

    // 设置初始温度值（如果没有MQTT数据）
    if (!paramValue.value && props.detail.dataBind?.modelValue) {
      paramValue.value = props.detail.dataBind.modelValue;
    }
    // 触发初始渲染
    chartsValue.value;
  } else {
    console.error('❌ 找不到温度计图表容器:', props.detail.identifier);
  }
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (myChart.value) {
    myChart.value.dispose();
  }
});
</script>

<style lang="scss">
.view-chart-temp {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // 确保容器有明确的尺寸
  &:empty {
    display: flex;
    justify-content: center;
    align-items: center;

    &::before {
      content: '温度计加载中...';
      color: #999;
      font-size: 14px;
    }
  }
}
</style>
