import request from '/@/utils/request'

// 定义系统资源响应类型
interface SystemResourceResponse {
  code: number;
  msg: string;
  data: {
    cpu: {
      usage: number;
      cores?: number;
      model?: string;
    };
    mem: {
      usage: number;
      total?: number;
      used?: number;
      free?: number;
    };
    sys?: {
      computerName?: string;
      osName?: string;
      osArch?: string;
      uptime?: string;
    };
  };
}

// 获取在线设备数量
export function getOnlineDeviceCount() {
  return request({
    url: '/iot/dashboard/onlineDeviceCount',
    method: 'get'
  })
}

// 获取今日告警数量
export function getTodayAlertCount() {
  return request({
    url: '/iot/dashboard/todayAlertCount',
    method: 'get'
  })
}

// 获取今日数据量
export function getTodayDataCount() {
  return request({
    url: '/iot/dashboard/todayDataCount',
    method: 'get'
  })
}

// 获取仪表盘概览数据（一次性获取所有数据）
export function getDashboardOverview() {
  return request({
    url: '/iot/dashboard/overview',
    method: 'get'
  })
}

// 获取系统资源使用情况
export function getSystemResources(): Promise<SystemResourceResponse> {
  return request({
    url: '/monitor/server',
    method: 'get'
  })
}

// 模拟系统资源数据，当API不可用时使用
export function getMockSystemResources(): Promise<SystemResourceResponse> {
  return new Promise((resolve) => {
    // 生成随机的CPU和内存使用率
    const cpuUsage = Math.floor(Math.random() * 60) + 20 // 20-80%之间的随机值
    const memoryUsage = Math.floor(Math.random() * 50) + 30 // 30-80%之间的随机值
    
    // 模拟API响应格式
    resolve({
      code: 200,
      msg: 'success',
      data: {
        cpu: {
          usage: cpuUsage, // CPU使用率
          cores: 8, // CPU核心数
          model: 'Intel(R) Core(TM) i7-10700 CPU @ 2.90GHz' // CPU型号
        },
        mem: {
          usage: memoryUsage, // 内存使用率
          total: 16384, // 总内存(MB)
          used: Math.round(16384 * memoryUsage / 100), // 已用内存(MB)
          free: Math.round(16384 * (100 - memoryUsage) / 100) // 剩余内存(MB)
        },
        sys: {
          computerName: '服务器',
          osName: 'Windows Server 2019',
          osArch: 'amd64',
          uptime: '10:25:36' // 运行时间
        }
      }
    })
  })
}

// 获取真实在线设备数量（调用后端 /deviceMonitor/scope）
export function getRealOnlineDeviceCount() {
  return request({
    url: '/deviceMonitor/scope',
    method: 'get'
  })
}

// 查询设备状态统计（首页专用）
export function getDeviceStatusStatistics() {
  return request({
    url: '/deviceMonitor/scope',
    method: 'get'
  })
} 