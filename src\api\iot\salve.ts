import request from '/@/utils/request'

// 定义查询参数和返回值的类型

// 查询参数类型
interface QueryParams {
  [key: string]: any;  // 根据实际查询条件来进一步定义
}

// 设备从机的基本数据类型
interface SalveData {
  id?: number;  // id 是可选的，因为在新增时并没有 id
  [key: string]: any;  // 根据实际数据结构可以添加更多字段
}

// 响应的数据类型（可以根据实际返回的格式进行调整）
interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

// 查询变量模板设备从机列表
export function listSalve(query: QueryParams) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve/list',
    method: 'get',
    params: query
  })
}

// 查询变量模板设备从机详细
export function getSalve(id: number) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve/' + id,
    method: 'get'
  })
}

// 新增变量模板设备从机
export function addSalve(data: SalveData) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve',
    method: 'post',
    data: data
  })
}

// 修改变量模板设备从机
export function updateSalve(data: SalveData) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve',
    method: 'put',
    data: data
  })
}

// 删除变量模板设备从机
export function delSalve(id: number) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve/' + id,
    method: 'delete'
  })
}

// 根据产品 id 查询从机列表
export function listByPid(params: QueryParams) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/salve/listByPId',
    method: 'get',
    params: params
  })
}
