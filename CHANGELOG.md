# <a href="https://gitee.com/lyt-top/vue-next-admin" target="_blank">vue-next-admin-template（不带国际化） 更新日志</a>

🎉🎉🔥 `vue-next-admin-template` 基于 （vue-next-admin-v2.4.33 版本） vue3.x 、Typescript、vite、Element plus 等，适配手机、平板、pc 的后台开源免费模板库（vue2.x 请切换 vue-prev-admin 分支）

## 2.4.33

`2023.04.12`

- 🎉 同步 master 分支 v2.4.33 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.32

`2023.03.26`

- 🎉 同步 master 分支 v2.4.32 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.31

`2023.03.10`

- 🎉 同步 master 分支 v2.4.31 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.3

`2023.02.22`

🚩🚩🚩 感谢 [驰骋工作流引擎-表单引擎-低代码开发平台](http://www.ccflow.org/) 赞助商的赞助。驰骋公司为社会提供流程引擎+表单引擎+低代码开发平台一体的开源软件解决方案，欢迎广大开发者前去体验！

- 🎉 同步 master 分支 v2.4.3 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.21

`2022.12.12`

- 🎉 同步 master 分支 v2.4.21 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.2

`2022.12.10`

- 🎉 同步 master 分支 v2.4.2 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.4.1

`2022.11.30`

- 🎉 同步 master 分支 v2.4.1 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.3.0

`2022.11.16`

- 🎉 同步 master 分支 v2.3.0 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.2.0

`2022.07.11`

- 🎉 同步 master 分支 v2.2.0 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.1.1

- 🎉 同步 master 分支 v2.1.1 版本内容，具体查看 [master CHANGELOG.md](https://gitee.com/lyt-top/vue-next-admin/blob/master/CHANGELOG.md)

## 2.0.2

- 🎉 同步 master 分支 v2.0.2 版本内容，具体查看 master CHANGELOG.md

## 0.2.2

`2021.12.21`

- 🎉 同步 master 分支 v1.2.2 版本内容，具体查看 master CHANGELOG.md

## 0.2.1

`2021.12.12`

- 🌟 更新 依赖更新最新版本
- 🐞 修复 浏览器标题问题
- 🐞 修复 element plus svg 图标引入
- 🐞 修复 默认显示英文问题，改成默认显示中文

## 0.2.0

`2021.12.04`

- 🎉 同步 master 分支 v1.2.0 版本内容，具体查看 master CHANGELOG.md

## 0.1.0

`2021.10.17`

- 🎉 新增 vue-next-admin-template 基础版本（不带国际化），切换 `vue-next-admin-template` 分支
