<template>
  <div class="gallery-wrap">
    <el-row>
      <!-- 左侧菜单 -->
      <el-col :span="4" class="left-menu">
        <el-menu class="menu-wrap" :default-active="categoryTypes[0]?.dictValue">
          <el-sub-menu index="1">
            <template #title>
              <div class="menu-title">
                <el-icon><Menu/></el-icon>
                <span>系统图库</span>
              </div>
            </template>
            <el-menu-item
                v-for="item in categoryTypes"
                :key="item.dictValue"
                :index="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
                @click="handleTypeClick(item.dictValue)"
            >
              {{ item.dictLabel }}
            </el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-col>
      <!-- 右侧内容 -->
      <el-col :span="20">
        <div class="content-wrap">
          <el-form @submit.native.prevent :model="queryParams"
              ref="queryForm" :inline="true" v-show="showSearch" label-width="68px" >
            <el-form-item label="文件名称" prop="fileName">
              <el-input v-model="queryParams.fileName" size="default"  placeholder="请输入文件名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
              <el-button size="default" @click="handleResetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="10" class="mb8" >
            <el-col :span="1.5">
              <el-upload ref="uploadRef" :action="`${upload.uploadUrl}?categoryName=${queryParams.categoryName}`" :headers="upload.headers" :before-upload="beforeUpload" :limit="500"
                  :on-success="handleAvatarSuccess" :show-file-list="false" :file-list="upload.imageList" multiple >
                <el-button type="primary" plain size="default">
                  <el-icon class="el-icon-right" v-auths="['scada:gallery:add']">
                    <ele-Upload />
                  </el-icon>
                  上传
                </el-button>
              </el-upload>
            </el-col>
            <el-col :span="1.5">
              <el-button size="default" type="success" :disabled="single" @click="handleEdit">
                <el-icon class="el-icon-edit" v-auths="['scada:gallery:edit']">
                  <ele-EditPen />
                </el-icon>
                修改
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" size="default" :disabled="multiple" @click="handleDelete" >
                <el-icon class="el-icon-delete" v-auths="['scada:gallery:remove']">
                  <ele-DeleteFilled />
                </el-icon>
                删除
              </el-button>
            </el-col>
            <!-- 右侧工具栏靠右 -->
            <div style="margin-left: auto;">
              <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch" @query-table="getList" ></right-toolbar>
            </div>
          </el-row>

          <div v-loading="loading">
            <el-checkbox-group
                class="img-box-wrap"
                v-if="total !== 0"
                v-model="checkImages"
                @change="checkboxChange"
            >
              <el-card
                  class="img-card"
                  :style="{ margin: '0 10px 10px 0' }"
                  v-for="item in galleryList"
                  :key="item.id"
                  :body-style="{ padding: '5px' }"
              >
                <img class="img" :src="baseApi + item.resourceUrl" />
                <div class="name-wrap">
                  <span>{{ item.fileName }}</span>
                </div>
                <el-checkbox class="checkbox" :value="item.id">
                  <span v-show="false">占位符</span>
                </el-checkbox>
              </el-card>
            </el-checkbox-group>
            <el-empty description="暂无数据" v-if="total === 0" />
            <el-pagination
                v-show="total > 0"
                size="small"
                v-model:current-page="queryParams.pageNum"
                v-model:page-size="queryParams.pageSize"
                :total="total"
                layout="total, sizes, prev, pager, next, jumper"
                :page-sizes="[20, 40, 60]"
                :pager-count="5"
                background
                class="mt15"
                style="justify-content: flex-end;"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    <!-- 修改文件 -->
    <DetailDialog ref="detailDialog" :id="fileId" @save="handleDialogSave" />
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent,ref, reactive, onMounted } from 'vue'
import { getDicts } from '/@/api/system/dict/data'
import { listGallery, delGallery } from '/@/api/scada/gallery'
import {Session} from "/@/utils/storage";
import { ElMessage, ElMessageBox } from 'element-plus';
// 引入组件
const DetailDialog = defineAsyncComponent(() => import('/@/views/scada/gallery/detail-dialog.vue'));
// 响应式数据
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
const loading = ref(true)
interface TypeOption {
  dictValue: string;
  dictLabel: string;
  listClass: string;
  cssClass: string;
}
const categoryTypes = ref<TypeOption[]>([])
const showSearch = ref(true)
const single = ref(true)
const multiple = ref(true)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  categoryName: '',
  fileName: '',
  deptIdStrs: null,
  moduleGuid: '云组态',
  resourceUrl: null,
  orderByColumn: 'id',
  isAsc: 'desc'
})
interface TypeList {
  categoryName: string;
  fileName: string;
  id: number;
  resourceUrl: string;
}
const galleryList = ref<TypeList[]>([])
const total = ref(0)
const uploadRef = ref<any>(null);
const upload = reactive({
  headers: { Authorization: 'Bearer ' + Session.get('token') },
  imageList: [] as any,
  uploadUrl: baseApi.value + '/scada/gallery/uploadFile'
})

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.pageSize = size
  getList()
}

// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.pageNum = page
  getList()
}
const checkImages = ref([])
const fileId = ref<number | null>(null)
const queryForm = ref(null)

// 获取分类和列表
async function getDatas() {
  const dictType = 'scada_gallery_type'
  try {
    const response = await getDicts(dictType);
    const res = response.data
    if (res.code === 200) {
      categoryTypes.value = res.data
      if (res.data.length > 0) {
        queryParams.categoryName = res.data[0].dictValue
      }
      await getList()
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取分类失败', error)
  }
}

async function getList() {
  loading.value = true
  try {
    const res = await listGallery(queryParams)
    const response = res.data
    if (response.code === 200) {
      galleryList.value = response.rows
      total.value = response.total
      checkImages.value = []
    }
  } finally {
    loading.value = false
  }
}

function handleQuery() {
  queryParams.pageNum = 1
  getList()
}

function handleResetQuery() {
  // 清空查询表单逻辑（可调用 resetForm）
  queryParams.fileName = ''
  handleQuery()
}

function handleTypeClick(type: any) {
  queryParams.pageNum = 1
  queryParams.categoryName = type
  checkImages.value = []
  single.value = true
  multiple.value = true
  getList()
}

function checkboxChange(selection: any) {
  single.value = selection.length !== 1
  multiple.value = !selection.length
}

function beforeUpload(file: File) {
  if (!queryParams.categoryName) {
    alert('请选择左侧上传的类型')
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 20
  if (!isLt2M) {
    alert('上传图片大小不能超过 20MB!')
  }
  return isLt2M
}

function handleAvatarSuccess(res: any) {
  if (res.code === 200) {
    alert('上传成功')
    upload.imageList = []
    getList()
  } else {
    alert(res.msg || '上传失败')
  }
}
// 定义接口
interface DetailDialogType {
  openDialog: (id: number | null) => void
}

// 声明 ref 并带上类型
const detailDialog = ref<InstanceType<typeof DetailDialog> & DetailDialogType | null>(null)
function handleEdit(row: any) {
  const fileId = row?.id || checkImages.value[0]
  if (fileId && detailDialog.value) {
    // 推荐方式：调用子组件开放的方法
    detailDialog.value.openDialog(fileId)
  }
  resetSelection()
}

function handleDialogSave() {
  getList()
}

async function handleDelete() {
  const ids = checkImages.value
  if (!ids.length) {
    ElMessage.warning('请至少选择一条数据')
    return
  }
  try {
    await ElMessageBox.confirm('是否确认删除此图标文件？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await delGallery(ids)
    ElMessage.success('删除成功')

    resetSelection()
    getList()
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('删除失败', err)
  }
}

function resetSelection() {
  checkImages.value = []
  single.value = true
  multiple.value = true
}

// 生命周期：组件挂载后
onMounted(() => {
  getDatas()
})
</script>

<style>
.gallery-wrap {
  .el-menu {
    background-color:transparent;
    width: 97%;
  }
  .el-menu-item {
    height: 56px;
    line-height: 56px;
    font-size: 14px;
    color: #303133;
    padding: 0 20px;
    list-style: none;
    cursor: pointer;
    position: relative;
    -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
  .menu-wrap {
    margin-top: 5px;
    height: 946px;
    overflow-x: hidden;
    overflow-y: auto;
    border-right: none;
  }

  .content-wrap {
    padding: 10px;
    background-color: #fff;
    .img-box-wrap {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      border-radius: 10px;
      align-content: center;
      background: #fbfaf7;
      padding: 20px 10px 10px 20px;
      margin-top: 10px;

      .img-card {
        width: 165px;
        height: auto;
        text-align: center;
        padding: 10px;
        position: relative;

        .img {
          width: 100px;
          height: 100px;
          margin-top: 10px;
        }

        .name-wrap {
          text-align: center;
          font-size: 11px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 10px;
        }

        .checkbox {
          position: absolute;
          top: 8px;
          right: 0px;
        }
      }
    }
  }
}
.menu-title {
  color: #303133;
}
</style>