<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.tableData.dialog.title"
            v-model="state.tableData.dialog.isShowDialog" width="900">

            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="state.tableData.param.userName" clearable size="default"
                            placeholder="请输入用户名称" style="width: 200px" />
                    </el-form-item>
                    <el-form-item label="手机号码" prop="phonenumber">
                        <el-input v-model="state.tableData.param.phonenumber" clearable size="default"
                            placeholder="请输入手机号码" style="width: 200px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" class="ml1" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>

            </div>
            <div class="app-container">
                <el-table :data="state.tableData.data" v-loading="state.tableData.loading" ref="tableRef"
                    @selection-change="handleSelectionChange" border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column label="用户名称" align="center" prop="userName" />
                    <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
                    <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
                    <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="statuslist" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                    class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="state.tableData.dialog.isShowDialog = false" size="default">取 消</el-button>
                    <el-button type="primary" @click="handleSelectUser" size="default">确定</el-button>
                </span>
            </template>


        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="systemUser">

import { reactive, onMounted, ref, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { useRoute } from 'vue-router';
import router from '/@/router';
import { allocatedUserList, authUserCancelAll, authUserSelectAll, unallocatedUserList } from '/@/api/system/role';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 定义变量内容
const SelectUserDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            roleId: '' as any,
            userName: undefined,
            phonenumber: undefined
        },
        dialog: {
            isShowDialog: false,
            title: '选择用户'

        },
    },
});
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

const ids = ref() //roleId
const tableRef = ref();  // 引用表格组件实例
// 打开弹窗
const openDialog = (roleId: any) => {
    state.tableData.param.roleId = roleId
    getTableData()
    getdictdata()
    state.tableData.dialog.isShowDialog = true;
};
// 初始化表格数据
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const response = await unallocatedUserList(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};

/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param.pageNum = 1
    state.tableData.param.pageSize = 10
    state.tableData.param.userName = undefined
    state.tableData.param.phonenumber = undefined

}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_normal_disable')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { userId: string; }) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 选择授权用户操作 */
const handleSelectUser = () => {
    const roleId = state.tableData.param.roleId;
    const userIds = ids.value.join(",");
    if (userIds == "") {
        ElMessage.error("请选择要分配的用户");
        return;
    }
    authUserSelectAll({ roleId: roleId, userIds: userIds }).then(res => {
        console.log(res);
        
        ElMessage.success(res.data.msg);
        if (res.data.code === 200) {
            state.tableData.dialog.isShowDialog = false;
            emit('refresh');
        }
    });
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
}
// 暴露变量
defineExpose({
    openDialog,
});
</script>