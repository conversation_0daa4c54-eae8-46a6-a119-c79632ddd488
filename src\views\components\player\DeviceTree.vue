<template>
  <div id="DeviceTree" style="width: 100%; height: 100%; background-color: #ffffff; overflow: auto">
    <div style="line-height: 3vh;margin-bottom: 10px;font-size: 17px;text-align: center;">
      {{ "设备列表" }}</div>
    <el-tree ref="tree" :props="defaultProps" :current-node-key="selectchannelId" :default-expanded-keys="expandIds"
      :highlight-current="true" @node-click="handleNodeClick" :load="loadNode" lazy node-key="id"
      style="min-width: 100%; display: inline-block !important">
      <template #default="{ node, }">
        <span class="custom-tree-node" style="width: 100%">
          <!-- 根据不同的设备类型和在线状态显示不同的图标 -->
          <span v-if="node.data.type === 0 && node.data.online" title="在线设备"
            class="device-online iconfont icon-jiedianleizhukongzhongxin2"></span>
          <!-- 其他情况省略，保持与原代码一致 -->
          <span v-if="node.data.online" style="padding-left: 1px" class="device-online">{{ node.label }}</span>
          <span v-if="!node.data.online" style="padding-left: 1px" class="device-offline">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {listDeviceShort } from '/@/api/iot/device';
import {listSipDeviceChannel } from '/@/api/iot/sipdevice';

const defaultProps = reactive({
  children: 'children',
  label: 'name',
  isLeaf: 'isLeaf',
});

let total = ref(0);
let channelList = ref([]);
let DeviceData = ref([]);
let expandIds = ref(['0']);
let selectData = ref({});
let selectchannelId = ref('');
let queryParams = reactive({
  pageNum: 1,
  pageSize: 100,
  status: 3,
  deviceType: 3,
});

const props = defineProps(['onlyCatalog', 'clickEvent']);

const tree = ref(null);

onMounted(() => {
  selectchannelId.value = '';
  expandIds.value = ['0'];
});

function handleNodeClick(data, node) {
  selectData.value = node.data;
  selectchannelId.value = node.data.value;
  if (node.level !== 0) {
    let deviceNode = tree.value.getNode(data.userData.channelSipId);
    if (typeof props.clickEvent == 'function' && node.level > 1) {
      props.clickEvent(deviceNode.data.userData);
    }
  }
}

async function loadNode(node, resolve) {
  if (node.level === 0) {
    try {
      const response = await listDeviceShort(queryParams);
      const data = response.rows;
      if (data.length > 0) {
        let nodeList = data.map(item => ({
          name: item.deviceName,
          isLeaf: false,
          id: item.serialNumber,
          type: 0,
          online: item.status === 3,
          userData: item,
        }));
        resolve(nodeList);
      } else {
        resolve([]);
      }
    } catch (error) {
      resolve([]);
    }
  } else {
    try {
      const res = await listSipDeviceChannel(node.data.userData.serialNumber);
      if (res.data != null) {
        channelDataHandler(res.data, resolve);
      } else {
        resolve([]);
      }
    } catch (error) {
      resolve([]);
    }
  }
}

function channelDataHandler(data, resolve) {
  if (data.length > 0) {
    let nodeList = data.map(item => {
      let channelType = item.id.substring(10, 13);
      let type = 3;
      if (item.basicData.ptztype === 1) type = 4;
      else if (item.basicData.ptztype === 2) type = 5;
      else if (item.basicData.ptztype === 3 || item.basicData.ptztype === 4) type = 6;

      return {
        name: item.name || item.id,
        isLeaf: true,
        id: item.id,
        deviceId: item.deviceId,
        type: type,
        online: item.status === 3,
        userData: item.basicData,
      };
    }).filter(node => ['111', '112', '118', '131', '132'].includes(node.id.substring(10, 13)));
    resolve(nodeList);
  } else {
    resolve([]);
  }
}

function reset() {
  // 强制更新逻辑根据实际需求调整
}
</script>

<style scoped>
.device-tree-main-box {
  text-align: left;
}

.device-online {
  color: #252525;
}

.device-offline {
  color: #727272;
}
</style>