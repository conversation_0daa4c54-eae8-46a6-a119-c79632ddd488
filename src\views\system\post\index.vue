<template>
	<div class="system-role-container layout-padding">
		<div class="system-role-padding layout-padding-view">
			<div class="system-user-search mb15">
				<el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
					<el-form-item label="岗位编码">
						<el-input v-model="state.tableData.param.postCode" clearable aria-label="First Name"
							placeholder="请输入岗位编码" style="width: 240px" />
					</el-form-item>
					<el-form-item label="岗位名称">
						<el-input v-model="state.tableData.param.postName" clearable placeholder="请输入岗位名称"
							style="width: 240px" />
					</el-form-item>
					<el-form-item label="岗位状态">
						<el-select v-model="state.tableData.param.status" placeholder="岗位状态" clearable
							style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button size="default" @click="handleQuery" type="primary" class="ml10">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:post:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddPost">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button v-auths="['system:post:edit']" size="default" type="success" class="ml10"
							:disabled="single" @click="handleUpdate('edit', undefined)">
							<el-icon><ele-EditPen /></el-icon>
							修改
						</el-button>
						<el-button v-auths="['system:post:remove']" size="default" type="danger" class="ml10"
							:disabled="multiple" @click="onRowDel">
							<el-icon><ele-DeleteFilled /></el-icon>
							删除
						</el-button>
						<el-button v-auths="['system:post:export']" size="default" type="warning" class="ml10"
							@click="handleExport">
							<el-icon><ele-Download /></el-icon>
							导出
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading" border
				@selection-change="handleSelectionChange" style="width: 100%"
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column type="selection" />
				<el-table-column prop="postId" label="岗位编号" align="center" />
				<el-table-column prop="postCode" label="岗位编码" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="postName" label="岗位名称" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="postSort" label="岗位排序" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column prop="status" label="状态" align="center" show-overflow-tooltip width="150">
					<template #default="scope">
						<DictTag :options="statuslist" :value="scope.row.status"></DictTag>
						<!-- <el-tag :type="scope.row.status == 0 ? '' : 'danger'">{{ scope.row.status == 0 ? '正常' :
							'停用'
							}}</el-tag> -->
					</template>
				</el-table-column>
				<!-- <el-table-column prop="describe" label="用户描述" show-overflow-tooltip></el-table-column> -->
				<el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip></el-table-column>
				<el-table-column label="操作" width="220" align="center">
					<template #default="scope">
						<div>
							<el-button v-auths="['system:post:edit']" class="ml15" text type="primary"
								@click="handleUpdate('edit', scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
							<el-button v-auths="['system:post:remove']" class="ml15" text type="primary"
								@click="onRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
						</div>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum" background
				v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total">
			</el-pagination>
		</div>
		<PostDialog ref="PostDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemRole">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { download } from '/@/utils/request';
import { delPost, listPost } from '/@/api/system/post';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const PostDialog = defineAsyncComponent(() => import('/@/views/system/post/dialog.vue'));

// 定义变量内容
const PostDialogRef = ref();
const state = reactive({
	tableData: {
		data: [],
		ruleForm: {
		},
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
			postCode: undefined,
			postName: undefined,
			status: ''
		},
	},
});
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //postId
interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const response = await listPost(state.tableData.param);
		console.log(response.data.rows, 'response');
		state.tableData.data = response.data.rows
		state.tableData.total = response.data.total
	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally {
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	}
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
	ids.value = selection.map((item: { postId: string; }) => item.postId);
	single.value = selection.length != 1;
	multiple.value = !selection.length;
}
/** 搜索按钮操作 */
const handleQuery = () => {
	state.tableData.param.pageNum = 1;
	getTableData();
}
// 重置按钮
const resetQuery = () => {
	state.tableData.param = {
		pageNum: 1,
		pageSize: 10,
		postCode: undefined,
		postName: undefined,
		status: ''
	}
	getTableData();
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 打开新增岗位弹窗
const onOpenAddPost = (type: string) => {
	PostDialogRef.value.openDialog(type);
};
// 打开修改岗位弹窗
const handleUpdate = (type: string, row: any | undefined) => {
	var postId = ''
	if (!row) {
		postId = ids.value
	} else {
		postId = row.roleId
	}
	PostDialogRef.value.openDialog(type, row, postId);
};
// 删除岗位
const onRowDel = (row: any) => {
	const postIds = row.postId || ids.value;
	// 使用 Element Plus 的消息框确认删除
	ElMessageBox.confirm(`是否确认删除岗位编号为"${postIds}"的数据项？`, '删除确认', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		// 删除岗位 API 调用
		try {
			await delPost(postIds);
			await getTableData();
			ElMessage.success('删除成功');
		} catch (error) {
			ElMessage.error('删除失败');
		}
	}).catch(() => {
		// 取消删除，不做任何操作
	});
};
/** 导出按钮操作 */
const handleExport = () => {
	const exportParams = {
		pageNum: state.tableData.param.pageNum || 1,
		pageSize: state.tableData.param.pageSize || 10,
		postCode: state.tableData.param.postCode || '',
		postName: state.tableData.param.postName || '',
		status: state.tableData.param.status || '',
	};
	download('system/post/export', {
		...state.tableData.param
	}, `post_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>

<style scoped lang="scss">
.system-role-container {
	.system-role-padding {
		padding: 15px;

		.el-table {
			flex: 1;
		}
	}
}
</style>
