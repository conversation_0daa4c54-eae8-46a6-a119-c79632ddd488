<template>
    <div
        :style="{
            fontSize: detail.style.fontSize + 'px',
            fontFamily: detail.style.fontFamily,
            color: detail.style.foreColor,
            textAlign: detail.style.textAlign,
            lineHeight: detail.style.position.h + 'px',
            border: detail.style.waterBorderWidth + 'px solid !important',
            borderRadius: detail.style.borderRadius + 'px !important',
            borderColor: detail.style.waterBorderColor,
        }"
        :class="classStyle"
        :id="detail.identifier"
    >
        {{ detail.style.text }}
        <div v-show="false">{{ dataInit }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useCounterStore } from '/@/stores/counterStore';
import topoUtil from '/@/utils/topo/topo-util';
import { getAnimate } from '/@/utils/topo/anime';

// Props
interface Props {
  detail: any;
  animateView?: any;
}
const props = defineProps<Props>();

// Composables
const counterStore = useCounterStore();

// Refs
const classStyle = ref<string | null>(null);

// Computed
const mqttData = computed(() => counterStore.mqttData);

const dataInit = computed(() => {
  if (mqttData.value) {
    // 数显款数值初始化
    if (props.detail.dataBind.identifier && mqttData.value.serialNumber == props.detail.dataBind.serialNumber) {
      const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataBind.identifier);
      if (message) {
        let value = !message.value ? 0 : message.value;
        if (props.detail.componentShow.indexOf('参数绑定') > -1) {
          let unit = props.detail.dataBind.paramUnit == null ? '' : props.detail.dataBind.paramUnit;
          props.detail.style.text = value + unit;
        }
      }
    }
    // 动画初始化
    if (
      props.detail.dataAction.serialNumber &&
      props.detail.dataAction.identifier &&
      props.detail.dataAction.paramJudge &&
      props.detail.dataAction.paramJudgeData != undefined &&
      mqttData.value.serialNumber == props.detail.dataAction.serialNumber
    ) {
      const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataAction.identifier);
      if (message) {
        let val = message.value;
        let isGd = topoUtil.judgeSize(props.detail.dataAction.paramJudge, val, props.detail.dataAction.paramJudgeData);
        if (isGd) {
          if (props.detail.dataBind.xyAction) {
            // 显隐判断
            getAnimate().set(document.getElementById(props.detail.identifier), {
              display: 'block',
            });
          }
          if (props.animateView) {
            props.animateView.play();
          }
        } else {
          if (props.detail.dataBind.xyAction) {
            // 显隐判断
            getAnimate().set(document.getElementById(props.detail.identifier), {
              display: 'none',
            });
          }
          if (props.animateView) {
            props.animateView.pause();
          }
        }
      }
    }
  }
});
</script>

<style lang="scss" scoped>
.view-text {
    height: 100%;
    width: 100%;
}
</style>
