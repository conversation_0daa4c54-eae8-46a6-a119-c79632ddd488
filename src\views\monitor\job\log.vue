<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form :model="state.tableData.param" ref="queryForm" size="default" :inline="true" v-show="showSearch"
                label-width="68px">
                <el-form-item label="任务名称" prop="jobName">
                    <el-input v-model="state.tableData.param.jobName" placeholder="请输入任务名称" clearable
                        style="width: 240px;" />
                </el-form-item>
                <el-form-item label="任务组名" prop="jobGroup">
                    <el-select v-model="state.tableData.param.jobGroup" placeholder="请选择执行状态" clearable
                        style="width: 240px">
                        <el-option v-for="dict in typelist" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="执行状态" prop="businessType">
                    <el-select v-model="state.tableData.param.status" placeholder="请选择执行状态" clearable
                        style="width: 240px">
                        <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作时间">
                    <el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
                        end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button size="default" type="primary" class="ml10" @click="getTableData">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        查询
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8" :justify="'space-between'">
                <div>
                    <el-button type="danger" size="default" :disabled="multiple" @click="handleDelete"
                        v-auths="['monitor:job:remove']">
                        <el-icon><ele-DeleteFilled /></el-icon>删除
                    </el-button>
                    <el-button type="danger" size="default" @click="handleClean" v-auths="['monitor:job:remove']">
                        <el-icon><ele-DeleteFilled /></el-icon>清空
                    </el-button>
                    <el-button type="warning" size="default" @click="handleExport" v-auths="['monitor:job:export']">
                        <el-icon><ele-Download /></el-icon>导出
                    </el-button>
                    <el-button type="warning" size="default" @click="handleClose">
                        <el-icon><ele-Close /></el-icon>关闭
                    </el-button>
                </div>
                <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                    @queryTable="getTableData"></right-toolbar>
            </el-row>
            <el-table v-loading="state.tableData.loading" :data="state.tableData.data"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="日志编号" align="center" prop="jobLogId" />
                <el-table-column label="任务名称" align="center" prop="jobName" />
                <el-table-column label="任务组名" width="100" align="center" prop="jobGroup">
                    <template #default="scope">
                        <DictTag :options="typelist" :value="scope.row.jobGroup"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="调用目标字符串" align="center" prop="invokeTarget" :show-overflow-tooltip="true" />
                <el-table-column label="日志信息" align="center" prop="jobMessage" :show-overflow-tooltip="true" />
                <el-table-column label="执行状态" width="100" align="center" prop="status">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.status"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="执行时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="default-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" @click="handleView(scope.row)"
                            v-auths="['monitor:job:query']">
                            <el-icon><ele-View /></el-icon>详细
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>

            <!-- 任务日志详细 -->
            <el-dialog title="任务详细" style="position: absolute; top: 100px;" v-model="jobdata.dialog.openView"
                width="700px" append-to-body>
                <el-form ref="form" :model="jobdata.ruleForm" label-width="120px" size="default">
                    <el-row>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="日志序号：">{{ jobdata.ruleForm.jobLogId }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务分组：">{{ jobGroupFormat(jobdata.ruleForm) }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="任务名称：">{{ jobdata.ruleForm.jobName }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="创建时间：">{{ jobdata.ruleForm.createTime }}</el-form-item>
                        </el-col>

                        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                            <el-form-item label="调用方法：">{{ jobdata.ruleForm.invokeTarget }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                            <el-form-item label="日志信息：">{{ jobdata.ruleForm.jobMessage }}</el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                            <el-form-item label="执行状态：">
                                <div v-if="jobdata.ruleForm.status == 0">正常</div>
                                <div v-else-if="jobdata.ruleForm.status == 1">失败</div>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                            <el-form-item label="异常信息：" v-if="jobdata.ruleForm.status == 1">{{ jobdata.ruleForm.exceptionInfo
                                }}</el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="jobdata.dialog.openView = false">关 闭</el-button>
                    </span>
                </template>
            </el-dialog>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, Ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { addDateRange, selectDictLabel } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { download } from '/@/utils/request';
import { useRoute } from 'vue-router';
import { parseTime } from '/@/utils/next'
import DictTag from '/@/components/DictTag/index.vue'
import { getJob } from '/@/api/monitor/job';
import { cleanJobLog, delJobLog, listJobLog } from '/@/api/monitor/jobLog';
import router from '/@/router';
const route = useRoute();
const dictStore = useDictStore();  // 使用 Pinia store
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            jobName: '',
            jobGroup: '' as any,
            status: '',
        }
    },
});
const jobdata = reactive({
    ruleForm: {
        jobName: '' as any,
        jobLogId: '' as any,
        jobGroup: '' as any,
        jobMessage: '' as any,
        invokeTarget: '' as any,
        concurrent: 1 as any,
        status: '' as any,
        exceptionInfo: 1 as any,
        createTime: '' as any,
    },
    dialog: {
        openView: false,
    },
})
// 显示搜索条件
const showSearch = ref(true);
interface statusOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<statusOption[]>([]); //状态列表
const typelist = ref<statusOption[]>([]); //类型列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref()
const dateRange = ref<[string, string]>(['', '']); //时间范围

// 初始化表格数据 查询登录日志列表
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const data = addDateRange(state.tableData.param, dateRange.value)
        const response = await listJobLog(data);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        jobName: '',
        jobGroup: '' as any,
        status: '',
    }
    getTableData()
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_common_status')
        typelist.value = await dictStore.fetchDict('sys_job_group')

        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { jobLogId: string; }) => item.jobLogId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 详细按钮操作 */
const handleView = (row: any) => {
    jobdata.dialog.openView = true;
    jobdata.ruleForm = row;

}
// 删除
const handleDelete = (row: { jobLogId: Ref<any, any>; }) => {
    const jobLogIds = row.jobLogId || ids.value;
    ElMessageBox.confirm('是否确认删除调度日志编号为"' + jobLogIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delJobLog(jobLogIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
}
// 关闭按钮
const handleClose = () => {
    const obj = { path: "/monitor/job" };
    router.push(obj)
}
// 清空
const handleClean = () => {
    ElMessageBox.confirm('是否确认清空所有调度日志数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            cleanJobLog().then(() => {
                getTableData();
                ElMessage.success('清空成功');
            })
        })
        .catch(() => { });
}
// 导出
const handleExport = () => {
    download('monitor/jobLog/export', {
        ...state.tableData.param
    }, `log_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 任务组名字典翻译
const jobGroupFormat = (row: { jobGroup: any; }) => {
    let aaa = ''
    typelist.value.forEach(item => {
        if (item.dictValue == row.jobGroup) {
            aaa = item.dictLabel
        }
    })
    return aaa
}
// 页面加载时
onMounted(() => {
    getdictdata()
    const jobId = route.params.jobId as any;
    if (jobId != undefined && jobId != 0) {
        getJob(jobId).then(async response => {
            state.tableData.param.jobName = response.data.data.jobName;
            state.tableData.param.jobGroup = response.data.data.jobGroup;
            getTableData();
        })
    } else {
        getTableData();

    }

    // getTableData();

});
</script>