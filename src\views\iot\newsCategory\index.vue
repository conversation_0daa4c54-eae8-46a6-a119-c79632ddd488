<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="分类名称" prop="categoryName">
                        <el-input v-model="state.tableData.param.categoryName" clearable size="default"
                            placeholder="请输入分类名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['iot:newsCategory:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['iot:newsCategory:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['iot:newsCategory:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['iot:newsCategory:export']" size="default" type="warning" class="ml10"
							@click="handleExport">
							<el-icon><ele-Download /></el-icon>
							导出
						</el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="分类编号" align="center" prop="categoryId" width="100" />
                <el-table-column label="分类名称" align="center" prop="categoryName" :show-overflow-tooltip="true" />
                <el-table-column label="显示顺序" align="center" prop="orderNum" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}')  }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="remark" min-width="200" header-align="center" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" icon="el-icon-edit"
                            @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:newsCategory:edit']"><el-icon><ele-View /></el-icon>查看</el-button>
                        <el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
                            v-auths="['iot:newsCategory:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <NewsCategoryDialog ref="NewsCategoryDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listNewsCategory, getNewsCategory, delNewsCategory, addNewsCategory, updateNewsCategory } from "/@/api/iot/newsCategory";
import { download } from '/@/utils/request';
import { parseTime } from '/@/utils/next'


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const NewsCategoryDialog = defineAsyncComponent(() => import('/@/views/iot/newsCategory/dialog.vue'));

// 定义变量内容
const NewsCategoryDialogRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            categoryName: undefined //分类名称
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //categoryId
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listNewsCategory(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        categoryName: undefined,
    }
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { categoryId: string; }) => item.categoryId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 打开新增新闻分类弹窗
const onOpenAddDic = (type: string) => {
    NewsCategoryDialogRef.value.openDialog(type);
};
// 打开修改新闻分类弹窗
const onOpenEditDic = (type: string, row: RowNewsCategoryType | undefined) => {
    var categoryId = ''
    if (!row) {
        categoryId = ids.value
    } else {
        categoryId = row.categoryId
    }
    NewsCategoryDialogRef.value.openDialog(type, row, categoryId);
};
// 删除新闻分类
const onRowDel = (row: RowNewsCategoryType) => {
    const categoryIds = row.categoryId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除新闻分类编号：“${categoryIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delNewsCategory(categoryIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
	download('iot/newsCategory/export', {
		...state.tableData.param
	}, `category_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
});
</script>
