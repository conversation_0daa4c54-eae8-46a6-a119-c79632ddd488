import type { Component } from 'vue'
import RightToolbar from '/@/components/RightToolbar/index.vue'
import dictTag from '/@/components/DictTag/index.vue'
import crontab from '/@/components/Crontab/index.vue'
import ScrollBoard from '/@/components/ScrollBoard/index.vue'
import BaiduMap from '/@/components/BaiduMap/index.vue'
import ImagePreview from '/@/components/ImagePreview/index.vue'
import ImageUpload from '/@/components/ImageUpload/index.vue'
import MonacoEditor from '/@/components/MonacoEditor/index.vue'

// ✨如果使用的是 JS 可以删除类型校验s
const globalComponent: {
  [propName: string]: Component
} = {
  RightToolbar,
  dictTag,
  crontab,
  ScrollBoard,
  BaiduMap,
  ImagePreview,
  ImageUpload,
  MonacoEditor,
}
export default globalComponent
