<template>
  <el-form size="small">
    <el-form-item>
      <el-radio :label="1" v-model="radioValue">
        不填，允许的通配符[, - * /]
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio :label="2" v-model="radioValue">
        每年
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio :label="3" v-model="radioValue">
        周期从
        <el-input-number v-model="cycle01" :min="fullYear" :max="2098" /> -
        <el-input-number v-model="cycle02" :min="cycle01 ? cycle01 + 1 : fullYear + 1" :max="2099" />
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio :label="4" v-model="radioValue">
        从
        <el-input-number v-model="average01" :min="fullYear" :max="2098" /> 年开始，每
        <el-input-number v-model="average02" :min="1" :max="2099 - average01 || fullYear" /> 年执行一次
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio :label="5" v-model="radioValue">
        指定
        <el-select clearable v-model="checkboxList" placeholder="可多选" multiple>
          <el-option
            v-for="item in 9"
            :key="item"
            :value="item - 1 + fullYear"
            :label="item - 1 + fullYear"
          />
        </el-select>
      </el-radio>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from "vue";

// Define props
const props = defineProps<{
  check: (value: any, min: any, max: any) => any;
  cron: any;
}>();

// Define emits
const emit = defineEmits<{
  (event: "update", field: string, value: string): void;
}>();

// Reactive state
const fullYear = ref<any>(new Date().getFullYear());
const radioValue = ref<any>(1);
const cycle01 = ref<any>(fullYear.value);
const cycle02 = ref<any>(fullYear.value);
const average01 = ref<any>(fullYear.value);
const average02 = ref<any>(1);
const checkboxList = ref<any[]>([]);

// Computed properties
const cycleTotal = computed(() => {
  const cycle01Val = props.check(cycle01.value, fullYear.value, 2098);
  const cycle02Val = props.check(
    cycle02.value,
    cycle01Val ? cycle01Val + 1 : fullYear.value + 1,
    2099
  );
  return `${cycle01Val}-${cycle02Val}`;
});

const averageTotal = computed(() => {
  const average01Val = props.check(average01.value, fullYear.value, 2098);
  const average02Val = props.check(
    average02.value,
    1,
    2099 - average01Val || fullYear.value
  );
  return `${average01Val}/${average02Val}`;
});

const checkboxString = computed(() => {
  return checkboxList.value.join();
});

// Watchers
watch(radioValue, () => {
  switch (radioValue.value) {
    case 1:
      emit("update", "year", "");
      break;
    case 2:
      emit("update", "year", "*");
      break;
    case 3:
      emit("update", "year", cycleTotal.value);
      break;
    case 4:
      emit("update", "year", averageTotal.value);
      break;
    case 5:
      emit("update", "year", checkboxString.value);
      break;
  }
});

watch(cycleTotal, () => {
  if (radioValue.value === 3) {
    emit("update", "year", cycleTotal.value);
  }
});

watch(averageTotal, () => {
  if (radioValue.value === 4) {
    emit("update", "year", averageTotal.value);
  }
});

watch(checkboxString, () => {
  if (radioValue.value === 5) {
    emit("update", "year", checkboxString.value);
  }
});

// Lifecycle hooks
onMounted(() => {
  fullYear.value = new Date().getFullYear();
  cycle01.value = fullYear.value;
  average01.value = fullYear.value;
});
defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList
});
</script>
