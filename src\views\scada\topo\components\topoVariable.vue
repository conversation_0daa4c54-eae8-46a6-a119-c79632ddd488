<template>
  <div>
    <el-form @submit.native.prevent :model="queryParams"
             ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="设备编号" prop="serialNumber">
        <el-input
            v-model="queryParams.serialNumber"
            placeholder="请输入设备编号"
            clearable
            size="default"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
        <el-button size="default" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
      </el-form-item>
    </el-form>

    <el-table v-if="!multiple" ref="singleTable" v-loading="loading" :data="bDeviceRealDataList" @select="selectRow" size="default">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="变量名称" align="center" prop="modelName" />
      <el-table-column label="变量标识" align="center" prop="identifier" />
    </el-table>

    <el-table v-else ref="multipleTable" v-loading="loading" :data="bDeviceRealDataList" @selection-change="handleSelectionChange" size="default">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="变量名称" align="center" prop="modelName" />
      <el-table-column label="变量标识" align="center" prop="identifier" />
    </el-table>

    <!-- 分页 -->
    <el-pagination v-show="total > 0" size="small" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                   layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 30]"
                   :pager-count="5" background class="mt15" style="justify-content: flex-end;"
                   @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </div>
</template>

<script setup lang="ts" >
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { listDeviceThingsModel } from '/@/api/scada/topo'
import {TableInstance} from "element-plus";

// Props
const props = defineProps({
  deviceImei: String,
  multiple: Boolean,
  textStatic: String
})

// Data
const loading = ref(true)
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  serialNumber: '',
  scadaGuid: useRoute().query.guid
})
const bDeviceRealDataList = ref([])
const total = ref(0)
const selectRowData = ref({})
const queryForm = ref(null)
const singleTable = ref<TableInstance | null>(null)
const multipleTable = ref(null)

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.pageSize = size
  getList()
}

// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.pageNum = page
  getList()
}

// Methods
const getList = () => {
  loading.value = true
  listDeviceThingsModel(queryParams)
      .then((res) => {
        res = res.data
        if (res.code === 200) {
          bDeviceRealDataList.value = res.rows
          total.value = res.total
        }
        loading.value = false
      })
      .catch((err) => {
        // eslint-disable-next-line no-console
        console.error(err)
        loading.value = false
      })
}

const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

const resetQuery = () => {
  // 假设你有 resetForm 工具方法
  // 如果没有，可以手动重置：
  queryParams.serialNumber = ''
  handleQuery()
}

const selectRow = (val: string, row: any) => {
  if (singleTable.value !== null) {
    singleTable.value.clearSelection()
    singleTable.value.toggleRowSelection(row, true)
  }
  selectRowData.value = row
}

// 存储选中的行数据
const selectRowsData = ref<string[]>([])

// 假设的 textStatic（来自 input 或其他来源）
const textStatic = ref('')

// 处理表格多选变化
const handleSelectionChange = (selection: any[]) => {
  if (textStatic.value === '') {
    selectRowsData.value = ['上传时间']
  } else {
    selectRowsData.value = []
  }

  selection.forEach((row) => {
    if (row.modelName) {
      selectRowsData.value.push(row.modelName)
    }
  })
}

// 暴露给父组件调用的方法
const clearSelection = () => {
  const tableRef = props.multiple ? multipleTable.value : singleTable.value
  if (tableRef && tableRef.clearSelection) {
    tableRef.clearSelection()
  }
}

// 挂载时加载数据
onMounted(() => {
  getList()
})

// 暴露方法给父组件
defineExpose({
  selectRowDataClick: () => selectRowData.value,
  selectRowsDataClick: () => selectRowsData.value,
  clearSelection
})
</script>

<style scoped></style>