import request from '/@/utils/request'

// 查询设备告警列表
export function listTicketLog(query: any) {
  return request({
    url: '/iot/ticketLog/list',
    method: 'get',
    params: query
  })
}

// 查询设备告警详细
export function getTicketLog(logId: string) {
  return request({
    url: '/iot/ticketLog/' + logId,
    method: 'get'
  })
}

// 新增设备告警
export function addTicketLog(data: any) {
  return request({
    url: '/iot/ticketLog',
    method: 'post',
    data: data
  })
}

// 修改设备告警
export function updateTicketLog(data: any) {
  return request({
    url: '/iot/ticketLog',
    method: 'put',
    data: data
  })
}

// 删除设备告警
export function delTicketLog(logId: string) {
  return request({
    url: '/iot/ticketLog/' + logId,
    method: 'delete'
  })
}
