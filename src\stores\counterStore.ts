// src/stores/counterStore.ts
import { defineStore } from 'pinia'
import Vue, { nextTick } from 'vue';
import uid from '/@/utils/uid.js';
import { deepCopy } from '/@/utils/index';
// 组件类型定义（假设你的组件结构是这样，按需调整）
interface Component {
  identifier: string;
  name: string;
  type: string;
  style: {
    visible: boolean;
    transform: number;
    borderWidth: number;
    borderStyle: string;
    borderColor: string;
    position: {
      x: number;
      y: number;
      w: number;
      h: number;
    };
  };
}
export const useCounterStore = defineStore('counter', {
  state: () => ({
    topoData: {
      name: '--',
      pageResolution: '',
      layer: {
        backColor: '',
        backgroundImage: '',
        widthHeightRatio: '',
        width: 1920,
        height: 1080,
      },
      components: [] as Component[], // 将 components 强类型为 Component[]
      zIndexTop: 0,
      zIndexBottom: -1,
    },
    selectedIsLayer: true,
    selectedComponent: null as Component | null, // selectedComponent 的类型是 Component 或 null
    selectedComponents: [] as string[], // selectedComponents 应该是一个字符串数组
    selectedComponentMap: {} as { [key: string]: Component }, // selectedComponentMap 的键为字符串，值为 Component
    copySrcItems: [] as any[], // copySrcItems 可以根据实际情况进一步强类型
    copyCount: 0,
    undoStack: [] as any[], // 根据实际情况强类型
    redoStack: [] as any[], // 根据实际情况强类型
    mqttData: {} as any, // 根据实际情况强类型
    historyData: [] as any[], // 根据实际情况强类型
  }),
  actions: {
    loadDefaultTopoData(jsonData:any) {
      this.topoData = jsonData;
    },
    async execute(command: any) {
      const { topoData, undoStack } = this;

      switch (command.op) {
        case 'add': {
          const component = command.component;
          if (!component || typeof component !== 'object') {
            console.error('无效的组件数据:', component);
            break;
          }

          const fuid = uid;
          component.identifier = fuid();
          component.name = `${component.type || 'unknown'}${topoData.components.length}`;
          component.style = {
            ...component.style,
            visible: true,
            transform: 0,
            borderWidth: component.style?.borderWidth || 0,
            borderStyle: component.style?.borderStyle || 'solid',
            borderColor: component.style?.borderColor || '#ccccccff',
          };

          // 确保 topoData.components 是数组
          if (!Array.isArray(topoData.components)) {
            topoData.components = [];
          }

          topoData.components.push(component);
          break;
        }

        case 'del': {
          // 获取所有被选中的组件索引
          const keys = topoData.components
              .map((component, index) =>
                  this.selectedComponentMap[component.identifier] !== undefined ? index : -1
              )
              .filter(index => index !== -1);

          // 保存被删除的组件，用于撤销操作
          const deletedComponents = keys.map(idx => deepCopy(topoData.components[idx]));
          command.deletedComponents = deletedComponents;

          // 排序并倒序删除
          keys.sort((a, b) => a - b).reverse().forEach((idx) => {
            topoData.components.splice(idx, 1);
          });
          break;
        }

        case 'move': {
          const { dx, dy, items } = command;
          items.forEach((item:any) => {
            item.style.position.x += dx;
            item.style.position.y += dy;
          });
          break;
        }

        case 'copy-add': {
          this.clearSelectedComponent();
          const identifiers = command.items.map(() => uid());
          const addedComponents = [];

          for (const [i, item] of command.items.entries()) {
            let component = deepCopy(item);
            component.identifier = identifiers[i];
            component.identifiers = identifiers;
            component.name = `${component.type}${topoData.components.length}`;
            component.style = {
              ...component.style,
              visible: true,
              position: {
                x: component.style.position.x + 25,
                y: component.style.position.y + 25,
                h: component.style.position.h,
                w: component.style.position.w,
              },
            };
            topoData.components.push(component);
            addedComponents.push(component);
            this.addSelectedComponent(component);
            this.increaseCopyCount();
          }

          // 保存添加的组件信息，用于撤销操作
          command.addedComponents = addedComponents;
          break;
        }

        default:
          // eslint-disable-next-line no-console
          console.warn('不支持的命令.');
      }

      // 异步操作处理，Pinia 不支持 `this.$nextTick`
      await nextTick();  // 使用 Vue 3 的 `nextTick`
      undoStack.push(command); // 将操作添加到撤销栈

      // 执行新操作时清空重做栈
      this.redoStack = [];
    },

    async undo() {
      const command = this.undoStack.pop();
      if (!command) return;

      // 将撤销的操作推入重做栈
      this.redoStack.push(command);

      switch (command.op) {
        case 'add': {
          const component = command.component;
          const index = this.topoData.components.findIndex(
            (comp) => comp.identifier === component.identifier
          );
          if (index !== -1) {
            this.topoData.components.splice(index, 1);
          }
          break;
        }

        case 'del': {
          // 恢复被删除的组件
          if (command.deletedComponents && Array.isArray(command.deletedComponents)) {
            command.deletedComponents.forEach((component: any) => {
              this.topoData.components.push(component);
            });
          }
          break;
        }

        case 'move': {
          const { dx, dy, items } = command;
          if (items && Array.isArray(items)) {
            items.forEach((item: any) => {
              item.style.position.x -= dx;
              item.style.position.y -= dy;
            });
          }
          break;
        }

        case 'copy-add': {
          // 移除复制添加的组件
          if (command.addedComponents && Array.isArray(command.addedComponents)) {
            const identifiersToRemove = command.addedComponents.map((item: any) => item.identifier);
            this.topoData.components = this.topoData.components.filter(
              (comp) => !identifiersToRemove.includes(comp.identifier)
            );
          }
          break;
        }

        default:
          console.warn('不支持的撤销操作:', command.op);
          break;
      }

      // 异步操作处理，Pinia 不支持 `this.$nextTick`
      await nextTick();  // 使用 Vue 3 的 `nextTick`
    },

    async redo() {
      const command = this.redoStack.pop();
      if (!command) return;

      // 将重做的操作推回撤销栈
      this.undoStack.push(command);

      switch (command.op) {
        case 'add': {
          const component = command.component;
          this.topoData.components.push(component);
          break;
        }

        case 'del': {
          // 重新删除组件
          if (command.deletedComponents && Array.isArray(command.deletedComponents)) {
            const identifiersToRemove = command.deletedComponents.map((item: any) => item.identifier);
            this.topoData.components = this.topoData.components.filter(
              (comp) => !identifiersToRemove.includes(comp.identifier)
            );
          }
          break;
        }

        case 'move': {
          const { dx, dy, items } = command;
          if (items && Array.isArray(items)) {
            items.forEach((item: any) => {
              item.style.position.x += dx;
              item.style.position.y += dy;
            });
          }
          break;
        }

        case 'copy-add': {
          // 重新添加复制的组件
          if (command.addedComponents && Array.isArray(command.addedComponents)) {
            command.addedComponents.forEach((component: any) => {
              this.topoData.components.push(component);
            });
          }
          break;
        }

        default:
          console.warn('不支持的重做操作:', command.op);
          break;
      }

      // 异步操作处理
      await nextTick();
    },

    setSelectedComponent(component: any) {
      const fuid = uid;
      if (!component.identifier) {
        component.identifier = fuid();
      }
      this.selectedComponents = [component.identifier];
      this.selectedComponentMap = { [component.identifier]: component };
      this.selectedComponent = component;
    },

    addSelectedComponent(component: any) {
      const fuid = uid;
      if (!component.identifier) {
        component.identifier = fuid();
      }
      if (this.selectedComponentMap[component.identifier]) {
        return;
      }
      this.selectedComponents.push(component.identifier);
      this.selectedComponentMap[component.identifier] = component;
      this.selectedComponent = component;
    },

    removeSelectedComponent(component: any) {
      if (!component.identifier) return;
      const index = this.selectedComponents.indexOf(component.identifier);
      if (index > -1) {
        this.selectedComponents.splice(index, 1);
      }
      delete this.selectedComponentMap[component.identifier];
      if (this.selectedComponent && component.identifier === this.selectedComponent.identifier) {
        this.selectedComponent = null;
      }
      if (this.selectedComponents.length === 1) {
        const _component = this.selectedComponentMap[this.selectedComponents[0]];
        this.selectedComponent = _component;
      }
    },

    clearSelectedComponent() {
      this.selectedComponents = [];
      this.selectedComponentMap = {};
      this.selectedComponent = null;
    },

    setLayerSelected(selected: boolean) {
      this.selectedIsLayer = selected;
    },

    setCopySrcItems(items: any) {
      this.copySrcItems = items;
      this.copyCount = 0;
    },

    increaseCopyCount() {
      this.copyCount++;
    },

    setMqttData(mqttData: any) {
      this.mqttData = mqttData;
    },
  }
  

})
