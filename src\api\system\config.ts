import request from '/@/utils/request'

// 定义查询参数列表的请求参数类型
interface ListConfigParams {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;  // 额外的查询条件
}

// 定义配置项的返回类型
interface Config {
  configId: string;
  configKey: string;
  configValue: string;
  remark?: string;
  createTime: string;
}

// 定义 API 返回的基础响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 查询参数列表
export function listConfig(query: ListConfigParams): Promise<ApiResponse<any>> {
  return request({
    url: '/system/config/list',
    method: 'get',
    params: query,
  })
}

// 查询参数详细
export function getConfig(configId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/system/config/${configId}`,
    method: 'get',
  })
}

// 根据参数键名查询参数值
export function getConfigKey(configKey: string): Promise<ApiResponse<Config>> {
  return request({
    url: `/system/config/configKey/${configKey}`,
    method: 'get',
  })
}

// 新增参数配置
export function addConfig(data: any): Promise<ApiResponse<null>> {
  return request({
    url: '/system/config',
    method: 'post',
    data: data,
  })
}

// 修改参数配置
export function updateConfig(data: any): Promise<ApiResponse<null>> {
  return request({
    url: '/system/config',
    method: 'put',
    data: data,
  })
}

// 删除参数配置
export function delConfig(configId: string): Promise<ApiResponse<null>> {
  return request({
    url: `/system/config/${configId}`,
    method: 'delete',
  })
}

// 刷新参数缓存
export function refreshCache(): Promise<ApiResponse<null>> {
  return request({
    url: '/system/config/refreshCache',
    method: 'delete',
  })
}
