<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px">
                    <el-form-item label="登录地址" prop="ipaddr">
                        <el-input v-model="state.tableData.param.ipaddr" clearable size="default" placeholder="请输入登录地址"
                            style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="state.tableData.param.userName" clearable size="default"
                            placeholder="请输入用户名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="序号" type="index" align="center" width="80">
                    <template #default="scope">
                        <span>{{ (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + scope.$index + 1
                            }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="会话编号" align="center" prop="tokenId" width="150" :show-overflow-tooltip="true" />
                <el-table-column label="登录名称" align="center" prop="userName" :show-overflow-tooltip="true" />
                <el-table-column label="部门名称" align="center" prop="deptName" />
                <el-table-column label="主机" align="center" prop="ipaddr" :show-overflow-tooltip="true" />
                <el-table-column label="登录地点" align="center" prop="loginLocation" :show-overflow-tooltip="true" />
                <el-table-column label="浏览器" align="center" prop="browser" />
                <el-table-column label="操作系统" align="center" prop="os" />
                <el-table-column label="登录时间" align="center" prop="loginTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.loginTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" icon="el-icon-delete"
                            @click="handleForceLogout(scope.row)"
                            v-auths="['monitor:online:forceLogout']"><el-icon><ele-DeleteFilled /></el-icon>强退</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="systemDic">
import {  reactive, onMounted } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { parseTime } from '/@/utils/next'
import { forceLogout, list } from '/@/api/monitor/online';

// 引入组件
// 定义变量内容
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
    },
});
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await list(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
    }
    getTableData()
}


// 删除新闻分类
const handleForceLogout = (row: { userName: string; tokenId: string; }) => {
    ElMessageBox.confirm('是否确认强退名称为"' + row.userName + '"的用户？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            forceLogout(row.tokenId).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
});
</script>
