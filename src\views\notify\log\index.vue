<template>
    <div class="layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true"  v-show="showSearch">
                    <el-form-item label="渠道编号" prop="channelId">
                        <el-input v-model="state.tableData.param.channelId" clearable size="default"
                            placeholder="请输入渠道编号" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="通知模版编号" prop="notifyTemplateId">
                        <el-input v-model="state.tableData.param.notifyTemplateId" clearable size="default"
                            placeholder="请输入渠道编号" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="发送账号" prop="sendAccount">
                        <el-input v-model="state.tableData.param.sendAccount" placeholder="请输入发送账号" clearable />
                    </el-form-item>
                    <el-form-item label="业务编码" prop="serviceCode">
                        <el-select v-model="state.tableData.param.serviceCode" placeholder="请选择通知业务" clearable
                            style="width: 180px; display: inline-block">
                            <el-option v-for="dict in service_code_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="创建时间">
                        <el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
                            value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
                            end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['iot:channel:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['iot:channel:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="编号" align="center" prop="id" width="80" />
                <el-table-column label="渠道编号" align="center" prop="channelId" width="100" />
                <el-table-column label="渠道账号" align="center" prop="channelName" />
                <el-table-column label="通知模版编号" align="center" prop="notifyTemplateId" width="160" />
                <el-table-column label="模板名称" align="center" prop="templateName" />
                <el-table-column label="业务编码" align="center" prop="serviceCode">
                    <template #default="scope">
                        <dict-tag :options="service_code_list" :value="scope.row.serviceCode" />
                    </template>
                </el-table-column>
                <el-table-column label="发送账号" align="center" prop="sendAccount" min-width="150" />
                <el-table-column label="发送状态" align="center" prop="sendStatus" width="100">
                    <template #default="scope">
                        <span v-if="scope.row.sendStatus == 0" class="status-error-wrap">失败</span>
                        <span v-if="scope.row.sendStatus == 1" class="status-success-wrap">成功</span>
                    </template>
                </el-table-column>
                <el-table-column label="发送时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
                    <template #default="scope">
                        <el-button size="mini" type="text" icon="el-icon-view" @click="handleUpdate(scope.row)"
                            v-auths="['notify:log:query']">详情</el-button>
                        <el-button size="mini" type="text" icon="el-icon-delete" @click="onRowDel(scope.row)"
                            v-auths="['notify:log:remove']">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addDateRange } from '/@/utils/next';
const dictStore = useDictStore();  // 使用 Pinia store
import { parseTime } from '/@/utils/next'
import { listLog, getLog, delLog, addLog, updateLog } from '/@/api/notify/log';
import { download } from '/@/utils/request';
// 定义变量内容
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            channelId: '',
            notifyTemplateId: '',
            sendAccount: '',
            serviceCode: ''

        },
    },
});
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //id
const showSearch = ref(true)    // 显示搜索条件
const dateRange = ref<[string, string]>(['', '']); //时间范围
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const service_code_list = ref<statusOption[]>([]);
const channel_type_list = ref<statusOption[]>([])
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const data = addDateRange(state.tableData.param, dateRange.value)
        const response = await listLog(data);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        channelId: '',
        notifyTemplateId: '',
        sendAccount: '',
        serviceCode: ''
    }
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { id: string; }) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 删除
const onRowDel = (row: any) => {
    const idss = row.id || ids.value;
    ElMessageBox.confirm(`是否确认删除通知日志编号为${idss}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delLog(idss).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('notify/channel/export', {
        ...state.tableData.param
    }, `channel_${new Date().getTime()}.xlsx`)
}
// 获取状态数据
const getdictdata = async () => {
    try {
        service_code_list.value = await dictStore.fetchDict('notify_service_code')
        channel_type_list.value = await dictStore.fetchDict('notify_channel_type')
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
