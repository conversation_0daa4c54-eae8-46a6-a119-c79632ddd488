// src/stores/dictStore.ts
import { defineStore } from 'pinia';
import { getDicts } from '/@/api/system/dict/data';

// 定义字典存储类型
interface DictState {
  dict: Record<string, any>;
}

export const useDictStore = defineStore('dictStore', {
  state: (): DictState => ({
    dict: {} // 用于缓存字典数据
  }),

  actions: {
    // 获取字典数据的方法
    async fetchDict(type: string): Promise<any> {
      // 1. 检查缓存中是否有数据
      const cachedDict = this.dict[type];
      if (cachedDict) {
        // console.log(`Using cached data for ${type}`);
        return cachedDict;
      }

      // 2. 如果没有缓存，发起请求
      try {
        const response = await getDicts(type);        
        const data = await response.data.data;

        // 3. 请求成功后将数据缓存到 Pinia store
        this.dict[type] = data;
        // console.log(`Fetched and cached data for ${type}`);
        return data;
      } catch (error) {
        // console.error(`Error fetching dictionary data for ${type}:`, error);
        throw error; // 可以选择抛出错误，或者返回一个空对象等
      }
    },
    // 清除字典缓存的方法
    clearDictCache(type?: string): void {
      if (type) {
        // 清除特定字典类型的缓存
        delete this.dict[type];
        // console.log(`Cache for dictionary type '${type}' cleared`);
      } else {
        // 清除所有字典缓存
        this.dict = {};
        // console.log("All dictionary caches cleared");
      }
    },
  },
});
