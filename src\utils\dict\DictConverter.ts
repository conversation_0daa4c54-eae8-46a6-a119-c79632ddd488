import DictOptions from './DictOptions';
import DictData from './DictData';

// Define the types of dict and dictMeta based on your application logic.
interface Dict {
  [key: string]: any; // Assuming dict is a plain object with string keys and any value type
}

interface DictMeta {
  labelField: string;
  valueField: string;
  // Add other fields if necessary
}

export default function createDictData(dict: Dict, dictMeta: DictMeta): DictData {
  // Determine the label and value fields based on the provided dictMeta and options
  const label = determineDictField(dict, dictMeta.labelField, ...DictOptions.DEFAULT_LABEL_FIELDS);
  const value = determineDictField(dict, dictMeta.valueField, ...DictOptions.DEFAULT_VALUE_FIELDS);
  
  console.log(new DictData(dict[label as any], dict[value as any], dict), 'new DictData(dict[label], dict[value], dict)');
  console.log(dictMeta, 'dictMeta');
  
  // Return a new DictData object
  return new DictData(dict[label as any], dict[value as any], dict);
}

/**
 * Determines the dictionary field based on the available fields
 * @param {Dict} dict The dictionary object
 * @param {string} fields The field names to search for in the dictionary
 * @returns {string | undefined} The field value found, or undefined if not found
 */
function determineDictField(dict: Dict, ...fields: string[]): string | undefined {
  // Find the first field that exists in the dict
  return fields.find(f => Object.prototype.hasOwnProperty.call(dict, f));
}
