<template>
	<div class="system-dic-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<div class="system-user-search mb15">
				<el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
					<el-form-item label="参数名称" prop="configName">
						<el-input v-model="state.tableData.param.configName" clearable aria-label="First Name" placeholder="请输入用户名称"
							style="width: 240px" />
					</el-form-item>
					<el-form-item label="参数键名" prop="configKey">
						<el-input v-model="state.tableData.param.configKey" clearable placeholder="请输入用户手机号码"
							style="width: 240px" />
					</el-form-item>
					<el-form-item label="系统内置"  prop="configType">
						<el-select v-model="state.tableData.param.configType"  placeholder="用户状态" clearable
							style="width: 240px">
							<el-option v-for="config in configTypelist" :key="config.dictValue" :label="config.dictLabel"
								:value="config.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item label="创建时间">
						<el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
							value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
							end-placeholder="结束日期"></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button size="default" type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:config:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddDic('add')">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button v-auths="['system:config:edit']" size="default" type="success" class="ml10"
							:disabled="single" @click="onOpenEditDic('edit', undefined)">
							<el-icon><ele-EditPen /></el-icon>
							修改
						</el-button>
						<el-button v-auths="['system:config:remove']" size="default" type="danger" class="ml10"
							:disabled="multiple" @click="onRowDel">
							<el-icon><ele-DeleteFilled /></el-icon>
							删除
						</el-button>
						<el-button v-auths="['system:config:export']" size="default" type="warning" class="ml10"
							@click="handleExport">
							<el-icon><ele-Download /></el-icon>
							导出
						</el-button>
						<el-button v-auths="['system:config:refresh']" size="default" type="info" class="ml10"
							@click="handleRefreshCache">
							<el-icon><ele-Refresh /></el-icon>
							刷新缓存
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading"
				@selection-change="handleSelectionChange" border style="width: 100%"
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column type="selection" width="55" align="center"></el-table-column>
				<el-table-column label="参数主键" align="center" prop="configId"></el-table-column>
				<el-table-column label="参数名称" align="center" prop="configName" show-overflow-tooltip />
                <el-table-column label="参数键名" align="center" prop="configKey" :show-overflow-tooltip="true" />
                <el-table-column label="参数键值" align="center" prop="configValue" />
				<el-table-column label="系统内置" align="center" prop="dictLabel">
					<template #default="scope">
						<el-tag :class="scope.row.configType == 'Y' ? 'primary-tag' : ''" :type="scope.row.configType == 'Y'? 'success' : 'danger'">{{ scope.row.configType == 'Y' ? '是' :
							'否'
							}}</el-tag>
					</template>
				</el-table-column>
				<el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
				<el-table-column label="创建时间" align="center" prop="createTime" width="180">
					<template #default="scope">
						<span>{{ (scope.row.createTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button size="default" text type="primary" icon="el-icon-edit"
							@click="onOpenEditDic('edit', scope.row)"
							v-auths="['system:config:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
						<el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
							v-auths="['system:config:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum" background
				v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total">
			</el-pagination>
		</el-card>
		<ConfigDialog ref="ConfigDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { addDateRange } from '/@/utils/next';
import { refreshCache } from '/@/api/system/dict/type';
import { download } from '/@/utils/request';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { delConfig, listConfig } from '/@/api/system/config';

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const ConfigDialog = defineAsyncComponent(() => import('/@/views/system/config/dialog.vue'));

// 定义变量内容
const ConfigDialogRef = ref();
const state = reactive<SysDicState>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
			configName: undefined,
			configKey: undefined,
			configType: ''
		},
	},
});
const showSearch = ref(true)    // 显示搜索条件
interface configTypeOption {
	dictValue: string;
	dictLabel: string;
}
const configTypelist = ref<configTypeOption[]>([]); //状态列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //configId
const dateRange = ref<[string, string]>(['', '']); //时间范围
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const data = addDateRange(state.tableData.param, dateRange.value)
		const response = await listConfig(data);
		state.tableData.data = response.data.rows;
		state.tableData.total = response.data.total;
	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally {
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	}
};
/** 重置按钮操作 */
const resetQuery = () => {
	state.tableData.param = {
		pageNum: 1,
		pageSize: 10,
		configName: undefined,
		configKey: undefined,
		configType: ''
	}
}
// 获取状态数据
const getdictdata = async () => {
	try {
		configTypelist.value = await dictStore.fetchDict('sys_yes_no')
		// 处理参数数据
	} catch (error) {
		console.error('获取参数数据失败:', error);
	}
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
	ids.value = selection.map((item: { configId: string; }) => item.configId);
	console.log(ids.value);

	single.value = selection.length != 1;
	multiple.value = !selection.length;
}
// 打开新增参数弹窗
const onOpenAddDic = (type: string) => {
	ConfigDialogRef.value.openDialog(type);
};
// 打开修改参数弹窗
const onOpenEditDic = (type: string, row: RowConfigType | undefined) => {
	var configId = ''
	if (!row) {
		configId = ids.value
	} else {
		configId = row.configId
	}
	ConfigDialogRef.value.openDialog(type, row, configId);
};
// 删除参数
const onRowDel = (row: RowConfigType) => {
	const configIds = row.configId || ids.value;
	ElMessageBox.confirm(`此操作将永久删除参数名称：“${configIds}”的数据项，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			delConfig(configIds).then(() => {
				getTableData();
				ElMessage.success('删除成功');
			})
		})
		.catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
	const exportParams = {
		pageNum: state.tableData.param.pageNum || 1,
		pageSize: state.tableData.param.pageSize || 10,
		configName: state.tableData.param.configName || '',
		configKey: state.tableData.param.configKey || '',
		configType: state.tableData.param.configType || '',
	};
	download('system/dict/type/export', {
		...state.tableData.param
	}, `config_${new Date().getTime()}.xlsx`)
}
// 刷新缓存处理函数
const handleRefreshCache = () => {
	try {
		// 调用刷新缓存的 API
		refreshCache().then(() => {
			ElMessage.success('刷新成功');
			// 清除参数缓存
			dictStore.clearDictCache();
		});

	} catch (error) {
		ElMessage.success('刷新失败');
		console.error('刷新缓存失败:', error);
	}
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>
<style scoped>

.primary-tag {
    background-color: #ebf5ff;
    /* 设置类似 primary 的蓝色背景 */
    color: #1890ff;
}
</style>