<template>
    <el-dialog title="通知模板场景" v-model="open" width="900px" append-to-body style="position: absolute; top: 100px;">
        <el-form :model="state.tableData.param" ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="模版名称" prop="sceneName">
                <el-input v-model="state.tableData.param.sceneName" placeholder="请输入模版名称" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="渠道类型">
                <el-select v-model="state.tableData.param.channelType" placeholder="请选择渠道类型" clearable size="default"
                    style="width: 200px">
                    <el-option v-for="option in channel_type_list" :key="option.dictValue" :label="option.dictLabel"
                        :value="option.dictValue"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"> <el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-table ref="multipleTable" v-loading="state.tableData.loading" :data="state.tableData.notifyTempList"
            @select="handleSelectionChange" @select-all="handleSelectionAll" size="default" border
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="编号" width="60" align="center" prop="id" />
            <el-table-column label="模板名称" align="center" prop="name" min-width="100" />
            <el-table-column label="渠道类型" align="center" prop="channelType">
                <template #default="scope">
                    <dict-tag :options="channel_type_list" :value="scope.row.channelType" />
                </template>
            </el-table-column>
            <el-table-column label="渠道账号" align="center" prop="channelName"></el-table-column>
            <el-table-column label="服务商" align="center" prop="provider"></el-table-column>
            <el-table-column label="是否启用" align="center" prop="status">
                <template #default="scope">
                    <el-switch v-model="scope.row.status" disabled :active-value="1" :inactive-value="0" />
                </template>
            </el-table-column>
        </el-table>

        <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
            v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleEmitData">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </span>
        </template>

    </el-dialog>
</template>

<script setup lang="ts" name="">
import { reactive, ref, nextTick } from 'vue';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listTemplate } from '/@/api/notify/template';
const dictStore = useDictStore();  // 使用 Pinia store
const emit = defineEmits(['notifyEvent']); // 定义 emit
const state = reactive({
    tableData: {
        notifyTempList: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            sceneName: '',
            channelType: undefined,
            serviceCode: 'alert',
            // name: null,
        },
    },
});
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const channel_type_list = ref<Option[]>([]);//联网方式
const open = ref(false);
const ids = ref<number[]>([]);
const multipleTable = ref();
const selectNotifyTemps = ref<any[]>([]);
/** 查询产品列表 */
const getList = () => {
    state.tableData.loading = true;
    state.tableData.notifyTempList = []
    try {
        listTemplate(state.tableData.param).then((response) => {
            state.tableData.notifyTempList = response.data.rows;
            state.tableData.total = response.data.total;
            // 设置选中
            if (selectNotifyTemps.value) {
                state.tableData.notifyTempList.forEach((row) => {
                    nextTick(() => {
                        if (selectNotifyTemps.value.some((x) => x.id === row.id)) {
                            multipleTable.value.toggleRowSelection(row, true);
                        }
                    });
                });
            } else {
                // 初始化
                selectNotifyTemps.value = [];
            }
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        sceneName: '',
        channelType: undefined,
        serviceCode: 'alert'
    }
    handleQuery();
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: any, row: any) => {
    // 场景ID是否存在于原始设备ID数组中
    let index = ids.value.indexOf(row.id);
    // 是否选中
    let value = selection.indexOf(row);
    if (index == -1 && value != -1) {
        // 不存在且选中
        ids.value.push(row.id);
        selectNotifyTemps.value.push(row);
    } else if (index != -1 && value == -1) {
        // 存在且取消选中
        ids.value.splice(index, 1);
        selectNotifyTemps.value.splice(index, 1);
    }
}
// 全选事件处理
const handleSelectionAll = (selection: any) => {
    for (let i = 0; i < state.tableData.notifyTempList.length; i++) {
        // 设备ID是否存在于原始设备ID数组中
        let index = ids.value.indexOf(state.tableData.notifyTempList[i].id);
        // 是否选中
        let value = selection.indexOf(state.tableData.notifyTempList[i]);
        if (index == -1 && value != -1) {
            // 不存在且选中
            ids.value.push(state.tableData.notifyTempList[i].id);
            selectNotifyTemps.value.push(state.tableData.notifyTempList[i]);
        } else if (index != -1 && value == -1) {
            // 存在且取消选中
            ids.value.splice(index, 1);
            selectNotifyTemps.value.splice(index, 1);
        }
    }

}
// 获取状态数据
const getdictdata = async () => {
    try {
        channel_type_list.value = await dictStore.fetchDict('notify_channel_type ')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 关闭通知模板场景列表
const cancel = () => {
    open.value = false;
}
/**确定通知模板场景，设备传递给父组件 */
const handleEmitData = () => {
    emit('notifyEvent', selectNotifyTemps.value);
    closeDialog()
}
/**关闭对话框 */
const closeDialog = () => {
    selectNotifyTemps.value = []
    open.value = false;
}
// 打开弹窗
const openDialog = () => {
    open.value = true
    getList();
    getdictdata()
};

// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getList();
};

// 暴露变量
defineExpose({
    openDialog,
    selectNotifyTemps,
    ids,
    getList
});
</script>
<style lang="scss" scoped></style>