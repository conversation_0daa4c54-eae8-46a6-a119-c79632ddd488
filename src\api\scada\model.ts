import request from '/@/utils/request' // 确保这个路径正确指向你的 axios 封装

/**
 * 查询模型管理列表
 * @param query 查询参数
 */
export function listModel(query?: any) {
    return request({
        url: '/scada/model/list',
        method: 'get',
        params: query,
    })
}

/**
 * 查询模型管理详细
 * @param id 模型ID
 */
export function getModel(id: string | number) {
    return request({
        url: `/scada/model/${id}`,
        method: 'get',
    })
}

/**
 * 新增模型管理
 * @param data 表单数据
 */
export function addModel(data: any) {
    return request({
        url: '/scada/model',
        method: 'post',
        data,
    })
}

/**
 * 修改模型管理
 * @param data 表单数据
 */
export function updateModel(data: any) {
    return request({
        url: '/scada/model',
        method: 'put',
        data,
    })
}

/**
 * 删除模型管理
 * @param id 模型ID
 */
export function delModel(id: string | number) {
    return request({
        url: `/scada/model/${id}`,
        method: 'delete',
    })
}