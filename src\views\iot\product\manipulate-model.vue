<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="640">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
                        <el-form-item label="模型名称" prop="modelName">
                            <el-input v-model="state.ruleForm.modelName" placeholder="请输入通用物模型标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                    <el-form-item label="模型标识" prop="identifier">
                        <el-input v-model="state.ruleForm.identifier" placeholder="请输入通用物模型标题" clearable></el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                    <el-form-item label="寄存器地址" prop="regAddr" v-if="state.ruleForm.isModbus">
                        <el-input v-model="state.ruleForm.regAddr" style="width: 385px" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="15" :md="15" :lg="15" :xl="15" class="mb20">
                    <el-form-item label="模型排序" prop="modelOrder">
                        <el-input-number v-model="state.ruleForm.modelOrder" controls-position="right"
                            placeholder="请输入排序" class="w100" />
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                    <el-form-item label="模型类别" prop="type">
                        <el-radio-group v-model="state.ruleForm.type" @change="typeChange(state.ruleForm.type)">
                            <el-radio-button :value="1">属性</el-radio-button>
                            <el-radio-button :value="2">功能</el-radio-button>
                            <el-radio-button :value="3">事件</el-radio-button>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-form-item label="模型特性" prop="property">
                    <el-row>
                        <el-tooltip effect="dark" content="设备详情中以图表方式展示" placement="top">
                            <el-checkbox name="isChart" label="图表展示" @change="isChartChange"
                                v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isChart" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="实时显示监测数据，但是不会存储到数据库" placement="top">
                            <el-checkbox name="isMonitor" label="实时监测" @change="isMonitorChange"
                                v-show="state.ruleForm.type == 1" v-model="state.ruleForm.isMonitor" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备上报数据，但是平台不能下发指令" placement="top">
                            <el-checkbox name="isReadonly" label="只读数据" @change="isReadonlyChange"
                                :disabled="state.ruleForm.type == 3" v-model="state.ruleForm.isReadonly" :true-value="1"
                                :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备上报的数据会存储到数据库作为历史数据" placement="top">
                            <el-checkbox name="isHistory" label="历史存储" v-model="state.ruleForm.isHistory"
                                :true-value="1" :false-label="0"></el-checkbox>
                        </el-tooltip>
                        <el-tooltip effect="dark" content="设备分享时需要指定是否拥有该权限" placement="top">
                            <el-checkbox name="isSharePerm" label="分享权限" v-model="state.ruleForm.isSharePerm"
                                :true-value="1" :false-label="0"></el-checkbox>
                        </el-tooltip>
                    </el-row>
                </el-form-item>
                <el-divider />
                <el-form-item label="数据类型" prop="datatype">
                    <el-select v-model="state.ruleForm.datatype" placeholder="请选择数据类型" style="width: 175px">
                        <!-- @change="dataTypeChange" -->
                        <el-option key="integer" label="整数" value="integer"></el-option>
                        <el-option key="decimal" label="小数" value="decimal"></el-option>
                        <el-option key="bool" label="布尔" value="bool"
                            :disabled="state.ruleForm.isChart == 1"></el-option>
                        <el-option key="enum" label="枚举" value="enum"
                            :disabled="state.ruleForm.isChart == 1"></el-option>
                        <el-option key="string" label="字符串" value="string"
                            :disabled="state.ruleForm.isChart == 1"></el-option>
                        <el-option key="array" label="数组" value="array"
                            :disabled="state.ruleForm.isChart == 1"></el-option>
                        <el-option key="object" label="对象" value="object"
                            :disabled="state.ruleForm.isChart == 1"></el-option>
                    </el-select>
                </el-form-item>
                <div v-if="state.ruleForm.datatype == 'integer' || state.ruleForm.datatype == 'decimal'">
                    <el-form-item label="取值范围">
                        <el-row>
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.min" placeholder="最小值" type="number" />
                            </el-col>
                            <el-col :span="2" align="center">到</el-col>
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.max" placeholder="最大值" type="number" />
                            </el-col>
                        </el-row>
                    </el-form-item>
                    <el-form-item label="单位">
                        <el-input v-model="state.ruleForm.specs.unit" placeholder="请输入单位，例如：℃" style="width: 385px" />
                    </el-form-item>
                    <el-form-item label="步长">
                        <el-input-number controls-position="right" v-model="state.ruleForm.specs.step"
                            placeholder="请输入步长，例如：1" type="number" style="width: 385px" />
                    </el-form-item>
                    <el-form-item label="计算公式" prop="formula">
                        <template #label>
                            <div style="display:flex;align-items: center">
                                <span>计算公式</span>
                                <el-tooltip content="<div>
                                    设备上行数据经计算公式计算后显示 。
                                    <br />
                                    公式中的%s为占位符，是固定字段。
                                    <br />
                                    如：
                                    <br />
                                    加：%s+10
                                    <br />
                                    减：%s-10
                                    <br />
                                    乘：%s*10
                                    <br />
                                    除：%s/10
                                    <br />
                                    除(保留小数)：%s%10.00
                                    <br />
                                </div>" raw-content style="cursor: pointer" effect="light" placement="top">
                                    <el-icon><ele-QuestionFilled /></el-icon>
                                </el-tooltip>
                            </div>
                        </template>
                        <el-input v-model="state.ruleForm.formula" style="width: 385px" />
                    </el-form-item>
                </div>
                <div v-if="state.ruleForm.datatype == 'bool'">
                    <el-form-item label="布尔值" prop="">
                        <el-row style="margin-bottom: 10px">
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.falseText" placeholder="例如：关闭" />
                            </el-col>
                            <el-col :span="10" :offset="1">（0 值对应文本）</el-col>
                        </el-row>
                        <el-row>
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.trueText" placeholder="例如：打开" />
                            </el-col>
                            <el-col :span="10" :offset="1">（1 值对应文本）</el-col>
                        </el-row>
                    </el-form-item>
                </div>
                <div v-if="state.ruleForm.datatype == 'enum'">
                    <el-form-item label="展示方式">
                        <el-select v-model="state.ruleForm.specs.showWay" placeholder="请选择展示方式" style="width: 175px">
                            <el-option key="select" label="下拉框" value="select"></el-option>
                            <el-option key="button" label="按钮" value="button"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="枚举项" prop="">
                        <el-row v-for="(item, index) in state.ruleForm.specs.enumList" :key="'enum' + index"
                            style="margin-bottom: 10px">
                            <el-col :span="8">
                                <el-input v-model="item.value" placeholder="参数值，例如：0" />
                            </el-col>
                            <el-col :span="8" :offset="1">
                                <el-input v-model="item.text" placeholder="参数描述，例如：中速档位" />
                            </el-col>
                            <el-col :span="4" :offset="1" v-if="index != 0"><a style="color: #f56c6c;cursor: pointer;"
                                    @click="removeEnumItem(index)">删除</a></el-col>
                            <el-col :span="4" :offset="1" v-else style="width: 28px;"></el-col>
                        </el-row>
                        <div>
                            +
                            <a style="color: #409eff;cursor: pointer;" @click="addEnumItem()">添加枚举项</a>
                        </div>
                    </el-form-item>
                </div>
                <div v-if="state.ruleForm.datatype == 'string'">
                    <el-form-item label="最大长度" prop="">
                        <el-row>
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.maxLength" placeholder="例如：1024"
                                    type="number" />
                            </el-col>
                            <el-col :span="14" :offset="1">（字符串的最大长度）</el-col>
                        </el-row>
                    </el-form-item>
                </div>
                <div v-if="state.ruleForm.datatype == 'array'">
                    <el-form-item label="元素个数" prop="">
                        <el-row>
                            <el-col :span="9">
                                <el-input v-model="state.ruleForm.specs.arrayCount" placeholder="例如：5" type="number" />
                            </el-col>
                        </el-row>
                    </el-form-item>
                    <el-form-item label="数组类型" prop="">
                        <el-radio-group v-model="state.ruleForm.specs.arrayType">
                            <el-radio value="integer">整数</el-radio>
                            <el-radio value="decimal">小数</el-radio>
                            <el-radio value="string">字符串</el-radio>
                            <el-radio value="object">对象</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="对象参数" v-if="state.ruleForm.specs.arrayType == 'object'">
                        <div style="background-color: #f8f8f8; border-radius: 5px">
                            <el-row style="padding: 0 10px 5px" v-for="(item, index) in state.ruleForm.specs.params"
                                :key="index">
                                <div style="margin-top: 5px" v-if="index == 0"></div>
                                <el-col :span="18">
                                    <el-input readonly v-model="item.name" size="default" placeholder="请选择设备"
                                        style="margin-top: 3px">
                                        <template v-slot:prepend>
                                            <el-tag size="default" effect="dark"
                                                style="margin-left: -21px; height: 26px; line-height: 26px">{{
                                                    item.order }}</el-tag>
                                            {{ state.ruleForm.identifier + '_' + item.id }}
                                        </template>
                                        <template v-slot:append>
                                            <el-button @click="editParameter(item, index)" size="default">编辑</el-button>
                                        </template>
                                    </el-input>
                                </el-col>
                                <el-col :span="2" :offset="2">
                                    <el-button size="default" plain type="danger" style="padding: 5px"
                                        @click="removeParameter(index)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                </el-col>
                            </el-row>
                        </div>
                        <div>
                            +
                            <a style="color: #409eff; cursor: pointer;" @click="addParameter()">添加参数</a>
                        </div>
                    </el-form-item>
                </div>
                <div v-if="state.ruleForm.datatype == 'object'">
                    <el-form-item label="对象参数" prop="">
                        <div style="background-color: #f8f8f8; border-radius: 5px">
                            <el-row style="padding: 0 10px 5px" v-for="(item, index) in state.ruleForm.specs.params"
                                :key="index">
                                <div style="margin-top: 5px" v-if="index == 0"></div>
                                <el-col :span="18">
                                    <el-input readonly v-model="item.name" size="default" placeholder="请选择设备"
                                        style="margin-top: 3px">
                                        <template v-slot:prepend>
                                            <el-tag size="default" effect="dark"
                                                style="margin-left: -21px; height: 26px; line-height: 26px">{{
                                                    item.order }}</el-tag>
                                            {{ state.ruleForm.identifier + '_' + item.id }}
                                        </template>
                                        <template v-slot:append>
                                            <el-button @click="editParameter(item, index)">编辑</el-button>
                                        </template> </el-input>
                                </el-col>
                                <el-col :span="2" :offset="2">
                                    <el-button size="default" plain type="danger" style="padding: 5px"
                                        @click="removeParameter(index)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                </el-col>
                            </el-row>
                        </div>
                        <div>
                            +
                            <a style="color: #409eff; cursor: pointer;" @click="addParameter()">添加参数</a>
                        </div>
                    </el-form-item>
                </div>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
        <!--物模型参数类型-->
        <thingsParameter v-model="paramData" @dataEvent="getParamData($event)" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useUserInfo } from '/@/stores/userInfo';
import { getTemplate, addTemplate, updateTemplate } from '/@/api/iot/template';
const thingsParameter = defineAsyncComponent(() => import('/@/views/iot/template/parameter.vue'));

// import thingsParameter from './parameter.vue';
const userInfoStore = useUserInfo();
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        modelName: '', // 通用物模型标题
        identifier: '', // 通用物模型类型
        modelOrder: 0,
        modelId: '',
        type: 1 as any,
        isChart: 1,
        isMonitor: 1 as any,
        isReadonly: 1 as any,
        isHistory: 1 as any,
        isSharePerm: 1 as any,
        datatype: 'integer' as any,
        regAddr: '' as any,
        formula: '' as any,
        specs: {
            enumList: [
                {
                    value: '',
                    text: '',
                },
            ],
            arrayType: 'integer',
            arrayCount: 5,
            showWay: 'select', // 显示方式select=下拉选择框，button=按钮
            params: [] as any,
            min: '',
            max: '',
            unit: '',
            step: undefined,
            falseText: '',
            trueText: '',
            maxLength: '',
        },
        productId: '' as any,
        productName: '' as any,
        isModbus: '' as any
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}
// 对象类型参数
let paramData = reactive({
    index: null as any,
    parameter: {},
    componentKey: 0
})
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    modelName: [
        {
            required: true,
            message: '物模型名称不能为空',
            trigger: 'blur',
        },
    ],
    identifier: [
        {
            required: true,
            message: '标识符，产品下唯一不能为空',
            trigger: 'blur',
        },
    ],
    modelOrder: [
        {
            required: true,
            message: '模型排序不能为空',
            trigger: 'blur',
        },
    ],
    type: [
        {
            required: true,
            message: '模型类别不能为空',
            trigger: 'change',
        },
    ],
    datatype: [
        {
            required: true,
            message: '数据类型不能为空',
            trigger: 'change',
        },
    ],

})
// 打开弹窗
const openDialog = (type: string, row: any, queryParams: { productId: any; productName: any; isModbus: any }) => {
    if (type === 'edit') {

      getTemplate(row.modelId).then(response => {
            let tempForm = response.data.data;
            // Json转对象
            tempForm.specs = JSON.parse(tempForm.specs);
            if (!tempForm.specs.enumList) {
                tempForm.specs.showWay = 'select';
                tempForm.specs.enumList = [
                    {
                        value: '',
                        text: '',
                    },
                ];
            }
            if (!tempForm.specs.arrayType) {
                tempForm.specs.arrayType = 'integer';
            }
            if (!tempForm.specs.arrayCount) {
                tempForm.specs.arrayCount = 5;
            }
            if (!tempForm.specs.params) {
                tempForm.specs.params = [];
            }
            // 对象和数组中参数删除前缀
            if ((tempForm.specs.type == 'array' && tempForm.specs.arrayType == 'object') || tempForm.specs.type == 'object') {
                for (let i = 0; i < tempForm.specs.params.length; i++) {
                    tempForm.specs.params[i].id = String(tempForm.specs.params[i].id).substring(String(tempForm.identifier).length + 1);
                }
            }
            state.ruleForm = tempForm;
            state.ruleForm.productId = queryParams.productId
            state.ruleForm.productName = queryParams.productName
            state.ruleForm.isModbus = queryParams.isModbus
        });
        state.dialog.title = '修改通用物模型';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.ruleForm.productId = queryParams.productId
        state.ruleForm.productName = queryParams.productName
        state.ruleForm.isModbus = queryParams.isModbus
        state.dialog.title = '新增通用物模型';
        state.dialog.submitTxt = '新 增';
    }
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 类型改变
const typeChange = (type: any) => {
    console.log(type);
    if (type == 1) {
        state.ruleForm.isChart = 1 as any;
        state.ruleForm.isHistory = 1;
        state.ruleForm.isMonitor = 1;
        state.ruleForm.isReadonly = 1;
        state.ruleForm.isSharePerm = 1;
        state.ruleForm.datatype = 'integer';
    } else if (type == 2) {
        state.ruleForm.isChart = 0;
        state.ruleForm.isHistory = 1;
        state.ruleForm.isSharePerm = 1;
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isReadonly = 0;
    } else if (type == 3) {
        state.ruleForm.isChart = 0;
        state.ruleForm.isHistory = 1;
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isReadonly = 1;
        state.ruleForm.isSharePerm = 0;
    }
}
// 是否图表展示改变
const isChartChange = () => {
    if (state.ruleForm.isChart == 1) {
        state.ruleForm.isReadonly = 1;
    } else {
        state.ruleForm.isMonitor = 0;
    }
}
// 是否实时监测改变
const isMonitorChange = () => {
    if (state.ruleForm.isMonitor == 1) {
        state.ruleForm.isReadonly = 1;
        state.ruleForm.isChart = 1;
    }
}
// 是否只读数据改变
const isReadonlyChange = () => {
    if (state.ruleForm.isReadonly == 0) {
        state.ruleForm.isMonitor = 0;
        state.ruleForm.isChart = 0;
    }
}
// 格式化物模型
const formatThingsSpecs = () => {
    var data = {
        type: '' as any,
        min: '' as any,
        max: '' as any,
        unit: '' as any,
        step: '' as any,
        maxLength: '' as any,
        falseText: '' as any,
        trueText: '' as any,
        showWay: '' as any,
        enumList: '' as any,
        arrayType: '' as any,
        arrayCount: '' as any,
        params: '' as any
    };
    data.type = state.ruleForm.datatype;
    if (state.ruleForm.datatype == 'integer' || state.ruleForm.datatype == 'decimal') {
        data.min = Number(state.ruleForm.specs.min ? state.ruleForm.specs.min : 0);
        data.max = Number(state.ruleForm.specs.max ? state.ruleForm.specs.max : 100);
        data.unit = state.ruleForm.specs.unit ? state.ruleForm.specs.unit : '';
        data.step = Number(state.ruleForm.specs.step ? state.ruleForm.specs.step : 1);
    } else if (state.ruleForm.datatype == 'string') {
        data.maxLength = Number(state.ruleForm.specs.maxLength ? state.ruleForm.specs.maxLength : 1024);
    } else if (state.ruleForm.datatype == 'bool') {
        data.falseText = state.ruleForm.specs.falseText ? state.ruleForm.specs.falseText : '关闭';
        data.trueText = state.ruleForm.specs.trueText ? state.ruleForm.specs.trueText : '打开';
    } else if (state.ruleForm.datatype == 'enum') {
        data.showWay = state.ruleForm.specs.showWay;
        if (state.ruleForm.specs.enumList && state.ruleForm.specs.enumList[0].text != '') {
            data.enumList = state.ruleForm.specs.enumList;
        } else {
            data.showWay = 'select';
            data.enumList = [
                {
                    value: '0',
                    text: '低',
                },
                {
                    value: '1',
                    text: '高',
                },
            ];
        }
    } else if (state.ruleForm.datatype == 'array') {
        data.arrayType = state.ruleForm.specs.arrayType;
        data.arrayCount = state.ruleForm.specs.arrayCount ? state.ruleForm.specs.arrayCount : 5;
        if (data.arrayType == 'object') {
            data.params = state.ruleForm.specs.params;
            // 物模型名称作为参数的标识符前缀
            for (let i = 0; i < data.params.length; i++) {
                data.params[i].id = state.ruleForm.identifier + '_' + data.params[i].id;
            }
        }
    } else if (state.ruleForm.datatype == 'object') {
        data.params = state.ruleForm.specs.params;
        // 物模型名称作为参数的标识符前缀
        for (let i = 0; i < data.params.length; i++) {
            data.params[i].id = state.ruleForm.identifier + '_' + data.params[i].id;
        }
    }
    return JSON.stringify(data);
}
/** 数据类型改变 */
const dataTypeChange = () => { }
/** 添加枚举项 */
const addEnumItem = () => {
    state.ruleForm.specs.enumList.push({
        value: '',
        text: '',
    });
}
/** 删除枚举项 */
const removeEnumItem = (index: any) => {
    state.ruleForm.specs.enumList.splice(index, 1);
}
/** 添加参数 */
const addParameter = () => {
    paramData.index = -1 as number;
    paramData.parameter = {};
    paramData.componentKey += 1;
}
/** 编辑参数*/
const editParameter = (data: any, index: any) => {
    if (paramData.index !== index) { // 确保值的变化
        paramData.index = index;   // 更新值
    }
    paramData.parameter = data;
    paramData.componentKey += 1;
}
/** 删除动作 */
const removeParameter = (index: any) => {
    state.ruleForm.specs.params.splice(index, 1);
}
/**获取设置的参数对象*/
const getParamData = (data: { index: number; parameter: any; }) => {
    console.log(data, 'data');

    if (data.index == -1) {
        state.ruleForm.specs.params.push(data.parameter);
    } else {
        state.ruleForm.specs.params[data.index] = data.parameter;
        // 解决数组在界面中不更新问题
        // this.$set(state.ruleForm.specs.params, data.index, state.ruleForm.specs.params[data.index]);
        state.ruleForm.specs.params[data.index] = state.ruleForm.specs.params[data.index];

    }
}
const containsUnderscore = (value: string) => {
    // 使用正则表达式检查值中是否包含下划线
    return /_/.test(value);
}
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            // 验证对象或对象数组中的参数不能为空
            if (state.ruleForm.datatype == 'object' || (state.ruleForm.datatype == 'array' && state.ruleForm.specs.arrayType == 'object')) {
                if (!state.ruleForm.specs.params || state.ruleForm.specs.params == 0) {
                    ElMessage.error('对象的参数不能为空');
                    return;
                }
                if (containsUnderscore(state.ruleForm.identifier)) {
                    ElMessage.error('对象类型模型标识输入不能包含下划线，请重新填写模型标识！');
                    return;
                }
            }
            // 验证对象参数标识符不能相同
            if (state.ruleForm.specs.params && state.ruleForm.specs.params.length > 0) {
                let arr = state.ruleForm.specs.params.map((item: { id: any; }) => item.id).sort();
                for (let i = 0; i < arr.length; i++) {
                    if (arr[i] == arr[i + 1]) {
                        ElMessage.error('参数标识 ' + arr[i] + ' 重复');
                        return;
                    }
                }
            }
            //验证模型特性为图表展示时，数据类型是否为整数或者小数
            if ((state.ruleForm.isChart == 1 && state.ruleForm.datatype != 'integer') && (state.ruleForm.isChart == 1 && state.ruleForm.datatype != 'decimal')) {
                ElMessage.error('请重新选择数据类型！');
            }
            else if (state.ruleForm.modelId != '') {
                // 格式化specs
                let tempForm = JSON.parse(JSON.stringify(state.ruleForm));
                tempForm.specs = formatThingsSpecs();
                if (state.ruleForm.type == 2) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                } else if (state.ruleForm.type == 3) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                }
                // 添加通用物模型的修改者
                tempForm.updateBy = userInfoStore.userInfos.username
              updateTemplate(tempForm).then((response) => {
                    ElMessage.success('修改成功');
                    emit('refresh');
                    closeDialog();
                });
            } else {
                // 格式化specs
                let tempForm = JSON.parse(JSON.stringify(state.ruleForm));
                tempForm.specs = formatThingsSpecs();
                if (state.ruleForm.type == 2) {
                    tempForm.isMonitor = 0;
                } else if (state.ruleForm.type == 3) {
                    tempForm.isMonitor = 0;
                    tempForm.isChart = 0;
                }
                // 添加通用物模型的创造者
                tempForm.createBy = userInfoStore.userInfos.username
              addTemplate(tempForm).then((response) => {
                    ElMessage.success('新增成功');
                    emit('refresh');
                    closeDialog();

                });
            }
            // if (state.ruleForm.modelId != '') {
            //     updateNewsCategory(state.ruleForm).then(response => {
            //         //  刷新页面
            //         emit('refresh');
            //         closeDialog();
            //         ElMessage.success('修改成功');
            //     });
            // } else {
            //     addNewsCategory(state.ruleForm).then(response => {
            //         //  刷新页面
            //         emit('refresh');
            //         closeDialog();
            //         ElMessage.success('新增成功');
            //     });
            // }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
