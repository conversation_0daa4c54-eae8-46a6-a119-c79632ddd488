<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form ref="queryForm" :inline="true" label-width="68px" style="margin-bottom:-20px;">
                <el-form-item label="产品名称" prop="productName">
                    <el-input v-model="state.tableData.param.productName" placeholder="请输入产品名称" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="分类名称" prop="categoryName">
                    <el-input v-model="state.tableData.param.categoryName" placeholder="请输入产品分类名称" clearable
                        size="default" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select style="width: 240px" v-model="state.tableData.param.status" placeholder="请选择状态" clearable
                        size="default">
                        <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button size="default" type="primary" class="ml10" @click="getTableData">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        查询
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
                <el-form-item style="float:right;">
                    <el-button type="primary" plain size="default" @click="handleAddProduct('add')"
                        v-auths="['iot:product:add']"><el-icon><ele-Plus /></el-icon>新增</el-button>
                    <el-button type="primary" plain size="default"
                        @click="handleChangeShowType"><el-icon><ele-Grid /></el-icon>切换</el-button>
                </el-form-item>
            </el-form>
        </el-card>
        <el-card style=" margin-top: 10px" shadow="hover">
            <el-row>
                <el-col :span="4">
                    <el-input v-model="categoryName" size="default" placeholder="请输入分类名称" style="max-width: 90%">
                    </el-input>
                    <div class="mt10">
                        <el-tree :data="treeState.tableData.data" :props="defaultProps" :expand-on-click-node="false"
                            :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all
                            highlight-current @node-click="handleNodeClick" />
                    </div>
                </el-col>
                <el-col :span="20" :xs="24">
                    <div v-if="showType == 'list'">
                        <el-table v-loading="state.tableData.loading" :data="productList" border style="width: 100%"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <el-table-column label="分类编码" align="center" header-align="center" prop="categoryId" />
                            <el-table-column label="所属分类" align="center" header-align="center" prop="categoryName" />
                            <el-table-column label="产品名称" align="center" prop="productName" />
                            <!-- <el-table-column label="设备类型" align="center" prop="deviceType" /> -->
                            <el-table-column label="设备类型" align="center" prop="deviceType">
                                <template #default="scope">
                                    <dict-tag :options="typelist" :value="scope.row.deviceType" />
                                </template>
                            </el-table-column>
                            <el-table-column label="状态" align="center" prop="status">
                                <template #default="scope">
                                    <dict-tag :options="statuslist" :value="scope.row.status"></dict-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="备注" align="center" prop="remark" />
                            <el-table-column label="创建时间" align="center" prop="createTime">
                                <template #default="scope">
                                    <span>{{ scope.row.createTime }}</span>
                                </template>
                            </el-table-column>

                            <el-table-column label="操作" align="center" class-name="small-padding fixed-width"
                                width="250">
                                <template #default="scope">
                                    <el-button size="default" text type="primary" @click="handleEditProduct('edit', scope.row)"
                                        v-auths="['iot:product:query']">
                                        <el-icon><ele-View /></el-icon>
                                        详情
                                    </el-button>
                                    <el-button size="default" text type="warning"
                                        @click="handleViewDevice(scope.row.productId)" v-auths="['iot:device:list']">
                                        <el-icon><ele-Search /></el-icon>
                                        查看设备
                                    </el-button>
                                    <el-button v-if="scope.row.status === 1" size="default" text type="danger"
                                        @click="handleDelete(scope.row)" v-auths="['iot:product:remove']">
                                        <el-icon><ele-Delete /></el-icon>
                                        删除
                                    </el-button>
                                    <el-button style="width: 46px;" v-else size="default" text>
                                    </el-button>
                                    <!-- <el-button v-if="item.status === 2" size="default" type="success"
                                @click="handleDeviceAuthorize(item)" v-auths="['iot:authorize:query']"
                                :disabled="item.isAuthorize !== 1">
                                <el-icon><ele-UserFilled /></el-icon>
                                设备授权
                            </el-button> -->

                                    <!-- <el-button type="danger" size="small" style="padding: 5px" @click="handleDelete(scope.row)"
                            v-auths="['iot:device:remove']"><el-icon><ele-Delete /></el-icon>删除</el-button>
                        <el-button type="primary" size="small" style="padding: 5px"
                            @click="handleEditDevice(scope.row, 'basic')"
                            v-auths="['iot:device:query']"><el-icon><ele-View /></el-icon>查看</el-button>
                        <el-button type="primary" size="small" style="padding: 5px"
                            @click="openSummaryDialog(scope.row)" v-if="scope.row.deviceId != 0"
                            v-auths="['iot:device:query']">二维码</el-button> -->
                                </template>
                            </el-table-column>
                        </el-table>

                        <el-empty description="暂无数据，请添加产品" v-if="state.tableData.total == 0"></el-empty>
                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                            v-model:current-page="state.tableData.param.pageNum" background
                            v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                        <!-- 下载SDK -->
                        <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
                <el-link type="danger" style="padding-left:10px;" :underline="false">该功能暂未实现，参考教程和项目的SDK示例</el-link>
                <el-form label-width="80px">
                    <el-form-item label="设备类型">
                        <el-radio-group v-model="form.datatype">
                            <el-radio v-for="dict in chiplist"  :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" 
                                style="margin-top:15px;width:160px;">{{ dict.dictLabel }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="downloadSdk" disabled>下 载</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </el-dialog> -->
                    </div>
                    <div v-if="showType == 'card'">
                        <el-row :gutter="30" v-loading="state.tableData.loading">
                            <el-col v-for="(item, index) in productList" :key="item.productId" :xs="24" :sm="12"
                                :md="12" :lg="8" :xl="6" style="margin-bottom: 30px; text-align: center;">
                                <el-card :body-style="{ padding: '20px' }" shadow="always" class="card-item">
                                    <el-row :gutter="10" justify="space-between">
                                        <el-col :span="20" style="text-align:left;">
                                            <el-link :underline="false" @click="handleEditProduct('edit', item)"
                                                style="font-weight: bold; font-size: 16px; line-height: 32px; white-space: nowrap;">
                                                <SvgIcon :name="'product'" :type="'menu'" :color="''" />{{
                                                    item.productName
                                                }}
                                                <el-tag type="info" size="default"
                                                    style="margin-left: 5px; font-weight: 200"
                                                    v-if="item.isSys === 1">系统</el-tag>
                                            </el-link>
                                        </el-col>
                                        <el-col :span="4">
                                            <el-tooltip class="item" effect="dark" content="取消发布" placement="top-start"
                                                v-if="item.status === 2">
                                                <el-button type="success" size="default" style="padding: 5px;"
                                                    @click="updataProductStatus(item.productId, 1, item.deviceType)">
                                                    已发布
                                                </el-button>
                                                <!-- @click="changeProductStatus(item.productId, 1, item.deviceType)" -->
                                            </el-tooltip>
                                            <el-tooltip class="item" effect="dark" content="现在发布" placement="top-start"
                                                v-if="item.status === 1">
                                                <el-button type="info" size="default" style="padding: 5px;"
                                                    @click="updataProductStatus(item.productId, 2, item.deviceType)">
                                                    未发布
                                                </el-button>
                                                <!-- @click="changeProductStatus(item.productId, 2, item.deviceType)" -->
                                            </el-tooltip>
                                        </el-col>
                                    </el-row>
                                    <el-row :gutter="10">
                                        <el-col :span="14">
                                            <el-descriptions :column="1" size="default"
                                                style="margin-top: 10px; white-space: nowrap;">
                                                <el-descriptions-item label="所属分类">
                                                    <el-link type="primary" :underline="false">{{ item.categoryName
                                                    }}</el-link>
                                                </el-descriptions-item>
                                                <el-descriptions-item label="设备类型">
                                                    <dict-tag :options="typelist" :value="item.deviceType" />
                                                </el-descriptions-item>
                                                <el-descriptions-item label="备注">
                                                    <!-- <dict-tag  :value="" /> -->
                                                     <span>{{ item.remark }}</span>
                                                </el-descriptions-item>
                                                <!-- <el-descriptions-item label="设备授权">
                                        <el-tag type="success" size="default" v-if="item.isAuthorize === 1">已启用</el-tag>
                                        <el-tag type="info" size="default" v-else>未启用</el-tag>
                                    </el-descriptions-item> -->
                                            </el-descriptions>
                                        </el-col>
                                        <el-col :span="10">
                                            <div style="margin-top: 10px;">
                                                <el-image v-if="item.imgUrl"
                                                    style="width: 100%; height: 100px; border-radius: 10px;" lazy
                                                    :preview-src-list="[baseUrl + item.imgUrl]"
                                                    :src="baseUrl + item.imgUrl" fit="cover"></el-image>
                                                <el-image v-else-if="item.deviceType === 2"
                                                    style="width: 100%; height: 100px; border-radius: 10px;"
                                                    :preview-src-list="[gateway]" :src="gateway" fit="cover"></el-image>
                                                <el-image v-else-if="item.deviceType === 3"
                                                    style="width: 100%; height: 100px; border-radius: 10px;"
                                                    :preview-src-list="[video]" :src="video" fit="cover"></el-image>
                                                <el-image v-else
                                                    style="width: 100%; height: 100px; border-radius: 10px;"
                                                    :preview-src-list="[product]" :src="product" fit="cover"></el-image>
                                            </div>
                                        </el-col>
                                    </el-row>
                                    <el-button-group style="margin-top: 15px; height: 28px;">
                                        <el-button size="default" type="primary" @click="handleEditProduct('edit', item)"
                                            v-auths="['iot:product:query']">
                                            <el-icon><ele-View /></el-icon>
                                            详情
                                        </el-button>
                                        <el-button v-if="item.status === 1" size="default" type="danger"
                                            @click="handleDelete(item)" v-auths="['iot:product:remove']">
                                            <el-icon><ele-Delete /></el-icon>
                                            删除
                                        </el-button>
                                        <!-- <el-button v-if="item.status === 2" size="default" type="success"
                                @click="handleDeviceAuthorize(item)" v-auths="['iot:authorize:query']"
                                :disabled="item.isAuthorize !== 1">
                                <el-icon><ele-UserFilled /></el-icon>
                                设备授权
                            </el-button> -->
                                        <el-button size="default" type="warning"
                                            @click="handleViewDevice(item.productId)" v-auths="['iot:device:list']">
                                            <el-icon><ele-Search /></el-icon>
                                            查看设备
                                        </el-button>
                                    </el-button-group>
                                </el-card>
                            </el-col>
                        </el-row>

                        <el-empty description="暂无数据，请添加产品" v-if="state.tableData.total == 0"></el-empty>
                        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                            v-model:current-page="state.tableData.param.pageNum" background
                            v-model:page-size="state.tableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
                        </el-pagination>
                        <!-- 下载SDK -->
                        <!-- <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
                <el-link type="danger" style="padding-left:10px;" :underline="false">该功能暂未实现，参考教程和项目的SDK示例</el-link>
                <el-form label-width="80px">
                    <el-form-item label="设备类型">
                        <el-radio-group v-model="form.datatype">
                            <el-radio v-for="dict in chiplist"  :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" 
                                style="margin-top:15px;width:160px;">{{ dict.dictLabel }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="downloadSdk" disabled>下 载</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </el-dialog> -->
                    </div>
                </el-col>
            </el-row>
        </el-card>
        <ProductDialog ref="ProductDialogRef" @refresh="getTableData()" />
    </div>
</template>
<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, watch } from 'vue';
import { ElMessageBox, ElMessage, ElTree } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { useUserInfo } from '/@/stores/userInfo';
import { useRouter } from 'vue-router';
import { delProduct, deviceCount, listProduct, changeProductStatus } from '/@/api/iot/product';
import { delSipconfigByProductId } from '/@/api/iot/sipConfig';
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import product from '/@/assets/images/product.png';
import { checkPermi } from '/@/utils/permission';
import { listCategory } from '/@/api/iot/category';
import { handleTree } from '/@/utils/next';
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
const userInfoStore = useUserInfo();
const dictStore = useDictStore();  // 使用 Pinia store
const router = useRouter();
// 引入组件
const ProductDialog = defineAsyncComponent(() => import('/@/views/iot/product/dialog.vue'));
// 定义变量内容
const ProductDialogRef = ref();
const showType = ref('card')
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            productName: undefined,
            categoryName: undefined,
            status: '',
            userId: undefined,
            categoryId: undefined
        },
    },
});
const treeState = reactive({
    tableData: {
        data: [],
        param: {

        },
    },
});
const categoryName = ref()   //树形空间名称
const tree = ref<InstanceType<typeof ElTree> | null>(null);  // 明确类型注解
const defaultProps = reactive({
    children: "children",
    label: "categoryName"
});
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
// 是否管理员
const ids = ref() //groupId
interface productTypeOption {
    productName: string;
    isSys: number;
    productId: any;
    deviceType: any;
    categoryName: any;
    networkMethod: any;
    isAuthorize: any;
    imgUrl: any;
    remark: any;
    status: any
}
const productList = ref<productTypeOption[]>([]);
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<TypeOption[]>([]);
const typelist = ref<TypeOption[]>([]);
const methodlist = ref<TypeOption[]>([]);
const chiplist = ref<TypeOption[]>([]);
// 初始化表格数据
const getTableData = async () => {

    try {
        state.tableData.loading = true;
        const response = await listProduct(state.tableData.param);
        state.tableData.total = response.data.total;
        productList.value = response.data.rows
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
// 初始化树状图
const getTreeData = async () => {
    try {
        const response = await listCategory(treeState.tableData.param);
        treeState.tableData.data = handleTree(response.data.data, "categoryId") as any;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {

    }
}
// 筛选节点
const filterNode = (value: string, data: TreeNodeData) => {
    if (!value) return true;
    return data.categoryName.indexOf(value) !== -1;
}
// 树形控件节点单击事件
const handleNodeClick = (data: { categoryId: any; }) => {
    state.tableData.param.categoryId = data.categoryId;
    state.tableData.param.pageNum = 1;
    getTableData();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        productName: undefined,
        categoryName: undefined,
        status: '',
        userId: undefined
    }
}
/** 切换显示方式 */
const handleChangeShowType = () => {
    showType.value = showType.value == 'card' ? 'list' : 'card';
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('iot_product_status')
        typelist.value = await dictStore.fetchDict('iot_device_type')
        methodlist.value = await dictStore.fetchDict('iot_network_method')
        chiplist.value = await dictStore.fetchDict('iot_device_chip')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/** 查看设备按钮操作 */
const handleViewDevice = (productId: any) => {
    router.push({
        path: '/iot/device',
        query: {
            t: Date.now(),
            productId: productId,
        },
    })
}
/** 新增按钮操作 */
const handleAddProduct = (type:any) => {
    ProductDialogRef.value.openDialog(type);
    // let productId = 0;
    // if (row != 0) {
    //     productId = row.productId || ids.value
    // }
    // router.push({
    //     path: '/iot/product-edit',
    //     query: {
    //         productId: productId,
    //         pageNum: state.tableData.param.pageNum
    //     }
    // });
}

/** 修改按钮操作 */
const handleEditProduct = (type:any,row: any) => {
    let productId = 0;
    if (row != 0) {
        productId = row.productId || ids.value
    }
    
    ProductDialogRef.value.openDialog(type,row,productId);
    // router.push({
    //     path: '/iot/product-edit',
    //     query: {
    //         productId: productId,
    //         pageNum: state.tableData.param.pageNum
    //     }
    // });
}
/** 删除按钮操作 */
const handleDelete = (row: { productId: any; }) => {
    const productIds = row.productId || ids.value;
    let msg = "";
    ElMessageBox.confirm('是否确认删除产品编号为"' + productIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(function () {
            // 删除SIP配置
            // delSipconfigByProductId(productIds).then(response => { });
            return delProduct(productIds).then(response => {
                msg = response.data.msg;
            });
        }).then(() => {
            getTableData();
            ElMessage.success(msg);
        }).catch(() => { });
}
/**同步获取产品下的设备数量**/
const getDeviceCountByProductId = (productId: any) => {
    return new Promise((resolve, reject) => {
        deviceCount(productId).then(res => {
            resolve(res);
        }).catch(error => {
            reject(error);
        })
    })
}
/** 更新产品状态 */
const updataProductStatus = async (productId: {}, status: number | undefined, deviceType: undefined) => {
    let message = "确定取消发布？";
    if (status == 2) {
        // 发布
        let authsssion = checkPermi(['iot:product:add']);
        if (!authsssion) {
            ElMessage.error("没有操作权限");
            return;
        }
        message = "产品发布后，可以创建对应的设备";
    } else if (status == 1) {
        // 取消发布
        let authsssion = checkPermi(['iot:product:edit']);
        if (!authsssion) {
            ElMessage.error("没有操作权限");
            return;
        }
        let result = await getDeviceCountByProductId(productId) as any;
        if (result.data.data > 0) {
            message = "重要提示：产品下已有 " + result.data.data + " 个设备，取消发布可以修改产品信息和模型，重新发布后对应设备状态将会被重置！"
        }

    }
    ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            let data = {
                productId: '' as any,
                status: '' as any,
                deviceType: '' as any,
            };
            data.productId = productId;
            data.status = status;
            data.deviceType = deviceType;
            changeProductStatus(data).then((response) => {
                console.log(response);
                getTableData();
                ElMessage.success(response.data.msg);
            }).catch(() => { });
        }).catch(() => { });
}
/** 设备授权操作 */
const handleDeviceAuthorize = (row: { productId: any; }) => {
    let productId = row.productId
    router.push({
        path: '/iot/product-edit',
        query: {
            productId: productId,
            tabPanelName: 'productAuthorize',
            pageNum: state.tableData.param.pageNum
        }
    });
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 监听 deptName 的变化
watch(categoryName, (val) => {
    // 确保树组件已挂载
    if (tree.value) {
        tree.value.filter(val);  // 调用树组件的 filter 方法
    }
});
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
    getTreeData()
});
</script>
<style scoped>
.card-item {
    border-radius: 15px;
}

:deep(.el-descriptions__cell) {
    display: flex !important;
}
</style>