/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BaiduMap: typeof import('./src/components/BaiduMap/index.vue')['default']
    Crontab: typeof import('./src/components/Crontab/index.vue')['default']
    Day: typeof import('./src/components/Crontab/day.vue')['default']
    DictTag: typeof import('./src/components/DictTag/index.vue')['default']
    Editor: typeof import('./src/components/Editor/index.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElBacktop: typeof import('element-plus/es')['ElBacktop']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FileUpload: typeof import('./src/components/FileUpload/index.vue')['default']
    Hour: typeof import('./src/components/Crontab/hour.vue')['default']
    IconSelector: typeof import('./src/components/iconSelector/index.vue')['default']
    IFrame: typeof import('./src/components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./src/components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./src/components/ImageUpload/index.vue')['default']
    JsonView: typeof import('./src/components/JsonView/index.vue')['default']
    List: typeof import('./src/components/iconSelector/list.vue')['default']
    Min: typeof import('./src/components/Crontab/min.vue')['default']
    MonacoEditor: typeof import('./src/components/MonacoEditor/index.vue')['default']
    Month: typeof import('./src/components/Crontab/month.vue')['default']
    Result: typeof import('./src/components/Crontab/result.vue')['default']
    RightToolbar: typeof import('./src/components/RightToolbar/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollBoard: typeof import('./src/components/ScrollBoard/index.vue')['default']
    Second: typeof import('./src/components/Crontab/second.vue')['default']
    SvgIcon: typeof import('./src/components/svgIcon/index.vue')['default']
    ThreeScene: typeof import('./src/components/threejs/ThreeScene.vue')['default']
    Week: typeof import('./src/components/Crontab/week.vue')['default']
    Year: typeof import('./src/components/Crontab/year.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
