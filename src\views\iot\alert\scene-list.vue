<template>
    <el-dialog title="选择场景" v-model="open" width="900px" append-to-body style="position: absolute; top: 100px;">
        <el-form :model="state.tableData.param" ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="场景名称" prop="sceneName">
                <el-input v-model="state.tableData.param.sceneName" placeholder="请输入场景名称" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"> <el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-table ref="multipleTable" v-loading="state.tableData.loading" :data="state.tableData.sceneList"
            @select="handleSelectionChange" @select-all="handleSelectionAll" size="default" border
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="场景名称" align="center" prop="sceneName" />
            <el-table-column label="状态" align="center" prop="enable" width="">
                <template #default="scope">
                    <el-tag type="success" size="small" v-if="scope.row.enable == 1">启动</el-tag>
                    <el-tag type="danger" size="small" v-if="scope.row.enable == 2">暂停</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="触发条件" align="center" prop="cond">
                <template #default="scope">
                    <span v-if="scope.row.cond == 1">任意条件</span>
                    <span v-if="scope.row.cond == 2">所有条件</span>
                    <span v-if="scope.row.cond == 3">不满足条件</span>
                </template>
            </el-table-column>
            <el-table-column label="执行方式" align="center" prop="executeMode">
                <template #default="scope">
                    <span v-if="scope.row.executeMode == 1">串行</span>
                    <span v-if="scope.row.executeMode == 2">并行</span>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
        </el-table>

        <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
            v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="handleEmitData">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </span>
        </template>

    </el-dialog>
</template>

<script setup lang="ts" name="">
import { reactive, ref, nextTick } from 'vue';
// import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listScene } from '/@/api/iot/scene';
import { parseTime } from '/@/utils/next'
// const dictStore = useDictStore();  // 使用 Pinia store
const emit = defineEmits(['sceneEvent']); // 定义 emit
const state = reactive({
    tableData: {
        sceneList: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            sceneName: '',
        },
    },
});

const open = ref(false);
const ids = ref<number[]>([]);
const multipleTable = ref();
const selectScenes = ref<any[]>([]);
/** 查询产品列表 */
const getList = () => {
    state.tableData.loading = true;
    state.tableData.sceneList = []
    try {
        listScene(state.tableData.param).then((response) => {
            state.tableData.sceneList = response.data.rows;
            state.tableData.total = response.data.total;
            // 设置选中
            if (selectScenes.value) {
                state.tableData.sceneList.forEach((row) => {
                    nextTick(() => {
                        if (selectScenes.value.some((x) => x.sceneId === row.sceneId)) {
                            // console.log(x,'xxx');

                            multipleTable.value.toggleRowSelection(row, true);
                        }
                    });
                });
            } else {
                // 初始化
                selectScenes.value = [];
            }
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        sceneName: '',
    }
    handleQuery();
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: any, row: any) => {
    // 场景ID是否存在于原始设备ID数组中
    let index = ids.value.indexOf(row.sceneId);
    // 是否选中
    let value = selection.indexOf(row);
    if (index == -1 && value != -1) {
        // 不存在且选中
        ids.value.push(row.sceneId);
        selectScenes.value.push(row);
    } else if (index != -1 && value == -1) {
        // 存在且取消选中
        ids.value.splice(index, 1);
        selectScenes.value.splice(index, 1);
    }
}
// 全选事件处理
const handleSelectionAll = (selection: any) => {
    for (let i = 0; i < state.tableData.sceneList.length; i++) {
        // 设备ID是否存在于原始设备ID数组中
        let index = ids.value.indexOf(state.tableData.sceneList[i].sceneId);
        // 是否选中
        let value = selection.indexOf(state.tableData.sceneList[i]);
        if (index == -1 && value != -1) {
            // 不存在且选中
            ids.value.push(state.tableData.sceneList[i].sceneId);
            selectScenes.value.push(state.tableData.sceneList[i]);
        } else if (index != -1 && value == -1) {
            // 存在且取消选中
            ids.value.splice(index, 1);
            selectScenes.value.splice(index, 1);
        }
    }

}
// 关闭选择场景列表
const cancel = () => {
    open.value = false;
}
/**确定选择场景，设备传递给父组件 */
const handleEmitData = () => {
    emit('sceneEvent', selectScenes.value);
    closeDialog()
}
/**关闭对话框 */
const closeDialog = () => {
    selectScenes.value = []
    open.value = false;
}
// 打开弹窗
const openDialog = () => {
    open.value = true
    getList();
};

// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getList();
};
// 暴露变量
defineExpose({
    openDialog,
    selectScenes,
    ids,
    getList
});
</script>
<style lang="scss" scoped></style>