<template>
    <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();

// Methods
const drawCircular = (x: number, y: number, r: number, start: number, end: number, color: string, type: string) => {
  if (!elCanvas.value) return;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  const unit = Math.PI / 180;
  ctx.beginPath();
  ctx.arc(x, y, r, start * unit, end * unit);
  (ctx as any)[type + 'Style'] = color;
  ctx.closePath();
  (ctx as any)[type]();
};

const onResize = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  const x = w / 2;
  const y = h / 2;
  const r = w / 2 > h / 2 ? h / 2 : w / 2;
  const color = getForeColor();
  drawCircular(x, y, r - 2, 0, 360, color, 'fill');
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};

// Lifecycle
onMounted(() => {
  onResize();
});
</script>
