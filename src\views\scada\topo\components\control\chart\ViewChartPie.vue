<template>
    <div>
        <div v-show="false">{{ height }}{{ width }}{{ chartsValue }}{{ foreColor }}</div>
        <div class="view-chart-pie" ref="chartView" :id="detail.identifier" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';
import { getDeviceStatistic } from '/@/api/scada/topo';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const chartView = ref<HTMLElement>();
const myChart = ref<any>(null);
const timer = ref<any>(null);

// Computed
const width = computed(() => {
  nextTick(() => {
    if (myChart.value) {
      myChart.value.resize({
        width: props.detail.style.position.w,
        height: props.detail.style.position.h,
      });
    }
  });
  return props.detail.style.position.w;
});

const height = computed(() => {
  nextTick(() => {
    if (myChart.value) {
      myChart.value.resize({
        width: props.detail.style.position.w,
        height: props.detail.style.position.h,
      });
    }
  });
  return props.detail.style.position.h;
});

const chartsValue = computed(() => {
  //将回调延迟到下次DOM更新循环之后执行。在修改数据之后立即使用它，然后等待DOM更新
  nextTick(() => {
    let current = {
      deviceOnlineCount: 0,
      deviceOfflineCount: 0,
      alertDeviceCount: 0,
      alertProcessedCount: 0,
      alertNotProcessedCount: 0,
      orderRecordProcessedNum: 0,
      orderRecordUntreatedNum: 0,
      orderRecordAbandonNum: 0,
    };
    if (props.detail.style.pieType == '设备状态') {
      myChart.value?.setOption(getDeviceChartsData(current));
    } else if (props.detail.style.pieType == '报警状态') {
      myChart.value?.setOption(getWarnChartsData(current));
    } else {
      myChart.value?.setOption(getOrderChartsData(current));
    }
  });
  return props.detail.style.pieType;
});

const foreColor = computed(() => {
  nextTick(() => {
    getStaticData();
  });
  return props.detail.style.foreColor;
});
// Methods
const getStaticData = async () => {
  try {
    const ress = await getDeviceStatistic();
    const res = ress.data;
    if (res.code === 200) {
      if (props.detail.style.pieType == '设备状态') {
        myChart.value?.setOption(getDeviceChartsData(res.data));
      } else if (props.detail.style.pieType == '报警状态') {
        myChart.value?.setOption(getWarnChartsData(res.data));
      } else {
        myChart.value?.setOption(getOrderChartsData(res.data));
      }
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
  }
};
//设备状态
const getDeviceChartsData = (currData: any) => {
  let option = {
    color: ['#13ce66', '#909399', '#ff4949'],
    title: {
      text: '设备状态',
      left: 'center',
      textStyle: {
        fontFamily: 'Microsoft YaHei',
        fontStyle: 'normal',
        fontWeight: 'normal', //标题颜色
        color: props.detail.style.foreColor,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      top: '10%',
      left: 'center',
      textStyle: { color: props.detail.style.foreColor },
    },
    series: [
      {
        name: '网关设备',
        type: 'pie',
        radius: ['40%', '50%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: currData.deviceOnlineCount, name: '在线 ' + currData.deviceOnlineCount, label: { color: props.detail.style.foreColor } },
          { value: currData.deviceOfflineCount, name: '离线 ' + currData.deviceOfflineCount, label: { color: props.detail.style.foreColor } },
          { value: currData.alertDeviceCount, name: '报警 ' + currData.alertDeviceCount, label: { color: props.detail.style.foreColor } },
        ],
      },
    ],
  };
  return option;
};
//报警状态
const getWarnChartsData = (currData: any) => {
  let option = {
    color: ['#13ce66', '#ff4949'],
    title: {
      text: '报警状态',
      left: 'center',
      textStyle: {
        fontFamily: 'Microsoft YaHei',
        fontStyle: 'normal',
        fontWeight: 'normal', //标题颜色
        color: props.detail.style.foreColor,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      top: '10%',
      left: 'center',
      textStyle: { color: props.detail.style.foreColor },
    },
    series: [
      {
        name: '网关设备',
        type: 'pie',
        radius: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: currData.alertProcessedCount, name: '已处理 ' + currData.alertProcessedCount, label: { color: props.detail.style.foreColor } },
          { value: currData.alertNotProcessedCount, name: '未处理 ' + currData.alertNotProcessedCount, label: { color: props.detail.style.foreColor } },
        ],
      },
    ],
  };
  return option;
};
//工单状态
const getOrderChartsData = (currData: any) => {
  let option = {
    color: ['#2979ff', '#fa3534', '#ff9900'], // '#5C7BD9',
    title: {
      text: '工单状态',
      left: 'center',
      textStyle: {
        fontFamily: 'Microsoft YaHei',
        fontStyle: 'normal',
        fontWeight: 'normal', //标题颜色
        color: props.detail.style.foreColor,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)',
    },
    legend: {
      top: '10%',
      left: 'center',
      textStyle: { color: props.detail.style.foreColor },
    },
    series: [
      {
        name: '网关设备',
        type: 'pie',
        radius: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '30',
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: currData.orderRecordProcessedNum, name: '已巡检 ' + currData.orderRecordProcessedNum, label: { color: props.detail.style.foreColor } },
          { value: currData.orderRecordUntreatedNum, name: '未巡检 ' + currData.orderRecordUntreatedNum, label: { color: props.detail.style.foreColor } },
          { value: currData.orderRecordAbandonNum, name: '已废弃 ' + currData.orderRecordAbandonNum, label: { color: props.detail.style.foreColor } },
        ],
      },
    ],
  };
  return option;
};

// Lifecycle
onMounted(() => {
  myChart.value = echarts.init(document.getElementById(props.detail.identifier));
  getStaticData();
  timer.value = setInterval(() => {
    getStaticData();
  }, 60000);
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (myChart.value) {
    myChart.value.dispose();
  }
});
</script>

<style lang="scss">
.view-chart-pie {
    height: 100%;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}
</style>
