import request from '/@/utils/request'

// 定义请求参数的类型
interface JobLogQuery {
  // 根据实际需求调整字段类型，例如：
  jobName?: string;
  status?: string;
  // 添加更多查询字段
}

interface JobLog {
  id: number;
  jobName: string;
  status: string;
  createTime: string;
  // 根据实际需要，定义调度日志字段
}

// 查询调度日志列表
export function listJobLog(query: any) {
  return request({
    url: '/monitor/jobLog/list',
    method: 'get',
    params: query
  }) as Promise<any>; // 返回值类型为 JobLog 数组
}

// 删除调度日志
export function delJobLog(jobLogId: any) {
  return request({
    url: `/monitor/jobLog/${jobLogId}`,
    method: 'delete'
  }) as Promise<void>; // 返回值类型为 void
}

// 清空调度日志
export function cleanJobLog() {
  return request({
    url: '/monitor/jobLog/clean',
    method: 'delete'
  }) as Promise<void>; // 返回值类型为 void
}
