<template>
	<div id="dom" class="chart-scrollbar">
		<div class="chart-warp">
			<div class="chart-warp-top">
				<ChartHead @child-button-click="toggleFullScreen" @showActive="getShowActive" />
			</div>
			<!-- 运维看板 -->
			<div class="chart-warp-bottom" v-if="activeTab == 0">
				<!-- 左边 -->
				<div class="big-data-down-left">
					<!-- 左1 -->
					<div class="flex-warp-item" style="height: 500px;">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测范围</div>
							<div>
								<div class="shebeitongji">
									<template v-for="(item, idx) in deviceRangeStats" :key="item.label">
										{{ item.label }}&nbsp;<span>{{ item.value }}</span>
										<span v-if="idx !== deviceRangeStats.length - 1">&nbsp;&nbsp;&nbsp;&nbsp;</span>
									</template>
								</div>
							</div>
							<div class="instrumentList" v-show="chartleftOneDatas.length > 0" ref="deviceRangeScrollRef" @mouseenter="deviceRangeScrollPause" @mouseleave="deviceRangeScrollStart">
								<div class="clearfix" v-for="item in chartleftOneDatas" :key="item.productId" style="height: 60px;">
									<div class="li">
										<div class="yq" id="yq">{{ item.deviceCount }}</div>
										<span>{{ item.productName }}</span>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- 左2 -->
					<div class="flex-warp-item">
						<!-- <div class="flex-warp-item-box">
							<div class="flex-title">当前设备状态</div>
							<div class="flex-content flex-content-overflow">
								<div class="d-states">
									<div class="d-states-item">
										<SvgIcon name="ele-Odometer" class="i-bg1" />
										<div class="d-states-flex">
											<div class="d-states-item-label">园区设备数</div>
											<div class="d-states-item-value">99</div>
										</div>
									</div>
									<div class="d-states-item">
										<SvgIcon name="ele-FirstAidKit" class="i-bg2" />
										<div class="d-states-flex">
											<div class="d-states-item-label">预警设备数</div>
											<div class="d-states-item-value">10</div>
										</div>
									</div>
									<div class="d-states-item">
										<SvgIcon name="ele-VideoPlay" class="i-bg3" />
										<div class="d-states-flex">
											<div class="d-states-item-label">运行设备数</div>
											<div class="d-states-item-value">20</div>
										</div>
									</div>
								</div>
								<div class="d-btn">
									<div class="d-btn-item" v-for="(v, k) in state.dBtnList" :key="k">
										<i class="d-btn-item-left el-icon-money"></i>
										<div class="d-btn-item-center">
											<div>{{ v.v2 }}|{{ v.v3 }}</div>
										</div>
										<div class="d-btn-item-eight">{{ v.v4 }}</div>
									</div>
								</div>
							</div>
						</div> -->
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测动态</div>
							<div class="flex-content">
								<div style="height: 100%;width: 100%;" ref="chartsLeftTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 左3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备故障分类对比</div>
							<div class="flex-content">
								<div style="height: 100%;" ref="chartsLeftThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 左4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">采集齐全率分析</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsLeftFourRef"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- 中间 -->
				<div class="big-data-down-center">
					<div class="big-data-down-center-one">
						
						<div class="big-data-down-center-one-content border">
							<img src="@/assets/images/dapingbeijing.png" alt="平台架构图" class="architecture-img" style="width: 100%;height: 100%;"/>
							<!-- <ThreeScene /> -->
							<!-- <div style="height: 100%" ref="chartsCenterOneRef"></div> -->
						</div>
					</div>
          <div class="big-data-down-center-two">
            <div class="flex-warp-item-box border">
              <div class="flex-title">
                <span>设备故障清单</span>
              </div>
              <div class="flex-content">
                <div class="head">
                  <span class="col no-col">序号</span>
                  <span class="col">所属名</span>
                  <span class="col">设备名称</span>
                  <span class="col">报警时间</span>
                  <span class="col">所属单位</span>
                  <span class="col">故障原因</span>
                  <span class="col">排查建议</span>
                  <span class="col">操作</span>
                </div>
                <div
                    class="scroll-view"
                    ref="scrollViewRef"
                    @mouseenter="onMouseenter"
                    @mouseleave="onMouseleave"
                >
                  <div class="marquee-view" ref="listRef">
                    <div class="row" v-for="(item, index) in deviceFaultsList" :key="index" :title="item.faultReason" @click="handleRowClick(item, index)">
                      <span class="col no-col">{{ index + 1 }}</span>
                      <span class="col truncate-text" >{{ item.belongsDeviceName || '未知' }}</span>
                      <span class="col truncate-text" >{{ item.deviceName }}</span>
                      <span class="col truncate-text" >{{ item.alertTime }}</span>
                      <span class="col truncate-text" >{{ item.groupName }}</span>
                      <span class="col truncate-text" >{{ item.faultReason }}</span>
                      <span class="col truncate-text" >{{ item.suggestion }}</span>
                      <span class="col action">
                        <button class="action-btn" @click.stop="handleTroubleshoot(item)">排查</button>
                        <button class="action-btn" @click.stop="handleShowDetail(item)">详情</button>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
				</div>

				<!-- 右边 -->
				<div class="big-data-down-right">
					<!-- 右1 -->
					<div class="flex-warp-item" style="height: 500px;">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备周故障对比</span>
								<!-- <span class="flex-title-small">单位：次</span> -->
							</div>
							<!-- <div class="top-RightOne">
								<div class="content leftcss">
									<div class="title">上周</div>
									<div class="num">82</div>
								</div>
								<div class="center">VS</div>
								<div class="content rightcss">
									<div class="num">0</div>
									<div class="title">本周</div>
								</div>
							</div> -->
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightOneRef"></div>
							</div>
						</div>
					</div>
					<!-- 右2 -->
					<!-- <div class="flex-warp-item">
						<div class="flex-warp-item-box">
							<div class="flex-title">设备运行统计</div>
							<div class="flex-content">
								<div class="task">
									<div class="task-item task-first-item">
										<div class="task-item-value task-first">25</div>
										<div class="task-item-label">待办任务</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task1">
											<div class="task-item-value">12</div>
											<div class="task-item-label">施肥</div>
										</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task2">
											<div class="task-item-value">3</div>
											<div class="task-item-label">施药</div>
										</div>
									</div>
									<div class="task-item">
										<div class="task-item-box task3">
											<div class="task-item-value">5</div>
											<div class="task-item-label">农事</div>
										</div>
									</div>
								</div>
								<div class="progress">
									<div class="progress-item">
										<span>施肥率</span>
										<div class="progress-box">
											<el-progress :percentage="70" color="#43bdf0"></el-progress>
										</div>
									</div>
									<div class="progress-item">
										<span>施药率</span>
										<div class="progress-box">
											<el-progress :percentage="36" color="#43bdf0"></el-progress>
										</div>
									</div>
									<div class="progress-item">
										<span>农事率</span>
										<div class="progress-box">
											<el-progress :percentage="91" color="#43bdf0"></el-progress>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flex-content" style="color: red;">
							<div style="height: 100%" ref="chartsRightTwoRef"></div>
						</div>
					</div> -->
					<!-- 右2 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border" style="position: relative;">
							<div class="flex-title">
								<span>设备运行统计</span>
							</div>
							<div style="position: absolute; right: 2%; top: 4%;">
								<!-- <el-select class="my-select" popper-class="mySelectStyle" size="small"
									v-model="selectvalue.groupId" clearable style="width: 140px;color: red!important;">
									<el-option v-for="item in options" :key="item.groupId" :label="item.groupName"
										:value="item.groupId" />
								</el-select> -->
								<el-tree-select class="my-select" popper-class="mySelectStyle"
									v-model="selectvalue.groupId" :data="options" :props="defaultProps" check-strictly
									:render-after-expand="true" style="width: 200px" :show-count="true" size="small"
									@node-click="getrunStatisticsList"
									:teleported="isFullscreen ? false : true"/>
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 右3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备运维考核</span>
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 右4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备故障区域排名</span>
								<!-- <span class="flex-title-small">单位：件</span> -->
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightFourRef"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
<!--			<div class="chart-warp-bottom" v-else-if="activeTab == 1">-->
<!--				<ops-management></ops-management>-->
<!--			</div>-->
		</div>

	<!-- 详情弹窗 - 根据全屏状态动态挂载 -->
	<Teleport :to="isFullscreen ? '#dom' : 'body'">
		<el-dialog
			v-model="detailDialogVisible"
			title="告警详情"
			width="800px"
			:before-close="handleCloseDetail"
			class="detail-dialog"
			:z-index="isFullscreen ? 2147483647 : 99999"
			:append-to-body="!isFullscreen"
		>
			<div class="detail-content" v-if="selectedFaultItem">
        <div class="detail-row">
          <span class="detail-label">设备名称:</span>
          <span class="detail-value">{{ selectedFaultItem.deviceName || '--' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">所属名:</span>
          <span class="detail-value">{{ selectedFaultItem.belongsDeviceName || '--' }}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">所属单位:</span>
          <span class="detail-value">{{ selectedFaultItem.groupName || '--' }}</span>
        </div>
				<div class="detail-row">
					<span class="detail-label">告警名称:</span>
					<span class="detail-value">{{ selectedFaultItem.alertName || '--' }}</span>
				</div>
				<div class="detail-row">
					<span class="detail-label">场景名称:</span>
					<span class="detail-value">{{ selectedFaultItem.scene.sceneName || '--' }}</span>
				</div>
				<div class="detail-row">
					<span class="detail-label">告警时间:</span>
					<span class="detail-value">{{ selectedFaultItem.alertTime || '--' }}</span>
				</div>
        <div class="detail-row">
          <span class="detail-label">告警时长:</span>
          <span class="detail-value">{{ formatAlertDuration(selectedFaultItem) }}</span>
        </div>
				<div class="detail-row">
					<span class="detail-label">告警状态:</span>
					<span class="detail-value">{{ getFaultStatus(selectedFaultItem) }}</span>
				</div>
				<div class="detail-row">
					<span class="detail-label">告警原因:</span>
					<span class="detail-value">{{ selectedFaultItem.faultReason || '--' }}</span>
				</div>
				<div class="detail-row">
					<span class="detail-label">告警描述:</span>
					<span class="detail-value">{{ selectedFaultItem.suggestion || '--' }}</span>
				</div>
				<div class="detail-section">
					<div class="section-title">触发列表:</div>
					<el-table :data="triggerTableData" style="width: 100%" class="device-table">
						<el-table-column prop="index" label="序号" width="80" align="center">
							<template #default="{ $index }">{{ $index + 1 }}</template>
						</el-table-column>
            <el-table-column prop="triggerSource" label="触发条件" align="center">
              <template #default="scope">{{ conditionText }}</template>
            </el-table-column>
						<el-table-column prop="triggerSource" label="触发选择" align="center">
							<template #default="scope">{{ scope.row.triggerSource }}</template>
						</el-table-column>
            <el-table-column prop="triggerDevice" label="触发内容" align="center">
              <template #default="scope">{{ scope.row.triggerDevice || '--' }}</template>
            </el-table-column>
            <el-table-column prop="triggerContent" label="触发事件" align="center">
              <template #default="scope">{{ scope.row.triggerContent }}</template>
            </el-table-column>
					</el-table>
				</div>
			</div>
		</el-dialog>
	</Teleport>
</div>
</template>

<script setup lang="ts" name="chartIndex">
import { defineAsyncComponent, reactive, onMounted, watch, nextTick, onActivated, ref, computed, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import 'echarts-wordcloud';
import { storeToRefs } from 'pinia';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { Affiliation, assessEvaluation, completenessRate, faultsList, faultsMalfunction, faultsWeek, MonitorDynamics, MonitorRang, regionalRankings, runStatistics } from './chart';
import { handleTree } from '/@/utils/next';
import opsManagement from './opsManagement.vue';
import ThreeScene from '/@/components/threejs/ThreeScene.vue';
import {listDeviceShort} from "@/api/iot/device";
const storesTagsViewRoutes = useTagsViewRoutes();
const { isTagsViewCurrenFull } = storeToRefs(storesTagsViewRoutes);

// 引入组件
const ChartHead = defineAsyncComponent(() => import('/@/views/chart/head.vue'));
// 定义变量内容
const state = reactive({
	myCharts: [] as EmptyArrayType,
});
const timeout = ref<number | undefined>();
const timeout1 = ref<number | undefined>();
const isFullscreen = ref(false);  // 控制全屏状态
const activeTab = ref(0); // 控制选中tab
// const chartsLeftOneRef = ref();// 左1 
const chartsLeftTwoRef = ref();//左2 设备监测范围
let myChart1 = null as any; // 设备监测动态
let myChart2 = null as any; // 设备故障分类对比
let myChart3 = null as any; // 采集齐全率分析
let myChart4 = null as any; // 设备周故障对比
let myChart5 = null as any; // 设备运行统计
let myChart6 = null as any; // 设备运维考核
let myChart7 = null as any; // 设备故障区域排名

// ResizeObserver 引用，用于清理
let resizeObserver1: ResizeObserver | null = null;
let resizeObserver2: ResizeObserver | null = null;
let resizeObserver3: ResizeObserver | null = null;
let resizeObserver4: ResizeObserver | null = null;
let resizeObserver5: ResizeObserver | null = null;
let resizeObserver6: ResizeObserver | null = null;
let resizeObserver7: ResizeObserver | null = null;
const chartsLeftThreeRef = ref();//设备故障分类对比 左3
const chartsLeftFourRef = ref();//采集齐全率分析 左4
const chartsCenterOneRef = ref();//中 1
const deviceFaultsList = ref([
  {
    belongsDeviceName: '未知',
    deviceName: '海南15-27压力表',
    alertTime: '2025-06-16 11:54',
    groupName: '海南三区1号站',
    faultReason: '设备故障异常值：A相电压异常值93；B相电压异常值79',
    suggestion: '建议一：...；建议二：...'
  },
  // 其他数据项...
]);//中 2
const listRef = ref<HTMLDivElement | null>(null); //列表dom
const scrollViewRef = ref<HTMLDivElement | null>(null); //滚动区域dom
const intervalId = ref<number | undefined>(); //滚动定时器
let isAutoScrolling = ref(true); //是否自动滚动标识
const isDialogOpen = ref(false); //弹窗是否打开的标识
const chartsRightOneRef = ref();//右1 设备周故障对比
const chartsRightTwoRef = ref();//右2 设备运行统计
const chartsRightThreeRef = ref();//右3 设备运维考核
const chartsRightFourRef = ref();//右4 设备故障区域排名
const chartDatas = reactive({});
interface product {
	productId: number;
	deviceCount: number;
	productName: string;
}
let chartleftOneDatas = ref<product[]>([])  //左1 设备监测范围
let chartleftTwoDatas = ref({
	abnormalCountsList: [] as any,//异常设备数
	collectionRatesList: [] as any,//采集齐全率
	datesList: [] as any,//统计日期
	onlineCountsList: [] as any,//在线设备数
	totalCountsList: [] as any //设备总数
})  //左2 设备监测动态
let chartleftThreeDatas = ref([])  //左3 设备监测范围
let chartleftFourDatas = ref({
	abnormalCountsList: [] as any,//异常设备数
	collectionRatesList: [] as any,//采集齐全率
	placesList: [] as any,//统计日期
	normalCountsList: [] as any,//正常设备数
	totalCountsList: [] as any //设备总数
})  //左4 采集齐全率分析
let chartRightOneDatas = ref({
	namesList: [] as any,//设备名称
	lastWeekDeviceCountsList: [] as any,//上周
	thisWeekDeviceCountsList: [] as any,//本周
})  //右1 设备周故障对比
const selectvalue = ref({
	groupId: '' as any,
	groupName: '',
}) //下拉框数据
let chartRightTwoDatas = ref({
	namesList: [] as any,//设备名称
	deviceNormalCountsList: [] as any,//正常数量
	deviceFaultsCountsList: [] as any,//故障数量
})  //右2 设备运行统计
let chartRightThreeDatas = ref({
	DatesList: [] as any,//设备名称
	handledFaultsCountsList: [] as any,//故障处置数
	deviceFaultsCountsList: [] as any,//设备故障数
})  //右3 设备运维考核
let chartRightFourDatas = ref({
	namesList: [] as any,//设备名称
	deviceFaultsCountsList: [] as any,//设备故障数
})  //右3 设备运维考核
const defaultProps = {
	children: 'children',
	label: 'groupName',
	value: 'groupId',
}
let options = ref([
	{
		groupId: 1111,
		groupName: `select1`,
	},
	{
		groupId: 222,
		groupName: `select2`,
	},
]
)

const deviceRangeScrollRef = ref<HTMLDivElement | null>(null);
let deviceRangeScrollTimer: number | undefined;
let isDeviceRangeScrolling = ref(true);

// 详情弹窗相关变量
const detailDialogVisible = ref(false);
const selectedFaultItem = ref<any>(null);
// 计算属性返回对应的中文描述
const conditionText = computed(() => {
  const cond = selectedFaultItem.value.scene.cond;
  switch (cond) {
    case 1:
      return "任意条件";
    case 2:
      return "所有条件";
    case 3:
      return "不满足";
    default:
      return "未知条件";
  }
});


// 获取显示文本的方法已移至 handleShowDetail 函数内部

const deviceTableData = ref([{ deviceCode: '', deviceName: '' }]);
const triggerTableData = ref<any[]>([]);

const deviceRangeScroll = () => {
  if (!deviceRangeScrollRef.value) return;
  const scrollBox = deviceRangeScrollRef.value;
  if (isDeviceRangeScrolling.value) {
    scrollBox.scrollTop += 1;
    if (scrollBox.scrollTop + scrollBox.clientHeight >= scrollBox.scrollHeight - 1) {
      scrollBox.scrollTop = 0;
    }
  }
};

const deviceRangeScrollStart = () => {
  isDeviceRangeScrolling.value = true;
};
const deviceRangeScrollPause = () => {
  isDeviceRangeScrolling.value = false;
};


onMounted(() => {
  deviceRangeScrollTimer = window.setInterval(deviceRangeScroll, 300);
});
onBeforeUnmount(() => {
  if (deviceRangeScrollTimer) clearInterval(deviceRangeScrollTimer);
});

// 切换全屏模式
const toggleFullScreen = () => {
	const elem = document.getElementById('dom') as any
	if (document.fullscreenElement) {
		document.exitFullscreen();
		isFullscreen.value = false;
	} else {
		if (elem.requestFullscreen) {
			elem.requestFullscreen();
		} else if (elem.webkitRequestFullscreen) { // Safari
			elem.webkitRequestFullscreen();
		} else if (elem.mozRequestFullScreen) { // Firefox
			elem.mozRequestFullScreen();
		} else if (elem.msRequestFullscreen) { // IE/Edge
			elem.msRequestFullscreen();
		}
		isFullscreen.value = true;
	}
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
	const isCurrentlyFullscreen = !!document.fullscreenElement;
	isFullscreen.value = isCurrentlyFullscreen;
	console.log('全屏状态变化:', isCurrentlyFullscreen);
};
// 获取页面显示情况
const getShowActive = (active: any) => {
	if (active == 0) {
		activeTab.value = 0

		nextTick(() => {
			allchartsinit()
			getAllList()
			autoScrolling();
		});
	} else {
		activeTab.value = active
		// 销毁图表实例和对应的ResizeObserver
		if (myChart1) {
			myChart1.dispose();
			myChart1 = null;
		}
		if (resizeObserver1) {
			resizeObserver1.disconnect();
			resizeObserver1 = null;
		}
		if (myChart2) {
			myChart2.dispose();
			myChart2 = null;
		}
		if (resizeObserver2) {
			resizeObserver2.disconnect();
			resizeObserver2 = null;
		}
		if (myChart3) {
			myChart3.dispose();
			myChart3 = null;
		}
		if (resizeObserver3) {
			resizeObserver3.disconnect();
			resizeObserver3 = null;
		}
		if (myChart4) {
			myChart4.dispose();
			myChart4 = null;
		}
		if (resizeObserver4) {
			resizeObserver4.disconnect();
			resizeObserver4 = null;
		}
		if (myChart5) {
			myChart5.dispose();
			myChart5 = null;
		}
		if (resizeObserver5) {
			resizeObserver5.disconnect();
			resizeObserver5 = null;
		}
		if (myChart6) {
			myChart6.dispose();
			myChart6 = null;
		}
		if (resizeObserver6) {
			resizeObserver6.disconnect();
			resizeObserver6 = null;
		}
		if (myChart7) {
			myChart7.dispose();
			myChart7 = null;
		}
		if (resizeObserver7) {
			resizeObserver7.disconnect();
			resizeObserver7 = null;
		}
		if (timeout.value !== undefined) clearInterval(timeout.value);
		timeout.value = undefined;
		if (timeout1.value !== undefined) clearInterval(timeout1.value);
		timeout1.value = undefined;
		if (intervalId.value !== undefined) clearInterval(intervalId.value);
		intervalId.value = undefined;
	}

}
// 设备检测范围 左1
const initChartsLeftOne = () => {

};
// 获取监控范围数据数据
const getMonitorRangList = async () => {
	const response = await MonitorRang()
	chartleftOneDatas.value = response.data.data[0].productDeviceCounts
	// 按照接口返回结构赋值
	const stats = response.data.data[0].deviceStatusCount || {};
	deviceRangeStats.value[0].value = stats.totalCount || 0;
	deviceRangeStats.value[1].value = stats.installedCount || 0;
	deviceRangeStats.value[2].value = stats.removedCount || 0;
	deviceRangeStats.value[3].value = stats.recycledCount || 0;
	deviceRangeStats.value[4].value = stats.scrappedCount || 0;
}


// 初始化设备监控动态数据 左2
const initChartsLeftTwo = () => {
	const handleResize = () => {
		if (myChart1 && !myChart1.isDisposed()) {
			myChart1.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver1) {
		resizeObserver1.disconnect();
	}
	resizeObserver1 = new ResizeObserver(handleResize);
	resizeObserver1.observe(chartsLeftTwoRef.value);
	myChart1 = echarts.init(chartsLeftTwoRef.value);
	const option = {
		grid: {
			// top: '30%',
			// right: 20,
			bottom: '13%',
			// left: 30,
		},
		tooltip: {
			trigger: 'axis',
			extraCssText: 'cursor: url(./images/pointer.png) 8 3, auto !important;',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			}
		},
		legend: {
			top: '5%',
			data: ['设备总数', '在线', '异常', '采集齐全率'],
			// 隐藏图例边框
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}
		},
		xAxis: [
			{
				type: 'category',
				data: chartleftTwoDatas.value.datesList,
				axisPointer: {
					type: 'shadow'
				},
				axisLabel: {
					interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
					// rotate: 38   //调整数值改变倾斜的幅度（范围-90到90）
					color: '#ccc',
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#2f4866',
						type: 'dashed',
					}
				},
				axisTick: {
					show: false, // (刻度线)
				},
			}
		],
		yAxis: [
			{
				type: 'value',
				minInterval: 1,
				name: '设备数',
				min: 0,
				// max: 6000,
				// interval: 1000,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisTick: {
					show: false, // (刻度线)
				},
				axisLabel: {
					formatter: '{value}',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				},


			},
			{
				type: 'value',
				name: '采集齐全率',
				min: 0,
				// max: 100,
				// interval: 20,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisLabel: {
					formatter: '{value}%',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				}
			}
		],
		series: [
			{
				name: '设备总数',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.totalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 起始颜色
						{ offset: 0, color: '#0b2f50' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '在线',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.onlineCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '异常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftTwoDatas.value.abnormalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2cb5cc' },   // 起始颜色
						{ offset: 0, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2cb5cc', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '采集齐全率',
				type: 'line',
				yAxisIndex: 1,
				smooth: true,  // 设置为 true，使得折线带有弧度
				tooltip: {
					valueFormatter: (value: any) => {
						return value + "%"; // 在 tooltip 中显示百分比
					}
				},
				data: chartleftTwoDatas.value.collectionRatesList,
				label: {
					show: true,
					position: 'top',
					color: '#1bb24e'
				},
				itemStyle: {

					color: '#24b256', // 折线点的颜色
					lineStyle: {
						width: 3,
						color: '#1aae4d' // 折线颜色
					}

				}
			}
		]
	};
	myChart1.setOption(option);
	state.myCharts.push(myChart1);
};
// 更新设备监控动态数据 左2
const updataChartsLeftTwo = () => {
	myChart1.setOption({
		xAxis: [
			{
				data: chartleftTwoDatas.value.datesList,

			}
		],
		series: [
			{
				data: chartleftTwoDatas.value.totalCountsList,
			},
			{
				data: chartleftTwoDatas.value.onlineCountsList,
			},
			{
				data: chartleftTwoDatas.value.abnormalCountsList,
			},
			{
				data: chartleftTwoDatas.value.collectionRatesList,
			}
		]
	});
}
// 获取设备监控动态数据 左2
const getMonitorDynamicsList = async () => {
	const response = await MonitorDynamics()
	const abnormalCountsList = [] as any
	const collectionRatesList = [] as any
	const datesList = [] as any
	const onlineCountsList = [] as any
	const totalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		abnormalCountsList.push(Number(item.abnormalCount))
		collectionRatesList.push(Number(item.collectionRate.replace('%', '')))
		datesList.push(item.date)
		onlineCountsList.push(Number(item.onlineCount))
		totalCountsList.push(Number(item.totalCount))
	})
	chartleftTwoDatas.value.abnormalCountsList = abnormalCountsList
	chartleftTwoDatas.value.collectionRatesList = collectionRatesList
	chartleftTwoDatas.value.datesList = datesList
	chartleftTwoDatas.value.onlineCountsList = onlineCountsList
	chartleftTwoDatas.value.totalCountsList = totalCountsList
	console.log(chartleftTwoDatas.value, '设备监测动态');
	updataChartsLeftTwo()

}

// 初始化设备故障分类对比数据 左3
const initChartsLeftThree = () => {
	const handleResize = () => {
		if (myChart2 && !myChart2.isDisposed()) {
			myChart2.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver2) {
		resizeObserver2.disconnect();
	}
	resizeObserver2 = new ResizeObserver(handleResize);
	resizeObserver2.observe(chartsLeftThreeRef.value);
	myChart2 = echarts.init(chartsLeftThreeRef.value);
	const option = {
		tooltip: {
			trigger: 'item',
			formatter: '{a} <br/>{b}: {c} ({d}%)'
		},
		series: [
			{
				name: '设备故障分类对比',
				type: 'pie',
				radius: [45, 70],
				center: ['50%', '50%'],
				// roseType: 'area',
				itemStyle: {
					borderRadius: 1,
				},
				data: chartleftThreeDatas.value,
				label: {
					show: true,  // 显示标签
					position: 'outside', // 标签位置：outside（外部）、inside（内部）
					color: '#ccc',  // 设置标签文字颜色
					fontSize: 12, // 设置文字大小
					formatter: '{b}\n{d}%',  // 显示名称和百分比，换行显示
				},
				emphasis: {
					label: {
						show: true,
						color: '#ffffff'  // 鼠标悬浮时文字颜色
					}
				}
			},
		],
	};
	myChart2.setOption(option);
	state.myCharts.push(myChart2);
};
//更新设备故障分类对比数据 左3
const updataChartsLeftThree = () => {
	myChart2.setOption({
		series: [
			{
				data:  chartleftThreeDatas.value,
			},
		],
	});
}
// 获取设备故障分类对比数据 左3
const getFaultsMalfunctionList = async () => {
	const response = await faultsMalfunction()
	const data = response.data.data.faultDeviceCounts.map((item: any) => {
		let newItem = {} as any;
		Object.entries(item).forEach(([key, value]) => {
			// 根据旧键名修改成新键名
			if (key === 'productName') {
				newItem.name = value;
			} else if (key === 'deviceFaultsCount') {
				newItem.value = value;
			} else {
				newItem[key] = value;  // 其他键保持不变
			}
		});
		return newItem;
	})
	chartleftThreeDatas.value = data.filter((item: { value: number; }) => item.value > 0);
	console.log(chartleftThreeDatas.value, '设备故障分类对比');

	updataChartsLeftThree()
}


// 初始化采集齐全率分析数据 左4
const initChartsLeftFour = () => {
	const handleResize = () => {
		if (myChart3 && !myChart3.isDisposed()) {
			myChart3.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver3) {
		resizeObserver3.disconnect();
	}
	resizeObserver3 = new ResizeObserver(handleResize);
	resizeObserver3.observe(chartsLeftFourRef.value);
	myChart3 = echarts.init(chartsLeftFourRef.value);
	const option = {
		grid: {
			top: '28%',
			// right: '10%',
			bottom: '25%',
			left: '10%'
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'cross',
				crossStyle: {
					color: '#999'
				}
			},
		},
		legend: {
			top: '3%',
			data: ['设备总数', '正常', '异常', '采集齐全率'],
			// 隐藏图例边框
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}
		},
		xAxis: [
			{
				type: 'category',
				data: chartleftFourDatas.value.placesList,
				axisPointer: {
					type: 'shadow'
				},
				axisLine: {
					show: true,
					lineStyle: {
						color: '#2f4866',
						type: 'dashed',
					}
				},
				axisTick: {
					show: false, // (刻度线)
				},
				axisLabel: {
					interval: 0, //坐标刻度之间的显示间隔，默认就可以了（默认是不重叠）
					rotate: 28,  //调整数值改变倾斜的幅度（范围-90到90）
					color: '#ccc',
				}
			}
		],
		yAxis: [
			{
				type: 'value',
				minInterval: 1,
				name: '监控数量',
				nameTextStyle: {
					color: '#ccc',
				},
				min: 0,
				alignTicks: true,
				axisLabel: {
					formatter: '{value}',
					color: '#ccc',

				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				}
			},
			{
				type: 'value',
				name: '采集齐全率',
				min: 0,
				alignTicks: true,
				nameTextStyle: {
					color: '#ccc',
				},
				axisLabel: {
					formatter: '{value}%',
					color: '#ccc',
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',  // 设置网格线为虚线
						color: '#2f4866'    // 设置网格线颜色
					}
				},

			}
		],
		series: [
			{
				name: '设备总数',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.totalCountsList,
				// data: [1, 2, 3, 4, 4, 4, 4,],
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 结束颜色
						{ offset: 0, color: '#0b2f50' }// 起始颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '正常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.normalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '异常',
				type: 'bar',
				tooltip: {
					valueFormatter: (value: any) => {
						return value;
					}
				},
				data: chartleftFourDatas.value.abnormalCountsList,
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2cb5cc' },   // 起始颜色
						{ offset: 0, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2cb5cc', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '采集齐全率',
				type: 'line',
				yAxisIndex: 1,
				smooth: true,  // 设置为 true，使得折线带有弧度
				tooltip: {
					valueFormatter: (value: any) => {
						return value + "%";
					}
				},
				data: chartleftFourDatas.value.collectionRatesList,
				// data: [0, 0, 0, 0, 0, 0.2, 1],
				label: {
					show: true,
					position: 'top',
					color: '#1bb24e'
				},
				itemStyle: {
					color: '#24b256', // 折线点的颜色
					lineStyle: {
						width: 3,
						color: '#1aae4d' // 折线颜色
					}

				}
			}
		]
	};
	myChart3.setOption(option);
	state.myCharts.push(myChart3);
};
// 更新采集齐全率分析数据 左4
const updataChartsLeftFour = () => {
	myChart3.setOption({
		xAxis: [
			{
				data: chartleftFourDatas.value.placesList,
			}
		],
		series: [
			{
				data: chartleftFourDatas.value.totalCountsList,

			},
			{
				data: chartleftFourDatas.value.normalCountsList,

			},
			{
				data: chartleftFourDatas.value.abnormalCountsList,

			},
			{
				data: chartleftFourDatas.value.collectionRatesList,
			}
		]
	});
}
// 获取采集齐全率分析数据 左4
const getCompletenessRate = async () => {
	const response = await completenessRate()
	const abnormalCountsList = [] as any
	const collectionRatesList = [] as any
	const placesList = [] as any
	const normalCountsList = [] as any
	const totalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		abnormalCountsList.push(Number(item.abnormalCount))
		collectionRatesList.push(Number(item.collectionRate.replace('%', '')))
		placesList.push(item.groupName)
		normalCountsList.push(Number(item.onlineCount))
		totalCountsList.push(Number(item.totalCount))
	})
	chartleftFourDatas.value.abnormalCountsList = abnormalCountsList
	chartleftFourDatas.value.collectionRatesList = collectionRatesList
	chartleftFourDatas.value.placesList = placesList
	chartleftFourDatas.value.normalCountsList = normalCountsList
	chartleftFourDatas.value.totalCountsList = totalCountsList
	console.log(chartleftFourDatas.value, '采集齐全率');
	updataChartsLeftFour()
}

// 初始化中间图表1
const initChartsCenterOne = () => {
	const myChart = echarts.init(chartsCenterOneRef.value);
	const option = {
		grid: {
			top: 15,
			right: 15,
			bottom: 20,
			left: 30,
		},
		tooltip: {},
		series: [
			{
				type: 'wordCloud',
				sizeRange: [12, 40],
				rotationRange: [0, 0],
				rotationStep: 45,
				gridSize: Math.random() * 20 + 5,
				shape: 'circle',
				width: '100%',
				height: '100%',
				textStyle: {
					fontFamily: 'sans-serif',
					fontWeight: 'bold',
					color: function () {
						return `rgb(${[Math.round(Math.random() * 160), Math.round(Math.random() * 160), Math.round(Math.random() * 160)].join(',')})`;
					},
				},
				data: [
					{ name: 'vue-next-admin', value: 520 },
					{ name: 'lyt', value: 520 },
					{ name: 'next-admin', value: 500 },
					{ name: '更名', value: 420 },
					{ name: '智慧农业', value: 520 },
					{ name: '男神', value: 2.64 },
					{ name: '好身材', value: 4.03 },
					{ name: '校草', value: 24.95 },
					{ name: '酷', value: 4.04 },
					{ name: '时尚', value: 5.27 },
					{ name: '阳光活力', value: 5.8 },
					{ name: '初恋', value: 3.09 },
					{ name: '英俊潇洒', value: 24.71 },
					{ name: '霸气', value: 6.33 },
					{ name: '腼腆', value: 2.55 },
					{ name: '蠢萌', value: 3.88 },
					{ name: '青春', value: 8.04 },
					{ name: '网红', value: 5.87 },
					{ name: '萌', value: 6.97 },
					{ name: '认真', value: 2.53 },
					{ name: '古典', value: 2.49 },
					{ name: '温柔', value: 3.91 },
					{ name: '有个性', value: 3.25 },
					{ name: '可爱', value: 9.93 },
					{ name: '幽默诙谐', value: 3.65 },
				],
			},
		],
	};
	myChart.setOption(option);
	state.myCharts.push(myChart);
};
// 获取设备故障清单 中2
const getfaultsList = async () => {
  try {
    const response = await faultsList();
    const formatTime = (timeStr: any) => {
      if (!timeStr) return '--';
      const date = new Date(timeStr);
      if (isNaN(date.getTime())) return '--';
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');
      const hours = date.getHours().toString().padStart(2, '0');
      const minutes = date.getMinutes().toString().padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    };
    const formattedData = response.data.data.map((item: any) => ({
      ...item,
      alertTime: formatTime(item.alertTime)
    }));
    deviceFaultsList.value = formattedData;
  } catch (error) {
    console.error('获取故障列表失败:', error);
  }
};
//设置自动滚动 中2
const autoScrolling = () => {
  intervalId.value = window.setInterval(() => {
    if (!scrollViewRef.value || !listRef.value) return;
    const scrollBox = scrollViewRef.value as HTMLDivElement;
    if (isAutoScrolling.value) {
      scrollBox.scrollTop += 1;
      const tolerance = 1;
      if (scrollBox.scrollTop + scrollBox.clientHeight >= scrollBox.scrollHeight - tolerance) {
        scrollBox.scrollTop = 0;
      }
    }
  }, 20);
};

// 初始化设备故障对比数据 右1
const initChartsRightOne = () => {
	const handleResize = () => {
		if (myChart4 && !myChart4.isDisposed()) {
			myChart4.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver4) {
		resizeObserver4.disconnect();
	}
	resizeObserver4 = new ResizeObserver(handleResize);
	resizeObserver4.observe(chartsRightOneRef.value);
	myChart4 = echarts.init(chartsRightOneRef.value);

	// 计算总数用于对比显示
	const lastWeekTotal = chartRightOneDatas.value.lastWeekDeviceCountsList.reduce((sum: number, val: number) => sum + val, 0);
	const thisWeekTotal = chartRightOneDatas.value.thisWeekDeviceCountsList.reduce((sum: number, val: number) => sum + val, 0);

	// 获取最大值用于设置坐标轴范围
	const leftData = chartRightOneDatas.value.lastWeekDeviceCountsList;
	const rightData = chartRightOneDatas.value.thisWeekDeviceCountsList;
	const leftmax = Math.max(...leftData);
	const rightmax = Math.max(...rightData);
	const maxValue = Math.max(leftmax, rightmax);

	const option = {
		backgroundColor: 'transparent',
		tooltip: {
			show: true,
			trigger: "item",
			formatter: function (params: any) {
				return `${params.seriesName}: ${params.value}`;
			},
		},
		// 添加标题显示总数对比和VS
		title: [
			{
				text: '上周',
				left: '16.5%',
				top: '5%',
				textAlign: 'center',
				textStyle: {
					color: '#00afff',
					fontSize: 14,
					fontWeight: 'bold'
				}
			},
			{
				text: lastWeekTotal.toString(),
				left: '16.5%',
				top: '12%',
				textAlign: 'center',
				textStyle: {
					color: '#00afff',
					fontSize: 24,
					fontWeight: 'bold'
				}
			},
			{
				text: 'VS',
				left: '50%',
				top: '8%',
				textAlign: 'center',
				textStyle: {
					color: '#ffffff',
					fontSize: 18,
					fontWeight: 'bold'
				}
			},
			{
				text: thisWeekTotal.toString(),
				left: '83.5%',
				top: '12%',
				textAlign: 'center',
				textStyle: {
					color: '#1aae4d',
					fontSize: 24,
					fontWeight: 'bold'
				}
			},
			{
				text: '本周',
				left: '83.5%',
				top: '5%',
				textAlign: 'center',
				textStyle: {
					color: '#1aae4d',
					fontSize: 14,
					fontWeight: 'bold'
				}
			}
		],
		grid: [
			// 左侧图表区域（上周数据）
			{
				top: "22%",
				left: "0%",
				width: "35%",
				height: "75%",
				containLabel: false,
			},
			// 右侧图表区域（本周数据）
			{
				top: "22%",
				left: "65%",
				width: "35%",
				height: "75%",
				containLabel: false,
			},
		],
		xAxis: [
			// 左侧X轴（上周数据，反向显示）
			{
				type: "value",
				inverse: true,
				show: false,
				min: 0,
				max: maxValue,
			},
			// 右侧X轴（本周数据）
			{
				gridIndex: 1,
				type: "value",
				min: 0,
				max: maxValue,
				show: false,
			},
		],
		yAxis: [
			// 左侧Y轴（隐藏标签）
			{
				type: "category",
				data: chartRightOneDatas.value.namesList,
				axisLabel: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				splitLine: {
					show: false
				}
			},
			// 右侧Y轴（隐藏标签）
			{
				gridIndex: 1,
				type: "category",
				data: chartRightOneDatas.value.namesList,
				axisLabel: {
					show: false,
				},
				axisTick: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				splitLine: {
					show: false
				}
			},
		],
		series: [
			// 左侧柱状图（上周数据）
			{
				name: "上周",
				type: "bar",
				barWidth: 20,
				itemStyle: {
					borderRadius: [8, 0, 0, 8],
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: '#00afff' },
						{ offset: 1, color: 'rgba(0, 175, 255, 0.6)' }
					]),
					borderColor: '#00afff',
					borderWidth: 1,
				},
				label: {
					show: true,
					color: '#ffffff',
					position: 'inside',
					fontSize: 12,
					fontWeight: 'bold'
				},
				data: leftData,
			},
			// 右侧柱状图（本周数据）
			{
				name: "本周",
				type: "bar",
				barWidth: 20,
				xAxisIndex: 1,
				yAxisIndex: 1,
				itemStyle: {
					borderRadius: [0, 8, 8, 0],
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: 'rgba(27, 190, 83, 0.6)' },
						{ offset: 1, color: '#1aae4d' }
					]),
					borderColor: '#1aae4d',
					borderWidth: 1,
				},
				label: {
					show: true,
					color: '#ffffff',
					position: 'inside',
					fontSize: 12,
					fontWeight: 'bold'
				},
				data: rightData,
			},
		],
		// 使用graphic添加中间的设备名称，与柱状图精确对齐
		graphic: chartRightOneDatas.value.namesList.map((name: string, index: number) => {
			const totalItems = chartRightOneDatas.value.namesList.length;
			// 与grid的top和height保持一致：22% + 75%
			const gridTop = 22;
			const gridHeight = 75;
			// 计算每个柱状图的中心位置，反转索引使设备名称从上到下显示
			const barHeight = gridHeight / totalItems;
			const reversedIndex = totalItems - 1 - index; // 反转索引
			const yPosition = gridTop + barHeight * reversedIndex + barHeight / 2;

			return {
				type: 'text',
				left: 'center',
				top: `${yPosition}%`,
				style: {
					text: name,
					textAlign: 'center',
					textVerticalAlign: 'middle',
					textBaseline: 'middle',
					fill: '#ffffff',
					fontSize: 12,
					fontWeight: 'bold'
				}
			};
		})
	};
	myChart4.setOption(option);
	state.myCharts.push(myChart4);
};
// 更新设备故障对比数据 右1
const updataChartsRightOne = () => {
	// 计算总数用于对比显示
	const lastWeekTotal = chartRightOneDatas.value.lastWeekDeviceCountsList.reduce((sum: number, val: number) => sum + val, 0);
	const thisWeekTotal = chartRightOneDatas.value.thisWeekDeviceCountsList.reduce((sum: number, val: number) => sum + val, 0);

	// 获取最大值用于设置坐标轴范围
	const leftData = chartRightOneDatas.value.lastWeekDeviceCountsList;
	const rightData = chartRightOneDatas.value.thisWeekDeviceCountsList;
	const leftmax = Math.max(...leftData);
	const rightmax = Math.max(...rightData);
	const maxValue = Math.max(leftmax, rightmax);

	myChart4.setOption({
		title: [
			{
				text: '上周',
				left: '16.5%',
				top: '5%',
				textAlign: 'center',
				textStyle: {
					color: '#00afff',
					fontSize: 14,
					fontWeight: 'bold'
				}
			},
			{
				text: lastWeekTotal.toString(),
				left: '16.5%',
				top: '12%',
				textAlign: 'center',
				textStyle: {
					color: '#00afff',
					fontSize: 24,
					fontWeight: 'bold'
				}
			},
			{
				text: 'VS',
				left: '50%',
				top: '8%',
				textAlign: 'center',
				textStyle: {
					color: '#ffffff',
					fontSize: 18,
					fontWeight: 'bold'
				}
			},
			{
				text: thisWeekTotal.toString(),
				left: '83.5%',
				top: '12%',
				textAlign: 'center',
				textStyle: {
					color: '#1aae4d',
					fontSize: 24,
					fontWeight: 'bold'
				}
			},
			{
				text: '本周',
				left: '83.5%',
				top: '5%',
				textAlign: 'center',
				textStyle: {
					color: '#1aae4d',
					fontSize: 14,
					fontWeight: 'bold'
				}
			}
		],
		xAxis: [
			{
				max: maxValue,
			},
			{
				max: maxValue,
			}
		],
		yAxis: [
			{
				data: chartRightOneDatas.value.namesList,
			},
			{
				data: chartRightOneDatas.value.namesList,
			}
		],
		series: [
			{
				data: leftData,
			},
			{
				data: rightData,
			}
		],
		// 更新中间的设备名称，与柱状图精确对齐
		graphic: chartRightOneDatas.value.namesList.map((name: string, index: number) => {
			const totalItems = chartRightOneDatas.value.namesList.length;
			// 与grid的top和height保持一致：22% + 75%
			const gridTop = 20;
			const gridHeight = 75;
			// 计算每个柱状图的中心位置，反转索引使设备名称从上到下显示
			const barHeight = gridHeight / totalItems;
			const reversedIndex = totalItems - 1 - index; // 反转索引
			const yPosition = gridTop + barHeight * reversedIndex + barHeight / 2;

			return {
				type: 'text',
				left: 'center',
				top: `${yPosition}%`,
				style: {
					text: name,
					textAlign: 'center',
					textVerticalAlign: 'middle',
					textBaseline: 'middle',
					fill: '#ffffff',
					fontSize: 12,
					fontWeight: 'bold'
				}
			};
		})
	});
}
// 获取设备故障周对比数据 右1
const getfaultsWeekList = async () => {
	const response = await faultsWeek()
	const namesList = [] as any
	const thisWeekDeviceCountsList = [] as any
	const lastWeekDeviceCountsList = [] as any

	// 创建设备名称到数据的映射，确保本周和上周数据对应正确
	const thisWeekMap = new Map()
	const lastWeekMap = new Map()

	// 处理本周数据
	response.data.data.thisWeekDeviceCounts.forEach((item: any) => {
		thisWeekMap.set(item.productName, item.deviceFaultsCount)
	})

	// 处理上周数据
	response.data.data.lastWeekDeviceCounts.forEach((item: any) => {
		lastWeekMap.set(item.productName, item.deviceFaultsCount)
	})

	// 获取所有设备名称（合并本周和上周的设备名称）
	const allDeviceNames = new Set([
		...response.data.data.thisWeekDeviceCounts.map((item: any) => item.productName),
		...response.data.data.lastWeekDeviceCounts.map((item: any) => item.productName)
	])

	// 按设备名称顺序构建数据数组，确保对应关系正确
	allDeviceNames.forEach((deviceName: string) => {
		namesList.push(deviceName)
		thisWeekDeviceCountsList.push(thisWeekMap.get(deviceName) || 0)
		lastWeekDeviceCountsList.push(lastWeekMap.get(deviceName) || 0)
	})

	chartRightOneDatas.value.namesList = namesList
	chartRightOneDatas.value.thisWeekDeviceCountsList = thisWeekDeviceCountsList
	chartRightOneDatas.value.lastWeekDeviceCountsList = lastWeekDeviceCountsList
	console.log(chartRightOneDatas.value, '设备故障周对比');
	updataChartsRightOne()
}


// 初始化设备运行统计数据 右2
const initChartsRightTwo = () => {
	const handleResize = () => {
		if (myChart5 && !myChart5.isDisposed()) {
			myChart5.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver5) {
		resizeObserver5.disconnect();
	}
	resizeObserver5 = new ResizeObserver(handleResize);
	resizeObserver5.observe(chartsRightTwoRef.value);
	myChart5 = echarts.init(chartsRightTwoRef.value);
	const option = {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		legend: {
			left: '3%',
			top: '5%',
			itemWidth: 15,
			itemHeight: 14,
			textStyle: {
				color: '#f7f7f7',
			}
		},
		grid: {
			top: '20%',
			left: '5%',
			right: '8%',
			bottom: '5%',
			containLabel: true
		},
		xAxis: {
			type: 'value',
			min: 0,
			// max: 1500,
			nameTextStyle: {
				color: '#ccc',
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			splitLine: {
				show: false,
			}
		},
		yAxis: {
			type: 'category',
			data: chartRightTwoDatas.value.namesList,
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			axisTick: {
				show: false, // (刻度线)
			},
			splitLine: {
				show: true,
				lineStyle: {
					type: 'dashed',  // 设置网格线为虚线
					color: '#2f4866'    // 设置网格线颜色
				}
			},
			axisLine: {
				show: false,
			},

		},
		series: [
			{
				name: '正常数',
				type: 'bar',
				stack: 'total',
				barWidth: 11,
				label: {
					show: true
				},
				emphasis: {
					focus: 'series'
				},
				data: chartRightTwoDatas.value.deviceNormalCountsList,
				itemStyle: {
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: '#1e90ff' },   // 起始颜色
						{ offset: 1, color: '#134b67' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#1e90ff', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '故障数',
				type: 'bar',
				stack: 'total',
				label: {
					show: true,
					color: '#ffffff',
				},
				// itemStyle: {
				// 	color: '#ff69b4',
				// },
				emphasis: {
					focus: 'series'
				},
				data: chartRightTwoDatas.value.deviceFaultsCountsList,
				itemStyle: {
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: '#ef686a' },//  起始颜色
						{ offset: 1, color: '#134b67' },// 结束颜色

					]),
					// 设置边框颜色为固定颜色
					borderColor: '#ef686a', // 边框固定颜色
					borderWidth: 2
				}
			},

		]
	};
	// const option = {
	// 	tooltip: {
	// 		trigger: 'item',  // 触发类型为 'item'，表示仅显示当前鼠标悬停的数据项
	// 		formatter: function (params,index) {
	// 			let tooltipContent = `${params.name}:<br/>`;
	// 			params.value.forEach((val, index) => {
	// 				tooltipContent += `指标${index + 1}: ${val}<br/>`; // 显示每个指标的名称和对应的数值
	// 			});
	// 			return tooltipContent;
	// 		}
	// 	},
	// 	legend: {
	// 		show: false,
	// 		data: ['正常数', '故障数']
	// 	},
	// 	radar: {
	// 		// shape: 'circle', ['示功仪', '温压一体仪', '压力变送器', '温度变送器', '智能电参']
	// 		indicator: [
	// 			{ name: '示功仪', },
	// 			{ name: '温压一体仪' },
	// 			{ name: '压力变送器' },
	// 			{ name: '温度变送器' },
	// 			{ name: '智能电参' },
	// 			// { name: 'Marketing', max: 25000 }
	// 		],
	// 		center: ['50%', '45%'],
	// 		radius: 75,
	// 		axisName: {
	// 			formatter: '【{value}】',
	// 			color: '#ccc'
	// 		},
	// 		axisLine: {
	// 			lineStyle: {
	// 				color: 'rgba(211, 253, 250, 0.8)'
	// 			}
	// 		},
	// 		splitLine: {
	// 			lineStyle: {
	// 				color: 'rgba(211, 253, 250, 0.8)'
	// 			}
	// 		}
	// 	},
	// 	series: [
	// 		{
	// 			name: '设备运行统计',
	// 			type: 'radar',
	// 			data: [
	// 				{
	// 					value: [1001, 952, 1314, 292, 1029],
	// 					name: '正常数',
	// 					itemStyle: {
	// 						color: '#00bdff' // 设置点的颜色
	// 					},
	// 					lineStyle: {
	// 						color: '#00bdff', // 设置线的颜色
	// 						width: 2 // 设置线宽
	// 					},
	// 					// areaStyle: {
	// 					// 	color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
	// 					// 		{
	// 					// 			color: 'rgba(255, 145, 124, 0.1)',
	// 					// 			offset: 0
	// 					// 		},
	// 					// 		{
	// 					// 			color: 'rgba(255, 145, 124, 0.9)',
	// 					// 			offset: 1
	// 					// 		}
	// 					// 	])
	// 					// }
	// 				},
	// 				{
	// 					value: [60, 60, 114, 15, 69],
	// 					name: '故障数',
	// 					itemStyle: {
	// 						color: 'rgba(255, 145, 124, 1)' // 设置点的颜色
	// 					},
	// 					lineStyle: {
	// 						color: 'blue', // 设置线的颜色
	// 						width: 2 // 设置线宽
	// 					},
	// 					areaStyle: {
	// 						color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
	// 							{
	// 								color: 'rgba(255, 145, 124, 0.1)',
	// 								offset: 0
	// 							},
	// 							{
	// 								color: 'rgba(255, 145, 124, 0.9)',
	// 								offset: 1
	// 							}
	// 						])
	// 					}
	// 				}
	// 			]
	// 		}
	// 	]
	// };
	myChart5.setOption(option);
	state.myCharts.push(myChart5);
};
// 更新设备运行统计数据 右2
const updataChartsRightTwo = () => {
	myChart5.setOption({
		yAxis: [
			{
				data: chartRightTwoDatas.value.namesList,
			},
		],
		series: [
			{
				data: chartRightTwoDatas.value.deviceNormalCountsList,
			},
			{
				data: chartRightTwoDatas.value.deviceFaultsCountsList,
			},
		]
	});
}
// 获取设备运行统计数据 右2
const getrunStatisticsList = async (data?: any) => {
	if (data) {
		selectvalue.value.groupId = data.groupId
		selectvalue.value.groupName = data.groupName
	}
	const response = await runStatistics(selectvalue.value)
	const namesList = [] as any
	const deviceFaultsCountsList = [] as any
	const deviceNormalCountsList = [] as any
	response.data.data.forEach((item: any) => {
		namesList.push(item.productName)
		deviceFaultsCountsList.push(item.deviceFaultsCount)
		deviceNormalCountsList.push(item.deviceNormalCount)
	})
	chartRightTwoDatas.value.namesList = namesList
	chartRightTwoDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	chartRightTwoDatas.value.deviceNormalCountsList = deviceNormalCountsList
	console.log(chartRightTwoDatas.value, '设备运行统计');

	updataChartsRightTwo()
}
// 获取查询所属单位列表 右2
const getAffiliationList = async () => {
	try {
		const response = await Affiliation()
		if (response.data.code == 200) {
			options.value = handleTree(response.data.data, "groupId") as any
			selectvalue.value.groupId = response.data.data[0].groupId
			selectvalue.value.groupName = response.data.data[0].groupName
			console.log(options.value, '所属单位列表');
			getrunStatisticsList()
			timeout1.value = window.setInterval(() => {
				getrunStatisticsList()
			}, 60000);
		}

	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally { }

}


// 初始化设备运维考核数据 右3
const initChartsRightThree = () => {
	const handleResize = () => {
		if (myChart6 && !myChart6.isDisposed()) {
			myChart6.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver6) {
		resizeObserver6.disconnect();
	}
	resizeObserver6 = new ResizeObserver(handleResize);
	resizeObserver6.observe(chartsRightThreeRef.value);
	myChart6 = echarts.init(chartsRightThreeRef.value);
	const option = {
		grid: {
			top: '28%',
			right: '5%',
			bottom: '15%',
			left: '10%',
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		legend: {
			data: ['设备故障数', '故障处置数'],
			// 隐藏图例边框
			top: '5%',
			left: '3%',
			// itemWidth: 15,
			// itemHeight: 14,
			itemStyle: {
				// 去掉图例项的边框
				borderWidth: 0,  // 图例项没有边框
				borderColor: 'transparent'  // 图例项边框颜色为透明
			},
			textStyle: {
				color: '#f7f7f7',
			}

		},
		xAxis: {
			type: 'category',
			data: chartRightThreeDatas.value.DatesList,
			axisTick: {
				show: false, // (刻度线)
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			axisLine: {
				show: true,
				lineStyle: {
					color: '#415872',
					type: 'dashed',
				}
			},
		},
		yAxis: {
			type: 'value',
			minInterval: 1,
			splitLine: {
				lineStyle: {
					type: 'dashed',
					color: '#2f4866',
				},
			},
			axisTick: {
				show: false,
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},

		},
		series: [
			{
				name: '设备故障数',
				data: chartRightThreeDatas.value.deviceFaultsCountsList,
				type: 'bar',
				barWidth: 15,
				label: {
					show: true, // Show data labels
					position: 'top', // Position at the top of the bars
					color: '#2574d6', // Text color
					fontWeight: 'bold', // Font weight
				},
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#2574d6' },   // 结束颜色
						{ offset: 0, color: '#0b2f50' }// 起始颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#2574d6', // 边框固定颜色
					borderWidth: 2,
				}
			},
			{
				name: '故障处置数',
				data: chartRightThreeDatas.value.handledFaultsCountsList,
				type: 'bar',
				barWidth: 15,
				label: {
					show: true, // Show data labels
					position: 'top', // Position at the top of the bars
					color: '#d4c26a', // Text color
					fontWeight: 'bold', // Font weight
				},
				itemStyle: {
					borderRadius: [10, 10, 0, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
					// 使用线性渐变色
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 1, color: '#dbbd3f' },   // 起始颜色
						{ offset: 0, color: '#364d4c' }// 结束颜色
					]),
					// 设置边框颜色为固定颜色
					borderColor: '#dbbd3f', // 边框固定颜色
					borderWidth: 2,
				}

			},
		],
	};
	// const option = {
	// 	tooltip: {
	// 		trigger: 'axis',
	// 		axisPointer: {
	// 			type: 'shadow'
	// 		}
	// 	},
	// 	legend: {
	// 		left: '3%',
	// 		right: '4%',
	// 		itemWidth: 15,
	// 		itemHeight: 14,
	// 		textStyle: {
	// 			color: '#f7f7f7',
	// 		}
	// 	},
	// 	grid: {
	// 		top: '15%',
	// 		left: '3%',
	// 		right: '5%',
	// 		bottom: '20%',
	// 		containLabel: true
	// 	},
	// 	xAxis: {
	// 		type: 'value',
	// 		min: 0,
	// 		max: 1500,
	// 		nameTextStyle: {
	// 			color: '#ccc',
	// 		},
	// 		axisLabel: {
	// 			formatter: '{value}',
	// 			color: '#ccc',
	// 		},
	// 		splitLine: {
	// 			show: false,
	// 		}
	// 	},
	// 	yAxis: {
	// 		type: 'category',
	// 		data: ['1月', '2月', '3月', '4月'],
	// 		axisLabel: {
	// 			formatter: '{value}',
	// 			color: '#ccc',
	// 		},
	// 		axisTick: {
	// 			show: false, // (刻度线)
	// 		},
	// 		splitLine: {
	// 			show: true,
	// 			lineStyle: {
	// 				type: 'dashed',  // 设置网格线为虚线
	// 				color: '#2f4866'    // 设置网格线颜色
	// 			}
	// 		},
	// 		axisLine: {
	// 			show: false,
	// 		},

	// 	},
	// 	series: [
	// 		{
	// 			name: '设备故障数',
	// 			type: 'bar',
	// 			stack: 'total',
	// 			barWidth: 11,
	// 			label: {
	// 				show: true
	// 			},
	// 			itemStyle: {
	// 				color: '#1e90ff',
	// 			},
	// 			emphasis: {
	// 				focus: 'series'
	// 			},
	// 			data: [1001, 952, 1314, 292, 1029],
	// 			itemStyle: {
	// 				// borderRadius: [0, 10, 10, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: '#ef686a',
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#1e90ff' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#1e90ff', // 边框固定颜色
	// 				borderWidth: 2,
	// 			}
	// 		},
	// 		{
	// 			name: '故障处置数',
	// 			type: 'bar',
	// 			stack: 'total',
	// 			label: {
	// 				show: true,
	// 				color: '#ffffff',
	// 			},
	// 			// itemStyle: {
	// 			// 	color: '#ff69b4',
	// 			// },
	// 			emphasis: {
	// 				focus: 'series'
	// 			},
	// 			data: [68, 60, 114, 15, 69,],
	// 			itemStyle: {
	// 				// borderRadius: [0, 10, 10, 0], // [左上, 右上, 左下, 右下] 四个角的圆角半径
	// 				color: '#ef686a',
	// 				// 使用线性渐变色
	// 				color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
	// 					{ offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#ef686a' }// 结束颜色
	// 				]),
	// 				// 设置边框颜色为固定颜色
	// 				borderColor: '#ef686a', // 边框固定颜色
	// 				borderWidth: 2
	// 			}
	// 		},

	// 	]
	// };
	myChart6.setOption(option);
	state.myCharts.push(myChart6);
};
// 更新设备运维考核数据 右3
const updataChartsRightThree = () => {
	myChart6.setOption({
		xAxis: [
			{
				data: chartRightThreeDatas.value.DatesList,
			}
		],
		series: [
			{
				data: chartRightThreeDatas.value.deviceFaultsCountsList,
			},
			{
				data: chartRightThreeDatas.value.handledFaultsCountsList,
			}
		]
	});
}
// 获取设备运维考核数据 右3
const getassessEvaluationList = async () => {
	const response = await assessEvaluation()
	const DatesList = [] as any
	const handledFaultsCountsList = [] as any
	const deviceFaultsCountsList = [] as any
	response.data.data.forEach((item: any) => {
		DatesList.push(item.date + '月')
		handledFaultsCountsList.push(item.handledFaultsCount)
		deviceFaultsCountsList.push(item.deviceFaultsCount)
	})
	chartRightThreeDatas.value.DatesList = DatesList
	chartRightThreeDatas.value.handledFaultsCountsList = handledFaultsCountsList
	chartRightThreeDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	console.log(chartRightThreeDatas.value, '设备运维考核');
	updataChartsRightThree()
}


// 初始化设备故障区域排名 右4
const initChartsRightFour = () => {
	const handleResize = () => {
		if (myChart7 && !myChart7.isDisposed()) {
			myChart7.resize();  // 调用 ECharts 的 resize 方法
		}
	};
	// 清理之前的观察者
	if (resizeObserver7) {
		resizeObserver7.disconnect();
	}
	resizeObserver7 = new ResizeObserver(handleResize);
	resizeObserver7.observe(chartsRightFourRef.value);
	myChart7 = echarts.init(chartsRightFourRef.value);
	const option = {
		grid: {
			top: '5%',
			bottom: '5%',
			left: '28%',
			right: '5%'
		},
		legend: {
			show: false
		},
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		xAxis: {
			show: false,
			max: 'dataMax',
			nameTextStyle: {
				color: '#ccc',
			},
			axisLabel: {
				formatter: '{value}',
				color: '#ccc',
			},
			splitLine: {
				show: false,
			}

		},
		yAxis: {
			type: 'category',
			data: chartRightFourDatas.value.namesList,
			inverse: true,
			animationDuration: 300,
			animationDurationUpdate: 300,
			axisLabel: {
				align: "left",
				margin: 110, // 距离右侧图形距离，配合axisLabel.left 和 grid.left 使用
				color: '#ccc',
				// 使用 formatter 来添加索引标签
				formatter: (params: any, index: any) => {
					let result;
					if (index == 0) {
						result = `{a1|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 1) {
						result = `{a2|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 2) {
						result = `{a3|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 3) {
						result = `{a4|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 4) {
						result = `{a5|${index + 1}}${" ".repeat(2)}${params}`;
					} else if (index == 5) {
						result = `{a6|${index + 1}}${" ".repeat(2)}${params}`;
					} else {
						result = `{a7|${index + 1}}${" ".repeat(2)}${params}`;
					}
					return result;
				},
				// formatter: '{value}',
				rich: {
					a1: {
						color: "#fff",
						backgroundColor: "#EA2739",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a2: {
						color: "#fff",
						backgroundColor: "#FF8C40",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a3: {
						color: "#fff",
						backgroundColor: "#FFC600",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a4: {
						color: "#fff",
						backgroundColor: "#438d15",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a5: {
						color: "#fff",
						backgroundColor: "#8B5CF6",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a6: {
						color: "#fff",
						backgroundColor: "#F472B6",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
					a7: {
						color: "#fff",
						backgroundColor: "#3C7DF9",
						width: 18,
						height: 18,
						align: "center",
						borderRadius: 4,
					},
				}
			},
			axisTick: {
				show: false, // (刻度线)
			},
			// splitLine: {
			// 	show: true,
			// 	lineStyle: {
			// 		type: 'dashed',  // 设置网格线为虚线
			// 		color: '#2f4866'    // 设置网格线颜色
			// 	}
			// },
			axisLine: {
				show: false,
			},
		},
		series: [
			{
				realtimeSort: true,
				name: '故障次数',
				type: 'bar',
				data: chartRightFourDatas.value.deviceFaultsCountsList,
				barWidth: 11,
				label: {
					show: true,
					position: 'right',
					valueAnimation: true,
					color: '#ffffff',
				},
				itemStyle: {
					borderRadius: [10, 10, 10, 10],
					color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
						{ offset: 0, color: '#2cb5cc' },
						{ offset: 0.5, color: '#188df0' },
						{ offset: 1, color: '#2574d6' }
					])
				},
				emphasis: {
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#2574d6' },
							{ offset: 0.5, color: '#2378f7' },
							{ offset: 1, color: '#2cb5cc' }
						])
					}
				},
			}
		],

		animationDuration: 0,
		animationDurationUpdate: 3000,
		animationEasing: 'linear',
		animationEasingUpdate: 'linear'
	};
	myChart7.setOption(option as any);
	state.myCharts.push(myChart7);
};
// 更新设备故障区域排名数据 右4
const updataChartsRightFour = () => {
	myChart7.setOption({
		yAxis: [
			{
				data: chartRightFourDatas.value.namesList,
			}
		],
		series: [
			{
				data: chartRightFourDatas.value.deviceFaultsCountsList,
			}
		]
	});
}
// 获取设备故障区域排名数据 右4
const getregionalRankingsList = async () => {
	const response = await regionalRankings()
	const namesList = [] as any
	const deviceFaultsCountsList = [] as any
	response.data.data.forEach((item: any) => {
		namesList.push(item.groupName)
		deviceFaultsCountsList.push(item.count)
	})
	chartRightFourDatas.value.namesList = namesList
	chartRightFourDatas.value.deviceFaultsCountsList = deviceFaultsCountsList
	console.log(chartRightFourDatas.value, '设备故障区域排名');
	updataChartsRightFour()
}
// 获取运维看板所有列表数据
const getAllList = () => {
	getMonitorRangList()
	getMonitorDynamicsList()
	getFaultsMalfunctionList()
	getCompletenessRate()
	getfaultsWeekList()
	getassessEvaluationList()
	getregionalRankingsList()
	getfaultsList()
	timeout.value = window.setInterval(() => {
		getMonitorRangList()
		getMonitorDynamicsList()
		getFaultsMalfunctionList()
		getCompletenessRate()
		getfaultsWeekList()
		getassessEvaluationList()
		getregionalRankingsList()
		getfaultsList()
	}, 60000);
}
// 初始化echarts
const allchartsinit = () => {
	// initChartsLeftOne()
	initChartsLeftTwo()
	initChartsLeftThree();
	initChartsLeftFour()
	// initChartsCenterOne();
	// initChartsCenterTwo();
	initChartsRightOne();
	initChartsRightTwo()
	initChartsRightThree();
	initChartsRightFour()
	// initEchartsResize();
	getAffiliationList()
}
// 批量设置 echarts resize
const initEchartsResizeFun = () => {
	nextTick(() => {
		for (let i = 0; i < state.myCharts.length; i++) {
			state.myCharts[i].resize();
		}
	});
};
// 批量设置 echarts resize
const initEchartsResize = () => {
	window.addEventListener('resize', initEchartsResizeFun);

	// 监听开发者工具的打开/关闭
	let devtools = {
		open: false,
		orientation: null
	};

	const threshold = 160;

	setInterval(() => {
		if (window.outerHeight - window.innerHeight > threshold ||
			window.outerWidth - window.innerWidth > threshold) {
			if (!devtools.open) {
				devtools.open = true;
				// 开发者工具打开时，延迟调整图表大小
				setTimeout(() => {
					initEchartsResizeFun();
				}, 500);
			}
		} else {
			if (devtools.open) {
				devtools.open = false;
				// 开发者工具关闭时，延迟调整图表大小
				setTimeout(() => {
					initEchartsResizeFun();
				}, 500);
			}
		}
	}, 500);
};
// 页面加载时
onMounted(() => {
	allchartsinit()
	getAllList()
  getfaultsList();
	nextTick(() => {
		autoScrolling();
	});
	deviceRangeScrollTimer = window.setInterval(deviceRangeScroll, 300);
	initEchartsResize();

	// 添加额外的窗口大小变化监听，确保F12开发者工具打开时页面正确显示
	const handleWindowResize = () => {
		// 强制重新计算页面高度
		const chartScrollbar = document.querySelector('.chart-scrollbar') as HTMLElement;
		if (chartScrollbar) {
			chartScrollbar.style.height = '100vh';
		}
		// 延迟调整图表大小
		setTimeout(() => {
			initEchartsResizeFun();
		}, 100);
	};

	window.addEventListener('resize', handleWindowResize);

	// 添加全屏状态变化监听器
	document.addEventListener('fullscreenchange', handleFullscreenChange);
	document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
	document.addEventListener('mozfullscreenchange', handleFullscreenChange);
	document.addEventListener('MSFullscreenChange', handleFullscreenChange);
});
onBeforeUnmount(() => {
	// 销毁图表实例和对应的ResizeObserver
	if (myChart1) {
		myChart1.dispose();
		myChart1 = null;
	}
	if (resizeObserver1) {
		resizeObserver1.disconnect();
		resizeObserver1 = null;
	}
	if (myChart2) {
		myChart2.dispose();
		myChart2 = null;
	}
	if (resizeObserver2) {
		resizeObserver2.disconnect();
		resizeObserver2 = null;
	}
	if (myChart3) {
		myChart3.dispose();
		myChart3 = null;
	}
	if (resizeObserver3) {
		resizeObserver3.disconnect();
		resizeObserver3 = null;
	}
	if (myChart4) {
		myChart4.dispose();
		myChart4 = null;
	}
	if (resizeObserver4) {
		resizeObserver4.disconnect();
		resizeObserver4 = null;
	}
	if (myChart5) {
		myChart5.dispose();
		myChart5 = null;
	}
	if (resizeObserver5) {
		resizeObserver5.disconnect();
		resizeObserver5 = null;
	}
	if (myChart6) {
		myChart6.dispose();
		myChart6 = null;
	}
	if (resizeObserver6) {
		resizeObserver6.disconnect();
		resizeObserver6 = null;
	}
	if (myChart7) {
		myChart7.dispose();
		myChart7 = null;
	}
	if (resizeObserver7) {
		resizeObserver7.disconnect();
		resizeObserver7 = null;
	}
	if (timeout.value !== undefined) clearInterval(timeout.value);
	timeout.value = undefined;
	if (timeout1.value !== undefined) clearInterval(timeout1.value);
	timeout1.value = undefined;
	if (intervalId.value !== undefined) clearInterval(intervalId.value);
	intervalId.value = undefined;
	if (deviceRangeScrollTimer) clearInterval(deviceRangeScrollTimer);

	// 清理窗口事件监听器
	window.removeEventListener('resize', initEchartsResizeFun);

	// 清理全屏状态监听器
	document.removeEventListener('fullscreenchange', handleFullscreenChange);
	document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
	document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
	document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
});
//鼠标进入，停止滚动
const onMouseenter = () => {
	// 如果弹窗打开，忽略鼠标进入事件，保持滚动
	if (!isDialogOpen.value) {
		isAutoScrolling.value = false;
	}
};
//鼠标移出，继续滚动
const onMouseleave = () => {
	// 如果弹窗打开，保持滚动状态
	if (!isDialogOpen.value) {
		isAutoScrolling.value = true;
	}
};
// 处理行点击事件
const handleRowClick = (item: any, index: number) => {
	console.log('点击了第', index + 1, '行:', item);
	// 这里可以添加更多的点击处理逻辑，比如：
	// - 显示详情弹窗
	// - 跳转到详情页面
	// - 高亮选中行等
};

// 处理排查按钮点击
const handleTroubleshoot = (item: any) => {
	console.log('排查设备故障:', item);
	// 这里可以添加排查相关的逻辑
};

// 处理详情按钮点击
const handleShowDetail = async (item: any) => {
	console.log('点击详情按钮，全屏状态:', isFullscreen.value);
	selectedFaultItem.value = item;
	detailDialogVisible.value = true;
	isDialogOpen.value = true; // 设置弹窗打开状态

	// 确保弹窗打开时背景滚动继续
	isAutoScrolling.value = true;

	// 模拟设备表格数据
	deviceTableData.value = [
		{
			deviceCode: generateDeviceCode(),
			deviceName: item.deviceName || 'Test'
		}
	];

	// 构建触发器表格数据
	if (item.scene && item.scene.triggers && Array.isArray(item.scene.triggers)) {
		// 准备查询参数
		const queryParams = {
			pageNum: 1,
			pageSize: 1000,
		};

		try {
			// 获取设备列表数据
			const response = await listDeviceShort(queryParams);
			const deviceList = response.data.rows;
			// 创建设备映射表，以 serialNumber 为键，deviceName 为值
			const deviceMap = new Map();
			deviceList.forEach((device: any) => {
				deviceMap.set(device.serialNumber, device.deviceName);
			});
		triggerTableData.value = item.scene.triggers.map((trigger: any) => {
			// 获取触发条件文本
			const getConditionText = (cond: number) => {
				switch (cond) {
					case 1: return "任意条件";
					case 2: return "所有条件";
					case 3: return "不满足条件";
					default: return "未知条件";
				}
			};

			// 获取触发源文本
			const getTriggerSourceText = (source: number) => {
				switch (source) {
					case 1: return "设备触发";
					case 2: return "定时触发";
					case 3: return "产品触发";
					default: return "未知触发";
				}
			};

			// 构建触发内容
			const buildTriggerContent = (trigger: any) => {
				const parts = [];
				if (trigger.parentName) parts.push(trigger.parentName+":");
				if (trigger.parentId) parts.push(trigger.parentId);
				if (trigger.operator) parts.push(trigger.operator);
				if (trigger.value !== undefined && trigger.value !== null) parts.push(trigger.value);
				return parts.length > 0 ? parts.join(' ') : '--';
			};

			// 根据触发源类型决定触发设备显示内容
			const getTriggerDevice = (trigger: any) => {
				switch (trigger.source) {
					case 1: // 设备触发
						if (trigger.deviceNums && Array.isArray(trigger.deviceNums)) {
							// 根据 serialNumber 查找对应的 deviceName
							const deviceNames = trigger.deviceNums
								.map((serialNumber: string) => deviceMap.get(serialNumber))
								.filter((name: string) => name) // 过滤掉未找到的设备
								.join(', '); // 多个设备名用逗号分隔
							return deviceNames || '--';
						}
						return '--';
					case 2: // 定时触发
						return trigger.cronExpression || '--';
					case 3: // 产品触发
						return trigger.productName || '--';
					default:
						return '--';
				}
			};

			return {
				sceneName: item.scene.sceneName || '--',
				conditionText: getConditionText(item.scene.cond),
				triggerSource: getTriggerSourceText(trigger.source),
				triggerDevice: getTriggerDevice(trigger),
				triggerContent: buildTriggerContent(trigger)
			};
		});
	} catch (error) {
		console.error('获取设备列表失败:', error);
		// 如果获取设备列表失败，使用原始数据
		triggerTableData.value = item.scene.triggers.map((trigger: any) => {
			const getConditionText = (cond: number) => {
				switch (cond) {
					case 1: return "任意条件";
					case 2: return "所有条件";
					case 3: return "不满足条件";
					default: return "未知条件";
				}
			};

			const getTriggerSourceText = (source: number) => {
				switch (source) {
					case 1: return "设备触发";
					case 2: return "定时触发";
					case 3: return "产品触发";
					default: return "未知触发";
				}
			};

			const buildTriggerContent = (trigger: any) => {
				const parts = [];
				if (trigger.parentName) parts.push(trigger.parentName+":");
				if (trigger.parentId) parts.push(trigger.parentId);
				if (trigger.operator) parts.push(trigger.operator);
				if (trigger.value !== undefined && trigger.value !== null) parts.push(trigger.value);
				return parts.length > 0 ? parts.join(' ') : '--';
			};

			return {
				sceneName: item.scene.sceneName || '--',
				conditionText: getConditionText(item.scene.cond),
				triggerSource: getTriggerSourceText(trigger.source),
				triggerDevice: trigger.source === 1 ? (Array.isArray(trigger.deviceNums) ? trigger.deviceNums.join(', ') : '--')
                     : trigger.source === 2 ? (trigger.cronExpression || '--')
                     : trigger.source === 3 ? (trigger.productName || '--') : '--',
				triggerContent: buildTriggerContent(trigger)
			};
		});
	}
} else {
	// 如果没有触发器数据，显示空数组
	triggerTableData.value = [];
}

	// 在下一个tick中确保弹窗正确显示，并强制恢复滚动
	nextTick(() => {
		// 强制恢复滚动状态，防止弹窗遮罩影响
		setTimeout(() => {
			isAutoScrolling.value = true;
		}, 100);
	});
};

// 关闭详情弹窗
const handleCloseDetail = () => {
	detailDialogVisible.value = false;
	selectedFaultItem.value = null;
	isDialogOpen.value = false; // 重置弹窗状态

	// 确保弹窗关闭后背景滚动继续
	isAutoScrolling.value = true;
};

// 获取故障状态
const getFaultStatus = (item: any) => {
	// 根据实际业务逻辑返回状态
	return '故障报警';
};

// 格式化告警时长
const formatAlertDuration = (item: any) => {
	if (!item || !item.alertTime || !item.createTime) {
		return '--';
	}

	try {
		// 将时间字符串转换为Date对象
		const alertTime = new Date(item.alertTime);
		const createTime = new Date(item.createTime);

		// 计算时间差（毫秒）
		const diffMs = alertTime.getTime() - createTime.getTime();

		// 如果时间差为负数或0，返回默认值
		if (diffMs <= 0) {
			return '0分钟';
		}

		// 转换为各种时间单位
		const diffSeconds = Math.floor(diffMs / 1000);
		const diffMinutes = Math.floor(diffSeconds / 60);
		const diffHours = Math.floor(diffMinutes / 60);
		const diffDays = Math.floor(diffHours / 24);

		// 格式化显示
		if (diffDays > 0) {
			const remainingHours = diffHours % 24;
			const remainingMinutes = diffMinutes % 60;
			if (remainingHours > 0) {
				return `${diffDays}天${remainingHours}小时${remainingMinutes}分钟`;
			} else if (remainingMinutes > 0) {
				return `${diffDays}天${remainingMinutes}分钟`;
			} else {
				return `${diffDays}天`;
			}
		} else if (diffHours > 0) {
			const remainingMinutes = diffMinutes % 60;
			if (remainingMinutes > 0) {
				return `${diffHours}小时${remainingMinutes}分钟`;
			} else {
				return `${diffHours}小时`;
			}
		} else if (diffMinutes > 0) {
			return `${diffMinutes}分钟`;
		} else {
			return `${diffSeconds}秒`;
		}
	} catch (error) {
		console.error('时间格式化错误:', error);
		return '--';
	}
};

// 生成设备编码
const generateDeviceCode = () => {
	return '53435f24-4f9c-1322-adf3-4d13bdd07192';
};
// 由于页面缓存原因，keep-alive
onActivated(() => {
	initEchartsResizeFun();
});
// 监听 pinia 中的 tagsview 开启全屏变化，重新 resize 图表，防止不出现/大小不变等
watch(
	() => isTagsViewCurrenFull.value,
	() => {
		initEchartsResizeFun();
	}
);

// 监听弹窗状态变化，确保弹窗打开时背景滚动继续
watch(
	() => detailDialogVisible.value,
	(newVal) => {
		isDialogOpen.value = newVal; // 同步弹窗状态
		if (newVal) {
			// 弹窗打开时，确保滚动继续
			isAutoScrolling.value = true;
			console.log('弹窗打开，确保滚动继续');
		} else {
			// 弹窗关闭时，也确保滚动继续
			isAutoScrolling.value = true;
			console.log('弹窗关闭，确保滚动继续');
		}
	}
);

const deviceRangeStats = ref([
  { label: '总数', value: 0 },
  { label: '监控(在装)', value: 0 },
  { label: '临时拆卸', value: 0 },
  { label: '长停回收', value: 0 },
  { label: '回收报废', value: 0 }
]);
</script>

<style scoped lang="scss">
@use './chart.scss' as *;

/* 你的其它样式 */

@font-face {
	font-family: electronicFont;
	src: url('../../../public/font/DS-DIGIT.TTF') format('truetype');
	font-display: swap;
	/* 使用swap来快速显示文字 */
}

:deep(*) {
	cursor: url(./images/pointer.png) 8 3, auto !important;
}

.top-RightOne {
	color: #ffffff;
	display: flex;
	width: 100%;
	justify-content: space-between;
	align-items: center;

	.leftcss {
		width: 45%;
		height: 30px;
		display: flex;
		align-items: center;
		border-radius: 20px 0 0 20px;
		background: linear-gradient(to right, #0f55e1, rgba(0, 0, 0, 0));

		.title {
			margin: 0 20px 0 20px;
			font-size: 15px;

		}

		.num {
			background-image: -webkit-linear-gradient(135deg,
					#6ac7ff,
					#bce6fe 25%,
					#97d4f8 50%,
					#74cbff 75%,
					#4b9fdc);
			-webkit-text-fill-color: transparent;
			-webkit-background-clip: text;
		}
	}

	.rightcss {
		width: 45%;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		background: linear-gradient(to left, #097f6f, rgba(0, 0, 0, 0));
		border-radius: 0 20px 20px 0;

		.title {
			margin: 0 20px 0 20px;
			font-size: 15px;
		}

		.num {
			background-image: -webkit-linear-gradient(135deg,
					#4db5ae,
					#44b7aa 25%,
					#41c8b2 50%,
					#2cb99f 75%,
					#21b495);
			-webkit-text-fill-color: transparent;
			-webkit-background-clip: text;
		}
	}
}

.instrumentList {
	width: 100%;
	height: 240px; // 可根据实际内容调整
	min-height: 60px;
	overflow-y: auto;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	// justify-content: center;
	// align-content: center;

	.clearfix {
		width: 20%;
		height: 100%;
	}

	.li {
		// float: left;
		width: 100%;
		height: 100%;
		padding: 0 0 5PX 0;
		display: flex;
		flex-direction: column;
		justify-content: center;

		span {
			opacity: .6;
			font-size: 12px;
			width: 100%;
			text-align: center;
		}
	}

	.yq {
		width: 2.7vw;
		height: 2.7vw;
		margin: 0 auto 5px auto;
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 25px;
		font-family: electronicFont;
		color: #fff32b;
	}

	.yq:before {
		position: absolute;
		width: 100%;
		height: 100%;
		content: "";
		background: url('/images/img1.png') center center;
		border-radius: 100px;
		background-size: 100% 100%;
		opacity: .3;
		left: 0;
		top: 0;
		animation: myfirst2 30s infinite linear;
	}

	.yq:after {
		position: absolute;
		width: 86%;
		background: url('/images/img2.png') center center;
		border-radius: 100px;
		background-size: 100% 100%;
		opacity: .3;
		height: 86%;
		content: "";
		// left: 7%;
		// top: 7%;
		animation: myfirst 30s infinite linear;
	}

	@keyframes myfirst {
		to {
			transform: rotate(-360deg)
		}
	}

	@keyframes myfirst2 {
		to {
			transform: rotate(360deg)
		}
	}
}


/* 给特定的el-select的选项设置背景颜色 */
:deep(.my-select .el-select__wrapper) {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	box-shadow: none;
	/* 设置选项背景色 */
	// { offset: 0, color: '#134b67' },   // 起始颜色
	// 					{ offset: 1, color: '#1e90ff' }/
}

:deep(.my-select span) {
	// background-color: red !important;
	box-shadow: none;
	color: #ffffff !important;
	/* 设置选项背景色 */
}

:deep(.my-select svg) {
	// background-color: red !important;
	box-shadow: none;
	color: #ffffff !important;
	/* 设置选项背景色 */
}

:deep(.el-popper) {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// border: none;
}
</style>
<style lang="scss">
.mySelectStyle {

	// 下拉框移入hover
	// .el-tree-node__children {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }


	// .el-popper__arrow:before {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }

	//下拉边框颜色
	// .el-select-dropdown__list {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	// padding: !important;
	// }

	// 小方块
	// .el-popper[data-popper-placement^=bottom] .el-popper__arrow::before  {
	// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	// }


	.el-tree-node {
		background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	}

	// 父级背景图 字体颜色
	.el-tree-node__content {
		background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	}

	// 字体颜色
	.el-select-dropdown__item span {
		color: #ffffff !important;
	}

	.el-tree-node__content:hover {
		background: rgba(255, 255, 255, 0.1) !important;
	}
}

// //下拉框背景色
.mySelectStyle.el-popper.is-light.mySelectStyle {
	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
	border: 1px solid #004f73 !important;
}

.mySelectStyle.el-popper[data-popper-placement^=bottom] .el-popper__arrow::before {
	background: #006c93 !important;
	border: none;
}


// .mySelectStyle.el-select-dropdown__item {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// }

// //下拉框的链接小方块
// .mySelectStyle.el-select-dropdown__item.is-active {
// 	background: #254277 !important;
// }

// //鼠标移动上去的选中色
// .mySelectStyle.el-select-dropdown__item.hover {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid #004f73 !important;
// }

// //下拉框背景色
// .mySelectStyle.el-popper.is-light.mySelectStyle {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid #004f73 !important;
// }


// //下拉框的链接小方块
// .mySelectStyle.el-popper[data-popper-placement^=bottom] .el-popper__arrow::before {
// 	background: #006c93 !important;
// 	border: none;
// }

// .mySelectStyle.el-popper[data-popper-placement^=top] .el-popper__arrow::before {
// 	background: linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// 	border: 1px solid linear-gradient(to right, #09446e, #006d94, #004f73) !important;
// }

// //鼠标移动上去的选中色
// .mySelectStyle {
// 	padding: 0 !important;

// 	/* 处理鼠标移出后的背景颜色 */
// 	.el-select-dropdown .el-select-dropdown__item {
// 		background: transparent !important;
// 		/* 或你想要的背景色 */
// 	}

// 	.el-select-dropdown__item.hover,
// 	.el-select-dropdown__item:hover {
// 		background: rgba(255, 255, 255, 0.1) !important;
// 	}

// 	/* 处理选中项的背景颜色 */
// 	.el-select-dropdown .el-select-dropdown__item.selected {
// 		background: rgba(255, 255, 255, 0.1) !important;
// 	}



// 	//下拉框的文本颜色
// 	.el-select-dropdown__item {
// 		color: #ffffff !important;
// 		font-size: 12px
// 	}
// }

::-webkit-scrollbar {
	display: none;
	// background-color: none;
}

// .big-data-down-center-two {
//  margin-top: 100px;
// }


//
//.flex-content {
//  padding: 10px;
//}

.head {
  display: flex;
  background-color: rgba(11, 196, 233, 0.1);
  color: #ffffff;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin-bottom: 2px;
}

.col {
  flex: 1;
  text-align: center;
  padding:3px 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.row {
  display: flex;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin: 1px 0;

  &:hover {
    background-color: rgba(11, 196, 233, 0.2);
    box-shadow: 0 2px 8px rgba(11, 196, 233, 0.3);
    transform: translateY(-1px);
  }

  &:active {
    background-color: rgba(11, 196, 233, 0.4);
    transform: translateY(0);
    box-shadow: 0 1px 4px rgba(11, 196, 233, 0.5);
  }
}
/* 操作按钮样式 */
.action-btn {
  margin: 0 2px;
  padding: 2px 5px;
  font-size: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  background-color: #007bff;
  color: white;
}
.no-col {
  width: 20px; /* 根据需要调整 */
  flex: 0 0 50px;
}
.chart-warp-bottom {
	margin-top: -50px;
}

/* 详情弹窗样式 - 全局样式 */
:deep(.detail-dialog) {
  .el-dialog {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border: 1px solid #0bc4e9;
    border-radius: 8px;
  }

  .el-dialog__header {
    background: rgba(11, 196, 233, 0.1);
    border-bottom: 1px solid #0bc4e9;
    padding: 15px 20px;

    .el-dialog__title {
      color: #ffffff;
      font-size: 18px;
      font-weight: bold;
    }
  }

  .el-dialog__body {
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
  }

  .el-overlay {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.detail-content {
  .detail-row {
    display: flex;
    margin-bottom: 15px;
    align-items: center;

    .detail-label {
      color: #0bc4e9;
      font-weight: bold;
      width: 140px;
      flex-shrink: 0;
    }

    .detail-value {
      color: #1a1a1a;
      flex: 1;
    }
  }

  .detail-section {
    margin-top: 20px;

    .section-title {
      color: #0bc4e9;
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 16px;
    }
  }
}

:deep(.device-table) {
  .el-table {
    background: transparent !important;

    .el-table__header {
      background: rgba(11, 196, 233, 0.1) !important;

      th {
        background: transparent !important;
        color: #ffffff !important;
        border-bottom: 1px solid #0bc4e9 !important;
      }
    }

    .el-table__body {
      tr {
        background: transparent !important;

        td {
          background: transparent !important;
          color: #ffffff !important;
          border-bottom: 1px solid rgba(11, 196, 233, 0.3) !important;
        }

        &:hover td {
          background: rgba(11, 196, 233, 0.1) !important;
        }
      }
    }
  }
}

/* 全屏模式下的弹窗样式调整 */
.detail-dialog {
  .el-dialog__wrapper {
    position: fixed !important;
  }

  .el-dialog {
    margin: 0 auto !important;
  }
}

/* 确保弹窗遮罩不影响背景滚动 */
:deep(.el-overlay) {
  pointer-events: none !important;
}

:deep(.el-dialog) {
  pointer-events: auto !important;
}
</style>
