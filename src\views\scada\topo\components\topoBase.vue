<script setup lang="ts">
// Vue 3 版本的 topoBase - 使用 Composition API
// 基础组件
import ViewText from './control/ViewText.vue';
// import ViewTextStatic from './control/ViewTextStatic.vue';
import View3DModel from './control/View3DModel.vue';
import ViewImage from './control/ViewImage.vue';
import ViewImageSwitch from './control/ViewImageSwitch.vue';
// import ViewKnobSwitch from './control/ViewKnobSwitch.vue';

// Canvas 形状组件
import ViewCircular from './control/canvas/ViewCircular.vue';
import ViewLine from './control/canvas/ViewLine.vue';
import ViewLineArrow from './control/canvas/ViewLineArrow.vue';
// import ViewLineWave from './control/canvas/ViewLineWave.vue';
import ViewBizierCurveArrow from './control/canvas/ViewBizierCurveArrow.vue';
import ViewRect from './control/canvas/ViewRect.vue';
import ViewTriangle from './control/canvas/ViewTriangle.vue';

// 图表组件
import ViewChart from './control/chart/ViewChart.vue';
import ViewChartPie from './control/chart/ViewChartPie.vue';
import ViewChartGauge from './control/chart/ViewChartGauge.vue';
import ViewChartWater from './control/chart/ViewChartWater.vue';
import ViewChartTemp from './control/chart/ViewChartTemp.vue';
import ViewChartMap from './control/chart/ViewChartMap.vue';
import ViewChartWrapper from './control/chart/ViewChartWrapper.vue';

// SVG 组件
// import ViewSvgImage from './control/svg/ViewSvgImage.vue';
// import ViewSvgStatic from './control/svg/ViewSvgStatic.vue';

// 媒体组件
import ViewVideo from './control/ViewVideo.vue';
import ViewVideoPlay from './control/ViewVideoPlay.vue';
import ViewVideoMp4 from './control/ViewVideoMp4.vue';

// 3D 组件
// import ViewThreeJs from './control/three/ViewThreeJs.vue';
// import ViewReliefBall from './control/three/ViewReliefBall.vue';
import ViewVR from './control/ViewVR.vue';

// 数据组件
// import ViewRealData from './control/ViewRealData.vue';
// import ViewHistory from './control/ViewHistory.vue';
// import ViewFlowBar from './control/ViewFlowBar.vue';
import ViewFlowBarDynamic from './control/ViewFlowBarDynamic.vue';

// 功能组件
import ViewMap from './control/ViewMap.vue';
import ViewPanel from './control/ViewPanel.vue';
import ViewTimer from './control/ViewTimer.vue';
import ViewWeather from './control/ViewWeather.vue';
import ViewWarn from './control/ViewWarn.vue';
import ViewOrder from './control/ViewOrder.vue';
import ViewComponent from './control/ViewComponent.vue';

// 导出组件映射表，供其他文件使用
const componentMap = {
  // 基础组件
  ViewText,
  // ViewTextStatic,
  View3DModel,
  ViewImage,
  ViewImageSwitch,
  // ViewKnobSwitch,

  // Canvas 形状组件
  ViewCircular,
  ViewLine,
  ViewLineArrow,
  // ViewLineWave,
  ViewBizierCurveArrow,
  ViewRect,
  ViewTriangle,

  // 图表组件
  ViewChart,
  ViewChartPie,
  ViewChartGauge,
  ViewChartWater,
  ViewChartTemp,
  ViewChartMap,
  ViewChartWrapper,

  // SVG 组件
  // ViewSvgImage,
  // ViewSvgStatic,

  // 媒体组件
  ViewVideo,
  ViewVideoPlay,
  ViewVideoMp4,

  // 3D 组件
  // ViewThreeJs,
  // ViewReliefBall,
  ViewVR,

  // 数据组件
  // ViewRealData,
  // ViewHistory,
  // ViewFlowBar,
  ViewFlowBarDynamic,

  // 功能组件
  ViewMap,
  ViewPanel,
  ViewTimer,
  ViewWeather,
  ViewWarn,
  ViewOrder,
  // ViewLuckDraw,
  ViewComponent,
};

// 类型映射表，用于动态组件解析
const typeComponentMap: Record<string, any> = {
  // 基础组件
  'text': ViewText,
  // 'text-static': ViewTextStatic,
  '3d-model': View3DModel,
  'image': ViewImage,
  'image-switch': ViewImageSwitch,
  // 'knob-switch': ViewKnobSwitch,

  // Canvas 形状组件
  'circular': ViewCircular,
  'line': ViewLine,
  'line-arrow': ViewLineArrow,
  // 'line-wave': ViewLineWave,
  'bizier-curve-arrow': ViewBizierCurveArrow,
  'rect': ViewRect,
  'triangle': ViewTriangle,

  // 图表组件
  'chart-line': ViewChart,
  'chart-line-step': ViewChart,
  'chart-bar': ViewChart,
  'chart-pie': ViewChartPie,
  'chart-gauge': ViewChartGauge,
  'chart-water': ViewChartWater,
  'chart-temp': ViewChartTemp,
  'chart-map': ViewChartMap,
  'chart-wrapper': ViewChartWrapper,

  // SVG 组件
  // 'svg-image': ViewSvgImage,
  // 'svg-static': ViewSvgStatic,

  // 媒体组件
  'video': ViewVideo,
  'video-play': ViewVideoPlay,
  'video-mp4': ViewVideoMp4,

  // 3D 组件
  // 'threejs': ViewThreeJs,
  // 'relief-ball': ViewReliefBall,
  'vr': ViewVR,

  // 数据组件
  // 'real-data': ViewRealData,
  // 'history': ViewHistory,
  // 'flow-bar': ViewFlowBar,
  'flow-bar-dynamic': ViewFlowBarDynamic,

  // 功能组件
  'map': ViewMap,
  'panel': ViewPanel,
  'timer': ViewTimer,
  'weather': ViewWeather,
  'warn': ViewWarn,
  'order': ViewOrder,
  // 'luck-draw': ViewLuckDraw,
  'component': ViewComponent,
};
</script>
