<template>
  <div class="model-wrap">
    <!-- 查询表单 -->
    <el-form :model="queryParams" ref="queryForm"  :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模型名称" prop="modelName">
        <el-input v-model="queryParams.modelName" size="default" placeholder="请输入模型名称" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="是否弃用" prop="success">
        <el-select v-model="queryParams.status" size="default" placeholder="请选择状态" clearable style="width: 240px">
          <el-option label="否" :value="0"></el-option>
          <el-option label="是" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
        <el-button size="default" @click="handleResetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
      </el-form-item>
    </el-form>
    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" :justify="'space-between'">
      <div>
        <el-button type="primary" size="default" @click="handleAdd"
                   v-auths="['scada:model:add']"><el-icon><ele-Plus /></el-icon>新增</el-button>
        <el-button type="success" size="default" :disabled="single" @click="handleUpdate"
                   v-auths="['scada:model:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
        <el-button type="danger" size="default" :disabled="multiple" @click="handleDelete"
                   v-auths="['scada:model:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
        <el-button type="warning" size="default" @click="handleExport"
                   v-auths="['scada:model:export']"><el-icon><ele-Download /></el-icon>导出</el-button>
        <span class="ml30" style="color: #f56c6c">3D物模管管理，暂时不可用。</span>
      </div>
      <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch" @query-table="getList" ></right-toolbar>
    </el-row>
    <!-- 表格 -->
    <el-table v-loading="loading" :data="modelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模型名称" align="center" prop="modelName" width="100" />
      <el-table-column label="模型地址" align="center" prop="modelUrl" />
      <el-table-column label="是否弃用" align="center" prop="status" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 0 ? 'success' : 'danger'">{{ row.status === 0 ? '否' : '是' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="缩略图" align="center" prop="imageUrl" width="160">
        <template #default="{ row }">
          <el-image
              v-if="row.imageUrl && typeof row.imageUrl === 'string' && row.imageUrl.trim() !== ''"
              :src="baseUrl+row.imageUrl"
              :preview-src-list="[row.imageUrl]"
              :style="{ width: '50px', height: '50px', objectFit: 'cover' }"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="260">
        <template #default="{ row }">
          <el-button style="color: #398ee5" size="default" text icon="Edit" @click="handleUpdate(row)" v-auths="['scada:model:edit']">修改</el-button>
          <el-button style="color: #e6a23c" size="default" text icon="View" @click="handlePreview(row)" v-auths="['scada:model:preview']">预览</el-button>
          <el-button style="color: #f56c6c" size="default" text icon="Delete" @click="handleDelete(row)" v-auths="['scada:model:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination v-show="total > 0" size="small" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                   layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 30]"
                   :pager-count="5" background class="mt15" style="justify-content: flex-end;"
                   @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    <!-- 对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.open" width="440px" append-to-body>
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
      <el-form ref="dialogForm" :model="dialog.form" :rules="dialog.rules" label-width="78px">
        <div>
        <el-form-item label="缩略图">
          <imageUpload v-model:model-value="dialog.form.imageUrl" ref="image-upload" :fileSize="5" :limit="1"/>
        </el-form-item>
        </div>
        <el-form-item label="模型名称" prop="modelName">
          <el-input v-model="dialog.form.modelName" placeholder="请输入模型名称" clearable />
        </el-form-item>
        <el-form-item label="是否弃用" prop="success">
          <el-select v-model="dialog.form.status" placeholder="请选择状态" clearable style="width: 100%">
            <el-option label="否" :value="0"></el-option>
            <el-option label="是" :value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模型地址" prop="modelUrl">
          <el-input v-model="dialog.form.modelUrl" type="textarea" placeholder="请输入模型地址" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleDialogCancel">取 消</el-button>
        <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listModel, getModel, delModel, addModel, updateModel } from '/@/api/scada/model'
import {download} from "/@/utils/request";
import imageUpload from '/@/components/ImageUpload/index.vue'
// 数据定义
const loading = ref(true)
const ids = ref()
const single = ref(true)
const multiple = ref(true)
const showSearch = ref(true)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  modelName: null,
  status: null,
})
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
const modelList = ref([])
const total = ref(0)

const dialog = reactive({
  open: false,
  title: '',
  form: {
    id: undefined,
    imageUrl: '',
    modelName: '',
    status: 0,
    modelUrl: '',
  },
  rules: {
    modelName: [{ required: true, message: '请输入模型名称', trigger: 'change' }],
    success: [{ required: true, message: '请选择状态', trigger: 'change' }],
    modelUrl: [{ required: true, message: '请输入模型地址', trigger: 'change' }],
  },
})

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.pageSize = size
  getList()
}

// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.pageNum = page
  getList()
}

// 获取列表数据
const getList = () => {
  loading.value = true
  listModel(queryParams).then((res) => {
    res = res.data
    if (res.code === 200) {
      modelList.value = res.rows
      total.value = res.total
    }
    loading.value = false
  })
}

// 搜索按钮操作
const handleQuery = () => {
  queryParams.pageNum = 1
  getList()
}

// 重置查询表单
const handleResetQuery = () => {
  queryParams.modelName = null
  queryParams.status = null
  handleQuery()
}

// 新增按钮操作
const handleAdd = () => {
  reset()
  dialog.open = true
  dialog.title = '添加模型管理'
}

// 表单重置
const reset = () => {
  dialog.form = {
    id: undefined,
    imageUrl: '',
    modelName: '',
    status: 0,
    modelUrl: '',
  }
}

// 取消按钮
const handleDialogCancel = () => {
  dialog.open = false
}

// 提交按钮
const handleDialogSubmit = () => {
  // 因为 Element Plus 不支持 this.$refs.validate，需要手动处理
  const valid = validateForm(dialog.form, dialog.rules)
  if (valid) {
    if (dialog.form.id) {
      updateModel(dialog.form).then((res) => {
        res = res.data
        if (res.code === 200) {
          ElMessage.success('修改成功')
          dialog.open = false
          getList()
        }
      })
    } else {
      addModel(dialog.form).then((res) => {
        res = res.data
        if (res.code === 200) {
          ElMessage.success('新增成功')
          dialog.open = false
          getList()
        }
      })
    }
  }
}

// 简单的表单校验函数（模拟 Vue 2 中的 $refs.validate）
function validateForm(formData: any, rules: any) {
  for (const key in rules) {
    const rule = rules[key]
    const value = formData[key]
    let valid = false
    for (const r of rule) {
      if (r.required && (value === null || value === '')) {
        ElMessage.error(r.message)
        return false
      } else {
        valid = true
      }
    }
    if (!valid) return false
  }
  return true
}

// 修改按钮操作
const handleUpdate = (row: any) => {
  dialog.title = '修改模型管理'
  const id = row.id || ids.value;
  getModel(id).then((res) => {
    res = res.data
    if (res.code === 200) {
      dialog.form = { ...res.data }
      dialog.open = true
    }
  })
}

// 预览
const handlePreview = (row: any) => {
  const id = row.id
  getModel(id).then((res) => {
    res = res.data
    if (res.code === 200) {
      if(res.data.modelUrl){
        window.open(res.data.modelUrl)
      }else{
        window.open('http://example.com')
      }
    }
  })
}

// 删除按钮操作
const handleDelete = (row: any) => {
  const id = row.id || ids.value;
  ElMessageBox.confirm('是否确认删除模型编号为"' + id + '"的数据项？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
      .then(() => {
        return delModel(id)
      })
      .then(() => {
        getList()
        ElMessage.success('删除成功')
      })
      .catch(() => {})
}

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map((item: { id: string; }) => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

// 导出按钮操作
const handleExport = () => {
  download('scada/model/export', {
    ...queryParams
  }, `模型${new Date().getTime()}.xlsx`)
}

// 页面初始化加载
onMounted(() => {
  getList()
})
</script>

<style scoped>
.model-wrap {
  padding: 20px;
}

.table-row-buttons {
  display: flex;
  align-items: center;
  gap: 10px; /* 按钮之间留出空隙 */
}

.disable {
  :deep(.el-upload--picture-card) {
    display: none !important;
  }
}
</style>
