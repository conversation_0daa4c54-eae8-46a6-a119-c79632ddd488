<template>
    <div class="layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-row>
                <!-- 驱动数据数据 -->
                <el-col :span="4">
                    <el-input v-model="deptName" size="default" placeholder="请输入插件名称" style="max-width: 90%">
                    </el-input>
                    <div class="mt10">
                        <el-tree style="max-width: 90%" :data="deptOptions" :props="defaultProps"
                            :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree" node-key="id"
                            default-expand-all highlight-current @node-click="handleNodeClick" />
                    </div>
                </el-col>
                <!-- 驱动数据 -->
                <el-col :span="20" :xs="24">
                    <div class="title">
                        {{ state.tableData.param.deptName }}
                    </div>
                    <el-form-item label="通道选择" style="max-width: 700px;" size="default">
                        <div class="select">
                            <el-select style="width: 300px;" v-model="optionsvalue" placeholder="请选择">xxxxxx
                                <el-option label="通道一" value="1" />
                                <el-option label="通道二" value="2" />
                                <el-option label="通道三" value="3" />
                                <el-option label="通道四" value="4" />
                            </el-select>
                            <button size="small">
                                断开
                            </button>
                            <button size="small">
                                打开
                            </button>
                        </div>
                    </el-form-item>
                </el-col>
            </el-row>

        </el-card>
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref, toRefs, watch } from 'vue';
import { ElMessageBox, ElMessage, ElUpload } from 'element-plus';
import { ElTree } from 'element-plus'; // 引入 ElTree 类型
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { deptTreeSelect } from '/@/api/system/user';

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
interface statusOption {
    dictValue: string;
    dictLabel: string;
}


// 定义变量内容
const state = reactive<any>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            status: undefined,
            deptId: undefined,
            deptName: '' as any,
        },
    },
});
interface Option {
    driveId: number;
    plugName: string;
    fileName: string;
    plugAllName: string;
    plugType: string;
    plugVersion: string;
    createTime: string;
    status: number;
}

const deptOptions = ref<Option[]>([]); //驱动列表
const deptName = ref()   //树形空间名称
const optionsvalue = ref(''); // 默认选中项
// const statuslist = ref<statusOption[]>([]);
// 创建对树的引用
const tree = ref<InstanceType<typeof ElTree> | null>(null);  // 明确类型注解
const defaultProps = reactive({
    children: "children",
    label: "plugName"
});
// 初始化表格数据
const getTableData = async () => {
    try {

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
        }, 500);
    }
};
// 筛选节点
const filterNode = (value: string, data: TreeNodeData) => {
    if (!value) return true;
    return data.label.indexOf(value) !== -1;
}
/** 查询部门下拉树结构 */
const getDeptTree = async () => {
    try {
        const response = await deptTreeSelect();
        // deptOptions.value = response.data.data;
        deptOptions.value = [
            {
                driveId: 1,
                plugName: 'Dlt645_ 2007Master',
                fileName: 'ts.Plugin.Dlt645',
                plugAllName: 'ts.Plugin.Dlt645',
                plugType: '采集驱动',
                plugVersion: '5.0.2.0',
                createTime: "2022-04-11 20:54:16",
                status: 1,
            },
            {
                driveId: 2,
                plugName: 'KafkaProducer',
                fileName: 'ts.Plugin.Kafka',
                plugAllName: 'ts.Plugin.Kafka',
                plugType: '业务驱动',
                plugVersion: '5.0.2.0',
                createTime: "2022-04-15 20:54:16",
                status: 1,
            },
            {
                driveId: 3,
                plugName: 'ModbusMaster',
                fileName: 'ts.Plugin.Modbus',
                plugAllName: 'ts.Plugin.Modbus',
                plugType: '	采集驱动',
                plugVersion: '5.0.2.0',
                createTime: "2022-04-18 20:54:16",
                status: 1,
            },
        ] as any
        state.tableData.param.deptName = deptOptions.value[0].plugName;
    } catch (error) {
        console.error('Error fetching department tree:', error);  // 错误处理
    }
}
// 获取状态数据
// const getdictdata = async () => {
// 	try {
// 		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
// 		// 处理字典数据
// 	} catch (error) {
// 		console.error('获取字典数据失败:', error);
// 	}
// };
// 树形控件节点单击事件
const handleNodeClick = (data: {
    plugName: any; id: any;
}) => {
    state.tableData.param.deptId = data.id;
    state.tableData.param.deptName = data.plugName;
    handleQuery();
}

/** 搜索按钮操作 */
const handleQuery = () => {
    getTableData();
}
// 监听 deptName 的变化
watch(deptName, (val) => {
    // 确保树组件已挂载
    if (tree.value) {
        tree.value.filter(val);  // 调用树组件的 filter 方法
    }
});
// 页面加载时
onMounted(() => {
    getTableData()
    getDeptTree()
    // getdictdata()
});
</script>

<style scoped lang="scss">
.title {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.select {
    display: flex;
    width: 100%;

    button {
        width: 60px;
        margin-left: 20px;
    }
}
</style>
