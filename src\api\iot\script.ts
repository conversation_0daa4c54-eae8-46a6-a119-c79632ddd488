import request from '/@/utils/request';

// 查询规则引擎脚本列表
export function listScript(query: any) {
    return request({
        url: '/iot/script/list',
        method: 'get',
        params: query,
    });
}

// 查询规则引擎脚本详细
export function getScript(scriptId: string) {
    return request({
        url: '/iot/script/' + scriptId,
        method: 'get',
    });
}

// 新增规则引擎脚本
export function addScript(data: any) {
    return request({
        url: '/iot/script',
        method: 'post',
        data: data,
    });
}

// 修改规则引擎脚本
export function updateScript(data: any) {
    return request({
        url: '/iot/script',
        method: 'put',
        data: data,
    });
}

// 删除规则引擎脚本
export function delScript(scriptId: string) {
    return request({
        url: '/iot/script/' + scriptId,
        method: 'delete',
    });
}

// 验证规则脚本
export function validateScript(scriptData: any) {
    return request({
        url: '/iot/script/validate',
        method: 'post',
        data: scriptData,
    });
}
