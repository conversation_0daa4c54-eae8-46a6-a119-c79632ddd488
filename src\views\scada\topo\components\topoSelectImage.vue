<template>
  <el-row class="topo-select-image" :gutter="10">
    <el-col :span="5">
      <el-menu class="menu-wrap" :default-active="categoryTypes[0]?.dictValue">
        <el-menu-item index="1" @click="handleClick('我的收藏')">
          <el-icon><Star/></el-icon>
          <span>我的收藏</span>
        </el-menu-item>
        <el-sub-menu index="2">
          <template #title>
            <div class="submenu-title">
              <el-icon><LocationFilled/></el-icon>
              <span>系统图库</span>
            </div>
          </template>
          <el-menu-item
              v-for="item in categoryTypes"
              :key="item.dictValue"
              :index="item.dictValue"
              @click="handleClick(item.dictValue)"
          >
            {{ item.dictLabel }}
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
    </el-col>

    <el-col :span="19">
      <el-form @submit.prevent :model="queryParams" ref="queryForm" inline label-width="68px">
        <el-form-item label="文件名称" prop="fileName">
          <el-input v-model="queryParams.fileName" size="default"  placeholder="请输入文件名称" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
          <el-button size="default" @click="handleResetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
          <el-button type="warning" size="default" :disabled="multiple" @click="handleCollection" ><el-icon><Star /></el-icon>收藏</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" v-if="queryParams.categoryName === '我的收藏'">
        <el-col :span="1.5">
          <el-upload
              ref="uploadRef"
              :action="`${upload.uploadUrl}?categoryName=${queryParams.categoryName}`"
              :headers="upload.headers"
              :before-upload="beforeUpload"
              :limit="500"
              :on-success="handleAvatarSuccess"
              :show-file-list="false"
              :file-list="upload.imageList"
              multiple
          >
            <el-button type="primary" plain size="default" @click="handleUploadFile">
              <el-icon><Upload /></el-icon>
              上传
            </el-button>
          </el-upload>
        </el-col>
        <el-col :span="1.5">
          <el-button
              type="danger"
              plain
              icon="Delete"
              size="default"
              :disabled="multiple"
              @click="handleDelete"
          >删除</el-button>
        </el-col>
      </el-row>

      <div v-loading="loading">
        <div class="data-box" v-if="total !== 0">
          <el-checkbox-group class="img-box-wrap" v-model="checkImages" @change="checkboxChange">
            <el-card
                class="img-card"
                :style="{ margin: '0 10px 10px 0' }"
                v-for="item in uploadList"
                :key="item.id"
                body-style="{ padding: '5px' }"
            >
              <img class="img" :src="baseApi + item.resourceUrl" />
              <div class="name-wrap">
                <span>{{ item.fileName }}</span>
              </div>
              <el-checkbox class="checkbox" :value="item"><span v-show="false">占位符</span></el-checkbox>
            </el-card>
          </el-checkbox-group>
        </div>
        <el-empty description="暂无数据" v-if="total === 0" />
      </div>

      <el-pagination
          v-show="total > 0"
          size="small"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="[20, 40, 60]"
          :pager-count="5"
          background
          class="mt15"
          style="justify-content: flex-end;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
      />
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 引入 API 方法
import { getDicts } from '/@/api/system/dict/data'
import { listGallery} from '/@/api/scada/gallery'
import { saveGalleryFavorites,getFavoriteGallerys, delFavoritesGallery } from '/@/api/scada/topo'
import {Session} from "/@/utils/storage";

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.pageSize = size
  if (queryParams.categoryName === '我的收藏') {
    getFavorites()
  } else {
    getList()
  }
}
const uploadRef = ref<any>(null);
// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.pageNum = page
  if (queryParams.categoryName === '我的收藏') {
    getFavorites()
  } else {
    getList()
  }
}
// 响应式状态
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
const loading = ref(true)
interface TypeOption {
  dictValue: string;
  dictLabel: string;
  listClass: string;
  cssClass: string;
}
const categoryTypes = ref<TypeOption[]>([])
const multiple = ref(true)

const queryParams = reactive({
  pageNum: 1,
  pageSize: 20,
  categoryName: '',
  fileName: '',
  moduleGuid: '云组态',
  orderByColumn: 'id',
  isAsc: 'desc'
})
interface TypeList {
  categoryName: string;
  fileName: string;
  id: number;
  resourceUrl: string;
}
const uploadList = ref<TypeList[]>([])
const total = ref(0)
const checkImages = ref<TypeList[]>([])
const upload = reactive({
  headers: { Authorization: 'Bearer ' + Session.get('token') },
  uploadUrl: [] as any,
  imageList: []
})


// 获取分类和列表
async function getDatas() {
  const dictType = 'scada_gallery_type'
  try {
    const response = await getDicts(dictType);
    const res = response.data
    if (res.code === 200) {
      categoryTypes.value = res.data
      if (res.data.length > 0) {
        queryParams.categoryName = res.data[0].dictValue
      }
      await getList()
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取分类失败', error)
  }
}


// 查询上传信息列表
async function getList() {
  loading.value = true
  try {
    const ress = await listGallery(queryParams)
    const res = ress.data
    if (res.code === 200) {
      uploadList.value = res.rows
      total.value = res.total
      checkImages.value = []
    }
  } finally {
    loading.value = false
  }
}

async function getFavorites() {
  loading.value = true
  try {
    const ress = await getFavoriteGallerys(queryParams)
    const res = ress.data
    if (res.code === 200) {
      uploadList.value = res.rows
      total.value = res.total
      checkImages.value = []
    }
  } finally {
    loading.value = false
  }
}

// 搜索按钮操作
function handleQuery() {
  queryParams.pageNum = 1
  if (queryParams.categoryName === '我的收藏') {
    getFavorites()
  } else {
    getList()
  }
}

// 重置查询
function handleResetQuery() {
  // 可以在这里重置表单逻辑
  queryParams.fileName = ''
  handleQuery()
}

// 复选框改变
function checkboxChange(selection:any) {
  multiple.value = selection.length === 0
}

// 收藏图标
function handleCollection() {
  if (checkImages.value.length === 0) {
    ElMessage.warning('请选择要收藏的图片')
    return
  }
  try {
    const ids = checkImages.value.map(item => item.id).join(',')
    const params = {
      categoryName: '我的收藏',
      idStr: ids,
      moduleGuid: '云组态',
    };
    saveGalleryFavorites(params);
    ElMessage.success({ message: '收藏成功' })
    checkImages.value = []
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('收藏失败:', error)
    ElMessage.error({ message: '收藏失败，请重试' })
  }
}

// 菜单点击
function handleClick(label:any) {
  queryParams.categoryName = label
  multiple.value = true
  checkImages.value = []
  if (queryParams.categoryName === '我的收藏') {
    getFavorites()
  } else {
    getList()
  }
}

// 上传前处理
function beforeUpload(file: File) {
  if (!queryParams.categoryName) {
    ElMessage.warning({ message: '请选择左侧上传的类型' })
    return false
  }
  const isLt2M = file.size / 1024 / 1024 < 10
  if (!isLt2M) {
    ElMessage.error({ message: '上传头像图片大小不能超过 10MB!' })
  }
  return isLt2M
}

// 上传成功回调
function handleAvatarSuccess(res: any) {
  if (res.code === 200) {
    ElMessage.success('上传成功')
    upload.imageList = []
    getFavorites()
  } else {
    ElMessage.error({ message: res.msg })
  }
}

// 处理上传文件的方法
function handleUploadFile() {
  upload.uploadUrl = baseApi.value + '/scada/center/uploadGalleryFavorites'
}

// 删除按钮操作
async function handleDelete() {
  const idsToDelete = checkImages.value.map(item => item.id)
  try {
    await ElMessageBox.confirm('是否确认删除此图标文件么？', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    await delFavoritesGallery(idsToDelete.toString())
    ElMessage.success('删除成功')
    if (queryParams.categoryName === '我的收藏') {
      getFavorites()
    } else {
      getList()
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('删除失败:', err)
  }
}

// 图片选择（外部调用）
function handleChoice() {
  return checkImages.value
}

// 清除选择（外部调用）
function clearChoice() {
  checkImages.value = []
}

// 暴露方法给父组件调用
defineExpose({
  handleChoice,
  clearChoice
})

// mounted 生命周期
onMounted(() => {
  getDatas()
})
</script>

<style scoped>
.topo-select-image {
  .el-menu {
    background-color: transparent !important;
    width: 97%;
  }
  .el-menu-item {
    height: 56px;
    line-height: 56px;
    font-size: 14px;
    color: #303133;
    padding: 0 20px;
    list-style: none;
    cursor: pointer;
    position: relative;
    -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
  }
  .menu-wrap {
    margin-top: 5px;
    height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    border-right: none;
    .submenu-title {
      padding: 0  0px;
      color: #303133;
    }
  }

  .data-box {
    height: 377px;
    overflow-y: auto;
    overflow-x: hidden;

    .img-box-wrap {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      align-content: center;
      padding: 0 10px;

      .img-card {
        width: 153px;
        height: auto;
        text-align: center;
        padding: 10px;
        position: relative;

        .img {
          width: 80px;
          height: 80px;
          margin-top: 10px;
        }

        .name-wrap {
          text-align: center;
          font-size: 11px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-top: 10px;
        }

        .checkbox {
          position: absolute;
          top: 8px;
          right: 0px;
        }
      }
    }
  }
}
:deep(.el-menu) {
  background-color: transparent !important;
}
</style>
<!-- 全局滚动条样式 -->
<style >
::-webkit-scrollbar {
  width: 2px;
  height: 3px;
  position: absolute;
}

::-webkit-scrollbar-thumb {
  background-color: #cccccc59;
}
</style>
