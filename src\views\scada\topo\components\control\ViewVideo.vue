<template>
    <div>
        <el-image
            v-if="editMode"
            :style="{
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
            }"
            :src="imageUrl"
            @dragstart.prevent
            @dragover.prevent
            @drop.prevent
        ></el-image>
        <div
            v-else
            class="video-placeholder"
            :style="{
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
            }"
        >
            <div class="video-placeholder-content">
                <div class="video-icon">
                    <el-icon size="48"><VideoPlay /></el-icon>
                </div>
                <h3>视频播放器</h3>
                <p>视频组件需要安装 ezuikit-js 依赖包</p>
                <p>请运行: npm install ezuikit-js</p>
                <div class="video-info" v-if="detail.dataBind">
                    <p><strong>设备序列号:</strong> {{ detail.dataBind.serialNumber || '未配置' }}</p>
                    <p><strong>通道号:</strong> {{ detail.dataBind.channelNumber || '未配置' }}</p>
                    <p><strong>访问令牌:</strong> {{ detail.dataBind.accessToken ? '已配置' : '未配置' }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'
import videoImage from '/@/assets/images/video.jpg'

// Props
interface Props {
  detail?: any
  editMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  detail: () => ({
    style: {
      position: {
        w: 640,
        h: 480
      }
    },
    dataBind: {
      serialNumber: '',
      channelNumber: '',
      accessToken: ''
    }
  })
})

// Refs
const imageUrl = ref(videoImage)

// 使用默认配置
const detail = computed(() => props.detail || {
  style: {
    position: {
      w: 640,
      h: 480
    }
  },
  dataBind: {
    serialNumber: '',
    channelNumber: '',
    accessToken: ''
  }
})
</script>

<style lang="scss" scoped>
.view-video {
    height: 100%;
    width: 100%;
}

.video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.video-placeholder-content {
    padding: 20px;
    z-index: 2;
}

.video-icon {
    margin-bottom: 15px;
    opacity: 0.9;
}

.video-placeholder-content h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
}

.video-placeholder-content p {
    margin: 8px 0;
    font-size: 14px;
    opacity: 0.9;
}

.video-info {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.video-info p {
    margin: 5px 0;
    font-size: 12px;
    text-align: left;
}

.video-placeholder::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/@/assets/images/video.jpg') center/cover;
    opacity: 0.1;
    z-index: 1;
}
</style>
