<template>
    <div
        :style="{
            fontSize: detail.style.fontSize + 'px',
            fontFamily: detail.style.fontFamily,
            color: detail.style.foreColor,
            textAlign: textAlign,
            lineHeight: lineHeight + 'px',
        }"
        @click="handleClick('滑动')"
        :id="detail.identifier"
    >
        {{ dateYear }} {{ dateWeek }} {{ dateDay }}
        <div></div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import anime from 'animejs'; //动作特效
import { formatTime1 } from '/@/utils/index';
import { getAnimate } from '/@/utils/topo/anime';

// Props
interface Props {
  detail: any;
  textAlign?: string;
  lineHeight?: number;
}
const props = defineProps<Props>();

// Refs
const timing = ref<any>(null);
const dateDay = ref<string | null>(null);
const dateYear = ref<string | null>(null);
const dateWeek = ref<string | null>(null);
const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
const animation = ref<any>(null);

// Computed
const textAlign = computed(() => props.textAlign || 'center');
const lineHeight = computed(() => props.lineHeight || props.detail.style.position.h);
// Methods
const handleClick = (label: string) => {
  let domId = document.getElementById(props.detail.identifier);
  if (label == '旋转') {
    // rotateAnimate(domId, 1000, true, false);
  } else if (label == '闪烁') {
    // scaleAnimate(domId, 1500, true, true);
  } else if (label == '滑动') {
    let translates = [
      { translateX: 40 },
      { translateX: 80 },
      { translateX: 120 },
      { translateX: 80 },
      { translateX: 40 },
      { translateX: 0 },
      { translateX: -40 },
      { translateX: -80 },
      { translateX: -120 },
      { translateX: -80 },
      { translateX: -40 },
      { translateX: 0 },
    ];
    // translateAnimate(domId, translates, 3000, true, true);
  } else {
    getAnimate().set(domId, {
      translateX: function () {
        return anime.random(50, 250);
      },
      rotate: function () {
        return anime.random(0, 360);
      },
    });
  }
};

const getTimer = () => {
  dateDay.value = formatTime1(new Date(), 'HH:mm:ss');
  dateYear.value = formatTime1(new Date(), 'yyyy-MM-dd');
  dateWeek.value = weekday[new Date().getDay()];
};

const timeFn = () => {
  timing.value = setInterval(() => {
    dateDay.value = formatTime1(new Date(), 'HH:mm:ss');
    dateYear.value = formatTime1(new Date(), 'yyyy-MM-dd');
    dateWeek.value = weekday[new Date().getDay()];
  }, 1000);
};

// Lifecycle
onMounted(() => {
  getTimer();
  timeFn();
});

onBeforeUnmount(() => {
  if (timing.value) {
    clearInterval(timing.value);
    timing.value = null;
  }
});
</script>
