<template>
  <div
      ref="setupAngle"
      class="setupAngle"
      @mousedown="mousedown = true"
      @mouseup="mousedown = false"
      @mousemove="onMouseMove"
      @mouseleave="mousedown = false"
  >
    <div
        class="container"
        :style="{ transform: 'rotate(' + angleData + 'deg)' }"
    >
      <div class="point"></div>
    </div>
    <div class="content">
      {{ angleData + '°' }}
    </div>
  </div>
</template>

<script setup >
import { ref } from 'vue';

// 响应式数据
const setupAngle = ref(null);
const angleData = ref(0);
const mousedown = ref(false);

// 计算角度函数
const calculateDegree = (x, y, centerX, centerY) => {
  const radians = Math.atan2(x - centerX, y - centerY);
  return radians * (180 / Math.PI) * -1 + 180;
};

// 鼠标移动事件
const onMouseMove = (event) => {
  if (mousedown.value) {
    const el = setupAngle.value;
    const centerX = (el.offsetHeight || el.clientHeight) / 2;
    const centerY = (el.offsetWidth || el.clientWidth) / 2;
    const angle = calculateDegree(event.offsetX, event.offsetY, centerX, centerY);
    angleData.value = Math.round(angle);
    // 触发自定义事件
    $emit('angle', angleData.value);
  }
};
</script>

<style scoped lang="scss">
.setupAngle {
  width: 100%;
  height: 100%;
  border: 5px solid #00ced1;
  box-sizing: border-box;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  background-color: #00ced1;
  box-shadow: #00ced1 0px 0px 10px;

  > .container {
    pointer-events: none;
    height: 100%;
    width: fit-content;
    padding: 5px 0;
    box-sizing: border-box;

    > .point {
      width: 15px;
      height: 15px;
      background-color: #e84545;
      border-radius: 50px;
      margin-top: -15px;
    }
  }

  > .content {
    position: absolute;
    font-size: 16px;
    color: #5b748e;
    font-weight: bold;
    pointer-events: none;
  }
}
</style>