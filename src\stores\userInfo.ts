import { defineStore } from 'pinia';
import Cookies from 'js-cookie';
import { Session } from '/@/utils/storage';
import { loginApi, getInfo } from '/@/api/login';
import {dynamicRoutes} from '/@/router/route';
import profileImage from '../assets/profile.jpg';
/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore('userInfo', {
	state: (): UserInfosState => ({
		userInfos: {
			token: Session.get('token'),
			username: '',
			photo: '',
			time: 0,
			roles: [],
			authBtnList: [],
			permissions:[],
			userId:''
		},
	}),
	actions: {
		// 登录
		async login(userInfo: any,) {
			const useinfo = await loginApi(userInfo)
			return useinfo
		},
		async setUserInfos() {
			// 存储用户信息到浏览器缓存
			if (Session.get('userInfo')) {
				this.userInfos = Session.get('userInfo');
			} else {
				const userInfos = <UserInfos>await this.getApiUserInfo();
				this.userInfos = userInfos;
			}
		},
		// 获取用户信息
		async getApiUserInfo() {
			return new Promise((resolve) => {
				setTimeout(async () => {
					// 模拟数据，请求接口时，记得删除多余代码及对应依赖的引入
					// const username = Cookies.get('username');
					// // 模拟数据
					// let defaultRoles: Array<string> = [];
					// let defaultAuthBtnList: Array<string> = [];
					// // admin 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
					// let adminRoles: Array<string> = ['admin'];
					// // admin 按钮权限标识
					// let adminAuthBtnList: Array<string> = ['btn.add', 'btn.del', 'btn.edit', 'btn.link'];
					// // test 页面权限标识，对应路由 meta.roles，用于控制路由的显示/隐藏
					// let testRoles: Array<string> = ['visitor'];
					// // test 按钮权限标识
					// let testAuthBtnList: Array<string> = ['btn.link'];
					// // 不同用户模拟不同的用户权限
					// if (username === 'admin') {
					// 	defaultRoles = adminRoles;
					// 	defaultAuthBtnList = adminAuthBtnList;
					// } else {
					// 	defaultRoles = testRoles;
					// 	defaultAuthBtnList = testAuthBtnList;
					// }
					// 用户信息模拟数据
					const { data } = await getInfo()
					
					const avatar = data.user.avatar === '' || data.user.avatar == null ? profileImage  : import.meta.env.VITE_APP_BASE_API + data.user.avatar;
					const userInfos = {
						username: data.user.userName,
						photo: avatar,
						time: new Date().getTime(),
						roles: data.roles,
						permissions:data.permissions,
						authBtnList: data.permissions,
						userId:data.user.userId
					};
					Session.set('userInfo', userInfos);
					resolve(userInfos);
				}, 0);
			});
		},
	},
});
