<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="名称" prop="modelName">
                        <el-input v-model="state.tableData.param.modelName" clearable size="default"
                            placeholder="请输入物模型名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="类别" prop="type">
                        <el-select v-model="state.tableData.param.type" placeholder="请选择模型类别" clearable
                            style="width: 240px">
                            <el-option v-for="dict in typelist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:template:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="名称" align="center" prop="modelName" width="100" />
                <el-table-column label="标识符" align="center" prop="identifier" />
                <!-- <el-table-column label="图表展示" align="center" prop="isMonitor" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isChart" />
                    </template>
                </el-table-column>
                <el-table-column label="实时监测" align="center" prop="" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isMonitor" />
                    </template>
                </el-table-column>
                <el-table-column label="只读" align="center" prop="" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isReadonly" />
                    </template>
                </el-table-column>
                <el-table-column label="历史存储" align="center" prop="" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isHistory" />
                    </template>
                </el-table-column>
                <el-table-column label="系统定义" align="center" prop="isSys" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isSys" />
                    </template>
                </el-table-column> -->
                <el-table-column label="物模型类别" align="center" prop="type" width="150">
                    <template #default="scope">
                        <DictTag :options="typelist" :value="scope.row.type" />
                        <!-- {{ scope.row.type }} -->
                    </template>
                </el-table-column>
                <el-table-column label="数据类型" align="center" prop="datatype" width="100">
                    <template #default="scope">
                        <DictTag :options="datatypelist" :value="scope.row.datatype" />
                    </template>
                </el-table-column>
                <el-table-column label="数据定义" align="left" header-align="center" prop="specs" min-width="150"
                    class-name="specsColor">
                    <template #default="scope">
                        <div v-html="formatSpecsDisplay(scope.row.specs)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="排序" align="center" prop="modelOrder" width="80" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" type="primary" @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:template:edit']"><el-icon size="small"><ele-View /></el-icon>查看</el-button>
                        <el-button size="default" type="danger" @click="onRowDel(scope.row)"
                            v-auths="['iot:template:remove']"><el-icon
                                size="large"><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <TemplateDialog ref="TemplateDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { parseTime } from '/@/utils/next'
import DictTag from '/@/components/DictTag/index.vue'
import { delTemplate, listTemplate } from '/@/api/iot/template';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const TemplateDialog = defineAsyncComponent(() => import('/@/views/iot/template/dialog.vue'));

// 定义变量内容
const TemplateDialogRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            modelName: undefined,//分类名称
            type: undefined
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //modelId
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const typelist = ref<TypeOption[]>([]);
const statuslist = ref<TypeOption[]>([]);
const datatypelist = ref<TypeOption[]>([]);
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listTemplate(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        modelName: undefined,
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        typelist.value = await dictStore.fetchDict('iot_things_type')
        statuslist.value = await dictStore.fetchDict('iot_yes_no')
        datatypelist.value = await dictStore.fetchDict('iot_data_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/** 格式化显示数据定义 */
const formatSpecsDisplay = (json: string | null | undefined) => {
    if (json == null || json == undefined) {
        return;
    }
    let specs = JSON.parse(json);
    if (specs.type === 'integer' || specs.type === 'decimal') {
        return (
            '<span style=\'width:50%;display:inline-block;\'>最大值：<span style="color:#F56C6C">' +
            specs.max +
            '</span></span>最小值：<span style="color:#F56C6C">' +
            specs.min +
            '</span><br /><span style=\'width:50%;display:inline-block;\'>步长：<span style="color:#F56C6C">' +
            specs.step +
            '</span></span>单位：<span style="color:#F56C6C">' +
            specs.unit
        );
    } else if (specs.type === 'string') {
        return '最大长度：<span style="color:#F56C6C">' + specs.maxLength + '</span>';
    } else if (specs.type === 'array') {
        return '<span style=\'width:50%;display:inline-block;\'>数组类型：<span style="color:#F56C6C">' + specs.arrayType + '</span></span>元素个数：<span style="color:#F56C6C">' + specs.arrayCount;
    } else if (specs.type === 'enum') {
        let items = '';
        for (let i = 0; i < specs.enumList.length; i++) {
            items = items + "<span style='width:50%;display:inline-block;'>" + specs.enumList[i].value + "：<span style='color:#F56C6C'>" + specs.enumList[i].text + '</span></span>';
            if (i > 0 && i % 2 != 0) {
                items = items + '<br />';
            }
        }
        return items;
    } else if (specs.type === 'bool') {
        return '<span style=\'width:50%;display:inline-block;\'>0：<span style="color:#F56C6C">' + specs.falseText + '</span></span>1：<span style="color:#F56C6C">' + specs.trueText;
    } else if (specs.type === 'object') {
        let items = '';
        for (let i = 0; i < specs.params.length; i++) {
            items = items + "<span style='width:50%;display:inline-block;'>" + specs.params[i].name + "：<span style='color:#F56C6C'>" + specs.params[i].datatype.type + '</span></span>';
            if (i > 0 && i % 2 != 0) {
                items = items + '<br />';
            }
        }
        return items;
    }
}
// 打开新增通用物模型弹窗
const onOpenAddDic = (type: string) => {
    TemplateDialogRef.value.openDialog(type);
};
// 打开修改通用物模型弹窗
const onOpenEditDic = (type: string, row: any | undefined) => {
    var modelId = ''
    if (!row) {
        modelId = ids.value
    } else {
        modelId = row.modelId
    }
    TemplateDialogRef.value.openDialog(type, row, modelId);
};
// 删除通用物模型
const onRowDel = (row: any) => {
    const modelIds = row.modelId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除通用物模型编号为：“${modelIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delTemplate(modelIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
