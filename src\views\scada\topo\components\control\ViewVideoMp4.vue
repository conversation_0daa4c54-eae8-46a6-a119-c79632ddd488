<template>
    <div>
        <video
            v-if="!showPlaceholder"
            :id="detail.identifier"
            controls
            autoplay
            muted
            :width="detail.style.position.w"
            :height="detail.style.position.h"
            style="background-color: black"
        ></video>
        <div
            v-else
            class="video-placeholder"
            :style="{
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
            }"
        >
            <div class="video-placeholder-content">
                <div class="video-icon">
                    <el-icon size="48"><VideoPlay /></el-icon>
                </div>
                <h3>FLV 视频播放器</h3>
                <p>FLV 视频组件需要安装 flv.js 依赖包</p>
                <p>请运行: npm install flv.js</p>
                <div class="video-info" v-if="detail.videoUrl">
                    <p><strong>视频地址:</strong> {{ detail.videoUrl }}</p>
                    <p><strong>视频格式:</strong> FLV 直播流</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { VideoPlay } from '@element-plus/icons-vue'

// Props
interface Props {
  detail?: any
}

const props = withDefaults(defineProps<Props>(), {
  detail: () => ({
    identifier: 'video-player-' + Date.now(),
    style: {
      position: {
        w: 640,
        h: 480
      }
    },
    videoUrl: ''
  })
})

// 使用默认配置
const detail = computed(() => props.detail || {
  identifier: 'video-player-' + Date.now(),
  style: {
    position: {
      w: 640,
      h: 480
    }
  },
  videoUrl: ''
})

// 显示占位符（因为 flv.js 未安装）
const showPlaceholder = ref(true)

// 生命周期
onMounted(() => {
  // 检查是否安装了 flv.js
  try {
    // 如果安装了 flv.js，这里可以初始化播放器
    // const flvjs = require('flv.js')
    // if (flvjs.isSupported()) {
    //   showPlaceholder.value = false
    //   initFlvPlayer()
    // }
    console.log('FLV 播放器组件已加载，但需要安装 flv.js 依赖')
  } catch (error) {
    console.log('flv.js 未安装，显示占位符')
  }
})

onBeforeUnmount(() => {
  // 清理播放器资源
})
</script>

<style lang="scss" scoped>
.view-text {
    height: 100%;
    width: 100%;
}

.video-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.video-placeholder-content {
    padding: 20px;
    z-index: 2;
}

.video-icon {
    margin-bottom: 15px;
    opacity: 0.9;
}

.video-placeholder-content h3 {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
}

.video-placeholder-content p {
    margin: 8px 0;
    font-size: 14px;
    opacity: 0.9;
}

.video-info {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.video-info p {
    margin: 5px 0;
    font-size: 12px;
    text-align: left;
}

.demo {
    padding: 5px;
    background-color: rgba(0, 206, 209, 0);
}
</style>
