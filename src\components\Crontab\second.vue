<template>
	<el-form size="small">
		<el-form-item>
			<el-radio v-model="radioValue" :label="1">
				秒，允许的通配符[, - * /]
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="2">
				周期从
				<el-input-number v-model="cycle01" :min="0" :max="58" /> -
				<el-input-number v-model="cycle02" :min="cycle01 ? cycle01 + 1 : 1" :max="59" /> 秒
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="3">
				从
				<el-input-number v-model="average01" :min="0" :max="58" /> 秒开始，每
				<el-input-number v-model="average02" :min="1" :max="59 - average01 || 0" /> 秒执行一次
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="4">
				指定
				<el-select clearable v-model="checkboxList" placeholder="可多选" multiple style="width:100%">
					<el-option v-for="item in 60" :key="item" :value="item-1">{{ item - 1 }}</el-option>
				</el-select>
			</el-radio>
		</el-form-item>
	</el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';

// 定义props
const props = defineProps<{
  check: (value: any, min: any, max: any) => any;
  radioParent: any;
}>();

// 定义响应式数据
const radioValue = ref<any>(1);
const cycle01 = ref<any>(1);
const cycle02 = ref<any>(2);
const average01 = ref<any>(0);
const average02 = ref<any>(1);
const checkboxList = ref<any[]>([]);

// 定义emit事件
const emit = defineEmits<{
  (event: 'update', field: any, value: any, target: any): void;
}>();

// 计算属性
const cycleTotal = computed(() => {
  const cycle01Value = props.check(cycle01.value, 0, 58);
  const cycle02Value = props.check(cycle02.value, cycle01Value ? cycle01Value + 1 : 1, 59);
  console.log(cycle01Value,cycle02Value,'----------------------------');
  
  return `${cycle01Value}-${cycle02Value}` as any;
});

const averageTotal = computed(() => {
  const average01Value = props.check(average01.value, 0, 58);
  const average02Value = props.check(average02.value, 1, 59 - average01.value || 0);
  return `${average01Value}/${average02Value}` as any;
});

const checkboxString = computed(() => {
  const str = checkboxList.value.join();
  return str === '' ? '*' : str as any;
});

// 方法
const radioChange = () => {
  console.log(radioValue.value,'radioValue.value');
  console.log(cycleTotal.value,'cycleTotal.value');
  switch (radioValue.value) {
    case 1:
      emit('update', 'second', '*', 'second');
      break;
    case 2:
      emit('update', 'second', cycleTotal.value,' ');
      break;
    case 3:
      emit('update', 'second', averageTotal.value,' ');
      break;
    case 4:
      emit('update', 'second', checkboxString.value,' ');
      break;
  }
};

const cycleChange = () => {
  if (radioValue.value === 2) {
    emit('update', 'second', cycleTotal.value, 'second');
  }
};

const averageChange = () => {
  if (radioValue.value === 3) {
    emit('update', 'second', averageTotal.value, 'second');
  }
};

const checkboxChange = () => {
  if (radioValue.value === 4) {
    emit('update', 'second', checkboxString.value, 'second');
  }
};

// 观察响应式数据
watch(radioValue, radioChange);
watch(cycleTotal, cycleChange);
watch(averageTotal, averageChange);
watch(checkboxString, checkboxChange);

// 监听props的变化
watch(() => props.radioParent, (newVal) => {
  radioValue.value = newVal;
});
onMounted(() => {
    console.log(props.radioParent,'props.radioParent');
    
});
defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList
});
</script>
