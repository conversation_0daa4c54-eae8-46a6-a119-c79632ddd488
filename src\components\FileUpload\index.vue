<template>
    <div class="upload-file">
        <el-upload multiple :action="uploadFileUrl" :before-upload="handleBeforeUpload" :file-list="fileList"
            :limit="props.limit" :on-error="handleUploadError" :on-exceed="handleExceed"
            :on-success="handleUploadSuccess" :show-file-list="false" :headers="headers" class="upload-file-uploader"
            ref="fileUpload">
            <div>
                <!-- 上传按钮 -->
                <el-button size="default" type="primary">选取文件</el-button>

                <!-- 上传提示 -->
                <div class="el-upload__tip" slot="tip" v-if="showTip">
                    请上传
                    <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
                    <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
                    的文件
                </div>
            </div>
        </el-upload>

        <!-- 文件列表 -->
        <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear"
            tag="ul">
            <li :key="file.url" class="el-upload-list__item ele-upload-list__item-content"
                v-for="(file, index) in fileList">
                <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
                    <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
                </el-link>
                <div class="ele-upload-list__item-content-action">
                    <el-link :underline="false" @click="handleDelete(index)" type="danger">删除</el-link>
                </div>
            </li>
        </transition-group>
    </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { Session } from '/@/utils/storage';
import { ElLoading, ElMessage } from 'element-plus';

// 定义传入的 props
const props = defineProps({
    modelValue: [String, Object, Array],
    limit: {
        type: Number,
        default: 5,
    },
    fileSize: {
        type: Number,
        default: 5,
    },
    fileType: {
        type: Array,
        default: () => ["doc", "xls", "ppt", "txt", "pdf"],
    },
    isShowTip: {
        type: Boolean,
        default: true
    },
    uploadFileUrl: {
        type: String,
        default: import.meta.env.VITE_APP_BASE_API + "/iot/tool/upload",
    }
});

// 定义 emit 用于触发事件
const emit = defineEmits(['update:modelValue']);

// 响应式变量
const fileUpload = ref()
const fileList = ref<any[]>([]);
const uploadList = ref<any[]>([]);
const number = ref(0);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
// 类型断言：将 loadingInstance 的类型定义为 ElLoading.Service 或 null
const loadingInstance = ref<any | null>(null);
// 计算属性：动态生成 headers
const headers = computed(() => ({
    Authorization: `Bearer ${Session.get('token')}`,
}));

// 计算属性：是否显示上传提示
const showTip = computed(() => {
    return props.isShowTip && (props.fileType || props.fileSize);
});

// 监听 value 的变化
watch(() => props.modelValue, (val) => {
    console.log(val, 'val');
    if (val) {
        console.log(val, 'val');

        let temp = 1;
        const list = Array.isArray(val) ? val : (val as string).split(',');
        fileList.value = list.map(item => {
            if (typeof item === "string") {
                item = { name: item, url: item };
            }
            item.uid = item.uid || new Date().getTime() + temp++;
            return item;
        });
    } else {
        fileList.value = [];
    }
}, { immediate: true });

// 上传前校验格式和大小
const handleBeforeUpload = (file: File) => {
    if (props.fileType) {
        const fileName = file.name.split('.');
        const fileExt = fileName[fileName.length - 1];
        const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
        if (!isTypeOk) {
            ElMessage.error(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
            return false;
        }
    }
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    loadingInstance.value = ElLoading.service({
        text: '正在上传图片，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)', // 自定义背景色
    });
    number.value++;
    return true;
};

// 文件个数超出
const handleExceed = () => {
    ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
};

// 上传失败
const handleUploadError = (err: any) => {
    ElMessage.error("上传文件失败，请重试");
    loadingInstance.value.close()
};

// 上传成功回调
const handleUploadSuccess = (res: any, file: File) => {
    if (res.code === 200) {
        uploadList.value.push({ name: res.fileName, url: res.fileName });
        uploadedSuccessfully();
    } else {
        number.value--;
        loadingInstance.value.close()
        ElMessage.error(res.msg);
        fileUpload.value.handleRemove(file);
        uploadedSuccessfully();
    }
};

// 删除文件
const handleDelete = (index: number) => {
    fileList.value.splice(index, 1);
    emit('update:modelValue', listToString(fileList.value));
};

// 上传结束处理
const uploadedSuccessfully = () => {
    if (number.value > 0 && uploadList.value.length === number.value) {
        fileList.value = fileList.value.concat(uploadList.value);        
        uploadList.value = [];
        number.value = 0;
        emit('update:modelValue', listToString(fileList.value));
        loadingInstance.value.close()
    }
};

// 获取文件名称
const getFileName = (name: string) => {
    const lastIndex = name.lastIndexOf("/");
    return lastIndex > -1 ? name.slice(lastIndex + 1) : "";
};

// 对象转成指定字符串分隔
const listToString = (list: any[], separator = ",") => {
    return list.map(item => item.url).join(separator);
};
</script>

<style scoped lang="scss">
.upload-file-uploader {
    margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
    border: 1px solid #e4e7ed;
    line-height: 2;
    margin-bottom: 10px;
    position: relative;
}

.upload-file-list .ele-upload-list__item-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
}

.ele-upload-list__item-content-action .el-link {
    margin-right: 10px;
}
</style>