<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-tabs  ref="productAlert" v-model="activeName" tab-position="left" style="padding:10px;min-height:400px;"
                @tab-click="tabChange">
                <el-tab-pane name="basic">
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label"><span style="color:red;">* </span>基本信息</span>
                        </span>
                    </template>
                    <el-form ref="DialogFormRef" :model="form" :rules="rules" label-width="100px">
                        <el-row :gutter="100">
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="8">
                                <el-form-item label="产品名称" prop="productName">
                                    <el-input v-model="form.productName" placeholder="请输入产品名称"
                                        :readonly="form.status == 2" />
                                </el-form-item>
                                <el-form-item label="产品分类" prop="categoryId">
                                    <el-select v-model="form.categoryId" placeholder="请选择分类" @change="selectCategory"
                                        style="width:100%" :disabled="form.status == 2">
                                        <el-option v-for="category in categoryShortList" :key="category.id"
                                            :label="category.name" :value="category.id"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item v-if="form.deviceType !== 3" label="通讯协议" prop="protocolCode">
                                    <el-select v-model="form.protocolCode" placeholder="请选择协议" style="width: 100%"
                                        :disabled="form.status == 2 || form.productId != 0" @change="changeProductCode($event)">
                                        <el-option v-for="protocol in protocolList" :key="protocol.protocolCode"
                                            :label="protocol.protocolName" :value="protocol.protocolCode" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item v-if="form.deviceType !== 3 && tempOpen">
                                    <el-alert style="padding: 28px;" title="当前通讯协议为modbus协议,请选择采集点模板,默认添加设备为网关设备"
                                        type="success" />
                                    <!-- <el-alert type="success" description="">
                                    </el-alert> -->
                                </el-form-item>
                                <el-form-item prop="templateId" v-if="tempOpen">
                                    <template #label>
                                        <span slot="label"><span style="color:#f56c6c">* </span>采集点模板</span>
                                    </template>
                                    <div v-if="selectRowData" style="display: inline-block ; padding-right: 5px">
                                        <span
                                            style="font-size: 9px;padding-right: 5px;color: #00bb00;font-size: 12px;font-weight: bold">{{
                                                selectRowData.templateName }}</span>
                                        <el-button plain size="small"
                                            :disabled="form.status == 2 || form.productId != 0"
                                            @click="deleteData">删除</el-button>
                                    </div>
                                    <el-button type="primary" size="small" plain
                                        :disabled="form.status == 2 || form.productId != 0"
                                        @click="selectTemplate">选择模板</el-button>
                                </el-form-item>

                                <el-form-item label="设备类型" prop="deviceType">
                                    <el-select v-model="form.deviceType" placeholder="请选择设备类型"
                                        :disabled="form.status == 2" style="width:100%">
                                        <el-option v-for="dict in device_type_list" :key="dict.dictValue"
                                            :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="传输协议" prop="transport">
                                    <el-select v-model="form.transport" placeholder="请选择传输协议" style="width: 100%"
                                        :disabled="form.status == 2">
                                        <el-option v-for="dict in transport_type_list" :key="dict.dictValue"
                                            :label="dict.dictLabel" :value="dict.dictValue" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="联网方式" prop="networkMethod">
                                    <el-select v-model="form.networkMethod" placeholder="请选择联网方式" style="width:100%;"
                                        :disabled="form.status == 2">
                                        <el-option v-for="dict in network_method_list" :key="dict.dictValue"
                                            :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                                    </el-select>
                                </el-form-item>

                                <el-form-item label="备注信息" prop="remark">
                                    <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" :rows="3"
                                        :readonly="form.status == 2" />
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="8">
                                <el-form-item label="启用授权" prop="networkMethod">
                                    <el-switch v-model="form.isAuthorize" @change="changeIsAuthorize()"
                                        :active-value="1" :inactive-value="0"
                                        :disabled="form.status == 2 || form.deviceType == 3" />
                                </el-form-item>
                                <el-form-item label="认证方式" prop="vertificateMethod">
                                    <el-select v-model="form.vertificateMethod" placeholder="请选择认证方式" style="width:100%"
                                        :disabled="form.status == 2 || form.deviceType == 3">
                                        <el-option v-for="dict in vertificate_method_list" :key="dict.dictValue"
                                            :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="定位方式" prop="locationWay">
                                    <el-select v-model="form.locationWay" placeholder="请选择设备状态" clearable
                                        :disabled="form.status == 2 || form.deviceType == 3" style="width: 100%">
                                        <el-option v-for="dict in location_way_list" :key="dict.dictValue"
                                            :label="dict.dictLabel" :value="Number(dict.dictValue)" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="产品编号" prop="productId">
                                    <el-input v-model="form.productId" placeholder="自动生成"
                                        :disabled="!form.mqttAccount || form.deviceType == 3" readonly />
                                </el-form-item>
                                <el-form-item label="Mqtt账号" prop="mqttAccount">
                                    <el-input v-model="form.mqttAccount" placeholder="不填自动生成"
                                        :disabled="form.deviceType == 3"
                                        :readonly="inputtype.accountInputType == 'password'"
                                        :type="inputtype.accountInputType">
                                        <template #append>
                                            <el-button slot="append" @click="changeInputType('account')">
                                                <el-icon :size="30"><ele-View /></el-icon>
                                            </el-button>
                                        </template>
                                        <!-- <el-button slot="append" icon="el-icon-view" style="font-size:18px;"
                                            @click="changeInputType('account')"></el-button> -->
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="Mqtt密码" prop="mqttPassword">
                                    <el-input v-model="form.mqttPassword" placeholder="不填则自动生成"
                                        :disabled="form.deviceType == 3"
                                        :readonly="inputtype.passwordInputType == 'password'"
                                        :type="inputtype.passwordInputType">
                                        <template #append>
                                            <el-button slot="append" @click="changeInputType('password')">
                                                <el-icon :size="30"><ele-View /></el-icon>
                                            </el-button>
                                        </template>
                                        <!-- <el-button slot="append" icon="el-icon-view" style="font-size:18px;"
                                            @click="changeInputType('password')"></el-button> -->
                                    </el-input>
                                </el-form-item>
                                <el-form-item label="产品秘钥" prop="mqttSecret">
                                    <el-input v-model="form.mqttSecret" placeholder="自动生成"
                                        :disabled="!form.mqttAccount || form.deviceType == 3" readonly
                                        :type="inputtype.keyInputType">
                                        <template #append>
                                            <el-button slot="append" @click="changeInputType('key')">
                                                <el-icon :size="30"><ele-View /></el-icon>
                                            </el-button>
                                        </template>
                                        <!-- <el-button slot="append" icon="el-icon-view" style="font-size:18px;"
                                            @click="changeInputType('key')"></el-button> -->
                                    </el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :xs="24" :sm="24" :md="24" :lg="12" :xl="8">
                                <el-form-item label="产品图片">
                                    <div v-if="form.status == 2 && form.imgUrl == null">
                                        <el-image style="height:145px;height:145px;border-radius:10px;"
                                            :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                            v-if="form.deviceType == 2"></el-image>
                                        <el-image style="height:145px;height:145px;border-radius:10px;"
                                            :preview-src-list="[video]" :src="video" fit="cover"
                                            v-else-if="form.deviceType == 3"></el-image>
                                        <el-image style="height:145px;height:145px;border-radius:10px;"
                                            :preview-src-list="[product]" :src="product" fit="cover" v-else></el-image>
                                    </div>
                                    <div v-else>
                                        <imageUpload ref="image-upload" v-model:model-value="form.imgUrl"
                                            :limit="form.status == 2 ? 0 : 1" :fileSize="1" @input="getImagePath($event)"></imageUpload>
                                        <div class="el-upload__tip" style="color:#f56c6c"
                                            v-if="form.productId == null || form.productId == 0">
                                            提示：上传后需要提交保存</div>
                                    </div>

                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-col :span="20">
                            <el-form-item style="text-align: center;margin:40px 0px;">
                                <el-button type="primary" @click="submitForm(DialogFormRef)"
                                    v-auths="['iot:product:edit']" v-if="form.productId != 0 && form.status != 2">修
                                    改</el-button>
                                <el-button type="primary" @click="submitForm(DialogFormRef)"
                                    v-auths="['iot:product:add']" v-if="form.productId == 0 && form.status != 2">新
                                    增</el-button>
                            </el-form-item>
                        </el-col>
                    </el-form>
                </el-tab-pane>
                <el-tab-pane label="" name="things" :disabled="form.productId == 0">
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label"><span style="color:red;" v-auths="['iot:model:list']">*
                                </span>产品模型</span>
                        </span>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="" name="productFirmware" :disabled="form.productId == 0"
                    v-if="form.deviceType !== 3">
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label" v-auths="['iot:firmware:list']">固件管理</span>
                            
                        </span>
                    </template>
                    <product-firmware v-model="form"  />
                </el-tab-pane>

                <el-tab-pane label="" name="productAuthorize" :disabled="form.productId == 0"
                    v-if="form.deviceType !== 3">
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label" v-auths="['iot:authorize:query']">设备授权</span>
                            
                        </span>
                    </template>
                    <product-authorize v-model="form"  />
                </el-tab-pane>
                <el-tab-pane label="" name="sipConfig" :disabled="form.productId == 0" v-if="form.deviceType === 3">
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label">SIP配置</span>
                        </span>
                    </template>
                    <config-sip  v-model="form"  />
                </el-tab-pane>


                <!-- 用于设置间距 -->

                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane :disabled="true"></el-tab-pane>
                <el-tab-pane v-if="form.status == 1" name="product04" disabled>
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label">
                                <el-button type="success" size="small" @click="ChangeProductStatus(2)"
                                    v-auths="['iot:product:add']">发布产品</el-button>
                            </span>
                        </span>
                    </template>
                </el-tab-pane>
                <el-tab-pane v-if="form.status == 2" name="product05" disabled>
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label">
                                <el-button type="danger" size="small" @click="ChangeProductStatus(1)"
                                    v-auths="['iot:product:edit']">取消发布</el-button>
                            </span>
                        </span>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="product06" disabled>
                    <template #label>
                        <span class="custom-tabs-label">
                            <span slot="label">
                                <el-button type="info" size="small" @click="goBack()">返回列表</el-button>
                            </span>
                        </span>
                    </template>
                </el-tab-pane>
            </el-tabs>
            <!--添加从机对话框-->
<!--            <el-dialog :title="state.tableData.dialog.title" v-model="state.tableData.dialog.isShowDialog"-->
<!--                width="1000px" style="position: absolute; top: 100px;" append-to-body>-->
<!--                <el-row :gutter="30">-->
<!--                    <el-col :span="11">-->
<!--                        <el-form ref="tempRef" :inline="true">-->
<!--                            <el-form-item size="small">-->
<!--                                <el-input v-model="tempParams.templateName" placeholder="模板名称">-->
<!--                                </el-input>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item size="small">-->
<!--                                <el-button type="primary" size="small"-->
<!--                                    @click="queryTemp"><el-icon><ele-Search /></el-icon>搜索</el-button>-->
<!--                                <el-button size="small"-->
<!--                                    @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>-->
<!--                            </el-form-item>-->
<!--                        </el-form>-->
<!--                        <el-table v-loading="state.tableData.loading" :data="state.tableData.data" highlight-current-row-->
<!--                            ref="multipleTable" style="width: 100%">-->
<!--                            <el-table-column label="选择采集点模板" align="left">-->
<!--                                <template #default="scope">-->
<!--                                    <el-radio v-model="currentRow" :value="scope.row.templateId"-->
<!--                                        @change.native="getCurrentRow(scope.row.templateId, scope.row)">{{-->
<!--                                            scope.row.templateName-->
<!--                                        }}</el-radio>-->
<!--                                </template>-->
<!--                            </el-table-column>-->
<!--                        </el-table>-->
<!--                        <el-pagination v-show="state.tableData.tempTotal > 0" :total="state.tableData.tempTotal"-->
<!--                            class="mt15" style="justify-content: flex-end;" size="small"-->
<!--                            layout="total, prev, pager, next" v-model:current-page="tempParams.pageNum"-->
<!--                            v-model:page-size="tempParams.pageSize" @size-change="ontempHandleSizeChange" background-->
<!--                            @current-change="ontempHandleCurrentChange" />-->
<!--                    </el-col>-->
<!--                    <el-col :span="13">-->
<!--                        <el-form :inline="true" :model="pointsParams">-->
<!--                            <el-form-item size="small">-->
<!--                                <span slot="label" style="font-size:16px;font-weight:400;">物模型列表</span>-->
<!--                            </el-form-item>-->
<!--                            <el-form-item size="small">-->
<!--                                <span slot="label" style="font-weight:400;font-size:12px;">从机数量:</span>-->
<!--                                {{ selectRowData.slaveTotal }}-->
<!--                            </el-form-item>-->
<!--                            <el-form-item size="small">-->
<!--                                <span slot="label" style="font-weight:400;font-size:12px;">变量数量:</span>-->
<!--                                {{ selectRowData.pointTotal }}-->
<!--                            </el-form-item>-->
<!--                            <el-form-item size="small">-->
<!--                                <span slot="label" style="font-weight:400;font-size:12px;">采集方式:</span>-->
<!--                                <dict-tag :options="collect_type_list" :value="selectRowData.pollingMethod" size="small"-->
<!--                                    style="display: inline-block" />-->
<!--                            </el-form-item>-->
<!--                        </el-form>-->
<!--                        <el-table v-loading="loading" :data="pointList" :border="false" size="small">-->
<!--                            <el-table-column prop="templateName" label="物模型名称">-->
<!--                            </el-table-column>-->
<!--                            <el-table-column prop="regAddr" label="寄存器">-->
<!--                            </el-table-column>-->
<!--                            <el-table-column prop="datatype" label="数值类型">-->
<!--                            </el-table-column>-->
<!--                        </el-table>-->
<!--                        <el-pagination v-show="total > 0" :total="total" size="small" layout="total, prev, pager, next"-->
<!--                            class="mt15" style="justify-content: flex-end;" v-model:current-page="pointsParams.pageNum"-->
<!--                            v-model:page-size="pointsParams.pageSize" @size-change="onHandleSizeChange" background-->
<!--                            @current-change="onHandleCurrentChange" />-->
<!--                    </el-col>-->
<!--                </el-row>-->

<!--                <template #footer>-->
<!--                    <span class="dialog-footer">-->
<!--                        <el-button @click="cancel">取 消</el-button>-->
<!--                        <el-button type="primary" @click="submitSelect">确 定</el-button>-->
<!--                    </span>-->
<!--                </template>-->
<!--            </el-dialog>-->
        </el-card>

    </div>
</template>
<script setup lang="ts" name="">

import { onMounted, reactive, ref } from 'vue';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listShortCategory } from '/@/api/iot/category';
import { listProtocol } from '/@/api/iot/protocol';
import { getTempByPId, listTemp } from '/@/api/iot/temp';
import imageUpload from '/@/components/ImageUpload/index.vue'
import productFirmware from "/@/views/iot/product/product-firmware.vue";
import productAuthorize from "/@/views/iot/product/product-authorize.vue"
// import configSip from "/@/views/iot/sip/sipconfig.vue"
import { useRoute } from 'vue-router';
import { addProduct, changeProductStatus, deviceCount, getProduct, updateProduct } from '/@/api/iot/product';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import product from '/@/assets/images/product.png';
// import { getAllPoints } from '/@/api/iot/template';
import router from '/@/router';
const dictStore = useDictStore();  // 使用 Pinia store
const route = useRoute();
interface categoryShortOption {
    protocolCode: number;
    protocolName: string;
}
interface categoryShortOption {
    id: number;
    name: string;
}
interface productOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface RowData {
    templateId: string;
    // 其他字段...
}
// 定义变量内容
const DialogFormRef = ref();
// 使用 ref 引用子组件实例
const productAlert = ref(null);
let activeName = ref('basic')
let form = ref({
    productId: 0 as any,
    networkMethod: 1,
    deviceType: 1,
    vertificateMethod: 3,
    transport: 'MQTT',
    imgUrl: "",
    locationWay: 1,
    status: '' as any,
    productName: '',
    categoryId: '',
    protocolCode: '',
    mqttPassword: '',
    remark: '',
    isModbus: false,
    templateId: '' as any,
    categoryName: '',
    isAuthorize: 0 as any,
    mqttAccount: '',
    mqttSecret: '',
    tslJson: ''
})

const state = reactive({
    tableData: {
        data: [] as RowData[],
        tempTotal: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: '',
            platform: ''
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
});
let currentRow = ref()
let pointList = reactive([])
const total = ref(0)
const loading = ref(false)
// 查询参数
const pointsParams = reactive({
    pageNum: 1,
    pageSize: 8,
    templateId: 0,
})
const tempParams = reactive({
    pageNum: 1,
    pageSize: 10,
    templateName: ''
})
// 输入框类型
const inputtype = reactive({
    keyInputType: "password",
    accountInputType: "password",
    passwordInputType: "password",
})

let categoryShortList = ref<categoryShortOption[]>([]); //产品分类

let protocolList = ref<categoryShortOption[]>([]); //通讯协议列表

let selectRowData = ref({
    templateName: '',
    slaveTotal: '',
    pointTotal: '',
    templateId: '',
    pollingMethod: ''
})


const device_type_list = ref<productOption[]>([]); //设备类型
const transport_type_list = ref<productOption[]>([]);//传输协议
const network_method_list = ref<productOption[]>([]);//联网方式
const vertificate_method_list = ref<productOption[]>([]);//认证方式
const location_way_list = ref<productOption[]>([]);//定位方式
const collect_type_list = ref<productOption[]>([]);//采集方式
const tempOpen = ref(false)
// 校验规则
const rules = reactive({
    productName: [{
        required: true,
        message: "产品名称不能为空",
        trigger: "blur"
    }],
    categoryId: [{
        required: true,
        message: "产品分类ID不能为空",
        trigger: "blur"
    }],
    deviceType: [{
        required: true,
        message: "请选择设备类型",
        trigger: "blur"
    }],
    protocolCode: [{
        required: true,
        message: "设备协议不能为空",
        trigger: "blur"
    }],
    transport: [{
        required: true,
        message: "传输协议不能为空",
        trigger: 'blur'
    }]

})
/**选项卡切换事件**/
// 选项卡切换事件
const tabChange = (tabItem: { paneName: string }) => {
    // 检查当前选中的选项卡名称
    if (tabItem.paneName === "things" && form.value.productId !== 0 && productAlert.value) {
        // 切换到 "things" 选项卡时，获取物模型
        // productAlert.value.getCacheThingsModel(form.value.productId);
    }
    // 更多的选项卡切换处理逻辑...
};
// 获取简短分类列表
const getShortCategory = () => {
    listShortCategory().then(response => {
        categoryShortList.value = response.data.data;
    })
}
//获取设备协议
const getProtocol = () => {
    const data = {
        protocolStatus: 1,
        pageSize: 99,
    };
    listProtocol(data).then(res => {
        protocolList = res.data.rows;
    })
}        /** 选择分类 */
const selectCategory = (val: any) => {
    for (var i = 0; i < categoryShortList.value.length; i++) {
        if (categoryShortList.value[i].id == val) {
            form.value.categoryName = categoryShortList.value[i].name;
            return;
        }
    }
}
/*选择模板*/
const selectTemplate = () => {
    // this.reset();
    state.tableData.dialog.isShowDialog = true;
    state.tableData.dialog.title = "选择模板";
    getTempList();
}
/** 查询设备采集变量模板列表(弹框列表) */
const getTempList = () => {
    try {
        state.tableData.loading = true;
        loading.value = true
        listTemp(tempParams).then(response => {
            state.tableData.data = response.data.rows;
            state.tableData.tempTotal = response.data.total;
            currentRow.value = state.tableData.data[0].templateId;
            // this.pointsParams.templateId = this.currentRow.templateId;
            getCurrentRow(state.tableData.data[0].templateId, state.tableData.data[0]);
            getList();
        })
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
            loading.value = false
        }, 500);
    }

}
/*按照模板名查询*/
const queryTemp = () => {
    getTempList();
}

/** 搜索按钮操作 */
const handleQuery = () => {
    tempParams.pageNum = 1
    getTempList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    tempParams.pageNum = 1
    tempParams.pageSize = 10
    tempParams.templateName = ''
    handleQuery()
}
// 物模型列表
const getList = () => {
    try {
        loading.value = true;
        // getAllPoints(pointsParams).then(response => {
        //     pointList = response.data.rows;
        //     total.value = response.data.total;
        // });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            loading.value = false;
        }, 500);
    }
}
// 取消按钮
const cancel = () => {
    state.tableData.dialog.isShowDialog = false
}
/*确认选择*/
const submitSelect = () => {
    state.tableData.dialog.isShowDialog = false
    form.value.templateId = selectRowData.value.templateId;
}
// 授权码状态修改
const changeIsAuthorize = () => {
    let text = form.value.isAuthorize == "1" ? "启用" : "停用";
    ElMessageBox.confirm('确认要' + text + '授权码吗？').then(function () {

    }).then(() => {
        if (form.value.productId != null && form.value.productId != 0) {
            updateProduct(form.value).then(response => {
                ElMessageBox.confirm("授权码已" + text, '系统提示', {
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'success',
                })
                // ElMessage.success("授权码已" + text);
            });
        }

    }).catch(() => {
        form.value.isAuthorize = 0;
    });
}
// 获取状态数据
const getdictdata = async () => {
    try {
        device_type_list.value = await dictStore.fetchDict('iot_device_type')
        transport_type_list.value = await dictStore.fetchDict('iot_transport_type')
        network_method_list.value = await dictStore.fetchDict('iot_network_method')
        vertificate_method_list.value = await dictStore.fetchDict('iot_vertificate_method')
        location_way_list.value = await dictStore.fetchDict('iot_location_way')
        collect_type_list.value = await dictStore.fetchDict('data_collect_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/**获取上传图片的路径 */
const getImagePath = (data: string) => {
    form.value.imgUrl = data;
}
/**改变输入框类型**/
const changeInputType = (name: string) => {
    if (name == "key") {
        inputtype.keyInputType = inputtype.keyInputType == "password" ? "text" : "password";
    } else if (name == "account") {
        inputtype.accountInputType = inputtype.accountInputType == "password" ? "text" : "password";
    } else if (name == "password") {
        inputtype.passwordInputType = inputtype.passwordInputType == "password" ? "text" : "password";
    }
}
/** 获取产品信息 */
const getProductMsg = () => {
    getProduct(form.value.productId).then(response => {
        form.value = response.data.data;        
        changeProductCode(form.value.protocolCode);
    });
}
// 切换采集模板
const getCurrentRow = (val: any, data: any) => {
    if (val != null) {
        selectRowData.value = data;
        pointsParams.templateId = val;
    }
    getList();
}
const deleteData = () => {
    selectRowData.value = {
        templateName: '',
        slaveTotal: '',
        pointTotal: '',
        templateId: '',
        pollingMethod: ''
    }
    form.value.templateId = null;
}
// 选择通讯协议
const changeProductCode = (val: string) => {
    
    if (val && val.startsWith("MODBUS")) {
        tempOpen.value = true;
        form.value.deviceType = 2;
        form.value.isModbus = true;
        if (form.value.productId != 0 && form.value.productId != null) {
            getTempDetail()
        }
    } else {
        form.value.isModbus = false;
        tempOpen.value = false;
    }
}
const getTempDetail = () => {
    const params = {
        productId: form.value.productId
    };
    getTempByPId(params).then(response => {
        selectRowData.value = response.data.data;
    })
}
/** 提交按钮 */
const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (form.value.productId != null && form.value.productId != 0) {
                updateProduct(form.value).then(response => {
                    changeProductCode(form.value.protocolCode);
                    // ElMessage.success("修改成功");
                    ElMessageBox.confirm('修改成功', '系统提示', {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'success',
                        })
                });
            } else {
                if (tempOpen.value && !form.value.templateId) {
                    ElMessage.error("请选择采集点模板");
                    return;
                }
                addProduct(form.value).then(response => {
                    if (!form.value.isModbus) {
                        ElMessageBox.confirm('添加成功,可以开始定义物模型或配置', '系统提示', {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'success',
                        })
                        // ElMessage.success("添加成功,可以开始定义物模型或配置");
                    } else {
                        ElMessageBox.confirm('物模型已经从采集点模板同步至产品', '系统提示', {
                            confirmButtonText: '确认',
                            cancelButtonText: '取消',
                            type: 'success',
                        })
                        // ElMessage.success("物模型已经从采集点模板同步至产品")
                    }
                    form.value = response.data.data
                    console.log(form.value,'form.value');
                    
                    changeProductCode(form.value.protocolCode);
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
}
/**同步获取产品下的设备数量**/
const getDeviceCountByProductId = (productId: number) => {
    return new Promise((resolve, reject) => {
        deviceCount(productId).then(res => {
            resolve(res);
        }).catch(error => {
            reject(error);
        })
    })
}
/** 更新产品状态 */
const ChangeProductStatus = async (status: any) => {
    let message = "确定取消发布？";
    if (status == 2) {
        message = "产品发布后，可以创建对应的设备";
    } else if (status == 1) {
        let result = await getDeviceCountByProductId(form.value.productId) as any;
        if (result.data.data > 0) {
            message = "重要提示：产品下已有 " + result.data.data + " 个设备，取消发布可以修改产品信息和模型，重新发布后对应设备状态将会被重置！"
        }
    }
    ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
    })
        .then(() => {
            let data = {
                productId: '',
                status: '',
                deviceType: '' as any
            };
            data.productId = form.value.productId;
            data.status = status;
            data.deviceType = form.value.deviceType;
            changeProductStatus(data).then(response => {
                ElMessage.success(response.data.msg);
                activeName.value = "basic";
                getProductMsg();
            }).catch(() => {
                if (status == 2) {
                    activeName.value = "basic";
                } else {
                    goBack();
                }
            });
        })
        .catch(() => {
            activeName.value = "basic";
        });
}
/** 返回按钮 */
const goBack = () => {
    try {     
        const obj = {
            path: "/iot/product",
            query: {
                t: Date.now(),
                pageNum: route.query.tabPanelName
            }
        };
        router.push(obj);
        reset();
    } catch (error) {
        console.error('Error during event handler:', error);
    }

}
// 表单重置
const reset = () => {
    form.value = {
        productId: 0 as any,
        networkMethod: 1,
        deviceType: 1,
        vertificateMethod: 3,
        transport: 'MQTT',
        imgUrl: "",
        locationWay: 1,
        status: '' as any,
        productName: '',
        categoryId: '',
        protocolCode: '',
        mqttPassword: '',
        remark: '',
        isModbus: false,
        templateId: '' as any,
        categoryName: '',
        isAuthorize: '' as any,
        mqttAccount: '',
        mqttSecret: '',
        tslJson: ''
    };
}
// 分页改变
const ontempHandleSizeChange = (val: number) => {
    tempParams.pageSize = val;
    getTempList();
};
// 分页改变
const ontempHandleCurrentChange = (val: number) => {
    tempParams.pageNum = val;
    getTempList();
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getList()
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getList()
};
// 页面加载时
onMounted(() => {
    const productId = route.query.productId;        
    form.value.productId = productId;
    if (form.value.productId != 0 && form.value.productId != null) {
        getProductMsg()
    }
    // 切换选项卡
    const tabPanelName = route.query.tabPanelName;
    if (tabPanelName != null && tabPanelName != '') {
        activeName.value = tabPanelName as any;
    }
    // 获取分类信息
    getShortCategory();
    // 设置账号密码输入框类型,新增时为text，查看时为password
    if (!form.value.productId || form.value.productId == 0) {
        inputtype.accountInputType = "text";
        inputtype.passwordInputType = "text";
    }

    //获取设备协议
    getProtocol();
    getdictdata()
});

</script>
<style scoped>


:deep(.el-button--small)  {
    padding: 16px 16px;
    font-size: 14px;
}
</style>