import request from '/@/utils/request';

// 定义查询操作日志列表的请求参数类型
interface QueryParams {
  pageNum?: number;
  pageSize?: number;
  operName?: string;
  status?: string;
  startTime?: string;
  endTime?: string;
}

// 定义 API 响应的通用类型
interface ApiResponse<T> {
  rows: never[];
  total: number;
  code: number;
  msg: string;
  data: T;
}

// 定义操作日志项的类型
interface Operlog {
  operId: number;
  operName: string;
  title: string;
  method: string;
  requestMethod: string;
  operatorType: string;
  operUrl: string;
  operIp: string;
  operLocation: string;
  params: string;
  result: string;
  errorMsg: string;
  operTime: string;
}

// 查询操作日志列表
export function list(query: QueryParams) {
  return request<ApiResponse<Operlog[]>>({
    url: '/monitor/operlog/list',
    method: 'get',
    params: query,
  });
}

// 删除操作日志
export function delOperlog(operId: any) {
  return request<ApiResponse<null>>({
    url: `/monitor/operlog/${operId}`,
    method: 'delete',
  });
}

// 清空操作日志
export function cleanOperlog() {
  return request<ApiResponse<null>>({
    url: '/monitor/operlog/clean',
    method: 'delete',
  });
}
