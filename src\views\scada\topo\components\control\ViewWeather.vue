<template>
    <div>
        <div v-show="false">{{ dataInit }}</div>
        <!-- 完整模式 -->
        <div
            :style="{
                fontFamily: detail.style.fontFamily,
                color: detail.style.foreColor,
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
                fontWeight: '600',
                border: detail.style.waterBorderWidth + 'px solid',
                borderRadius: detail.style.borderRadius + 'px',
                borderColor: detail.style.waterBorderColor,
                padding: '10px 20px',
            }"
            v-if="detail.style.weatherModel == '完整模式'"
        >
            <div :style="{ fontSize: detail.style.position.h / 13 + 'px', padding: '10px' }">
                <i class="el-icon-place"></i>
                <span>{{ detail.weatherDetail.city }}</span>
            </div>
            <el-row :gutter="20">
                <el-col style="width: auto">
                    <div class="">
                        <img :src="getWeaImage(detail.weatherDetail.wea_img)" :style="{ width: detail.style.position.h / 2 + 'px', height: detail.style.position.h / 2 + 'px' }" />
                    </div>
                </el-col>
                <el-col style="width: auto">
                    <div :style="{ fontSize: detail.style.position.h / 5 + 'px' }">{{ detail.weatherDetail.tem }}°C</div>
                    <div :style="{ fontSize: detail.style.position.h / 13 + 'px' }">{{ detail.weatherDetail.wea }} {{ detail.weatherDetail.win }} {{ detail.weatherDetail.win_speed }}</div>
                </el-col>
            </el-row>
            <div :style="{ fontSize: detail.style.position.h / 13 + 'px' }">
                <span>湿度：{{ detail.weatherDetail.humidity }}</span>
                <span style="margin-left: 10px; margin-right: 10px">|</span>
                <span>气压：{{ detail.weatherDetail.pressure }}</span>
                <span style="margin-left: 10px; margin-right: 10px">|</span>
                <span>
                    空气质量：
                    <span style="color: #ffb527">{{ detail.weatherDetail.air_level }}</span>
                </span>
            </div>
        </div>
        <!-- 简约模式 -->
        <div
            :style="{
                fontFamily: detail.style.fontFamily,
                color: detail.style.foreColor,
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
                fontWeight: '600',
                border: detail.style.waterBorderWidth + 'px solid',
                borderRadius: detail.style.borderRadius + 'px',
                borderColor: detail.style.waterBorderColor,
            }"
            v-else
        >
            <div :style="{ fontSize: detail.style.position.h / 13 + 'px', padding: '10px' }">
                <i class="el-icon-place"></i>
                <span>{{ detail.weatherDetail.city }}</span>
            </div>
            <el-row :gutter="20">
                <el-col style="width: auto">
                    <div class="">
                        <img :src="getWeaImage(detail.weatherDetail.wea_img)" :style="{ width: detail.style.position.h / 2 + 'px', height: detail.style.position.h / 2 + 'px' }" />
                    </div>
                </el-col>
                <el-col style="width: auto">
                    <div :style="{ fontSize: detail.style.position.h / 5 + 'px' }">{{ detail.weatherDetail.tem }}°C</div>
                    <div :style="{ fontSize: detail.style.position.h / 10 + 'px' }">
                        {{ detail.weatherDetail.wea }}
                    </div>
                </el-col>
            </el-row>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import axios from 'axios'
import qing from '@/assets/topo-img/weather/qing.png'
import xue from '@/assets/topo-img/weather/xue.png'
import shachen from '@/assets/topo-img/weather/shachen.png'
import wu from '@/assets/topo-img/weather/wu.png'
import bingbao from '@/assets/topo-img/weather/bingbao.png'
import yun from '@/assets/topo-img/weather/yun.png'
import yu from '@/assets/topo-img/weather/yu.png'
import yin from '@/assets/topo-img/weather/yin.png'
import lei from '@/assets/topo-img/weather/lei.png'

// Props
interface Props {
  detail?: any
}
const props = defineProps<Props>()

// 使用默认的天气组件配置
const detail = ref(props.detail || {
  style: {
    fontFamily: 'Arial, sans-serif',
    foreColor: '#333',
    position: {
      w: 300,
      h: 200,
      x: 0,
      y: 0
    },
    waterBorderWidth: 1,
    borderRadius: 5,
    waterBorderColor: '#ddd',
    weatherModel: '完整模式'
  },
  componentShow: ['天气'],
  dataBind: {
    districtCode: '101010100' // 默认北京
  },
  weatherDetail: {
    city: '北京',
    wea_img: 'qing',
    tem: '20',
    wea: '晴',
    win: '北风',
    win_speed: '3级',
    humidity: '45%',
    pressure: '1013',
    air_level: '良'
  }
})

// 天气图片映射
const weatherImages = {
  qing,
  xue,
  shachen,
  wu,
  bingbao,
  yun,
  yu,
  yin,
  lei
}

// 定时器
let weatherTimer: NodeJS.Timeout | null = null

// 计算属性
const dataInit = computed(() => {
  if (detail.value.componentShow.indexOf('天气') > -1) {
    if (detail.value.dataBind.districtCode) {
      getWeatherTest()
    }
  }
  return true
})

// 获取天气数据
const getWeatherTest = () => {
  const query = {
    appid: '53464153',
    appsecret: '6VAV5iHz',
    version: 'v61',
    unescape: 1,
    cityid: detail.value.dataBind.districtCode,
  }

  const url = 'http://v1.yiketianqi.com/api' // 易客云

  axios({
    url: url,
    method: 'get',
    params: query,
  }).then((res) => {
    console.log('更新天气组件', res.data)
    detail.value.weatherDetail = res.data
  }).catch((error) => {
    console.error('获取天气数据失败:', error)
  })
}



// 获取天气图标
const getWeaImage = (val: string) => {
  return weatherImages[val as keyof typeof weatherImages] || weatherImages.qing
}

// 生命周期
onMounted(() => {
  getWeatherTest()
  if (detail.value.dataBind.districtCode) {
    weatherTimer = setInterval(() => {
      getWeatherTest()
    }, 60000 * 60) // 每小时更新一次
  }
})

onBeforeUnmount(() => {
  if (weatherTimer) {
    clearInterval(weatherTimer)
    weatherTimer = null
  }
})
</script>

<style lang="scss" scoped>
.weatherClass {
    border: 1px solid;
    border-radius: 10px;
    border-color: #000;
}
</style>
