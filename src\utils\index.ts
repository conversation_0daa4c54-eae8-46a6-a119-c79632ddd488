import { parseTime } from './next';

/*** 表格时间格式化 */
export function formatDate(cellValue: string | null | undefined): string {
    if (cellValue == null || cellValue === '') return '';
    const date = new Date(cellValue);
    const year = date.getFullYear();
    const month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    const hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    const minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    const seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * @param {number} time
 * @param {string} [option]
 * @returns {string}
 */
export function formatTime(time: number | string, option?: string): string {
    if (('' + time).length === 10) {
        time = parseInt(time as string) * 1000;
    } else {
        time = +time;
    }
    const d = new Date(time) as any;
    const now = Date.now();
    const diff = (now - d) / 1000;

    if (diff < 30) {
        return '刚刚';
    } else if (diff < 3600) {
        // less 1 hour
        return Math.ceil(diff / 60) + '分钟前';
    } else if (diff < 3600 * 24) {
        return Math.ceil(diff / 3600) + '小时前';
    } else if (diff < 3600 * 24 * 2) {
        return '1天前';
    }
    if (option) {
        return parseTime(time, option) as any;
    } else {
        return `${d.getMonth() + 1}月${d.getDate()}日${d.getHours()}时${d.getMinutes()}分`;
    }
}

/**
 * @param {string} [url]
 * @returns {Record<string, string>}
 */
export function getQueryObject(url?: string): Record<string, string> {
    url = url == null ? window.location.href : url;
    const search = url.substring(url.lastIndexOf('?') + 1);
    const obj: Record<string, string> = {};
    const reg = /([^?&=]+)=([^?&=]*)/g;
    search.replace(reg, (rs,$1,$2) => {
        const name = decodeURIComponent($1);
        let val = decodeURIComponent($2);
        val = String(val);
        obj[name] = val;
        return rs;
    });
    return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str: string): number {
    // returns the byte length of a utf8 string
    let s = str.length;
    for (let i = str.length - 1; i >= 0; i--) {
        const code = str.charCodeAt(i);
        if (code > 0x7f && code <= 0x7ff) s++;
        else if (code > 0x7ff && code <= 0xffff) s += 2;
        if (code >= 0xdc00 && code <= 0xdfff) i--;
    }
    return s;
}

/**
 * @param {Array<any>} actual
 * @returns {Array<any>}
 */
export function cleanArray(actual: any[]): any[] {
    const newArray: any[] = [];
    for (let i = 0; i < actual.length; i++) {
        if (actual[i]) {
            newArray.push(actual[i]);
        }
    }
    return newArray;
}

/**
 * @param {Record<string, any>} json
 * @returns {string}
 */
export function param(json: Record<string, any>): string {
    if (!json) return '';
    return cleanArray(
        Object.keys(json).map((key) => {
            if (json[key] === undefined) return '';
            return encodeURIComponent(key) + '=' + encodeURIComponent(json[key]);
        })
    ).join('&');
}

/**
 * @param {string} url
 * @returns {Record<string, string>}
 */
export function param2Obj(url: string): Record<string, string> {
    const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ');
    if (!search) {
        return {};
    }
    const obj: Record<string, string> = {};
    const searchArr = search.split('&');
    searchArr.forEach((v) => {
        const index = v.indexOf('=');
        if (index !== -1) {
            const name = v.substring(0, index);
            const val = v.substring(index + 1, v.length);
            obj[name] = val;
        }
    });
    return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val: string): string {
    const div = document.createElement('div');
    div.innerHTML = val;
    return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Record<string, any>} target
 * @param {(Record<string, any> | any[])} source
 * @returns {Record<string, any>}
 */
export function objectMerge(target: Record<string, any>, source: Record<string, any> | any[]): Record<string, any> {
    if (typeof target !== 'object') {
        target = {};
    }
    if (Array.isArray(source)) {
        return source.slice();
    }
    Object.keys(source).forEach((property) => {
        const sourceProperty = source[property];
        if (typeof sourceProperty === 'object') {
            target[property] = objectMerge(target[property], sourceProperty);
        } else {
            target[property] = sourceProperty;
        }
    });
    return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element: HTMLElement, className: string): void {
    if (!element || !className) {
        return;
    }
    let classString = element.className;
    const nameIndex = classString.indexOf(className);
    if (nameIndex === -1) {
        classString += '' + className;
    } else {
        classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length);
    }
    element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type: 'start' | 'end'): Date {
    if (type === 'start') {
        return new Date().getTime() - 3600 * 1000 * 24 * 90 as any;
    } else {
        return new Date(new Date().toDateString());
    }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {Function}
 */
export function debounce(func: Function, wait: number, immediate: boolean): Function {
    let timeout: NodeJS.Timeout | null = null;
    let args: any;
    let context: any;
    let timestamp: number;
    let result: any;
    const later = function () {
        const last = +new Date() - timestamp;
        if (last < wait && last > 0) {
            timeout = setTimeout(later, wait - last);
        } else {
            timeout = null;
            if (!immediate) {
                result = func.apply(context, args);
                if (!timeout) context = args = null;
            }
        }
    };
    return function (...args: any) {
        context = this;
        timestamp = +new Date();
        const callNow = immediate && !timeout;
        if (!timeout) timeout = setTimeout(later, wait);
        if (callNow) {
            result = func.apply(context, args);
            context = args = null;
        }
        return result;
    };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Record<string, any>} source
 * @returns {Record<string, any>}
 */
export function deepClone(source: Record<string, any>): Record<string, any> {
    if (!source && typeof source !== 'object') {
        throw new Error('error arguments', 'deepClone' as any);
    }
    const targetObj = source.constructor === Array ? [] : {};
    Object.keys(source).forEach((keys) => {
        if (source[keys] && typeof source[keys] === 'object') {
            targetObj[keys] = deepClone(source[keys]);
        } else {
            targetObj[keys] = source[keys];
        }
    });
    return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr: any[]): any[] {
    return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString(): string {
    const timestamp = +new Date() + '';
    const randomNum = parseInt((1 + Math.random()) * 65536 as any) + '';
    return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} ele
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele: HTMLElement, cls: string): boolean {
    return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'));
}

/**
 * Add class to element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function addClass(ele: HTMLElement, cls: string): void {
    if (!hasClass(ele, cls)) ele.className += ' ' + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} ele
 * @param {string} cls
 */
export function removeClass(ele: HTMLElement, cls: string): void {
    if (hasClass(ele, cls)) {
        const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)');
        ele.className = ele.className.replace(reg, ' ');
    }
}

export function makeMap(str: string, expectsLowerCase: boolean): (val: string) => boolean {
    const map = Object.create(null);
    const list = str.split(',');
    for (let i = 0; i < list.length; i++) {
        map[list[i]] = true;
    }
    return expectsLowerCase ? (val: string) => map[val.toLowerCase()] : (val: string) => map[val];
}

export const exportDefault: string = 'export default ';

export const beautifierConf = {
    html: {
        indent_size: '2',
        indent_char: ' ',
        max_preserve_newlines: '-1',
        preserve_newlines: false,
        keep_array_indentation: false,
        break_chained_methods: false,
        indent_scripts: 'separate',
        brace_style: 'end-expand',
        space_before_conditional: true,
        unescape_strings: false,
        jslint_happy: false,
        end_with_newline: true,
        wrap_line_length: '110',
        indent_inner_html: true,
        comma_first: false,
        e4x: true,
        indent_empty_lines: true,
    },
    js: {
        indent_size: '2',
        indent_char: ' ',
        max_preserve_newlines: '-1',
        preserve_newlines: false,
        keep_array_indentation: false,
        break_chained_methods: false,
        indent_scripts: 'normal',
        brace_style: 'end-expand',
        space_before_conditional: true,
        unescape_strings: false,
        jslint_happy: true,
        end_with_newline: true,
        wrap_line_length: '110',
        indent_inner_html: true,
        comma_first: false,
        e4x: true,
        indent_empty_lines: true,
    },
};

// 首字母大小
export function titleCase(str: string): string {
    return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

// 下划转驼峰
export function camelCase(str: string): string {
    return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

export function isNumberStr(str: string): boolean {
    return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}

/**
 * @desc 时间戳转日期毫秒
 * @param {String} format 时间格式 Y:年，M:月，D:天 h:小时，m:分钟，s:秒 默认:'Y/M/D h:m'
 */
export function formatDate2(arg: string | number, format?: any): string | null {
    let now;
    if (!arg) {
        return null;
    } else {
        now = new Date(arg);
    }
    format = format || 'Y.M.D h:m';

    const year = now.getFullYear();
    let month = now.getMonth() + 1;
    month = month >= 10 ? month : '0' + month as any;
    let date = now.getDate();
    date = date >= 10 ? date : '0' + date as any;
    let hour = now.getHours();
    hour = hour >= 10 ? hour : '0' + hour as any;
    let minute = now.getMinutes();

    minute = minute >= 10 ? minute : '0' + minute as any;
    let second = now.getSeconds();
    second = second >= 10 ? second : '0' + second as any;
    return format.replace('Y', year).replace('M', month).replace('D', date).replace('h', hour).replace('m', minute).replace('s', second);
}

/**
 * 获取日期
 * @param day
 * @returns {string}
 * @deprecated
 * 废弃，建议用moment
 */
export function getDay(day: number): string {
    var today = new Date();
    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
    today.setTime(targetday_milliseconds);
    var tYear = today.getFullYear();
    var tMonth = today.getMonth();
    var tDate = today.getDate();
    tMonth = doHandleMonth(tMonth + 1) as any;
    tDate = doHandleMonth(tDate) as any;
    return tYear + '-' + tMonth + '-' + tDate;
}

/**
 * 获取2位数月份
 * @param month
 * @returns {*}
 * @deprecated
 * 废弃，建议用moment
 */
function doHandleMonth(month: any): string {
    var m = month;
    if (month.toString().length == 1) {
        m = '0' + month;
    }
    return m;
}

/**
 * 获取页面列表最大行数，避免滚动条
 * @param offsetHeight
 * @returns {number}
 */
export function getPageSize(offsetHeight: number = 0): number {
    let pageHeight = document.documentElement.clientHeight - 220 - offsetHeight;
    let pageRows = parseInt(pageHeight / 33);
    pageRows = pageRows ? pageRows : 20;
    return pageRows;
}

/**
 * 获取页面高度
 * @param offsetHeight
 * @returns {number}
 */
export function getPageHeight(offsetHeight: number = 0): number {
    return document.documentElement.clientHeight - (120 + offsetHeight);
}

/**
 * 拷贝对象
 * @param obj
 * @returns {*[] | Function | {} | *}
 */
export function clone(obj: any): any {
    let temp = null;
    if (obj instanceof Array) {
        temp = obj.concat();
    } else if (obj instanceof Function) {
        temp = obj;
    } else {
        temp = {};
        for (let item in obj) {
            let val = obj[item];

            temp[item] = typeof val == 'object' ? clone(val) : val;
        }
    }
    return temp;
}

/**
 * 深度拷贝
 * @param obj
 * @returns {*}
 */
export function deepCopy(obj: any): any {
    let result = Array.isArray(obj) ? [] : {};
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            if (typeof obj[key] === 'object') {
                result[key] = deepCopy(obj[key]); //递归复制
            } else {
                result[key] = obj[key];
            }
        }
    }
    return result;
}

/**
 * 检查数据类型
 * @param val
 * @returns {array/object/date/number/string}
 */
export function objType(val: any): any | null {
    if (!val) {
        return null;
    } else if (val instanceof Array) {
        return 'array';
    } else if (val instanceof Object) {
        return 'object';
    } else if (val instanceof Date) {
        return 'date';
    } else if (val instanceof Number) {
        return 'number';
    } else if (typeof val == 'string') {
        return 'string';
    }
}

/**
 * @param {date} time 需要转换的时间
 * @param {String} fmt 需要转换的格式 如 yyyy-MM-dd、yyyy-MM-dd HH:mm:ss
 */
export function formatTime1(time: number | string, fmt: string): string {
    if (!time) return '';
    const date = new Date(time);
    const o: any = {
        'M+': date.getMonth() + 1,
        'd+': date.getDate(),
        'H+': date.getHours(),
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'q+': Math.floor((date.getMonth() + 3) / 3),
        S: date.getMilliseconds(),
    };
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    for (const k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
        }
    }
    return fmt;
}

/**
 * 流量格式
 * @param flow
 * @returns {{flow: any, unit: *}}
 */
export function formatFlow(flow: number): { flow: number, unit: string } {
    flow = isNaN(flow) || !flow ? 0 : Number(flow);
    let unit = '';
    if (flow / 1024 < 1) {
        flow = flow.toFixed(1) as any;
        unit = 'B';
    } else if (flow / 1024 / 1024 < 1) {
        flow = (flow / 1024).toFixed(1) as any;
        unit = 'KB';
    } else if (flow / 1024 / 1024 / 1024 < 1) {
        flow = (flow / 1024 / 1024).toFixed(1) as any;
        unit = 'MB';
    } else if (flow / 1024 / 1024 / 1024 / 1024 < 1) {
        flow = (flow / 1024 / 1024 / 1024).toFixed(1) as any;
        unit = 'GB';
    }
    flow = flow % 1.0 == 0 ? parseInt(flow as any) : flow;
    return { flow, unit };
}

/**
 * 字符串转16进制
 * @param str
 * @returns {string}
 */
export function strToHex(str: string): string {
    if (str === '') {
        return '';
    }
    let hexCharCode: string[] = [];
    hexCharCode.push('0x');
    for (let i = 0; i < str.length; i++) {
        hexCharCode.push(str.charCodeAt(i).toString(16));
    }
    return hexCharCode.join('');
}

/**
 * 16进制转字符串
 * @param hexCharCodeStr
 * @returns {string}
 */
export function hexToStr(hexCharCodeStr: string): string {
    let trimedStr = hexCharCodeStr.trim();
    let rawStr = trimedStr.substr(0, 2).toLowerCase() === '0x' ? trimedStr.substr(2) : trimedStr;
    let len = rawStr.length;
    if (len % 2 !== 0) {
        alert('Illegal Format ASCII Code!');
        return '';
    }
    let curCharCode;
    let resultStr: string[] = [];
    for (let i = 0; i < len; i = i + 2) {
        curCharCode = parseInt(rawStr.substr(i, 2), 16); // ASCII Code Value
        resultStr.push(String.fromCharCode(curCharCode));
    }
    return resultStr.join('');
}

/**
 * 获取指定范围N个随机数
 * @param min
 * @param max
 * @param size
 * @returns {Array}
 */
export function randomVal(min: number, max: number, size: number): number[] {
    let result: number[] = [];
    for (let i = 0; i < size; i++) {
        result.push(Math.floor(Math.random() * (max - min + 1) + min));
    }
    return result;
}
