import request from '/@/utils/request';

// 定义查询参数的类型
interface QueryParams {
  pageNum?: number;
  pageSize?: number;
  ipaddr?: string;
  userName?: string;
  status?: string;
}

// 定义返回值类型
interface ApiResponse<T> {
  rows: never[];
  total: number;
  code: number;
  msg: string;
  data: T;
}

// 查询登录日志列表
export function list(query: QueryParams) {
  return request<ApiResponse<any>>({
    url: '/monitor/logininfor/list',
    method: 'get',
    params: query,
  });
}

// 删除登录日志
export function delLogininfor(infoId: any) {
  return request<ApiResponse<null>>({
    url: `/monitor/logininfor/${infoId}`,
    method: 'delete',
  });
}

// 解锁用户登录状态
export function unlockLogininfor(userName: string) {
  return request<ApiResponse<null>>({
    url: `/monitor/logininfor/unlock/${userName}`,
    method: 'get',
  });
}

// 清空登录日志
export function cleanLogininfor() {
  return request<ApiResponse<null>>({
    url: '/monitor/logininfor/clean',
    method: 'delete',
  });
}
