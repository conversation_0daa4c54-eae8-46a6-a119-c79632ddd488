import request from '/@/utils/request'

// 定义请求返回的类型，可以根据实际返回的数据结构进行调整
interface ResponseData<T = any> {
  data: T;
  code: number;
  message: string;
}

// 定义设备分组类型
interface Group {
  groupId: number;
  groupName: string;
  description?: string;
  // 其他设备分组字段...
}

// 定义设备ID数组的返回类型
type DeviceIds = number[];

// 查询设备分组列表
export function listGroup(query: Record<string, any>): Promise<ResponseData<any>> {
  return request({
    url: '/iot/deviceGroup/list',
    method: 'get',
    params: query
  });
}

// 查询设备分组详细
export function getGroup(groupId: any): Promise<ResponseData<any>> {
  return request({
    url: `/iot/deviceGroup/${groupId}`,
    method: 'get'
  });
}

// 查询分组下的关联设备ID数组
export function getDeviceIds(groupId: number): Promise<ResponseData<any>> {
  return request({
    url: `/iot/deviceGroup/getDeviceIds/${groupId}`,
    method: 'get'
  });
}

// 新增设备分组
export function addGroup(data: any): Promise<ResponseData<Group>> {
  return request({
    url: '/iot/deviceGroup',
    method: 'post',
    data: data
  });
}

// 修改设备分组
export function updateGroup(data: any): Promise<ResponseData<Group>> {
  return request({
    url: '/iot/deviceGroup',
    method: 'put',
    data: data
  });
}

// 更新分组下的设备
interface UpdateDeviceGroupsData {
  groupId: number;
  deviceIds: number[];
}

export function updateDeviceGroups(data: any): Promise<ResponseData> {
  return request({
    url: '/iot/deviceGroup/updateDeviceGroups',
    method: 'put',
    data: data
  });
}

// 删除设备分组
export function delGroup(groupId: number): Promise<ResponseData> {
  return request({
    url: `/iot/deviceGroup/${groupId}`,
    method: 'delete'
  });
}
