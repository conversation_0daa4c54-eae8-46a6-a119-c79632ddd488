<template>
    <div class="layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="渠道名称" prop="name">
                        <el-input v-model="state.tableData.param.name" clearable size="default" placeholder="请输入渠道名称"
                            style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="渠道类型" prop="channelType">
                        <el-select v-model="state.tableData.param.channelType" placeholder="请选择渠道类型" clearable
                            size="default" style="width: 180px">
                            <el-option v-for="option in channelTypeList" :key="option.value" :label="option.label"
                                :value="option.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['iot:channel:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['iot:channel:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic(undefined, undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['iot:channel:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['iot:channel:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="编号" align="center" prop="id" width="100" />
                <el-table-column label="渠道名称" align="center" prop="name" />
                <el-table-column label="发送渠道类型" align="center" prop="channelType" width="200">
                    <template #default="scope">
                        <dict-tag :options="channel_type_list" :value="scope.row.channelType" />
                    </template>
                </el-table-column>
                <el-table-column label="服务商" align="center" prop="provider" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" icon="el-icon-edit"
                            @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:channel:edit']"><el-icon><ele-View /></el-icon>详情</el-button>
                        <el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
                            v-auths="['iot:channel:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <!-- 添加或修改通知渠道对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogstate.title" v-model="dialogstate.open"
            width="700px" append-to-body>
            <div>
                <!-- <el-divider style="margin-top: -30px"></el-divider> -->
                <el-form ref="DialogFormRef" :model="dialogstate.form" :rules="dialogstate.rules" label-width="130px">
                    <el-form-item label="渠道名称" prop="name">
                        <el-input size="default" v-model="dialogstate.form.name" placeholder="请输入渠道名称" />
                    </el-form-item>
                    <el-form-item label="渠道类型" prop="channelType">
                        <el-select v-model="dialogstate.form.channelType" placeholder="请选择渠道类型" clearable size="default"
                            style="width: 100%" @change="changeChannel">
                            <el-option v-for="option in channelTypeList" :key="option.value" :label="option.label"
                                :value="option.value"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="服务商" prop="provider">
                        <el-select placeholder="请选择服务商" clearable size="default" style="width: 100%"
                            v-model="dialogstate.form.provider" :disabled="dialogstate.form.channelType == null"
                            @change="changeService">
                            <el-option v-for="provider in list" :key="provider.value" :label="provider.label"
                                :value="provider.value" />
                        </el-select>
                    </el-form-item>
                    <div>
                        <el-form-item v-for="(item, index) in dialogstate.configList" :key="index" :label="item.label">
                            <el-input v-model="item.value" placeholder="请输入配置内容" v-if="item.type == 'string'" />
                            <el-input v-model="item.value" placeholder="请输入配置内容" type="number"
                                v-if="item.type == 'int'" />
                            <!-- <editor v-model="item.value" :min-height="192" v-if="item.type == 'text'" /> -->
                            <el-switch v-model="item.value" active-color="#13ce66" inactive-color="#c0c0c0"
                                v-if="item.type == 'boolean'"></el-switch>
                        </el-form-item>
                    </div>
                </el-form>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="submitForm(DialogFormRef)" v-auths="['notify:channel:edit']"
                        v-show="dialogstate.form.id">修
                        改</el-button>
                    <el-button type="primary" @click="submitForm(DialogFormRef)" v-auths="['notify:channel:add']"
                        v-show="!dialogstate.form.id">新
                        增</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="systemDic">
import {  reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { download } from '/@/utils/request';
import { parseTime } from '/@/utils/next'
import { addChannel, delChannel, getChannel, getChannelMessage, getConfigContent, listChannel, updateChannel } from '/@/api/notify/channel';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
// 定义变量内容
const DialogFormRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            name: undefined, //渠道名称
            channelType: undefined
        },
    },
});
const dialogstate = reactive({
    open: false,
    title: '添加通知渠道',
    form: {
        id: undefined,
        name: undefined,
        channelType: undefined as any,
        provider: undefined as any,
        channelChildren: [] as any,
        configContent: [] as any
    },
    configList: [] as any,
    rules: {
        name: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }],
        channelType: [{ required: true, message: '请选择渠道类型', trigger: 'change' }],
        provider: [{ required: true, message: '请选择服务商', trigger: 'change' }],
    },
});
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const channel_type_list = ref<statusOption[]>([]);
const channelTypeList = ref<any[]>([]);
const channelMsgList = ref<[]>([]);
const showSearch = ref(true)    // 显示搜索条件
const list = ref([]) as any //服务商列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //id
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listChannel(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        name: undefined,
        channelType: undefined
    }
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { id: string; }) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 取消按钮
const cancel = () => {
    dialogstate.open = false;
    reset();
}
const reset = () => {
    dialogstate.form = {
        id: undefined,
        name: undefined,
        channelType: undefined as any,
        provider: undefined as any,
        channelChildren: [] as any,
        configContent: [] as any
    };
    dialogstate.configList = [] as any;
}
// 打开新增
const onOpenAddDic = (type: string) => {
    // ChannelDialogRef.value.openDialog(type);
    reset()
    dialogstate.open = true;
    dialogstate.title = '新增通知渠道';
};
// 打开修改
const onOpenEditDic = (type: any, row: any | undefined) => {
    reset()
    var id = ''
    if (row) {
        id = row.id
    } else {
        id = ids.value
    }
    // const id = row.id | ids.value;
    console.log(id, 'id');
    nextTick(() => {
        if (type == 'edit') {
            dialogstate.form.channelType = row.channelType;
            dialogstate.form.provider = row.provider;
            getConfig()
        } else {
            getChannel(id).then((response) => {
                dialogstate.form.channelType = response.data.data.channelType;
                dialogstate.form.provider = response.data.data.provider;
                getConfig()
            });
        }
    });

    // });
    setTimeout(() => {
        getChannel(id).then((response) => {
            dialogstate.form = response.data.data;
            dialogstate.open = true;
            dialogstate.title = '修改通知渠道';
            getServiceList();
            if (dialogstate.form.configContent != '') {
                const List = JSON.parse(dialogstate.form.configContent);
                for (let j = 0; j < dialogstate.configList.length; j++) {
                    for (const key in List) {
                        if (dialogstate.configList[j].attribute == key) {
                            dialogstate.configList[j].value = List[key];
                        }
                    }
                }
            }

        });
    }, 500);
};
// 删除
const onRowDel = (row: any) => {
    const idss = row.id || ids.value;
    ElMessageBox.confirm(`是否确认删除通知渠道编号为${idss}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delChannel(idss).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('notify/channel/export', {
        ...state.tableData.param
    }, `channel_${new Date().getTime()}.xlsx`)
}
//获取渠道具体信息
const getInfo = () => {
    state.tableData.loading = true;
    getChannelMessage().then((response: any) => {
        channelMsgList.value = response.data.data;
        //渠道类型
        channelTypeList.value = response.data.data.map((item: any) => {
            return { value: item.channelType, label: item.channelName };
        });
        //取出服务商列表
        // this.providerList = response.data.map((item:any) => item.providerList);
    });
    state.tableData.loading = false;
}
//渠道发生改变
const changeChannel = () => {
    getServiceList();
    dialogstate.form.provider = '' as any;
    dialogstate.configList = [];
}
//获取服务商列表
const getServiceList = () => {
    const selectedChannel = dialogstate.form.channelType;
    dialogstate.form.channelChildren = channelMsgList.value.filter((item: any) => selectedChannel.includes(item.channelType)).map((item:any) => item.providerList);
    for (var i = 0; i < dialogstate.form.channelChildren.length; i++) {
        list.value = dialogstate.form.channelChildren[i].map((item: any) => {
            return { value: item.provider, label: item.providerName, config: item.configContent };
        });
    }
}
//服务商发生改变后获取配置内容
const changeService = () => {
    getServiceList();
    getConfig();
}
//获取配置内容
const getConfig = () => {
    getConfigContent(dialogstate.form.provider, dialogstate.form.channelType).then((res) => {
        dialogstate.configList = res.data.data.map((item: any) => {
            return { value: item.value, label: item.name, attribute: item.attribute, type: item.type };
        });
        for (let i = 0; i < dialogstate.configList.length; i++) {
            if (dialogstate.configList[i].type === 'boolean') {
                dialogstate.configList[i].value = Boolean(dialogstate.configList[i].value);
            }
        }
    });
}
// 提交
const submitForm = async (formEl: FormInstance | undefined) => {
    const configUseList = dialogstate.configList.map((item: any) => {
        return [item.attribute, item.value];
    });
    const object = Object.fromEntries(configUseList);
    const json = JSON.stringify(object);
    const query = {
        id: dialogstate.form.id,
        name: dialogstate.form.name,
        channelType: dialogstate.form.channelType,
        provider: dialogstate.form.provider,
        configContent: json,
    };
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogstate.form.id != undefined) {
                updateChannel(query).then(response => {
                    //  刷新页面
                    getTableData();
                    dialogstate.open = false;
                    ElMessage.success('修改成功');
                });
            } else {
                addChannel(query).then(response => {
                    //  刷新页面
                    getTableData();
                    dialogstate.open = false;
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        channel_type_list.value = await dictStore.fetchDict('notify_channel_type')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getInfo()
    getdictdata()
});
</script>
