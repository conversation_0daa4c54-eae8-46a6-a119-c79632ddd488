// import {  reactive, onMounted, ref } from 'vue';
// /**
//  * 通用js方法封装处理
//  * Copyright (c) 2019 ruoyi
//  */

import { reactive } from "vue"

// 日期格式化
export function parseTime(time: string | number | Date, pattern?: string) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = reactive({
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  })
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result: string, key: string) => {
    let value = formatObj[key as keyof typeof formatObj]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value as any
    }
    // 强制返回 string 类型，避免类型不匹配
    return value.toString(); // 确保返回 string 类型
  })
  return time_str
}

//   // 表单重置
//   export function resetForm(refName: string | number) {
//     if (this.$refs[refName]) {
//       this.$refs[refName].resetFields();
//     }
//   }
//   // 添加日期范围
export function addDateRange(params: any, dateRange?: any[], propName?: string) {
  let search = params;

  // 确保 params 是一个对象
  if (typeof search !== 'object' || search === null || Array.isArray(search)) {
    search = {};  // 如果 params 不符合格式，重新赋值为对象
  }

  // 确保 dateRange 是数组并且有两个值
  dateRange = Array.isArray(dateRange) ? dateRange : [];

  // 如果没有传入 propName，直接构建 params[beginTime] 和 params[endTime]
  if (typeof propName === 'undefined') {
    search['params[beginTime]'] = dateRange[0];  // 构建 params[beginTime]
    search['params[endTime]'] = dateRange[1];    // 构建 params[endTime]
  } else {
    // 如果传入了 propName，则动态生成 params[begin{propName}] 和 params[end{propName}]
    search[`params[begin${propName}]`] = dateRange[0];
    search[`params[end${propName}]`] = dateRange[1];
  }

  return search;
}



// //   // 添加日期范围
//   export function addDateRange(params: any, dateRange: any[], propName?: string) {
//     let search = params;
//     search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
//     dateRange = Array.isArray(dateRange) ? dateRange : [];
//     if (typeof (propName) === 'undefined') {
//       search.params['beginTime'] = dateRange[0];
//       search.params['endTime'] = dateRange[1];
//     } else {
//       search.params['begin' + propName] = dateRange[0];
//       search.params['end' + propName] = dateRange[1];
//     }
//     return search;
//   }

// 回显数据字典
export function selectDictLabel(datas: any, value: string | undefined) {
  if (value === undefined) {
    return "";
  }

  var actions: string[] = [];
  console.log(datas, 'datas');
  console.log(value, 'value');
  Object.keys(datas).some((key) => {
    if (datas[key] == ('' + value)) {
      actions.push(datas[key].label);
      return true;
    }
  })
  console.log(actions);


  if (actions.length === 0) {
    actions.push(value);
  }
  return actions.join('');
}

//   // 回显数据字典（字符串、数组）
//   export function selectDictLabels(datas: { [x: string]: {
//       value: string; label: any 
// } }, value: string | undefined, separator: undefined) {
//     if (value === undefined || value.length ===0) {
//       return "";
//     }
//     if (Array.isArray(value)) {
//       value = value.join(",");
//     }
//     var actions: never[] = [];
//     var currentSeparator = undefined === separator ? "," : separator;
//     var temp = value.split(currentSeparator);
//     Object.keys(value.split(currentSeparator)).some((val) => {
//       var match = false;
//       Object.keys(datas).some((key) => {
//         if (datas[key].value == ('' + temp[val])) {
//           actions.push(datas[key].label + currentSeparator);
//           match = true;
//         }
//       })
//       if (!match) {
//         actions.push(temp[val] + currentSeparator);
//       }
//     })
//     return actions.join('').substring(0, actions.join('').length - 1);
//   }

//   // 字符串格式化(%s )
//   export function sprintf(str: string) {
//     var args = arguments, flag = true, i = 1;
//     str = str.replace(/%s/g, function () {
//       var arg = args[i++];
//       if (typeof arg === 'undefined') {
//         flag = false;
//         return '';
//       }
//       return arg;
//     });
//     return flag ? str : '';
//   }

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str: string) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

//   // 数据合并
//   export function mergeRecursive(source: { [x: string]: any }, target: { [x: string]: any }) {
//     for (var p in target) {
//       try {
//         if (target[p].constructor == Object) {
//           source[p] = mergeRecursive(source[p], target[p]);
//         } else {
//           source[p] = target[p];
//         }
//       } catch (e) {
//         source[p] = target[p];
//       }
//     }
//     return source;
//   };

//   /**
//    * 构造树型结构数据
//    * @param {*} data 数据源
//    * @param {*} id id字段 默认 'id'
//    * @param {*} parentId 父节点字段 默认 'parentId'
//    * @param {*} children 孩子节点字段 默认 'children'
//    */
// interface TreeNode {
//   [key: string]: any; // 每个节点可以是任意结构，但必须是一个对象
// }

export function handleTree(data: any[], id: string, parentId?: string, children?: string): any[] {
  // 配置项，支持动态配置 id, parentId, childrenList
  const config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  // 初始化 maps 和树形结构
  const childrenListMap: Record<string, any[]> = {}; // 子节点映射表
  const nodeIds: Record<string, any> = {}; // 节点 id 映射
  const tree: any[] = []; // 树形结构

  // 构建 childrenListMap 和 nodeIds
  for (const d of data) {
    const parentIdValue = d[config.parentId];
    if (!childrenListMap[parentIdValue]) {
      childrenListMap[parentIdValue] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentIdValue].push(d);
  }

  // 构建根节点
  for (const d of data) {
    const parentIdValue = d[config.parentId];
    if (!nodeIds[parentIdValue]) {
      tree.push(d); // 如果没有父节点，则是根节点
    }
  }

  // 递归地构建树形结构
  for (const t of tree) {
    adaptToChildrenList(t);
  }

  // 递归函数，给每个节点添加 childrenList 属性
  function adaptToChildrenList(o: any): void {
    const children = childrenListMap[o[config.id]];
    if (children && children.length > 0) {
      o[config.childrenList] = children;
      // 递归处理子节点
      for (const c of children) {
        adaptToChildrenList(c);
      }
    }
  }

  return tree;
}

// export function handleTree(data: any, id: any, parentId: any, children: any) {
//   let config = {
//     id: id || 'id',
//     parentId: parentId || 'parentId',
//     childrenList: children || 'children'
//   };

//   var childrenListMap = {};
//   var nodeIds = {};
//   var tree = [];

//   for (let d of data) {
//     let parentId = d[config.parentId];
//     if (childrenListMap[parentId] == null) {
//       childrenListMap[parentId] = [];
//     }
//     nodeIds[d[config.id]] = d;
//     childrenListMap[parentId].push(d);
//   }

//   for (let d of data) {
//     let parentId = d[config.parentId];
//     if (nodeIds[parentId] == null) {
//       tree.push(d);
//     }
//   }

//   for (let t of tree) {
//     adaptToChildrenList(t);
//   }

//   function adaptToChildrenList(o: { [x: string]: any }) {
//     if (childrenListMap[o[config.id]] !== null) {
//       o[config.childrenList] = childrenListMap[o[config.id]];
//     }
//     if (o[config.childrenList]) {
//       for (let c of o[config.childrenList]) {
//         adaptToChildrenList(c);
//       }
//     }
//   }
//   return tree;
// }

//   /**
//   * 参数处理
//   * @param {*} params  参数
//   */
export function tansParams(params: { [x: string]: any }) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && value !== "" && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
            let params = propName + '[' + key + ']';
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result
}

//   // 验证是否为blob格式
export async function blobValidate(data: { text: () => any }) {
  try {
    const text = await data.text();
    JSON.parse(text);
    return false;
  } catch (error) {
    return true;
  }
}
