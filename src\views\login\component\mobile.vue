<template>
	<el-form :model="state.ruleForm" ref="ruleFormRef" :rules="state.smsRules" size="large" class="login-content-form">
		<el-form-item class="login-animation1" prop="phone">
			<el-input text :placeholder="$t('message.mobile.placeholder1')" v-model="state.ruleForm.phone" clearable
				autocomplete="off">
				<template #prefix>
					<i class="iconfont icon-dianhua el-input__icon"></i>
				</template>
			</el-input>
		</el-form-item>
		<el-form-item class="login-animation2" prop="phonecode">
			<el-col :span="15">
				<el-input text maxlength="4" :placeholder="$t('message.mobile.placeholder2')"
					v-model="state.ruleForm.phonecode" clearable autocomplete="off">
					<template #prefix>
						<el-icon class="el-input__icon"><ele-Position /></el-icon>
					</template>
				</el-input>
			</el-col>
			<el-col :span="1"></el-col>
			<el-col :span="8">
				<el-button v-waves class="login-content-code" :disabled="state.ruleForm.phone == '' || isDisabled"
					@click="getSmsCode">{{ buttonText }}</el-button>
			</el-col>
		</el-form-item>
		<el-form-item class="login-animation3">
			<el-button round type="primary" v-waves class="login-content-submit" @click="handleSmsLogin(ruleFormRef)"
				:loading="state.smsloading.signIn">
				<span>{{ $t('message.mobile.btnText') }}</span>
			</el-button>
		</el-form-item>
		<div class="font12 mt30 login-animation4 login-msg">{{ $t('message.mobile.msgText') }}</div>
	</el-form>
</template>

<script setup lang="ts" name="loginMobile">
import { reactive, ref ,computed} from 'vue';
import { useI18n } from 'vue-i18n';
import { ElMessage, FormInstance } from 'element-plus';
import { NextLoading } from '/@/utils/loading';
import { storeToRefs } from 'pinia';
import { useRoute, useRouter } from 'vue-router';
import Cookies from 'js-cookie';
import { useThemeConfig } from '/@/stores/themeConfig';
import { getSmsLoginCaptcha, smsLogin } from '/@/api/login';
import { Session } from '/@/utils/storage';
import { formatAxis } from '/@/utils/formatTime';
import { initFrontEndControlRoutes } from '/@/router/frontEnd';
import { initBackEndControlRoutes } from '/@/router/backEnd';

// 定义变量内容
const { t } = useI18n();
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
// 时间获取
const currentTime = computed(() => {
	return formatAxis(new Date());
});
const state = reactive({
	ruleForm: {
		phone: '18654963214',
		phonecode: '',
	},
	smsRules: {
		phone: [
			{ required: true, message: "手机号码不能为空", trigger: "blur" },
			{
				pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
				message: "请输入正确的手机号码",
				trigger: "blur"
			}
		],
		phonecode: [
			{
				required: true,
				trigger: 'change',
				message: '验证码不能为空',
			},
		],

	},
	smsloading: {
		signIn: false,
	},
});
const ruleFormRef = ref<FormInstance>()
// 倒计时器对象
var countdownTimer = ref(null)
// 剩余秒数
const remainingSeconds = ref(60)
const isDisabled = ref(false)
let buttonText = ref('获取验证码')
// 点击登录按钮
const handleSmsLogin = async (formEl: FormInstance | undefined) => {
	// 表单校验
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			try {
				state.smsloading.signIn = true
				let info = await smsLogin(state.ruleForm)
				console.log(info);
				// // 存储 token 到浏览器缓存
				Session.set('token', Math.random().toString(36).substr(0));
				// // 模拟数据，对接接口时，记得删除多余代码及对应依赖的引入。用于 `/src/stores/userInfo.ts` 中不同用户登录判断（模拟数据）
				// Cookies.set('username', state.ruleForm.username);
				if (!themeConfig.value.isRequestRoutes) {
					// 前端控制路由，2、请注意执行顺序
					const isNoPower = await initFrontEndControlRoutes();
					signInSuccess(isNoPower);
				} else {
					// 模拟后端控制路由，isRequestRoutes 为 true，则开启后端控制路由
					// 添加完动态路由，再进行 router 跳转，否则可能报错 No match found for location with path "/"
					const isNoPower = await initBackEndControlRoutes();
					// 执行完 initBackEndControlRoutes，再执行 signInSuccess
					signInSuccess(isNoPower);
				}
			} catch (err) {
				// formEl.clearValidate()
				// getcode()


			}
		} else {
			console.log('error submit!', fields)
		}
	})

	// }
};
// 登录成功后的跳转
const signInSuccess = (isNoPower: boolean | undefined) => {
	if (isNoPower) {
		ElMessage.warning('抱歉，您没有登录权限');
		Session.clear();
	} else {
		// 初始化登录成功时间问候语
		let currentTimeInfo = currentTime.value;
		// 登录成功，跳到转首页
		// 如果是复制粘贴的路径，非首页/登录页，那么登录成功后重定向到对应的路径中
		if (route.query?.redirect) {
			router.push({
				path: <string>route.query?.redirect,
				query: Object.keys(<string>route.query?.params).length > 0 ? JSON.parse(<string>route.query?.params) : '',
			});
		} else {
			router.push('/');
		}
		// 登录成功提示
		const signInText = t('message.signInText');
		ElMessage.success(`${currentTimeInfo},${signInText}`);
		// 添加 loading，防止第一次进入界面时出现短暂空白
		NextLoading.start();
	}
	state.smsloading.signIn = false;
};
//验证电话号码
const validatePhoneNumber = (number: string) => {
	const regExp = /^1[3456789]\d{9}$/; // 使用正则表达式进行校验
	return regExp.test(number);
};
// 获取手机验证码
const getSmsCode = async () => {
	if (validatePhoneNumber(state.ruleForm.phone)) {
		// let countdownTimer = setInterval(() => {
		// 	if (remainingSeconds.value > 0) {
		// 		remainingSeconds.value--;
		// 		buttonText.value = `${remainingSeconds.value}秒后获取`;
		// 	} else {
		// 		clearInterval(countdownTimer);
		// 		buttonText.value = `发送验证码`;
		// 		isDisabled.value = false;
		// 	}
		// }, 1000);
		// isDisabled.value = true;
		// return
		getSmsLoginCaptcha(state.ruleForm.phone).then((res) => {
			if (res.code == 200) {
				ElMessage({
					type: 'success',
					message: '获取成功'
				})
				// var remainingSeconds = 60
				let countdownTimer = setInterval(() => {
					if (remainingSeconds.value > 0) {
						remainingSeconds.value--;
						buttonText.value = `${remainingSeconds.value}秒后获取`;
					} else {
						clearInterval(countdownTimer);
						buttonText.value = `发送验证码`;
						isDisabled.value = false;
					}
				}, 1000);
				isDisabled.value = true;
			} else {
				ElMessage({
					type: 'warning',
					message: '获取失败'
				})
			}

		}).catch((err) => {
			console.log(err);

		})
	} else {
		ElMessage({ message: '手机号码格式错误，请输入正确手机号！', type: 'error' })
	}

}
</script>

<style scoped lang="scss">
.login-content-form {
	margin-top: 20px;

	@for $i from 1 through 4 {
		.login-animation#{$i} {
			opacity: 0;
			animation-name: error-num;
			animation-duration: 0.5s;
			animation-fill-mode: forwards;
			animation-delay: calc($i/10) + s;
		}
	}

	.login-content-code {
		width: 100%;
		padding: 0;
	}

	.login-content-submit {
		width: 100%;
		letter-spacing: 2px;
		font-weight: 300;
		margin-top: 15px;
	}

	.login-msg {
		color: var(--el-text-color-placeholder);
	}
}
</style>
