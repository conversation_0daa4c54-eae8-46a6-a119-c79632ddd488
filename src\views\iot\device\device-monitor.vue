<template>
    <div style="padding-left: 20px">
        <el-form :inline="true" label-width="100px">
            <el-form-item label="监测间隔(ms)">
                <el-tooltip class="item" effect="light" content="取值范围500-10000毫秒" placement="top">
                    <el-input v-model="state.monitorInterval" placeholder="请输入监测间隔" type="number" clearable size="small"
                        style="width: 180px" />
                </el-tooltip>
            </el-form-item>
            <el-form-item label="监测次数">
                <el-tooltip class="item" effect="light" content="取值方位1-300" placement="top">
                    <el-input v-model="state.monitorNumber" placeholder="请输入监测次数" type="number" clearable size="small"
                        style="width: 180px" />
                </el-tooltip>
            </el-form-item>
            <el-form-item>
                <el-button type="success" size="default" @click="beginMonitor()" style="margin-left: 30px"
                    v-auths="['iot:service:invoke ']"><el-icon>
                        <VideoPlay />
                    </el-icon>开始监测</el-button>
                <el-button type="danger" @click="stopMonitor()" size="default"
                    v-auths="['iot:service:invoke ']"><el-icon>
                        <VideoPause />
                    </el-icon>停止监测</el-button>
            </el-form-item>
        </el-form>
        <el-row :gutter="20" v-loading="state.chartLoading" element-loading-text="正在接收设备数据，请耐心等待......"
            element-loading-spinner="el-icon-loading">
            <el-col :span="12" v-for="(item, index) in state.monitorThings" :key="index" style="margin-bottom: 20px">
                <el-card shadow="hover" :body-style="{ paddingTop: '10px', marginBottom: '-20px' }">
                    <div :ref="setChartRef" style="height: 210px; padding: 0"></div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>
<script setup lang="ts" name="">
import { nextTick, reactive, ref, watch } from 'vue';
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';
import * as echarts from 'echarts';
import mqttTool from '/@/utils/mqttTool';
interface MonitorData {
    id: string;
    name: string;
    data: any[];
    datatype: {
        unit?: string;
    };
}
const props = defineProps({
    device: {
        type: Object,
        default: () => ({})
    }
});
const state = reactive({
    monitorInterval: 1000,// 实时监测间隔
    monitorNumber: 60,// 实时监测次数
    monitorThings: [] as MonitorData[],// 监测物模型
    chartLoading: false,// 图表遮罩层
});
const deviceInfos = ref({
    deviceId: 0,
    serialNumber: '', // Add serialNumber property
    monitorList: [] as MonitorData[],
    status: 0,
    isShadow: 0,
    rssi: 0,
});// 设备信息
const monitorChart = ref([]) as any;// 图表集合
const dataList = ref<any>([]) as any;// 图表数据集合
// 用于存储所有图表的 ref
const chartRefs = ref<HTMLElement[]>([]);
// 动态设置 ref
const setChartRef = (el: any) => {
    if (el) {
        chartRefs.value.push(el);
    }
};
const isMqttActive = ref(true);
/** 更新实时监测参数*/
const beginMonitor = () => {
    // mqttCallback()
    if (deviceInfos.value.status != 3) {
        ElMessage.error('设备不在线，下发指令失败');
        return;
    }
    // 清空图表数据
    for (let i = 0; i < dataList.value.length; i++) {
        dataList.value[i].data = [];
    }
    if (state.monitorInterval < 500 || state.monitorInterval > 10000) {
        ElMessage.error('实时监测的间隔范围500-10000毫秒');
    }
    if (state.monitorNumber == 0 || state.monitorNumber > 300) {
        ElMessage.error('实时监测数量范围1-300');
    }
    // Mqtt发布实时监测消息
    let model = {
        name: '更新实时监测',
        value: state.monitorNumber,
        type: 4
    };
    // model.name = '更新实时监测';
    // model.value = state.monitorNumber;
    // model.type = 4;
    isMqttActive.value = true;
    mqttPublish(deviceInfos.value, model);
    state.chartLoading = true;
}
/** 停止实时监测 */
const stopMonitor = () => {
    if (deviceInfos.value.status != 3) {
        ElMessage.error('设备不在线，下发指令失败');
        return;
    }
    state.chartLoading = false;
    // Mqtt发布实时监测
    let model = {
        name: '关闭实时监测',
        value: 0,
        type: 4
    };
    model.name = '关闭实时监测';
    model.value = 0;
    model.type = 4;
    isMqttActive.value = false;
    mqttPublish(deviceInfos.value, model);
}
/**监测数据 */
const getMonitorChart = () => {
    let color = ['#1890FF', '#91CB74', '#FAC858', '#EE6666', '#73C0DE', '#3CA272', '#FC8452', '#9A60B4', '#ea7ccc'];
    for (let i = 0; i < state.monitorThings.length; i++) {
        const chartElement = chartRefs.value[i];
        // 设置宽度
        chartElement.style.width = document.documentElement.clientWidth / 2 - 255 + 'px';
        monitorChart.value[i] = echarts.init(chartElement);
        // 在图表初始化后立即调用 resize
        monitorChart.value[i].resize();
        var option;
        option = {
            title: {
                left: 'center',
                text: state.monitorThings[i].name + ' （单位 ' + (state.monitorThings[i].datatype.unit != undefined ? state.monitorThings[i].datatype.unit : '无') + '）',
                textStyle: {
                    fontSize: 14,
                },
            },
            grid: {
                top: '50px',
                left: '20px',
                right: '20px',
                bottom: '10px',
                containLabel: true,
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    animation: true,
                },
            },
            xAxis: {
                type: 'time',
                show: false,
                splitLine: {
                    show: false,
                },
            },
            yAxis: {
                type: 'value',
                boundaryGap: [0, '100%'],
                splitLine: {
                    show: true,
                },
            },
            series: [
                {
                    name: state.monitorThings[i].name,
                    type: 'line',
                    symbol: 'none',
                    sampling: 'lttb',
                    itemStyle: {
                        color: i > 9 ? color[0] : color[i],
                    },
                    areaStyle: {},
                    data: [],
                },
            ],
        };
        option && monitorChart.value[i].setOption(option);
    }
}
const mqttPublish = (device: any, model: any) => {   
    let topic = '';
    let message = '';
    if (model.type == 4) {
        // 实时监测
        topic = '/' + device.productId + '/' + device.serialNumber + '/monitor/get';
        // let topicMonitor = '/' + device.productId + '/' + device.serialNumber + '/monitor/post';
        message = '{"count":' + model.value + ',"interval":' + state.monitorInterval + '}';
    } else {
        return;
    }
    if (topic != '') {
        // 发布
        mqttTool.publish(topic, message, model.name)
            .then((res) => {
                ElNotification({
                    // title: 'Success',
                    message: res,
                    type: 'success',
                })
                mqttCallback();
            })
            .catch((res) => {
                ElNotification({
                    title: 'Error',
                    message: res,
                    type: 'error',
                })

            });
    }
}
/* Mqtt回调处理  */
const mqttCallback = () => {
    if (mqttTool.client) {
        mqttTool.client.on('message', (topic, message, buffer) => {
            if (!isMqttActive.value) return;  // 如果标志位为 false，则跳过处理
            let topics = topic.split('/');
            let productId = topics[1];
            let deviceNum = topics[2];
            const parsedMessage = JSON.parse(message.toString());
            // console.log(parsedMessage, 'parsedMessage');

            if (!parsedMessage) {
                return;
            }
            if (topics[3] == 'status') {
                console.log('接收到【设备状态】主题：', topic);
                console.log('接收到【设备状态】内容：', message);
                // 更新列表中设备的状态
                if (deviceInfos.value.serialNumber == deviceNum) {
                    deviceInfos.value.status = parsedMessage.status;
                    deviceInfos.value.isShadow = parsedMessage.isShadow;
                    deviceInfos.value.rssi = parsedMessage.rssi;
                }
            }
            if (topics[3] == 'monitor') {
                console.log('接收到【实时监测】主题：', topic);
                console.log('接收到【实时监测】内容：', message);
                // 实时监测
                state.chartLoading = false;
                for (let k = 0; k < parsedMessage.length; k++) {
                    let value = parsedMessage[k].value;
                    let id = parsedMessage[k].id;
                    let remark = parsedMessage[k].remark;
                    // 数据加载到图表
                    for (let i = 0; i < dataList.value.length; i++) {
                        if (id == dataList.value[i].id) {
                            // 普通类型匹配
                            if (dataList.value[i].length > 50) {
                                dataList.value[i].shift();
                            }
                            dataList.value[i].data.push([getTime(), value]);
                            // 更新图表
                            monitorChart.value[i].setOption({
                                series: [
                                    {
                                        data: dataList.value[i].data,
                                    },
                                ],
                            });
                            break;
                        } else if (dataList.value[i].id.indexOf('array_') == 0) {
                            // 数组类型匹配,例如：gateway_temperature,图表id去除前缀后匹配
                            let index = dataList.value[i].id.substring(6, 8);
                            let identity = dataList.value[i].id.substring(9);
                            if (identity == id) {
                                let values = value.split(',');
                                if (dataList.value[i].length > 50) {
                                    dataList.value[i].shift();
                                }
                                dataList.value[i].data.push([getTime(), values[index]]);
                                // 更新图表
                                monitorChart.value[i].setOption({
                                    series: [
                                        {
                                            data: dataList.value[i].data,
                                        },
                                    ],
                                });
                                break;
                            }
                        }
                    }
                }
            }
        });
    }
}
/* 获取当前时间*/
const getTime = () => {
    let date = new Date();
    let y = date.getFullYear();
    let m = date.getMonth() + 1;
    let d = date.getDate();
    let H = date.getHours();
    let mm = date.getMinutes();
    let s = date.getSeconds();
    m = m < 10 ? '0' + m : m as any;
    d = d < 10 ? '0' + d : d as any;;
    H = H < 10 ? '0' + H : H as any;;
    return y + '-' + m + '-' + d + ' ' + H + ':' + mm + ':' + s;
}
watch(
    () => props.device,
    (newVal, oldVal) => {
        
        deviceInfos.value = newVal as any;
        if (deviceInfos.value && deviceInfos.value.deviceId != 0) {
            // 监测数据
            state.monitorThings = deviceInfos.value.monitorList;
            // 监测数据集合初始化
            dataList.value = [] as any;
            for (let i = 0; i < state.monitorThings.length; i++) {
                dataList.value.push({
                    id: state.monitorThings[i].id,
                    name: state.monitorThings[i].name,
                    data: [],
                });
                // dataList.value[i].data.push(["2022-03-14 23:32:09", "30"]);
            }
            // console.log(state.monitorThings);

            // 绘制监测图表
            nextTick(() => {
                getMonitorChart();
            });
            // mqttCallback();
        }
        // console.log(newVal);     
        // if (newVal) {
        //     state.monitorThings = newVal.monitorThings;
        // }
    },
    { immediate: true }
);
</script>