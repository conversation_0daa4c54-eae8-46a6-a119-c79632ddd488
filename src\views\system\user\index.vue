<template>
	<div class="system-user-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<el-row>
				<!-- 部门数据 -->
				<el-col :span="4">
					<el-input v-model="deptName" size="default" placeholder="请输入部门名称" style="max-width: 90%">
					</el-input>
					<div class="mt10">
						<el-tree :data="deptOptions.data" :props="defaultProps" :expand-on-click-node="false"
							:filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all
							highlight-current @node-click="handleNodeClick" />
					</div>
				</el-col>
				<!-- 用户数据 -->
				<el-col :span="20" :xs="24">
					<div class="system-user-search mb15">
						<el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
							<el-form-item label="用户名称">
								<el-input v-model="state.tableData.param.userName" aria-label="First Name"
									placeholder="请输入用户名称" style="width: 240px" />
							</el-form-item>
							<el-form-item label="手机号码">
								<el-input v-model="state.tableData.param.phonenumber" placeholder="请输入用户手机号码"
									style="width: 240px" />
							</el-form-item>
							<el-form-item label="状态">
								<el-select v-model="state.tableData.param.status" placeholder="用户状态" clearable
									style="width: 240px">
									<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
										:value="dict.dictValue" />
								</el-select>
							</el-form-item>
							<el-form-item label="创建时间">
								<el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
									value-format="YYYY-MM-DD" type="daterange" range-separator="-"
									start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
							</el-form-item>
							<el-form-item>
								<el-button size="default" type="primary" class="ml10" @click="handleQuery">
									<el-icon>
										<ele-Search />
									</el-icon>
									查询
								</el-button>
								<el-button size="default" @click="resetQuery">
									<el-icon><ele-Refresh /></el-icon>
									重置
								</el-button>
							</el-form-item>
						</el-form>
						<!-- <el-input size="default" placeholder="请输入用户名称" style="max-width: 180px"> </el-input>
						<el-button size="default" type="primary" class="ml10">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button> -->
						<el-row :gutter="10" class="mb8" :justify="'space-between'">
							<div>
								<el-button v-auths="['system:user:add']" size="default" type="primary" class="ml5"
									@click="onOpenAddUser('add')">
									<el-icon><ele-Plus /></el-icon>
									新增
								</el-button>
								<el-button v-auths="['system:user:edit']" size="default" type="success" class="ml10"
									:disabled="single" @click="onOpenEditUser('edit', undefined)">
									<el-icon><ele-EditPen /></el-icon>
									修改
								</el-button>
								<el-button v-auths="['system:user:remove']" size="default" type="danger" class="ml10"
									:disabled="multiple" @click="onRowDel">
									<el-icon><ele-DeleteFilled /></el-icon>
									删除
								</el-button>
								<el-button v-auths="['system:user:import']" size="default" type="info" class="ml10"
									@click="handleImport">
									<el-icon><ele-Upload /></el-icon>
									导入
								</el-button>
								<el-button v-auths="['system:user:export']" size="default" type="warning" class="ml10"
									@click="handleExport">
									<el-icon><ele-Download /></el-icon>
									导出
								</el-button>
							</div>
							<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
								@queryTable="getTableData" :columns="columns"></right-toolbar>
						</el-row>
					</div>
					<el-table :data="state.tableData.data" v-loading="state.tableData.loading"
						@selection-change="handleSelectionChange" border style="width: 100%"
						:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
						<el-table-column type="selection" />
						<el-table-column prop="userId" label="用户编号" align="center" v-if="columns[0].visible" />
						<el-table-column prop="userName" label="用户名称" align="center" v-if="columns[1].visible"
							show-overflow-tooltip></el-table-column>
						<el-table-column prop="nickName" label="用户昵称" align="center" v-if="columns[2].visible"
							show-overflow-tooltip></el-table-column>
						<el-table-column prop="dept.deptName" label="部门" align="center" v-if="columns[3].visible"
							show-overflow-tooltip></el-table-column>
						<el-table-column prop="phonenumber" label="手机号码" align="center" v-if="columns[4].visible"
							show-overflow-tooltip></el-table-column>
						<!-- <el-table-column prop="roleSign" label="关联角色" show-overflow-tooltip></el-table-column> -->
						<!-- <el-table-column prop="email" label="邮箱" align="center" show-overflow-tooltip></el-table-column> -->
						<el-table-column prop="status" label="用户状态" align="center" show-overflow-tooltip width="150"
							v-if="columns[5].visible">
							<template #default="scope">
								<el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
									@change="handleStatusChange(scope.row)">
								</el-switch>
							</template>
						</el-table-column>
						<!-- <el-table-column prop="describe" label="用户描述" show-overflow-tooltip></el-table-column> -->
						<el-table-column prop="createTime" label="创建时间" align="center" show-overflow-tooltip
							v-if="columns[6].visible"></el-table-column>
						<el-table-column label="操作" width="220" align="center">
							<template #default="scope">
								<div v-if="scope.row.userId !== 1">
									<el-button v-auths="['system:user:edit']" :disabled="scope.row.userId == 1"
										class="ml15" text type="primary"
										@click="onOpenEditUser('edit', scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
									<el-button v-auths="['system:user:remove']" :disabled="scope.row.userId == 1"
										class="ml15" text type="primary"
										@click="onRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
									<el-dropdown v-auths="['system:user:resetPwd', 'system:user:edit']"
										@command="(command: any) => handleCommand(command, scope.row)">
										<el-button text type="primary" class="ml15"
											:disabled="scope.row.userId == 1"><el-icon><ele-DArrowRight /></el-icon>更多</el-button>
										<template #dropdown>
											<el-dropdown-menu>
												<div v-auths="['system:user:resetPwd']">
												<el-dropdown-item 
													command="handleResetPwd"><el-icon><ele-Key /></el-icon>重置密码</el-dropdown-item>
												</div>
												<div v-auths="['system:user:edit']">
												<el-dropdown-item 
													command="handleAuthRole"><el-icon><ele-CircleCheck /></el-icon>分配角色</el-dropdown-item>
												</div>
											</el-dropdown-menu>
										</template>
									</el-dropdown>
									<!-- <el-dropdown size="small" @command="(command: any) => handleCommand(command, scope.row)"
									>
									<el-button size="small" type="text">更多</el-button>
									<el-dropdown-menu #dropdown>
										<el-dropdown-item command="handleResetPwd" icon="el-icon-key"
											>重置密码</el-dropdown-item>
										<el-dropdown-item command="handleAuthRole" icon="el-icon-circle-check"
											>分配角色</el-dropdown-item>
									</el-dropdown-menu>
								</el-dropdown> -->
								</div>
							</template>
						</el-table-column>

					</el-table>
				</el-col>
			</el-row>
			<!-- 用户导入对话框 -->
			<el-dialog :title="upload.title" v-model="dialogVisible" width="400px" append-to-body
				style="position: absolute; top: 150px">
				<el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
					:action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
					:on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
					<i><el-icon :size="70" color="#c0c4cc"><ele-UploadFilled /></el-icon></i>
					<div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
				</el-upload>
				<div style="text-align: center;" slot="tip">
					<div class="el-upload__tip" slot="tip">
						<el-checkbox v-model="upload.updateSupport" /> 是否更新已经存在的用户数据
					</div>
					<span class="el-upload__tip">仅允许导入xls、xlsx格式文件。</span>
					<el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
						@click="importTemplate">下载模板</el-link>
				</div>
				<div slot="footer" style="display: flex; justify-content: flex-end;margin-top: 20px;">
					<el-button type="primary" @click="submitFileForm">确 定</el-button>
					<el-button @click="dialogVisible = false">取 消</el-button>
				</div>
			</el-dialog>
			<!-- 分页 -->
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum" background
				v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total">
			</el-pagination>
		</el-card>
		<UserDialog ref="userDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemUser">

import { defineAsyncComponent, reactive, onMounted, ref, toRefs, watch } from 'vue';
import { ElMessageBox, ElMessage, ElUpload } from 'element-plus';
import { ElTree } from 'element-plus'; // 引入 ElTree 类型
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from "/@/api/system/user";
import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
import router from '/@/router';
import { addDateRange } from '/@/utils/next';
import { Session } from '/@/utils/storage';
import { download } from '/@/utils/request';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const UserDialog = defineAsyncComponent(() => import('/@/views/system/user/dialog.vue'));
interface statusOption {
	dictValue: string;
	dictLabel: string;
}


// 定义变量内容
const userDialogRef = ref();
const state = reactive<SysUserState>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
			userName: undefined,
			phonenumber: undefined,
			status: undefined,
			deptId: undefined
		},
	},
});
const deptOptions = reactive({ data: [] }); //用户列表
const deptName = ref()   //树形空间名称
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
const dateRange = ref<[string, string]>(['', '']); //时间范围
const columns = ref([
	{ key: 0, label: `用户编号`, visible: true },
	{ key: 1, label: `用户名称`, visible: true },
	{ key: 2, label: `用户昵称`, visible: true },
	{ key: 3, label: `部门`, visible: true },
	{ key: 4, label: `手机号码`, visible: true },
	{ key: 5, label: `状态`, visible: true },
	{ key: 6, label: `创建时间`, visible: true }
],)
// 创建对树的引用
const tree = ref<InstanceType<typeof ElTree> | null>(null);  // 明确类型注解
const defaultProps = reactive({
	children: "children",
	label: "label"
});
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

const ids = ref() //userId
// 导出数据
const upload = reactive({
	title: '',
	isUploading: false,
	updateSupport: false,
	headers: {
		Authorization: `Bearer ${Session.get('token')}`,
	},
	url: `${import.meta.env.VITE_API_URL}system/user/importData`,
});
const dialogVisible = ref(false)
const uploadRef = ref<InstanceType<typeof ElUpload> | null>(null);
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const data = addDateRange(state.tableData.param, dateRange.value)
		const response = await listUser(data);
		state.tableData.data = response.data.rows;
		state.tableData.total = response.data.total;
	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally {
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	}
};
// 筛选节点
const filterNode = (value: string, data: TreeNodeData) => {
	if (!value) return true;
	return data.label.indexOf(value) !== -1;
}
/** 查询部门下拉树结构 */
const getDeptTree = async () => {
	try {
		const response = await deptTreeSelect();
		deptOptions.data = response.data.data;
	} catch (error) {
		console.error('Error fetching department tree:', error);  // 错误处理
	}
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 树形控件节点单击事件
const handleNodeClick = (data: { id: any; }) => {
	state.tableData.param.deptId = data.id;
	handleQuery();
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
	ids.value = selection.map((item: { userId: string; }) => item.userId);
	single.value = selection.length != 1;
	multiple.value = !selection.length;
}
/** 搜索按钮操作 */
const handleQuery = () => {
	state.tableData.param.pageNum = 1;
	getTableData();
}
// 重置按钮
const resetQuery = () => {
	state.tableData.param = {
		pageNum: 1,
		pageSize: 10,
		userName: undefined,
		phonenumber: undefined,
		status: undefined,
		deptId: undefined
	}
	dateRange.value = ['', '']
	getTableData();
}
// 更改用户状态
const handleStatusChange = (row: any) => {
	let text = row.status === "0" ? "启用" : "停用";
	ElMessageBox.confirm(`确认要${text}"${row.userName}"用户吗？，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			return changeUserStatus(row.userId, row.status).then(() => {
				getTableData();
				ElMessage.success('修改成功');
			});
		})
		.catch((err) => {
			row.status = row.status === "0" ? "1" : "0";
		});
};
// 更多操作触发
const handleCommand = (command: any, row: any) => {
	switch (command) {
		case "handleResetPwd":
			handleResetPwd(row);
			break;
		case "handleAuthRole":
			handleAuthRole(row);
			break;
		default:
			break;
	}
}
/** 重置密码按钮操作 */
const handleResetPwd = async (row: {
	[x: string]: any;
}) => {
	try {
		const { value: password } = await ElMessageBox.prompt(
			'请输入 "' + '用户' + '" 的新密码', '提示',
			{
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				inputPattern: /^.{5,20}$/, // 正则表达式：密码长度5到20
				inputErrorMessage: '用户密码长度必须介于 5 和 20 之间',
				closeOnClickModal: false,
				showInput: true
			}
		);
		// 手动验证输入的密码是否符合要求
		if (!/^.{5,20}$/.test(password)) {
			ElMessage.error('密码长度不符合要求，必须在5到20个字符之间');
			return;
		}
		resetUserPwd(row.userId, password).then((response: { data: any; }) => {
			console.log("修改成功，新密码是：" + password, response.data);
		}).catch(() => { })
		// 如果输入合法，可以继续执行后续操作
		ElMessage.success('密码修改成功');
	} catch (error) {
		// 用户点击取消或者关闭弹框时
		if (error === 'cancel') {
			ElMessage.info('取消修改');
		}
	}
}
/** 分配角色操作 */
const handleAuthRole = (row: any) => {
	const userId = row.userId;
	router.push("/system/user-auth/role/" + userId);
}
// 打开新增用户弹窗
const onOpenAddUser = (type: string) => {
	userDialogRef.value.openDialog(type);
};
// 打开修改用户弹窗
const onOpenEditUser = (type: string, row: RowUserType | undefined) => {
	// this.reset();
	var userId = ''
	if (!row) {
		userId = ids.value
	} else {
		userId = row.userId
	}
	userDialogRef.value.openDialog(type, row, userId);
};
// 删除用户
const onRowDel = (row: RowUserType) => {
	const userIds = row.userId || ids.value;
	ElMessageBox.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			delUser(userIds).then(res => {
				getTableData();
				ElMessage.success('删除成功');
			})

		})
		.catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
	const exportParams = {
		pageNum: state.tableData.param.pageNum || 1,
		pageSize: state.tableData.param.pageSize || 10,
		userName: state.tableData.param.userName || '',
		phonenumber: state.tableData.param.phonenumber || '',
		status: state.tableData.param.status || '',
		deptId: state.tableData.param.deptId || ''
	};
	download('system/user/export', {
		...state.tableData.param
	}, `user_${new Date().getTime()}.xlsx`)
}
/** 导入按钮操作 */
const handleImport = () => {
	upload.title = "用户导入";
	dialogVisible.value = true;

}
/** 下载模板操作 */
const importTemplate = () => {
	download('system/user/importTemplate', {
	}, `user_template_${new Date().getTime()}.xlsx`)
}
// 文件上传中处理
// 处理文件上传进度
const handleFileUploadProgress = (event: any, file: any, fileList: any) => {
	// 可以在这里更新上传进度
	upload.isUploading = true;
};
// 文件上传成功处理
const handleFileSuccess = (response: any, file: any, fileList: any) => {
	upload.isUploading = false;
	dialogVisible.value = false; // 上传成功后关闭对话框
	// 清除文件列表
	if (uploadRef.value) {
		uploadRef.value.clearFiles();
	}
	// 显示上传结果
	ElMessageBox.alert(
		`<div style='overflow: auto; overflow-x: hidden; max-height: 70vh; padding: 10px 20px 0;'>${response.msg}</div>`,
		'导入结果',
		{ dangerouslyUseHTMLString: true }
	);
	getTableData()
};
// 提交上传文件
const submitFileForm = () => {
	// 提交文件
	if (uploadRef.value) {
		uploadRef.value.submit();
	}
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
}
// 监听 deptName 的变化
watch(deptName, (val) => {
	// 确保树组件已挂载
	if (tree.value) {
		tree.value.filter(val);  // 调用树组件的 filter 方法
	}
});
// 页面加载时
onMounted(() => {
	getTableData()
	getDeptTree()
	getdictdata()
});
</script>

<style scoped lang="scss">
.system-user-container {
	:deep(.el-card__body) {
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;

		.el-table {
			flex: 1;
		}
	}
}
</style>
