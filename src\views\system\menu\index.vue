<template>
	<div class="system-menu-container layout-pd">
		<el-card shadow="hover">
			<div class="system-menu-search mb15">
				<el-form :model="queryParams" ref="queryForm" size="default" :inline="true" v-show="showSearch">
					<el-form-item label="菜单名称" prop="menuName">
						<el-input v-model="queryParams.menuName" placeholder="请输入菜单名称" clearable />
					</el-form-item>
					<el-form-item label="状态">
						<el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button size="default" type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:menu:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddMenu('add')">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button size="default" type="info" class="ml10" @click="toggleExpandAll">
							<el-icon><ele-Sort /></el-icon>
							展开/折叠
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table v-if="refreshTable" :data="state.tableData.data" v-loading="state.tableData.loading"
				style="width: 100%" row-key="menuId" :default-expand-all="isExpandAll" border
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column prop="menuName" label="菜单名称" :show-overflow-tooltip="true" width="140"
					align="center"></el-table-column>
				<el-table-column prop="icon" label="图标" width="100" align="center">
					<template #default="scope">
						<SvgIcon :name="scope.row.icon" :type="'menu'" :color="''" />
					</template>
				</el-table-column>
				<el-table-column prop="orderNum" label="排序" width="60" align="center"></el-table-column>
				<el-table-column prop="perms" label="权限标识" :show-overflow-tooltip="true"
					align="center"></el-table-column>
				<!-- <el-table-column prop="path" label="路由路径" show-overflow-tooltip></el-table-column> -->
				<!-- <el-table-column label="组件路径" show-overflow-tooltip>
					<template #default="scope">
						<span>{{ scope.row.component }}</span>
					</template>
				</el-table-column> -->
				<el-table-column prop="component" label="组件路径" :show-overflow-tooltip="true" align="center"
					width="200"></el-table-column>
				<el-table-column prop="status" label="状态" width="100" align="center">
					<template #default="scope">
						<DictTag :options="statuslist" :value="scope.row.status"></DictTag>
						<!-- <el-tag :class="scope.row.status == 0?'primary-tag':''" :type="scope.row.status == 0?'success':'danger'">{{ scope.row.status == 0 ? '正常' : '停用'
							}}</el-tag>	 -->
					</template>
				</el-table-column>
				<el-table-column label="创建时间" prop="createTime" align="center" width="200">
					<template #default="scope">
						<span>{{ scope.row.createTime }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" show-overflow-tooltip align="center"  class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button v-auths="['system:menu:add']" size="default" text type="primary"
							@click="onOpenAddMenu('add',scope.row)"><el-icon><ele-Plus /></el-icon>新增</el-button>
						<el-button class="ml15" v-auths="['system:menu:edit']" size="default" text type="primary"
							@click="onOpenEditMenu('edit',scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
						<el-button class="ml15" v-auths="['system:menu:remove']" size="default" text type="primary"
							@click="onTabelRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-card>
		<MenuDialog ref="menuDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemMenu">
import { defineAsyncComponent, ref, onMounted, reactive, defineComponent, nextTick } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { useRoutesList } from '/@/stores/routesList';
import { listMenu, getMenu, delMenu, addMenu, updateMenu } from "/@/api/system/menu";
import { handleTree } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store

// import { setBackEndControlRefreshRoutes } from "/@/router/backEnd";

// 引入组件
const MenuDialog = defineAsyncComponent(() => import('/@/views/system/menu/dialog.vue'));
interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass:string;
	cssClass:string;
}
// 定义变量内容
const stores = useRoutesList();
const { routesList } = storeToRefs(stores);
const menuDialogRef = ref();
const state = reactive({
	tableData: {
		data: [] as any,
		loading: true,
	},
});
// 搜索内容
let queryParams = reactive({
	menuName: undefined,
	status: ''
});
// 状态列表
const statuslist = ref<statusOption[]>([]);
// 是否展开，默认全部折叠
const isExpandAll = ref(false)
// 重新渲染表格状态
const refreshTable = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 获取路由数据，真实请从接口获取
const getTableData = () => {
	state.tableData.loading = true;
	listMenu(queryParams).then(response => {
		state.tableData.data = handleTree(response.data.data, "menuId");
		// state.tableData.loading = false;
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	});
	// state.tableData.data = routesList.value;
};
/** 重置按钮操作 */
const resetQuery = () => {
	queryParams.menuName = undefined
	queryParams.status = ''
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
/** 展开/折叠操作 */
const toggleExpandAll = () => {
	refreshTable.value = false;
	isExpandAll.value = !isExpandAll.value;
	nextTick(() => {
		refreshTable.value = true;
	});
}
// 打开新增菜单弹窗
const onOpenAddMenu = (type: string,row?: RouteRecordRaw) => {
	console.log(row,'row');
	
	menuDialogRef.value.openDialog(type,row);
};
// 打开编辑菜单弹窗
const onOpenEditMenu = (type: string, row: RouteRecordRaw) => {
	menuDialogRef.value.openDialog(type, row);
};
// 删除当前行
const onTabelRowDel = (row: RouteRecordRaw) => {
	ElMessageBox.confirm(`此操作将永久删除路由：${row.path}, 是否继续?`, '提示', {
		confirmButtonText: '删除',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		// 删除角色 API 调用
		try {
			await delMenu(row.menuId);
			await getTableData();
			ElMessage.success('删除成功');
		} catch (error) {
			ElMessage.error('删除失败');
		}
	}).catch(() => {
		// 取消删除，不做任何操作
	});
};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>
