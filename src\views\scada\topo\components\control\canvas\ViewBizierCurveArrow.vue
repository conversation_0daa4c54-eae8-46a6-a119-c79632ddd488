<template>
    <div class="view-bizier-curve-arrow" @mousemove="onMousemove($event)" @mouseup="onMouseUp($event)">
        <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>

        <template v-if="editMode && selected">
            <template v-for="(pass, index) in points" :key="index">
                <div
                    class="passby"
                    @mousedown.stop="aroowPassDown(pass, $event, index)"
                    :style="{
                        left: pass.x - 5 + 'px',
                        top: pass.y - 5 + 'px',
                    }"
                ></div>
            </template>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
  editMode?: boolean;
  selected?: boolean;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();
const lineWidth = ref(2);
const flag = ref(false);
const passItem = ref<any>({});
const points = ref<any[]>([]); // 控制点（包含起始和终点）
const FACTOR_H = 5; // 箭头 水平高度倍数
const FACTOR_V = 4; // 箭头 垂直长度倍数

// Methods
const drawLine = (ctx: CanvasRenderingContext2D) => {
  const color = getForeColor();
  ctx.beginPath();
  if (points.value.length >= 4) {
    ctx.moveTo(points.value[0].x, points.value[0].y);
    ctx.bezierCurveTo(
      points.value[1].x, points.value[1].y,
      points.value[2].x, points.value[2].y,
      points.value[3].x, points.value[3].y
    );
  }
  ctx.lineWidth = lineWidth.value; // 设置线宽状态
  ctx.strokeStyle = color; // 设置线的颜色状态
  ctx.stroke(); // 进行绘制
  ctx.closePath();
};

const reDraw = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  drawLine(ctx);
};

const onResize = () => {
  let width = props.detail.style.lineWidth;
  if (width == undefined || typeof width != 'number') {
    width = 2;
  }
  lineWidth.value = width;
  reDraw();
};

const aroowPassDown = (pass: any, event: MouseEvent, index: number) => {
  flag.value = true;
  pass.startX = event.pageX;
  pass.startY = event.pageY;
  pass.temp = {};
  pass.temp.x = pass.x;
  pass.temp.y = pass.y;
  passItem.value = pass;
};

const onMousemove = (event: MouseEvent) => {
  if (!flag.value) return;
  event.cancelBubble = true;
  const dx = event.pageX - passItem.value.startX;
  const dy = event.pageY - passItem.value.startY;
  passItem.value.x = passItem.value.temp.x + dx;
  passItem.value.y = passItem.value.temp.y + dy;
  reDraw();
};

const onMouseUp = (event: MouseEvent) => {
  flag.value = false;
};

const getForeColor = () => {
  return props.detail.style.foreColor || '#000';
};

// Lifecycle
onMounted(() => {
  let width = props.detail.style.lineWidth;
  if (width == undefined) {
    width = 2;
  } else if (typeof width == 'string') {
    width = parseInt(width);
  }
  points.value = props.detail.style.points || [];
  // 增加2个中间节点，应该可以动态控制，这里暂时写死
  onResize();
});

// Expose methods for parent components
defineExpose({
  onResize,
  getForeColor
});
</script>

<style lang="scss">
.view-bizier-curve-arrow {
    height: 100%;
    width: 100%;
    position: relative;

    .passby {
        position: absolute;
        height: 10px;
        width: 10px;
        background-color: white;
        border: 1px solid rgb(34, 14, 223);
        cursor: move;
    }
}
</style>
