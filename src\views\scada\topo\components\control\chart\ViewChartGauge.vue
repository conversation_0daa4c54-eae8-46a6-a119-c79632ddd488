<template>
  <div class="view-chart-gauge" ref="chartView" :id="detail.identifier">
    <!-- 隐藏的依赖项，确保响应式更新 -->
    <div v-show="false">{{ height }}{{ width }}{{ chartsValue }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';
import { storeToRefs } from 'pinia';
import * as echarts from 'echarts';
import { useCounterStore } from '/@/stores/counterStore';

// 定义组件名称
defineOptions({
  name: 'ViewChartGauge'
});

// Props
interface Props {
  editMode?: boolean;
  selected?: boolean;
  detail: any;
}
const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  selected: false
});

// Composables
const counterStore = useCounterStore();
const { mqttData } = storeToRefs(counterStore);

// Refs
const chartView = ref<HTMLElement>();
const myChart = ref<any>(null);
const timer = ref<any>(null);
const paramValue = ref(0);
// const refreshTimer = ref<NodeJS.Timeout | null>(null);

const width = computed(() => {
  return props.detail.style?.position?.w || 200;
});

const height = computed(() => {
  return props.detail.style?.position?.h || 200;
});

// 监听尺寸变化并调整图表大小
watch([width, height], ([newWidth, newHeight], [oldWidth, oldHeight]) => {
  if (myChart.value && (newWidth !== oldWidth || newHeight !== oldHeight)) {
    nextTick(() => {
      try {
        myChart.value.resize();
      } catch (error) {
        console.error('❌ 调整仪表盘图表尺寸失败:', error);
      }
    });
  }
}, { immediate: false });

const chartsValue = computed(() => {
  // 检查 MQTT 数据绑定
  if (props.detail.dataBind?.identifier && mqttData.value && mqttData.value.serialNumber) {
    if (mqttData.value.serialNumber == props.detail.dataBind.serialNumber) {
      const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataBind.identifier);
      if (message) {
        let paramVal = message.value;
        if (isNaN(paramVal)) {
          paramVal = 0;
        }
        paramValue.value = paramVal;

        // 延迟更新图表
        nextTick(() => {
          setOption(paramVal);
        });
        return paramVal;
      } else {
        console.log('  ❌ 未找到匹配的消息');
      }
    } else {
      console.log('  ❌ 序列号不匹配');
    }
  } else {
    console.log('  ❌ 数据绑定条件不满足');
  }

  // 如果没有 MQTT 数据，返回当前的 paramValue 或默认值
  const finalValue = paramValue.value || props.detail.dataBind?.modelValue || 0;
  // 延迟更新图表
  nextTick(() => {
    setOption(finalValue);
  });

  return finalValue;
});
// Methods
const setOption = (paramValue: number) => {
  var data = [
    {
      title: props.detail.dataBind?.modelName || '仪表盘',
      sub_title: paramValue + (props.detail.dataBind?.unit || ''),
      value: paramValue,
      min: props.detail.dataBind?.paramMin || 0,
      max: props.detail.dataBind?.paramMax || 100,
      rate: 0 // 添加 rate 属性
    },
  ];
  // 不同数据长度，圆心位置
  var pos_cfg: { [key: number]: string[][] } = {
    1: [['50%', '50%']],
  };
  var data_len = data.length;
  // 获取位置信息
  var pos_info = pos_cfg[data_len] || [['50%', '50%']];
  // 圆环颜色配置
  var color_cfg = [
    [
      { offset: 0, color: 'rgba(90, 255, 163, 1)' },
      { offset: 0.5, color: 'rgba(80, 192, 255, 1)' },
      { offset: 1, color: 'rgba(102, 255, 255, 1)' },
    ],
    [
      { offset: 0, color: 'rgba(50, 197, 255, 1)' },
      { offset: 0.5, color: 'rgba(254, 219, 101, 1)' },
      { offset: 1, color: 'rgba(250, 100, 0, 1)' },
    ],
  ];
  // 渲染数据
  var series = [],
      item = null;
  for (var i in data) {
    item = data[i];
    // 处理最大值及最小值
    if (!item.min) item.min = 0;
    if (!item.max) item.max = item.value / 0.8 + Math.random() * parseInt(String(item.value * 0.2)) + 1;
    // 获取比率
    item.rate = Math.round((item.value / item.max) * 10000) / 100;
    // 拼接图表参数
    series.push(
      {
        name: '最外层',
        type: 'gauge',
        center: pos_info[i],
        radius: '95%',
        startAngle: 150,
        endAngle: -209.999,
        axisLine: {
          show: true,
          lineStyle: { width: 2, color: [[1, 'rgba(25, 235, 255,1)']] },
        },
        axisLabel: { show: false },
        axisTick: { show: false },
        splitLine: { show: false },
        detail: { show: false },
        pointer: { show: false },
      },
      {
        name: '内层渐变区',
        type: 'gauge',
        radius: '60%',
        splitNumber: 10, // 刻度数量
        center: pos_info[i],
        startAngle: 150,
        endAngle: -209.999,
        axisLine: {
          lineStyle: {
            color: [
              [
                1,
                {
                  type: 'radial',
                  colorStops: [
                    { offset: 0.72, color: '#0320462e' },
                    { offset: 0.84, color: '#08698961' },
                    { offset: 0.98, color: '#0FAFCBa6' },
                    { offset: 1, color: '#0EA4C1f0' },
                  ],
                },
              ],
            ],
            width: 1000,
          },
        },
        splitLine: { show: false }, // 分隔线
        axisTick: { show: false }, // 刻度线
        axisLabel: { show: false }, // 刻度标签
        pointer: { show: false }, // 仪表盘指针
        detail: { show: false },
      },
      {
        name: '中间层',
        type: 'gauge',
        center: pos_info[i],
        radius: '80%',
        min: item.min, // 最小刻度
        max: item.max, // 最大刻度
        splitNumber: 10, // 刻度数量
        startAngle: 245,
        endAngle: -65,
        data: [{ value: item.rate }],
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [item.rate / 100, echarts.graphic.LinearGradient(0, 1, 1, 0, color_cfg[i])],
              [1, 'rgba(50, 197, 255,.1)'],
            ],
          },
        },
        axisLabel: { show: true, color: props.detail.style.foreColor, distance: 32, textStyle: { fontSize: props.detail.style.fontSize - 4 } },
        axisTick: { show: true, length: -5, distance: -10, lineStyle: { color: 'rgba(25, 235, 255, 1)' } },
        splitLine: { show: true, length: -10, distance: -10, lineStyle: { width: 1, color: 'rgba(25, 235, 255, 1)' } },
        detail: {
          offsetCenter: [0, '-5%'],
          textStyle: { fontSize: props.detail.style.fontSize, color: props.detail.style.foreColor },
          formatter: [item.title, '{name|' + item.sub_title + '}'].join('\n'),
          rich: { name: { fontSize: props.detail.style.fontSize + 4, lineHeight: 18, color: props.detail.style.foreColor, fontWeight: '600' } },
        },
        title: { color: props.detail.style.foreColor },
        pointer: { show: false },
      }
    );
  }
  var option = {
    series: series,
  };
  if (myChart.value) {
    myChart.value.setOption(option);
  }
};

// Lifecycle
onMounted(() => {
  myChart.value = echarts.init(document.getElementById(props.detail.identifier));
  setOption(0);
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (myChart.value) {
    myChart.value.dispose();
  }
});
</script>

<style lang="scss">
.view-chart-gauge {
    height: 100%;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
