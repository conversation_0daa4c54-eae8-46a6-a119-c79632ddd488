<template>
	<div class="system-dept-dialog-container">
		<el-dialog style="position: absolute; top: 100px; padding: 10px 10px" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" width="600px">
			<el-form style="margin: 20px 20px;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
				size="default" label-width="100px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="岗位名称" prop="postName">
							<el-input v-model="state.ruleForm.postName" placeholder="请输入部门名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="岗位编码" prop="postCode">
							<el-input v-model="state.ruleForm.postCode" placeholder="请输入负责人" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="18" :md="18" :lg="18" :xl="18" class="mb20">
						<el-form-item label="显示排序" prop="postSort">
							<el-input-number v-model="state.ruleForm.postSort" :min="0" :max="999"
								controls-position="right" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="岗位状态">
							<el-radio-group v-model="state.ruleForm.status">
								<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue">{{
									item.dictLabel
								}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入部门描述"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
						state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemDeptDialog">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { addPost, getPost, updatePost } from '/@/api/system/post';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
// 原始数据存储
const initialState = {
	ruleForm: {
		postName: '', // 岗位名称
		postCode: '', // 岗位编码
		postSort: undefined, // 岗位顺序
		status: '0', // 岗位状态
		remark: '',//备注
		postId: ''//部门id

	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
}
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	// deptData: [...initialState.deptData],
	dialog: { ...initialState.dialog },
});
interface statusOption {
	dictValue: string;
	dictLabel: string;
}
const statuslist = ref<statusOption[]>([]); //状态
// 校验规则
const rules = reactive({
	postName: [
		{ required: true, message: "岗位名称不能为空", trigger: "blur" }
	],
	postCode: [
		{ required: true, message: "岗位编码不能为空", trigger: "blur" }
	],
	postSort: [
		{ required: true, message: "岗位顺序不能为空", trigger: "blur" }
	]

})
// 打开弹窗
const openDialog = (type: string, row: any, postId: string) => {
	if (type === 'edit') {
		if (row) {
			getPost(row.postId).then(response => {
				state.ruleForm = response.data.data;
			});
		} else {
			getPost(postId).then(response => {
				state.ruleForm = response.data.data;
			});
		}

		state.dialog.title = '修改岗位';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();
		state.dialog.title = '添加岗位';
		state.dialog.submitTxt = '新 增';
	}
	state.dialog.isShowDialog = true;
	getDeptData();
	getdictdata()
};
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm }
	// state.deptData = [...initialState.deptData]
	state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.postId != '') {
				updatePost(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				// const data = addDateRange(state.ruleForm)
				// console.log(data,'data');
				// return
				addPost(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
	// if (state.dialog.type === 'add') { }
};

// 初始化部门数据
const getDeptData = () => {
	// listDept(queryParams).then(response => {
	// 	state.deptData = handleTree(response.data.data, "deptId");
	// 	console.log(state.deptData);

	// });
};
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
