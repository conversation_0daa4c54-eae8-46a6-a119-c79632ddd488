<template>
	<div class="system-user-dialog-container">
		<el-dialog :title="state.dialog.title" v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="DialogFormRef" :model="state.ruleForm" size="default" label-width="90px" :rules="rules">
				<el-row :gutter="35">
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="账户名称">
							<el-input v-model="state.ruleForm.username" placeholder="请输入账户名称" clearable></el-input>
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="用户昵称" prop="nickName">
							<el-input v-model="state.ruleForm.nickName" placeholder="请输入用户昵称" clearable></el-input>
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="关联角色">
							<el-select v-model="state.ruleForm.roleSign" placeholder="请选择" clearable class="w100">
								<el-option label="超级管理员" value="admin"></el-option>
								<el-option label="普通用户" value="common"></el-option>
							</el-select>
						</el-form-item>
					</el-col> -->
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="归属部门" prop="deptId">
							<el-tree-select v-model="state.ruleForm.deptId" :props="cascaderProps"
								:data="state.deptData" :render-after-expand="true" style="width: 300"
								:show-count="true" @node-click="handleNodeClick">
								<template #default="{ node, data }">
									<span>{{ data.label }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-tree-select>
							<!-- {{ state.ruleForm.deptId }} -->
							<!-- <el-cascader :show-all-levels="false" v-model="state.ruleForm.deptId" :options="state.deptData"
								:props="cascaderProps" placeholder="请选择部门" clearable class="w100"
								@change="cascaderChange">
								<template #default="{ node, data }">
									<span>{{ data.label }}</span>
								</template>
							</el-cascader> -->
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="手机号码" prop="phonenumber">
							<el-input v-model="state.ruleForm.phonenumber" placeholder="请输入手机号" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="邮箱" prop="email">
							<el-input v-model="state.ruleForm.email" placeholder="请输入邮箱" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item v-if="state.ruleForm.userId == undefined" label="用户名称" prop="userName">
							<el-input v-model="state.ruleForm.userName" placeholder="请输入用户名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item v-if="state.ruleForm.userId == undefined" label="用户密码" prop="password">
							<el-input v-model="state.ruleForm.password" placeholder="请输入用户密码" type="password"
								maxlength="20" show-password />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="用户性别">
							<el-select v-model="state.ruleForm.sex" placeholder="请选择" clearable class="w100">
								<el-option v-for="item in sexlist" :key="item.dictValue" :label="item.dictLabel"
									:value="item.dictValue"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="状态">
							<el-radio-group v-model="state.ruleForm.status">
								<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue">{{
										item.dictLabel
									}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="岗位">
							<el-select v-model="state.ruleForm.postIds" multiple placeholder="请选择岗位">
								<el-option v-for="item in postOptions" :key="item.postId" :label="item.postName"
									:value="item.postId" :disabled="item.status == 1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="角色">
							<el-select v-model="state.ruleForm.roleIds" multiple placeholder="请选择角色">
								<el-option v-for="item in roleOptions" :key="item.roleId" :label="item.roleName"
									:value="item.roleId" :disabled="item.status == 1"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注">
							<el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入用户描述"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{ state.dialog.submitTxt
						}}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemUserDialog">
import { ElMessage, FormInstance } from 'element-plus';
import { reactive, ref, getCurrentInstance } from 'vue';
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect } from "/@/api/system/user";
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store


// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const DialogFormRef = ref<FormInstance>()

// 定义变量内容
interface PostOption {
	postId: number;
	postName: string;
	status: number;
}

interface RoleOption {
	roleId: number;
	roleName: string;
	status: number;
}
interface sexOption {
	dictValue: string;
	dictLabel: string;
}
interface statusOption {
	dictValue: string;
	dictLabel: string;
}
// 原始数据存储
const initialState = {
	ruleForm: {
		userId: undefined as string | undefined,
		userName: '', // 账户名称
		nickName: '', // 用户昵称
		deptId: '', // 部门
		roleIds: [], // 角色
		phonenumber: '', // 手机号
		email: '', // 邮箱
		sex: '', // 性别
		password: '', // 账户密码
		status: '0', // 用户状态
		remark: '', // 用户备注
		postIds: [], // 岗位选项
	},
	deptData: [] as DeptTreeType[], // 部门数据
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
};

// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	deptData: [...initialState.deptData],
	dialog: { ...initialState.dialog },
});
const postOptions = ref<PostOption[]>([]);
const roleOptions = ref<RoleOption[]>([]);
const sexlist = ref<sexOption[]>([]);
const statuslist = ref<statusOption[]>([]);

// let postOptions: Post[] = []; // 确保是 Post 类型的数组

// let roleOptions: Post[] = []
const rules = reactive({
	userName: [
		{ required: true, message: "用户名称不能为空", trigger: "blur" },
		{ min: 2, max: 20, message: '用户名称长度必须介于 2 和 20 之间', trigger: 'blur' }
	],
	nickName: [
		{ required: true, message: "用户昵称不能为空", trigger: "blur" }
	],
	password: [
		{ required: true, message: "用户密码不能为空", trigger: "blur" },
		{ min: 5, max: 20, message: '用户密码长度必须介于 5 和 20 之间', trigger: 'blur' }
	],
	email: [
		{
			type: "email",
			message: "请输入正确的邮箱地址",
			trigger: ["blur", "change"]
		}
	],
	phonenumber: [
		{
			pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
			message: "请输入正确的手机号码",
			trigger: "blur"
		}
	]
})
// 点击节点时更新父节点
const handleNodeClick = (node: any, data: any) => {	
	// 判断是否是父节点
	// if (!node.isLeaf) {
	// 如果是父节点，更新模型值
	state.ruleForm.deptId = data.data.id;
};
// 定义 cascaderProps 来指定如何提取每个选项的值和标签
const cascaderProps = {
	value: 'id',
	label: 'label',
	children: 'children',
};
// 选择岗位 绑定deptId
const cascaderChange = (value: string | any[]) => {
	state.ruleForm.deptId = value[value.length - 1]
};

// 打开弹窗
const openDialog = async (type: string, row: RowUserType, userId: string) => {
	if (type === 'edit') {
		if (!row) {
			state.ruleForm.userId = userId
		} else {
			state.ruleForm = row;
		}
		state.dialog.title = '修改用户';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();
		state.dialog.title = '新增用户';
		state.dialog.submitTxt = '新 增';
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	userDialogFormRef.value.resetFields();
		// });
	}
	state.dialog.isShowDialog = true;
	getMenuData();
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.userId != undefined) {
				updateUser(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				addUser(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})

	// if (state.dialog.type === 'add') { }
};
// 获取性别和状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		sexlist.value =  await dictStore.fetchDict('sys_user_sex')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 重置方法
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm };
	state.deptData = [...initialState.deptData];
	state.dialog = { ...initialState.dialog };
};
// 初始化部门数据和岗位数据
const getMenuData = async () => {
	getdictdata()
	deptTreeSelect().then(response => {
		state.deptData = response.data.data
	});
	if (state.ruleForm.userId != undefined) {
		getUser(state.ruleForm.userId).then(response => {
			state.ruleForm = response.data.data
			state.ruleForm.postIds = response.data.postIds
			state.ruleForm.roleIds = response.data.roleIds
			postOptions.value = response.data.posts || [];
			roleOptions.value = response.data.roles || [];
			state.ruleForm.password = "";
		});
	} else {
		getUser(state.ruleForm.userId).then(response => {
			postOptions.value = response.data.posts || [];
			roleOptions.value = response.data.roles || [];
		});
	}


};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
