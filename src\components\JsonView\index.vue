<template>
  <div class="json-view-wrap">
    <div class="json-placeholder">...</div>
    <button class="copy-button" @click="copyJson">复制</button>
  </div>
</template>

<script setup>
import { computed} from 'vue'
import {useClipboard} from '@vueuse/core'

const props = defineProps({
  data: {
    type: [Object, Array, String, Number, Boolean],
    required: true
  }
})

const prettyJson = computed(() => {
  return JSON.stringify(props.data, null, 2)
})

const {copy} = useClipboard()

const copyJson = () => {
  copy(prettyJson.value)
}
</script>

<style scoped>
.json-view-wrap {
  position: relative;
  background-color: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.json-placeholder {
  font-size: 24px;
  color: #999;
  font-weight: bold;
  letter-spacing: 2px;
}

.copy-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: transparent;
  border: none;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 3px;
  transition: all 0.3s;
}

.copy-button:hover {
  background-color: #409eff;
  color: white;
}
</style>