import request from '/@/utils/request'

// 查询设备服务下发日志列表
export function listLog(query: any) {
  return request({
    url: '/iot/log/list',
    method: 'get',
    params: query
  })
}

// 查询设备服务下发日志详细
export function getLog(id: string) {
  return request({
    url: '/iot/log/' + id,
    method: 'get'
  })
}

// 新增设备服务下发日志
export function addLog(data: any) {
  return request({
    url: '/iot/log',
    method: 'post',
    data: data
  })
}

// 修改设备服务下发日志
export function updateLog(data: any) {
  return request({
    url: '/iot/log',
    method: 'put',
    data: data
  })
}

// 删除设备服务下发日志
export function delLog(id: string) {
  return request({
    url: '/iot/log/' + id,
    method: 'delete'
  })
}
