import request from '/@/utils/request'

// 定义 query 类型，假设它是一个对象
interface QueryParams {
  [key: string]: any;  // 你可以根据实际情况进行更精确的定义
}

// 查询在线用户列表
export function list(query: QueryParams) {
  return request({
    url: '/monitor/online/list',
    method: 'get',
    params: query
  })
}

// 强退用户
export function forceLogout(tokenId: string) {
  return request({
    url: '/monitor/online/' + tokenId,
    method: 'delete'
  })
}
