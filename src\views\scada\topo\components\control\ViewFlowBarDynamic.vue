<template>
  <div>
    <svg class="svgBgClass">
      <polyline
          :points="points"
          :style="{
                    fill: 'none',
                    stroke: props.detail.style.backColor,
                    strokeWidth: props.detail.style.lineHeight,
                    strokeLinecap: props.detail.style.lineType,
                    strokeLinejoin: props.detail.style.lineType,
                    'fill-rule': props.detail.style.lineType,
                }"
      />
    </svg>
    <svg class="svgClass">
      <polyline
          :id="props.detail.identifier"
          :points="points"
          :stroke-dasharray="props.detail.style.lineWidth + ' ' + props.detail.style.lineInterval"
          :style="{
                    fill: 'none',
                    stroke: props.detail.style.foreColor,
                    strokeWidth: props.detail.style.lineHeight,
                    strokeLinecap: props.detail.style.lineType,
                    strokeLinejoin: props.detail.style.lineType,
                    'fill-rule': props.detail.style.lineType,
                }"
      />
    </svg>
    <div class="view-line-arrow" @mousemove="onMousemove" @mouseup="onMouseUp">
      <canvas ref="elCanvas" :width="props.detail.style.position.w" :height="props.detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>

      <template v-if="props.editMode && props.selected">
        <template v-for="(pass, passIndex) in props.detail.style.spotPoints" :key="passIndex">
          <div
              class="passby"
              @mousedown.stop="(event) => aroowPassDown(pass, event)"
              :style="{
                            left: pass.x - 5 + 'px',
                            top: pass.y - 5 + 'px',
                        }"
          ></div>
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useCounterStore } from '/@/stores/counterStore';
import topoUtil from '/@/utils/topo/topo-util';

// Props
interface Props {
  detail: any;
  editMode?: boolean;
  selected?: boolean;
}
const props = defineProps<Props>();

// Store
const counterStore = useCounterStore();

// Refs
const elCanvas = ref<HTMLCanvasElement>();
const direction = ref('水平');
const lineWidth = ref(10);
const flag = ref(false);
const passItem = ref<any>({});
const points = ref<string>('');
const width = ref(0);
const height = ref(0);
const FACTOR_H = ref(0); // 箭头 水平高度倍数
const FACTOR_V = ref(0); // 箭头 垂直长度倍数
// Methods (需要在 computed 之前定义)
// 更新 SVG 路径点
const updatePoints = () => {
  if (props.detail.style.spotPoints) {
    let spotPoints = props.detail.style.spotPoints;
    let horizontalPoints: string[] = [];
    spotPoints.forEach((element: any) => {
      horizontalPoints.push(element.x + ',' + element.y);
    });
    const newPoints = horizontalPoints.join(' ');
    if (points.value !== newPoints) {
      points.value = newPoints;
      console.log('更新路径点:', points.value);
    }
  }
};

// Computed
const mqttData = computed(() => counterStore.mqttData);

const dataInit = computed(() => {
  // 当 spotPoints 变化时，更新 points
  updatePoints();
  return props.detail.style.spotPoints;
});

const anchorPointNumInit = computed(() => {
  return props.detail.style.anchorPointNum;
});

const animations = computed(() => {
  return props.detail.style.animations + '-' + props.detail.style.speed;
});

const dashoffset = computed(() => {
  return props.detail.style.lineWidth + props.detail.style.lineInterval;
});
// Watch
watch(anchorPointNumInit, (newVal) => {
  if (newVal) {
    props.detail.style.spotPoints = [];
    for (let i = 0; i < newVal; i++) {
      let data = {
        x: 20 + 50 * i,
        y: 20,
      };
      props.detail.style.spotPoints.push(data);
    }
  }
});

watch(dataInit, () => {
  // 触发重新计算点位
});

// 监听 spotPoints 的深度变化
watch(() => props.detail.style.spotPoints, () => {
  updatePoints();
}, { deep: true });

watch(animations, (newVal) => {
  goggle(newVal, props.detail.style.lineWidth + props.detail.style.lineInterval, randomStr());
});

watch(dashoffset, (newVal) => {
  goggle(props.detail.style.animations + '-' + props.detail.style.speed, newVal, randomStr());
});

watch(mqttData, (newVal, oldVal) => {
  // 流动条
  if (props.detail.dataBind.identifier && newVal && newVal.serialNumber == props.detail.dataBind.serialNumber) {
    let val = '';
    const message = newVal.message?.find((item: any) => item.id === props.detail.dataBind.identifier);
    if (message) {
      if (oldVal && Object.keys(oldVal).length !== 0 && newVal.message[0]?.value === oldVal.message[0]?.value) {
        // console.log("数据未变化无需更新流动条");
      } else {
        val = newVal.message[0]?.value;
        const numVal = Number(val);
      // 条件判断一
        let isGd = topoUtil.judgeSize(props.detail.dataAction.paramJudge, numVal, props.detail.dataAction.paramJudgeData);
      // 条件判断二
        let isGd01 = topoUtil.judgeSize(props.detail.dataAction.paramJudge01, numVal, props.detail.dataAction.paramJudgeData01);
      if (isGd) {
          goggle(props.detail.dataAction.direction + '-' + props.detail.style.speed);
      } else if (isGd01) {
          goggle(props.detail.dataAction.direction01 + '-' + props.detail.style.speed);
      } else {
        goggle('停止-中');
      }
    }
  }
  }
});
// Methods
const randomStr = () => {
  return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
};
const goggle = (newVal: string, dashoffsetParam?: number, randomStrParam?: string) => {
  let element = document.getElementById(props.detail.identifier);
  if (!element) {
    console.warn('找不到动画元素:', props.detail.identifier);
    return;
  }

  let idSub = props.detail.identifier.substring(0, 8);
  let shineForward = '';
  let shineReverse = '';
  let dashoffsetValue = dashoffsetParam;

  if (!dashoffsetValue) {
    dashoffsetValue = props.detail.style.lineWidth + props.detail.style.lineInterval;
  }

  console.log('应用动画:', newVal, '偏移量:', dashoffsetValue, '随机字符串:', randomStrParam);

  let val = newVal.split('-');
  if (val[0] == '正向') {
    if (val[1] == '快') {
      if (randomStrParam) {
        element.style.animation = 'shine-forward-' + randomStrParam + ' 0.15s infinite linear';
        shineForward = '@keyframes shine-forward-' + randomStrParam + ' {0% {stroke-dashoffset: ' + dashoffsetValue + 'px;}100% {stroke-dashoffset: 0px;}}';
        insertRule(shineForward);
      } else {
        element.style.animation = 'shine-forward-' + idSub + ' 0.15s infinite linear';
      }
    } else if (val[1] == '中') {
      if (randomStrParam) {
        element.style.animation = 'shine-forward-' + randomStrParam + ' 0.3s infinite linear';
        shineForward = '@keyframes shine-forward-' + randomStrParam + ' {0% {stroke-dashoffset: ' + dashoffsetValue + 'px;}100% {stroke-dashoffset: 0px;}}';
        insertRule(shineForward);
      } else {
        element.style.animation = 'shine-forward-' + idSub + ' 0.3s infinite linear';
      }
    } else {
      if (randomStrParam) {
        element.style.animation = 'shine-forward-' + randomStrParam + ' 0.5s infinite linear';
        shineForward = '@keyframes shine-forward-' + randomStrParam + ' {0% {stroke-dashoffset: ' + dashoffsetValue + 'px;}100% {stroke-dashoffset: 0px;}}';
        insertRule(shineForward);
      } else {
        element.style.animation = 'shine-forward-' + idSub + ' 0.5s infinite linear';
      }
    }
  } else if (val[0] == '反向') {
    if (val[1] == '快') {
      if (randomStrParam) {
        element.style.animation = 'shine-reverse-' + randomStrParam + ' 0.15s infinite linear';
        shineReverse = '@keyframes shine-reverse-' + randomStrParam + ' {0% {stroke-dashoffset: 0px;}100% {stroke-dashoffset: ' + dashoffsetValue + 'px;}}';
        insertRule(shineReverse);
      } else {
        element.style.animation = 'shine-reverse-' + idSub + ' 0.15s infinite linear';
      }
    } else if (val[1] == '中') {
      if (randomStrParam) {
        element.style.animation = 'shine-reverse-' + randomStrParam + ' 0.3s infinite linear';
        shineReverse = '@keyframes shine-reverse-' + randomStrParam + ' {0% {stroke-dashoffset: 0px;}100% {stroke-dashoffset: ' + dashoffsetValue + 'px;}}';
        insertRule(shineReverse);
      } else {
        element.style.animation = 'shine-reverse-' + idSub + ' 0.3s infinite linear';
      }
    } else {
      if (randomStrParam) {
        element.style.animation = 'shine-reverse-' + randomStrParam + ' 0.5s infinite linear';
        shineReverse = '@keyframes shine-reverse-' + randomStrParam + ' {0% {stroke-dashoffset: 0px;}100% {stroke-dashoffset: ' + dashoffsetValue + 'px;}}';
        insertRule(shineReverse);
      } else {
        element.style.animation = 'shine-reverse-' + idSub + ' 0.5s infinite linear';
      }
    }
  } else {
    element.style.animation = '';
  }

  // 对于初始化时的固定 keyframes，总是插入
  if (!randomStrParam) {
    shineForward = '@keyframes shine-forward-' + idSub + ' {0% {stroke-dashoffset: ' + dashoffsetValue + 'px;}100% {stroke-dashoffset: 0px;}}';
    shineReverse = '@keyframes shine-reverse-' + idSub + ' {0% {stroke-dashoffset: 0px;}100% {stroke-dashoffset: ' + dashoffsetValue + 'px;}}';
    insertRule(shineForward);
    insertRule(shineReverse);
  }
};
const insertRule = (keyframes: string) => {
  if (!keyframes || keyframes.trim() === '') return;

  console.log('插入样式规则:', keyframes);

  try {
    // 尝试找到合适的样式表
    let targetStyleSheet = null;

    for (let i = 0; i < document.styleSheets.length; i++) {
      let styleSheet = document.styleSheets[i];
      try {
        // 检查是否可以访问样式表
        if (styleSheet.cssRules) {
          targetStyleSheet = styleSheet;
          break;
        }
      } catch (error) {
        // 跨域样式表，跳过
        continue;
      }
    }

    // 如果没有找到合适的样式表，创建一个新的
    if (!targetStyleSheet) {
      const style = document.createElement('style');
      document.head.appendChild(style);
      targetStyleSheet = style.sheet;
    }

    if (targetStyleSheet) {
      targetStyleSheet.insertRule(keyframes);
      console.log('样式规则插入成功');
    }
  } catch (error) {
    console.error('插入样式规则失败:', error);

    // 降级方案：直接添加到 head
    try {
      const style = document.createElement('style');
      style.innerHTML = keyframes;
      document.head.appendChild(style);
      console.log('使用降级方案插入样式');
    } catch (fallbackError) {
      console.error('降级方案也失败:', fallbackError);
    }
  }
};

const drawArrow = (ctx: CanvasRenderingContext2D, x2: number, y2: number, lineWidthParam: number, color: string) => {
  // (x1, y1)是线段起点  (x2, y2)是线段终点
  ctx.beginPath(); // 坐标原点 => (x2, y2)
  ctx.moveTo(x2, y2);
  ctx.lineTo(x2 - lineWidthParam * FACTOR_H.value, y2 - lineWidthParam * FACTOR_V.value);
  ctx.lineTo(x2 - lineWidthParam * FACTOR_H.value, y2 + lineWidthParam * FACTOR_V.value);
  ctx.closePath();
  ctx.fillStyle = color; // 设置线的颜色状态
  ctx.fill();
};

const drawLine = (ctx: CanvasRenderingContext2D) => {
  const lineWidthValue = lineWidth.value;
  const color = getForeColor();
  ctx.beginPath();
  const pointsArray = props.detail.style.spotPoints;
  for (let index = 0; index < pointsArray.length; index++) {
    const begin = pointsArray[index];
    const end = pointsArray[index + 1];
    if (!end) break;
    ctx.moveTo(begin.x, begin.y);
    ctx.lineTo(end.x, end.y);
    if (index == pointsArray.length - 2) break;
  }
  ctx.lineWidth = lineWidthValue; // 设置线宽状态
  ctx.strokeStyle = color; // 设置线的颜色状态
  ctx.stroke(); // 进行绘制
  ctx.closePath();
};

const reDraw = () => {
  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const el = elCanvas.value;
  if (!el) return;

  const ctx = el.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  drawLine(ctx);
  const pointsArray = props.detail.style.spotPoints;
  if (pointsArray.length > 0) {
    const lastPoint = pointsArray[pointsArray.length - 1];
    drawArrow(ctx, lastPoint.x, lastPoint.y, lineWidth.value, getForeColor());
  }
};

const onResize = () => {
  reDraw();
};

const aroowPassDown = (pass: any, event: MouseEvent) => {
  flag.value = true;
  pass.startX = event.pageX;
  pass.startY = event.pageY;
  pass.temp = {};
  pass.temp.x = pass.x;
  pass.temp.y = pass.y;
  passItem.value = pass;
  console.log('flag', flag.value);
};

const onMousemove = (event: MouseEvent) => {
  if (!flag.value) return;
  event.stopPropagation();
  const dx = event.pageX - passItem.value.startX;
  const dy = event.pageY - passItem.value.startY;
  passItem.value.x = passItem.value.temp.x + dx;
  passItem.value.y = passItem.value.temp.y + dy;

  // 更新 SVG 路径点
  updatePoints();

  // 重新绘制 Canvas
  reDraw();
};

const onMouseUp = () => {
  flag.value = false;

  // 鼠标释放后重新应用动画，确保水流在新路径上正确流动
  if (props.detail.style.animations && props.detail.style.speed) {
    console.log('重新应用水流动画');

    // 稍微延迟一下，确保 DOM 更新完成
    setTimeout(() => {
      // 使用新的随机字符串重新生成动画
      goggle(
        props.detail.style.animations + '-' + props.detail.style.speed,
        props.detail.style.lineWidth + props.detail.style.lineInterval,
        randomStr()
      );
    }, 50);
  }
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};



// Lifecycle
onMounted(() => {
  width.value = props.detail.style.position.x;
  height.value = props.detail.style.position.y;

  if (props.detail.style.anchorPointNum && props.detail.style.spotPoints.length == 0) {
    props.detail.style.spotPoints = [];
    for (let i = 0; i < props.detail.style.anchorPointNum; i++) {
      let data = {
        x: 20 + 50 * i,
        y: 20,
      };
      props.detail.style.spotPoints.push(data);
    }
  }

  goggle(props.detail.style.animations + '-' + props.detail.style.speed, props.detail.style.lineWidth + props.detail.style.lineInterval);
});

// 导出方法供父组件使用
defineExpose({
  onResize,
  reDraw,
  updatePoints
});
</script>

<style lang="scss" scoped>
.svgClass {
  position: absolute;
  height: 100%;
  width: 100%;
}

.svgBgClass {
  position: absolute;
  height: 100%;
  width: 100%;
}

/* 正向快速流动 */
.shap-forward-quick {
  animation: shine-forward 0.15s infinite linear;
}

/* 正向中速流动 */
.shap-forward-medium {
  animation: shine-forward 0.3s infinite linear;
}

/* 正向慢速流动 */
.shap-forward-slow {
  animation: shine-forward 0.5s infinite linear;
}

/* 反向快速流动 */
.shap-reverse-quick {
  animation: shine-reverse 0.15s infinite linear;
}

/* 反向中速流动 */
.shap-reverse-medium {
  animation: shine-reverse 0.3s infinite linear;
}

/* 反向慢速流动 */
.shap-reverse-slow {
  animation: shine-reverse 0.5s infinite linear;
}

/* 正向流动*/
@keyframes shine-forward {
  0% {
    stroke-dashoffset: 21px;
  }

  100% {
    stroke-dashoffset: 0px;
  }
}

/* 反向流动*/
@keyframes shine-reverse {
  0% {
    stroke-dashoffset: 0px;
  }

  100% {
    stroke-dashoffset: 21px;
  }
}

.view-line-arrow {
  height: 100%;
  width: 100%;
  position: relative;

  .passby {
    position: absolute;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background-color: white;
    border: 1px solid rgb(34, 14, 223);
    cursor: move;
  }
}
</style>
