<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="500">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="封面" prop="categoryName">
                            <imageUpload v-model="state.ruleForm.pageImage" :limit="1" :fileSize="1"
                                :class="{ disable: uploadDisabled }" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="名称" prop="pageName">
                            <el-input v-model="state.ruleForm.pageName" placeholder="请输入名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="描述">
                        <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入描述"
                            maxlength="150"></el-input>
                    </el-form-item>
                </el-col>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { computed, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import imageUpload from '/@/components/ImageUpload/index.vue'
import { addCenter, getCenter, updateCenter } from '/@/api/scada/center';
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const uploadDisabled = computed(() => {
    return state.ruleForm.pageImage !== '';
});
// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        pageImage: '', // 组态信息标题
        pageName: '', // 组态信息类型
        remark: '', // 状态
        id: ''
    },
    dialog: {
        isShowDialog: false,
        title: '',
        submitTxt: '',
    },
}

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    pageName: [{ required: true, message: '请输入名称', trigger: 'change' }],

})
// 打开弹窗
const openDialog = (type: string, row: any, id: string) => {
    if (type === 'edit') {
        if (row != undefined) {
            getCenter(row.id).then(response => {
                state.ruleForm = response.data.data
            });
        } else {
            getCenter(id).then(response => {
                state.ruleForm = response.data.data
            });
        }
        state.dialog.title = '修改组态信息';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '新增组态信息';
        state.dialog.submitTxt = '新 增';
    }
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.id != '') {
                updateCenter(state.ruleForm).then(response => {
                  response = response.data
                  if (response.code === 200) {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                  }

                });
            } else {
                addCenter(state.ruleForm).then(response => {
                  response = response.data
                  if (response.code === 200) {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                  }
                });
            }

        } else {
          // eslint-disable-next-line no-console
            console.log('error submit!', fields)
        }
    })
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
