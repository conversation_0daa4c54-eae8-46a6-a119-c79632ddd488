<template>
    <div>
        <el-tabs type="border-card" v-model:active="tabActive">
            <el-tab-pane label="秒" v-if="shouldHide('second')">
                <CrontabSecond @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronsecond" />
            </el-tab-pane>
            <el-tab-pane label="分钟" v-if="shouldHide('min')">
                <CrontabMin @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronmin" />
            </el-tab-pane>
            <el-tab-pane label="小时" v-if="shouldHide('hour')">
                <CrontabHour @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronhour" />
            </el-tab-pane>
            <el-tab-pane label="日" v-if="shouldHide('day')">
                <CrontabDay @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronday" />
            </el-tab-pane>
            <el-tab-pane label="月" v-if="shouldHide('month')">
                <CrontabMonth @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronmonth" />
            </el-tab-pane>
            <el-tab-pane label="周" v-if="shouldHide('week')">
                <CrontabWeek @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronweek" />
            </el-tab-pane>
            <el-tab-pane label="年" v-if="shouldHide('year')">
                <CrontabYear @update="updateCrontabValue" :check="checkNumber" :cron="crontabValueObj"
                    :radioParent="tabActive" ref="cronyear" />
            </el-tab-pane>
        </el-tabs>

        <div class="popup-main">
            <div class="popup-result">
                <p class="title">时间表达式</p>
                <table>
                    <thead>
                        <tr>
                            <!-- 动态渲染表头 -->
                            <th v-for="(item, index) in tabTitles" :key="index" width="40">{{ item }}</th>
                            <th>Cron 表达式</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <!-- 动态渲染表格数据 -->
                            <td><span>{{ crontabValueObj.second }}</span></td>
                            <td><span>{{ crontabValueObj.min }}</span></td>
                            <td><span>{{ crontabValueObj.hour }}</span></td>
                            <td><span>{{ crontabValueObj.day }}</span></td>
                            <td><span>{{ crontabValueObj.month }}</span></td>
                            <td><span>{{ crontabValueObj.week }}</span></td>
                            <td><span>{{ crontabValueObj.year }}</span></td>
                            <td><span>{{ crontabValueString }}</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <CrontabResult :ex="crontabValueString" />

            <div class="pop_btn">
                <el-button size="small" type="primary" @click="submitFill">确定</el-button>
                <el-button size="small" type="warning" @click="clearCron">重置</el-button>
                <el-button size="small" @click="hidePopup">取消</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import CrontabSecond from './second.vue';
import CrontabMin from './min.vue';
import CrontabHour from './hour.vue';
import CrontabDay from './day.vue';
import CrontabMonth from './month.vue';
import CrontabWeek from './week.vue';
import CrontabYear from './year.vue';
import CrontabResult from './result.vue';

interface CronObj {
    second: any;
    min: any;
    hour: any;
    day: any;
    month: any;
    week: any;
    year: any;
}

const props = defineProps<{
    expression?: any;
    hideComponent?: any[];
}>();

const emit = defineEmits<{
    (event: 'hide'): void;
    (event: 'fill', value: string): void;
}>();
interface Instance {
    cycle01: any;
    cycle02: any;
    radioValue: any;
    average01: any;
    average02: any;
    checkboxList: any;
    workday: any;
    weekday: any
}
const cronsecond = ref<Instance | null>(null);
const cronmin = ref<Instance | null>(null)
const cronhour = ref<Instance | null>(null)
const cronday = ref<Instance | null>(null)
const cronmonth = ref<Instance | null>(null)
const cronweek = ref<Instance | null>(null)
const cronyear = ref<Instance | null>(null)
const tabTitles = ref(["秒", "分钟", "小时", "日", "月", "周", "年"]);
const tabActive = ref(1);
const crontabValueObj = ref<CronObj>({
    second: "*" as any,
    min: "*" as any,
    hour: "*" as any,
    day: "*" as any,
    month: "*" as any,
    week: "?" as any,
    year: "" as any,
});

const shouldHide = (key: string): boolean => {
    if (props.hideComponent && props.hideComponent.includes(key)) return false;
    return true;
};

const resolveExp = () => {
    console.log(props.expression, 'props.expression');
    // 反解析 表达式
    if (props.expression) {
        const arr = props.expression.split(' ');
        if (arr.length >= 6) {
            //6 位以上是合法表达式
            let obj = {
                second: arr[0],
                min: arr[1],
                hour: arr[2],
                day: arr[3],
                month: arr[4],
                week: arr[5],
                year: arr[6] ? arr[6] : "",
            };
            crontabValueObj.value = {
                ...obj,
            };
            for (let i in obj) {
                if (obj[i as keyof CronObj]) changeRadio(i, obj[i as keyof CronObj]);
            }

        }
    } else {
        clearCron();
    }
};
// tab切换值
// const    tabCheck = (index) => {
//       tabActive.value = index;
//     }
// 由子组件触发，更改表达式组成的字段值
const updateCrontabValue = (name: string, value: string, from?: string) => {
    console.log(name, value, from, 'updateCrontabValue');
    crontabValueObj.value[name as keyof CronObj] = value;
    if (from && from != name) {
        changeRadio(name, value);
    }
};
// 赋值到组件
const changeRadio = (name: string, value: string) => {
    const arr = ['second', 'min', 'hour', 'month'];
    const refName = `cron${name}`;
    console.log(name, value, '+++++++')

    let insValue = 0;

    if (arr.includes(name)) {
        if (value === "*") {
            insValue = 1;
        } else if (value.includes("-")) {
            if (refName == 'cronsecond' && cronsecond.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronsecond.value.cycle01 = 0)
                    : (cronsecond.value.cycle01 = Number(indexArr[0]));
                cronsecond.value.cycle02 = Number(indexArr[1]);
                cronsecond.value.radioValue = 2
            }
            else if (refName == 'cronmin' && cronmin.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronmin.value.cycle01 = 0)
                    : (cronmin.value.cycle01 = Number(indexArr[0]));
                cronmin.value.cycle02 = Number(indexArr[1])
                cronmin.value.radioValue = 2
            } else if (refName == 'cronhour' && cronhour.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronhour.value.cycle01 = 0)
                    : (cronhour.value.cycle01 = Number(indexArr[0]));
                cronhour.value.cycle02 = Number(indexArr[1]);
                cronhour.value.radioValue = 2
            } else if (refName == 'cronday' && cronday.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronday.value.cycle01 = 0)
                    : (cronday.value.cycle01 = Number(indexArr[0]));
                cronday.value.cycle02 = Number(indexArr[1]);
                cronday.value.radioValue = 2
            } else if (refName == 'cronmonth' && cronmonth.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronmonth.value.cycle01 = 0)
                    : (cronmonth.value.cycle01 = Number(indexArr[0]));
                cronmonth.value.cycle02 = Number(indexArr[1]);
                cronmonth.value.radioValue = 2
            } else if (refName == 'cronweek' && cronweek.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronweek.value.cycle01 = 0)
                    : (cronweek.value.cycle01 = Number(indexArr[0]));
                cronweek.value.cycle02 = Number(indexArr[1]);
                cronweek.value.radioValue = 2
            } else if (refName == 'cronyear' && cronyear.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronyear.value.cycle01 = 0)
                    : (cronyear.value.cycle01 = Number(indexArr[0]));
                cronyear.value.cycle02 = Number(indexArr[1]);
                cronyear.value.radioValue = 2
            }

            insValue = 2;

        }
        else if (value.includes("/")) {
            if (refName == 'cronsecond' && cronsecond.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronsecond.value.average01 = 0)
                    : (cronsecond.value.average01 = Number(indexArr[0]));
                cronsecond.value.average02 = Number(indexArr[1])
                cronsecond.value.radioValue = 3
            } else if (refName == 'cronmin' && cronmin.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronmin.value.average01 = 0)
                    : (cronmin.value.average01 = Number(indexArr[0]));
                cronmin.value.average02 = Number(indexArr[1])
                cronmin.value.radioValue = 3
            } else if (refName == 'cronhour' && cronhour.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronhour.value.average01 = 0)
                    : (cronhour.value.average01 = Number(indexArr[0]));
                cronhour.value.average02 = Number(indexArr[1])
                cronhour.value.radioValue = 3
            } else if (refName == 'cronday' && cronday.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronday.value.average01 = 0)
                    : (cronday.value.average01 = Number(indexArr[0]));
                cronday.value.average02 = Number(indexArr[1])
                cronday.value.radioValue = 3
            } else if (refName == 'cronmonth' && cronmonth.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronmonth.value.average01 = 0)
                    : (cronmonth.value.average01 = Number(indexArr[0]));
                cronmonth.value.average02 = Number(indexArr[1])
                cronmonth.value.radioValue = 3
            } else if (refName == 'cronweek' && cronweek.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronweek.value.average01 = 0)
                    : (cronweek.value.average01 = Number(indexArr[0]));
                cronweek.value.average02 = Number(indexArr[1])
                cronweek.value.radioValue = 3
            } else if (refName == 'cronyear' && cronyear.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronyear.value.average01 = 0)
                    : (cronyear.value.average01 = Number(indexArr[0]));
                cronyear.value.average02 = indexArr[1]
                cronyear.value.radioValue = 3
            }
            insValue = 3;
        } else {
            if (refName == 'cronsecond' && cronsecond.value) {
                cronsecond.value.checkboxList = value.split(",");
                cronsecond.value.radioValue = 4
            } else if (refName == 'cronmin' && cronmin.value) {
                cronmin.value.checkboxList = value.split(",");
                cronmin.value.radioValue = 4
            } else if (refName == 'cronhour' && cronhour.value) {
                cronhour.value.checkboxList = value.split(",");
                cronhour.value.radioValue = 4
            } else if (refName == 'cronday' && cronday.value) {
                cronday.value.checkboxList = value.split(",");
                cronday.value.radioValue = 4
            } else if (refName == 'cronmonth' && cronmonth.value) {
                cronmonth.value.checkboxList = value.split(",");
                cronmonth.value.radioValue = 4
            } else if (refName == 'cronweek' && cronweek.value) {
                cronweek.value.checkboxList = value.split(",");
                cronweek.value.radioValue = 4
            } else if (refName == 'cronyear' && cronyear.value) {
                cronyear.value.checkboxList = value.split(",");
                cronyear.value.radioValue = 4
            }
        }
    } else if (name === "day") {
        if (value === "*") {
            if (cronday.value) {
                cronday.value.radioValue = 1
            }
        } else if (value === "?") {
            if (cronday.value) {
                cronday.value.radioValue = 2
            }
        } else if (value.indexOf("-") > -1) {
            let indexArr = value.split("-") as any;
            if (cronday.value) {
                isNaN(indexArr[0])
                    ? (cronday.value.cycle01 = 0)
                    : (cronday.value.cycle01 = Number(indexArr[0]));
                cronday.value.cycle02 = Number(indexArr[1])
                cronday.value.radioValue = 3
            }
        } else if (value.indexOf("/") > -1) {
            let indexArr = value.split("/") as any;
            if (cronday.value) {
                isNaN(indexArr[0])
                    ? (cronday.value.average01 = 0)
                    : (cronday.value.average01 = Number(indexArr[0]));
                cronday.value.average02 = Number(indexArr[1])
                cronday.value.radioValue = 4
            }
        } else if (value.indexOf("W") > -1) {
            let indexArr = value.split("W") as any;
            if (cronday.value) {
                isNaN(indexArr[0])
                    ? (cronday.value.workday = 0)
                    : (cronday.value.workday = Number(indexArr[0]));
                cronday.value.radioValue = 5
            }

        } else if (value === "L") {
            if (cronday.value) {
                cronday.value.radioValue = 6
            }
        } else {
            if (cronday.value) {
                cronday.value.checkboxList = value.split(",");
                cronday.value.radioValue = 7
            }
        }
    }
    else if (name === "week") {
        if (value === "*") {
            if (cronweek.value) {
                cronweek.value.radioValue = 1
            }
        } else if (value == "?") {
            if (cronweek.value) {
                cronweek.value.radioValue = 2
            }
        } else if (value.indexOf("-") > -1) {
            let indexArr = value.split("-") as any;
            if (cronweek.value) {
                isNaN(indexArr[0])
                    ? (cronweek.value.cycle01 = 0)
                    : (cronweek.value.cycle01 = Number(indexArr[0]));
                cronweek.value.cycle02 = Number(indexArr[1])
                cronweek.value.radioValue = 3
            }
        } else if (value.indexOf("#") > -1) {
            let indexArr = value.split("#") as any;
            console.log(indexArr,'indexArrindexArrindexArrindexArr');
            
            if (cronweek.value) {
                isNaN(indexArr[0])
                    ? (cronweek.value.average01 = 1)
                    : (cronweek.value.average01 = Number(indexArr[1]));
                cronweek.value.average02 = Number(indexArr[0])
                cronweek.value.radioValue = 4
            }
        } else if (value.indexOf("L") > -1) {
            let indexArr = value.split("L") as any;
            if (cronweek.value) {
                isNaN(indexArr[0])
                    ? (cronweek.value.weekday = 1)
                    : (cronweek.value.weekday = Number(indexArr[0]));
                cronweek.value.radioValue = 5
            }
        } else {
            if (cronweek.value) {
                cronweek.value.checkboxList = value.split(",");
                cronweek.value.radioValue = 6
            }
        }
    } else if (name === "year") {
        if (value == "") {
            if (cronyear.value) {
                cronyear.value.radioValue = 1
            }
        } else if (value == "*") {
            if (cronyear.value) {
                cronyear.value.radioValue = 2
            }
        } else if (value.indexOf("-") > -1) {
            if (cronyear.value) {
                const indexArr = value.split("-") as any;
                isNaN(indexArr[0])
                    ? (cronyear.value.cycle01 = 1)
                    : (cronyear.value.cycle01 = Number(indexArr[0]));
                cronyear.value.cycle02 = Number(indexArr[1])
                cronyear.value.radioValue = 3
            }
        } else if (value.indexOf("/") > -1) {
            if (cronyear.value) {
                const indexArr = value.split("/") as any;
                isNaN(indexArr[0])
                    ? (cronyear.value.average01 = 1)
                    : (cronyear.value.average01 = Number(indexArr[0]));
                cronyear.value.average02 = Number(indexArr[1])
                cronyear.value.radioValue = 4
            }
        } else {
            if (cronyear.value) {
                cronyear.value.checkboxList = value.split(",");
                cronyear.value.radioValue = 5
            }
        }

    }


};
// 表单选项的子组件校验数字格式（通过-props传递）
const checkNumber = (value: any, minLimit: any, maxLimit: any): any => {
    value = Math.floor(value);
    if (value < minLimit) {
        return minLimit;
    } else if (value > maxLimit) {
        return maxLimit;
    }
    return value;
};
// 隐藏弹窗
const hidePopup = () => {
    emit('hide');
};
// 填充表达式
const submitFill = () => {
    emit('fill', crontabValueString.value);
    hidePopup();
};

const clearCron = () => {
    crontabValueObj.value = {
        second: "*",
        min: "*",
        hour: "*",
        day: "*",
        month: "*",
        week: "?",
        year: "",
    };
};

const crontabValueString = computed(() => {
    const obj = crontabValueObj.value;
    return `${obj.second} ${obj.min} ${obj.hour} ${obj.day} ${obj.month} ${obj.week} ${obj.year}`;
});

watch(() => props.expression, resolveExp);
watch(() => props.hideComponent, () => { }, { deep: true });

onMounted(() => {
    resolveExp();
});
</script>
<style scoped>
.pop_btn {
    text-align: center;
    margin-top: 20px;
}

.popup-main {
    position: relative;
    margin: 10px auto;
    background: #fff;
    border-radius: 5px;
    font-size: 12px;
    overflow: hidden;
}

.popup-title {
    overflow: hidden;
    line-height: 34px;
    padding-top: 6px;
    background: #f2f2f2;
}

.popup-result {
    box-sizing: border-box;
    line-height: 24px;
    margin: 25px auto;
    padding: 15px 10px 10px;
    border: 1px solid #ccc;
    position: relative;
}

.popup-result .title {
    position: absolute;
    top: -28px;
    left: 50%;
    width: 140px;
    font-size: 14px;
    margin-left: -70px;
    text-align: center;
    line-height: 30px;
    background: #fff;
}

.popup-result table {
    text-align: center;
    width: 100%;
    margin: 0 auto;
}

.popup-result table span {
    display: block;
    width: 100%;
    font-family: arial;
    line-height: 30px;
    height: 30px;
    white-space: nowrap;
    overflow: hidden;
    border: 1px solid #e8e8e8;
}

.popup-result-scroll {
    font-size: 12px;
    line-height: 24px;
    height: 10em;
    overflow-y: auto;
}
</style>