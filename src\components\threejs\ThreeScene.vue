<template>
    <div ref="container" class="three-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

// 为 container DOM 元素类型化 ref
const container = ref<HTMLDivElement | null>(null);
let scene: THREE.Scene, camera: THREE.PerspectiveCamera, renderer: THREE.WebGLRenderer, cube: THREE.Mesh, cube1: THREE.Mesh, line: THREE.Line, movingLine: THREE.Line,model: THREE.Group;
let index = 20;
let num = 15;
let points: THREE.Vector3[] = [];
let geometry2: THREE.BufferGeometry;
let material: THREE.Material;
let PointsMaterial: THREE.PointsMaterial;
let flyPoints: THREE.Points;
let resizeObserver: ResizeObserver | null = null;
let lineMaterial: { color: { setRGB: (arg0: number, arg1: number, arg2: number) => void; }; }
let movingLineMaterial: THREE.LineBasicMaterial;
let curve: {
    getSpacedPoints: any; getPoints: (arg0: number) => any; getPointAt: (arg0: number) => any; 
}
// 初始化场景
const initScene = () => {
    // 创建场景
    scene = new THREE.Scene();
    model = new THREE.Group();
    scene.background = new THREE.Color("rgb(225,225,225,0.1)"); // 设置背景颜色

    // 创建相机
    camera = new THREE.PerspectiveCamera(
        75,
        container.value?.clientWidth ?? 1 / (container.value?.clientHeight ?? 1), // 防止除以零错误
        0.1,
        1000
    );
    camera.position.set(0, 100, 200); // 设置相机位置
    camera.lookAt(0, 0, 0); // 设置相机朝向

    // 创建渲染器
    renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(container.value?.clientWidth ?? 0, container.value?.clientHeight ?? 0);
    renderer.setPixelRatio(window.devicePixelRatio); // 支持高清屏幕
    container.value?.appendChild(renderer.domElement);

    // 创建立方体
    const geometry = new THREE.BoxGeometry(10, 10, 10);
    const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
    cube = new THREE.Mesh(geometry, material);
    cube.position.set(0, 5, 0)
    // 创建立方体1
    const geometry1 = new THREE.BoxGeometry(10, 10, 10);
    const material1 = new THREE.MeshBasicMaterial({ color: 0x000000 });
    cube1 = new THREE.Mesh(geometry1, material1);
    cube1.position.set(30, 5, 30)
    // 创建网格辅助线
    const gridHelper = new THREE.GridHelper(100, 10, 0x0000ff, 0x808080);
    scene.add(gridHelper, cube, cube1);

    // 创建飞线
    curve = new THREE.CatmullRomCurve3([
        new THREE.Vector3(0, 10, 0),   // 起点
        new THREE.Vector3(15, 50, 15),
        new THREE.Vector3(30, 5, 30)  // 终点
    ]);
    const points = curve.getSpacedPoints(100);  // 获取曲线上的50个点
    const lineMaterial = new THREE.LineBasicMaterial({}); // 红色飞线
    const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
    line = new THREE.Line(lineGeometry, lineMaterial);
    scene.add(line);
    // 创建动态流线材质
    movingLineMaterial = new THREE.LineBasicMaterial({
        color: 0xff0000, // 红色
        linewidth: 4,
        opacity: 0.8,  // 设置透明度
        transparent: true
    });

    // 创建动态流线的初始几何体（只有一个点）
    const movingLineGeometry = new THREE.BufferGeometry().setFromPoints([points[0]]);
    movingLine = new THREE.Line(movingLineGeometry, movingLineMaterial);
    scene.add(movingLine);
    // 添加轨道控制器
    new OrbitControls(camera, renderer.domElement); // 添加轨道控制器
    animate();
};

// 处理窗口尺寸变化
const handleResize = () => {
    const width = container.value?.clientWidth ?? 0;
    const height = container.value?.clientHeight ?? 0;

    // 更新渲染器和相机
    renderer.setSize(width, height);
    camera.aspect = width / height;
    camera.updateProjectionMatrix();
};
let time = 0; // 动画时间
// 动画函数
const animate = () => {
    time += 0.01; // 增加时间

    // 计算动态线位置
    const t = (time % 1); // 让时间在 0 到 1 之间循环
    const position = curve.getPointAt(t); // 获取曲线上的位置
    movingLine.geometry.setFromPoints([position]); // 更新动态线的位置
    // // 设置材质的颜色
    camera.lookAt(0, 0, 0); // 让相机始终朝向立方体
    renderer.render(scene, camera);
    requestAnimationFrame(animate);
};

// 生命周期钩子
onMounted(() => {
    // 观察容器尺寸变化
    resizeObserver = new ResizeObserver(handleResize);
    resizeObserver.observe(container.value!);

    initScene();
});

onBeforeUnmount(() => {
    // 清理资源
    if (resizeObserver) {
        resizeObserver.disconnect();
    }
    scene.remove(cube);
    cube.geometry.dispose();
    cube.material.dispose();
    scene.remove(line);
    renderer.dispose();
});
</script>

<style scoped>
.three-container {
    width: 100%;
    height: 100%;
    min-height: 300px;
    overflow: hidden;
}
</style>
