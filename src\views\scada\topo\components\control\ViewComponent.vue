<template>
  <div class="view-component" :id="detail.identifier" ref="xcomp">
    <div v-show="false">{{ animateChange }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, createApp} from 'vue';
import { storeToRefs } from 'pinia';
import { useCounterStore } from '/@/stores/counterStore';
import topoUtil from '/@/utils/topo/topo-util';
import { getAnimate } from '/@/utils/topo/anime';
import { getComponent } from '/@/api/scada/component';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';

// 定义组件名称
defineOptions({
  name: 'ViewComponent'
});

// 定义 props，继承 BaseView 的 props
const props = defineProps({
  editMode: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
});

// 定义 emits
// const emit = defineEmits(['refreshData', 'resize']);

// 使用 Pinia store
const counterStore = useCounterStore();
const { mqttData } = storeToRefs(counterStore);

// 响应式数据
const xcomp = ref<HTMLElement>();
const data = ref({
  id: '',
  componentTemplate: '',
  componentStyle: '',
  componentScript: '',
});

// 动画实例引用（从 BaseView 继承的功能）
const animateView = ref(null);

// 计算属性：动画变化监听
const animateChange = computed(() => {
  if (mqttData.value) {
    // 动画初始化
    if (
      props.detail.dataAction?.serialNumber &&
      props.detail.dataAction?.identifier &&
      props.detail.dataAction?.paramJudge &&
      props.detail.dataAction?.paramJudgeData !== undefined &&
      mqttData.value.serialNumber === props.detail.dataAction.serialNumber
    ) {
      const message = mqttData.value.message?.find((item: any) => item.id === props.detail.dataAction.identifier);
      if (message) {
        let val = message.value;
        let isGd = topoUtil.judgeSize(props.detail.dataAction.paramJudge, val, props.detail.dataAction.paramJudgeData);
        if (isGd) {
          if (props.detail.dataBind?.xyAction) {
            // 显隐判断
            getAnimate().set(document.getElementById(props.detail.identifier), {
              display: 'block',
            });
          }
          if (animateView.value) {
            (animateView.value as any).play();
          }
        } else {
          if (props.detail.dataBind?.xyAction) {
            // 显隐判断
            getAnimate().set(document.getElementById(props.detail.identifier), {
              display: 'none',
            });
          }
          if (animateView.value) {
            (animateView.value as any).pause();
          }
        }
      }
    }
  }
  return mqttData.value;
});

// 方法
const initEchart = () => {
  let id = props.detail.dataBind?.componentId;
  if (id) {
    getComponentDataById(id);
  }
};

// 获取自定义Component详情
const getComponentDataById = (id: string) => {
  getComponent(id).then((res) => {
    res = res.data;
    if (res.code === 200) {
      data.value = res.data;
      loadData();
    }
  });
};

// 加载数据
const loadData = () => {
  let template = data.value.componentTemplate;
  if (!template) return;

  // 处理样式
  let styleCss = data.value.componentStyle;
  if (styleCss) {
    // 移除之前的样式
    const existingStyle = document.getElementById(`component-style-${data.value.id}`);
    if (existingStyle) {
      existingStyle.remove();
    }

    let style = document.createElement('style');
    style.id = `component-style-${data.value.id}`;
    style.innerHTML = styleCss;
    document.head.appendChild(style);
  }

  let script = data.value.componentScript;
  if (script) {
    script = script.replace(/export default/, 'return');
  }

  try {
    let obj = new Function(script)();
    obj.template = template;

    // 清空之前的内容
    if (xcomp.value?.innerHTML) {
      xcomp.value.innerHTML = '';
    }

    let newDiv = document.createElement('div');
    newDiv.setAttribute('id', `xcomp-${data.value.id}`);
    xcomp.value?.appendChild(newDiv);

    // 使用 Vue 3 的 createApp 创建应用
    const app = createApp(obj);

    // 注册 Element Plus 组件库
    app.use(ElementPlus);

    // 注册 Element Plus 图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }

    app.mount(`#xcomp-${data.value.id}`);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('组件加载失败:', error);
  }
};

// 生命周期
onMounted(() => {
  initEchart();
});
</script>

<style lang="scss">
.view-component {
  height: 100%;
  width: 100%;
  padding: 10px;
  overflow-y: auto;
}
</style>
