import mqtt, { MqttClient } from 'mqtt';
import { Session } from './storage';

interface MqttTool {
  client: MqttClient | null;
  connect: () => void;
  end: () => Promise<string>;
  reconnect: () => Promise<string>;
  subscribe: (topics: string[]) => Promise<string>;
  unsubscribe: (topics: string) => Promise<string>;
  publish: (topic: string, message: string, name: string) => Promise<string>;
}

const mqttTool: MqttTool = {
  client: null,

  // 连接Mqtt
  connect() {
    const options = {
      username: 'FastBee',
      password: Session.get('token'),
      cleanSession: true,
      keepAlive: 30,
      clientId: 'web-' + Math.random().toString(16).substr(2),
      connectTimeout: 60000,
    };

    // 配置Mqtt地址
    let url: string = import.meta.env.VITE_MQTT_URL || '';

    if (url === '') {
      console.log('自动获取mqtt连接地址');
      if (window.location.protocol === 'http:') {
        url = `ws://${window.location.hostname}:8083/mqtt`;
      } else {
        url = `wss://${window.location.hostname}/mqtt`;
      }
    }
    console.log('mqtt地址：', url);

    mqttTool.client = mqtt.connect(url, options);

    mqttTool.client.on('connect', () => {
      console.log('mqtt连接成功');
    });

    // 重新连接
    mqttTool.client.on('reconnect', () => {
      console.log('正在重连:');
    });

    // 发生错误
    mqttTool.client.on('error', (error) => {
      console.log('Mqtt客户端连接失败：', error);

      if (mqttTool.client) {
        // 客户端存在，则尝试断开连接
        mqttTool.client.end(true, () => {
          console.log('客户端已断开连接');
        });
      } else {
        // 如果客户端未连接，记录详细错误信息
        console.error('客户端未初始化或连接失败，无法断开连接');
      }
    });


    // 断开连接
    mqttTool.client.on('close', () => {
      console.log('已断开Mqtt连接');
    });
  },

  // 断开连接
  async end() {
    return new Promise<string>((resolve) => {
      if (mqttTool.client == null) {
        resolve('未连接');
        console.log('未连接');
        return;
      }
      mqttTool.client.end();
      mqttTool.client = null;
      console.log('Mqtt服务器已断开连接！');
      resolve('连接终止');
    });
  },

  // 重新连接
  async reconnect() {
    return new Promise<string>((resolve) => {
      if (mqttTool.client == null) {
        resolve('未连接');
        console.log('未连接');
        return;
      }
      console.log('正在重连...');
      mqttTool.client.reconnect();
    });
  },

  // 消息订阅
  async subscribe(topics: string[]) {
    return new Promise<string>((resolve) => {
      if (mqttTool.client == null) {
        resolve('未连接');
        console.log('未连接');
        return;
      }

      mqttTool.client.subscribe(topics, { qos: 1 }, (err) => {
        console.log('订阅主题：', topics);
        if (!err) {
          console.log('订阅成功');
          resolve('订阅成功');
        } else {
          console.log('订阅失败，主题可能已经订阅');
          resolve('订阅失败');
        }
      });
    });
  },

  // 取消订阅
  async unsubscribe(topics: string) {
    return new Promise<string>((resolve) => {
      if (mqttTool.client == null) {
        resolve('未连接');
        console.log('未连接');
        return;
      }

      mqttTool.client.unsubscribe(topics, (err) => {
        if (!err) {
          resolve('取消订阅成功');
          console.log('取消订阅成功');
        } else {
          resolve('取消订阅失败');
          console.log('取消订阅失败');
        }
      });
    });
  },

  // 发布消息
  async publish(topic: string, message: string, name: string) {

    return new Promise<string>((resolve, reject) => {
      if (mqttTool.client == null) {
        resolve('Mqtt客户端未连接');
        console.log('Mqtt客户端未连接');
        return;
      }

      mqttTool.client.publish(topic, message, { qos: 0 }, (err) => {
        console.log('发送主题：', topic);
        console.log('发送内容：', message);

        if (!err) {
          if (topic.indexOf('offline') > 0) {
            console.log(`[${name}] 影子指令发送成功`);
            resolve(`[${name}] 影子指令发送成功`);
          } else {
            console.log(`[${name}] 指令发送成功`);
            resolve(`[${name}] 指令发送成功`);
          }
        } else {
          console.log(`[${name}] 指令发送失败`);
          reject(`[${name}] 指令发送失败`);
        }
      });
    });
  },
};

export default mqttTool;
