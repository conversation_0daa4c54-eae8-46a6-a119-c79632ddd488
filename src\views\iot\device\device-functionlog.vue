<template>
    <div style="padding-left:20px;">
        <el-form :model="state.tableData.param" ref="queryForm" :inline="true" v-show="state.tableData.showSearch"
            label-width="68px">
            <el-form-item label="请选择设备从机:" label-width="120px" v-if="state.tableData.isSubDev">
                <el-select v-model="state.tableData.param.slaveId" placeholder="请选择设备从机">
                    <el-option v-for="slave in slaveList" :key="slave.slaveId"
                        :label="`${slave.deviceName}   (从机地址:${slave.slaveId})`" :value="slave.slaveId"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="日志类型" prop="logType">
                <el-select style="width: 200px;" v-model="state.tableData.param.logType" placeholder="请选择类型" clearable
                    size="default">
                    <el-option v-for="dict in event_typelist" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                </el-select>
            </el-form-item>
            <el-form-item label="标识符" prop="identity">
                <el-input v-model="state.tableData.param.identity" placeholder="请输入标识符" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker v-model="daterangeTime" style="width: 240px" size="default" date-format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
                    end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"> <el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="state.tableData.loading" :data="state.tableData.data" size="default"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" border>
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column :label="state.tableData.showName" align="center" prop="identify" />
            <el-table-column label="指令类型" align="center" prop="funType" width="120px">
                <template #default="scope">
                    <dict-tag :options="function_type_list" :value="scope.row.funType" />
                </template>
            </el-table-column>
            <el-table-column label="设置值" align="center" prop="funValue" />
            <el-table-column label="设备编号" align="center" prop="serialNumber" />
            <el-table-column label="下发时间" align="center" prop="createTime" />
            <el-table-column label="下发结果描述" align="center" prop="resultMsg" />
            <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                    <el-button size="default" text type="primary" @click="handleDelete(scope.row)"
                        v-auths="['iot:log:remove']"><el-icon>
                            <Delete />
                        </el-icon>删除</el-button>
                </template>
            </el-table-column>

        </el-table>
        <div style="height:40px;">
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </div>

    </div>
</template>
<script setup lang="ts" name="">
import { reactive, ref, watch } from 'vue';
import { listEventLog } from '/@/api/iot/eventLog';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { delLog, listLog } from '/@/api/iot/functionLog';
import { ElMessage, ElMessageBox } from 'element-plus';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义 props
const props = defineProps({
    device: {
        type: Object
    }
})

const daterangeTime = ref(['', ''])
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        showSearch: true,
        param: {
            pageNum: 1,
            pageSize: 10,
            logType: '',
            identity: '',
            serialNumber: '',
            deviceId: '' as any,
            slaveId: '' as any,
            beginTime: daterangeTime.value[0],
            endTime: daterangeTime.value[1]

        },
        showName: '',
        isSubDev: false
    },
});

interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface Slave {
    slaveId: string;
    deviceName: string;
}

let slaveList = reactive<Slave[]>([]);
const event_typelist = ref<Option[]>([]);
const event_type_list = ref<Option[]>([]);
const function_type_list = ref<Option[]>([]);
let thingsModel = reactive({
    properties: [] as any,
    functions: [] as any,
    events: [] as any,
});
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        state.tableData.param.beginTime = daterangeTime.value[0]
        state.tableData.param.endTime = daterangeTime.value[1]
        const response = await listLog(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getTableData();
}
/** 重置按钮操作 */
const resetQuery = () => {
    daterangeTime.value = ['', ''];
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        logType: '',
        identity: '',
        serialNumber: '',
        deviceId: '' as any,
        slaveId: '' as any,
        beginTime: daterangeTime.value[0],
        endTime: daterangeTime.value[1]
    };
    handleQuery();
}
/** 删除按钮操作 */
const handleDelete = (row: any) => {
    const ids = row.id
    ElMessageBox.confirm(`是否确认删除设备服务下发日志编号为${ids}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delLog(ids).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });

}
// 获取状态数据
const getdictdata = async () => {
    try {
        event_typelist.value = await dictStore.fetchDict('iot_event_type')
        event_type_list.value = await dictStore.fetchDict('iot_event_type')
        function_type_list.value = await dictStore.fetchDict('iot_function_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 监听props的变化
watch(() => props.device, (newVal) => {
    console.log(newVal, 'newValnewValnewVal');

    try {
        if (newVal && newVal.deviceId != 0) {
            state.tableData.isSubDev = newVal.subDeviceList && newVal.subDeviceList.length > 0;
            state.tableData.showName = state.tableData.isSubDev ? '寄存器地址' : '标识符';
            state.tableData.param.deviceId = newVal.deviceId;
            state.tableData.param.slaveId = newVal.slaveId;
            state.tableData.param.serialNumber = newVal.serialNumber;
            slaveList = newVal.subDeviceList;
            getTableData();
            getdictdata()
            thingsModel = newVal.cacheThingsModel;
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
</script>