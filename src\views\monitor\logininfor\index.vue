<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form :model="state.tableData.param" ref="queryForm" size="default" :inline="true"
                    v-show="showSearch" label-width="68px">
                    <el-form-item label="登录地址" prop="ipaddr">
                        <el-input v-model="state.tableData.param.ipaddr" placeholder="请输入登录地址" clearable
                            style="width: 240px;" />
                    </el-form-item>
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="state.tableData.param.userName" placeholder="请输入用户名称" clearable
                            style="width: 240px;" />
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="登录状态" clearable
                            style="width: 240px">
                            <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="登录时间">
                        <el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
							value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
							end-placeholder="结束日期"></el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>

                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['monitor:logininfor:remove']" type="danger" size="default"
                            :disabled="multiple" @click="handleDelete">
                            <el-icon><ele-DeleteFilled /></el-icon>删除
                        </el-button>
                        <el-button v-auths="['monitor:logininfor:remove']" type="danger" size="default"
                            @click="handleClean">
                            <el-icon><ele-DeleteFilled /></el-icon>清空
                        </el-button>
                        <el-button v-auths="['monitor:logininfor:unlock']" type="primary" size="default"
                            :disabled="single" @click="handleUnlock">
                            <el-icon><ele-Lock /></el-icon>解锁
                        </el-button>
                        <el-button v-auths="['monitor:logininfor:export']" type="warning" size="default"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>导出
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange"
                border style="width: 100%" :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="访问编号" align="center" prop="infoId" />
                <el-table-column label="用户名称" align="center" prop="userName" :show-overflow-tooltip="true"
                    sortable="custom" :sort-orders="['descending', 'ascending']" width="130" />
                <el-table-column label="登录地址" align="center" prop="ipaddr" width="130" :show-overflow-tooltip="true" />
                <el-table-column label="登录地点" align="center" prop="loginLocation" :show-overflow-tooltip="true" />
                <el-table-column label="浏览器" align="center" prop="browser" :show-overflow-tooltip="true" />
                <el-table-column label="操作系统" align="center" prop="os" />
                <el-table-column label="登录状态" align="center" prop="status">
                    <template #default="scope">
                         <DictTag :options="statuslist" :value="scope.row.status"></DictTag>
                        <!-- <el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">{{ scope.row.status == 0 ? '成功' :
                            '失败'
                            }}</el-tag> -->
                    </template>
                </el-table-column>
                <el-table-column label="操作信息" align="center" prop="msg" />
                <el-table-column label="登录日期" align="center" prop="loginTime" sortable="custom"
                    :sort-orders="['descending', 'ascending']" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.loginTime }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, Ref } from 'vue';
import { list, delLogininfor, cleanLogininfor, unlockLogininfor } from '/@/api/monitor/logininfor';
//   import { DictTag, Pagination } from '@/components'; // 假设这些组件已在项目中定义
import { ElMessageBox, ElMessage } from 'element-plus';
import { addDateRange } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { download } from '/@/utils/request';
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            ipaddr: undefined,
            userName: undefined,
            status: '',
            orderByColumn:undefined,
            isAsc:''
        }
    },
});
const defaultSort = reactive({
  prop: 'loginTime',
  order: 'descending'
});
// 显示搜索条件
const showSearch = ref(true);
interface statusOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<statusOption[]>([]); //状态列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref()
const selectName = ref()
const dateRange = ref<[string, string]>(['', '']); //时间范围



// 初始化表格数据 查询登录日志列表
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const data = addDateRange(state.tableData.param, dateRange.value)
        const response = await list(data);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        ipaddr: undefined,
        userName: undefined,
        status: '',
        orderByColumn:undefined,
        isAsc:''
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_common_status')
        // const status = await getDicts('sys_normal_disable');
        // statuslist.value = status.data.data
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { infoId: string; }) => item.infoId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
    selectName.value = selection.map(item => item.userName);
}
// 排序触发事件
const handleSortChange = (column: { prop: undefined; order: string; }) => {
  state.tableData.param.orderByColumn = column.prop;  // 设置排序字段
  state.tableData.param.isAsc = column.order;  // 判断升序或降序
  getTableData();  // 触发数据获取
};
// 删除
const handleDelete = (row: { infoId: Ref<any, any>; }) => {
    const infoIds = row.infoId || ids.value;
    console.log(infoIds);
    
    ElMessageBox.confirm('是否确认删除访问编号为"' + infoIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delLogininfor(infoIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
}
// 清空
const handleClean = () => {
    ElMessageBox.confirm('是否确认清空所有登录日志数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            cleanLogininfor().then(() => {
                getTableData();
                ElMessage.success('清空成功');
            })
        })
        .catch(() => { });
}
// 解锁
const handleUnlock = () => {
    const username = selectName.value
    ElMessageBox.confirm('是否确认解锁用户"' + username + '"数据项?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            unlockLogininfor(username).then(() => {
                getTableData();
                ElMessage.success("用户" + username + "解锁成功");
            })
        })
        .catch(() => { });
}
// 导出
const handleExport = () => {
    download('monitor/logininfor/export', {
        ...state.tableData.param
    }, `logininfor_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
