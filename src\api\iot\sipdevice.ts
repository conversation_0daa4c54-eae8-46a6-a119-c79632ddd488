import request from '/@/utils/request'

// 定义请求参数的类型
interface SipDevice {
  // 这里定义具体的 SipDevice 类型，按需填写
  id: number;
  name: string;
  [key: string]: any; // 可以根据实际情况扩展
}

interface QueryParams {
  [key: string]: any; // 查询参数的类型，根据实际情况设置
}

interface PTZData {
  direction: string;
  speed?: number;
  [key: string]: any;
}

// 查询监控设备列表
export function listSipDevice(query: QueryParams) {
  return request({
    url: '/sip/device/list',
    method: 'get',
    params: query
  })
}

// 查询设备通道列表
export function listSipDeviceChannel(deviceId: number) {
  return request({
    url: `/sip/device/listchannel/${deviceId}`,
    method: 'get'
  })
}

// 查询监控设备详细信息
export function getSipDevice(deviceId: number) {
  return request({
    url: `/sip/device/${deviceId}`,
    method: 'get'
  })
}

// 新增监控设备
export function addSipDevice(data: SipDevice) {
  return request({
    url: '/sip/device',
    method: 'post',
    data
  })
}

// 修改监控设备
export function updateSipDevice(data: SipDevice) {
  return request({
    url: '/sip/device',
    method: 'put',
    data
  })
}

// 删除监控设备
export function delSipDevice(deviceId: number) {
  return request({
    url: `/sip/device/${deviceId}`,
    method: 'delete'
  })
}

// 根据 SIP ID 删除监控设备
export function delSipDeviceBySipId(sipId: string) {
  return request({
    url: `/sip/device/sipid/${sipId}`,
    method: 'delete'
  })
}

// 控制 PTZ 方向
export function ptzdirection(deviceId: number, channelId: number, data: PTZData) {
  return request({
    url: `/sip/ptz/direction/${deviceId}/${channelId}`,
    method: 'post',
    data
  })
}

// 控制 PTZ 缩放
export function ptzscale(deviceId: number, channelId: number, data: PTZData) {
  return request({
    url: `/sip/ptz/scale/${deviceId}/${channelId}`,
    method: 'post',
    data
  })
}
