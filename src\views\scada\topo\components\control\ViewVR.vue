<template>
  <div class="vrClass" @contextmenu.prevent="onContextmenu">
    <div class="PSViewer" ref="psvdbg">
      <div class="vr-placeholder">
        <div class="vr-placeholder-content">
          <h3>VR 全景视图</h3>
          <p>VR 组件需要安装 photo-sphere-viewer 依赖包</p>
          <p>请运行: npm install photo-sphere-viewer</p>
          <div class="vr-info" v-if="detail.style.url">
            <p><strong>全景图片:</strong> {{ detail.style.url }}</p>
            <p><strong>标记点数量:</strong> {{ (detail.style.markers || []).length }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Props
interface Props {
  detail?: any
  editMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  detail: () => ({
    style: {
      url: '',
      markers: [],
      position: {
        w: 800,
        h: 600
      }
    }
  })
})

// 使用默认配置
const detail = computed(() => props.detail || {
  style: {
    url: '',
    markers: [],
    position: {
      w: 800,
      h: 600
    }
  }
})

// 右键菜单处理
const onContextmenu = (event: MouseEvent) => {
  console.log('VR 组件右键菜单', event)
  // 这里可以添加右键菜单逻辑
  return true
}
</script>

<style scoped>
.vrClass {
  height: 100%;
  width: 100%;
}

.PSViewer {
  width: 100%;
  height: 100%;
  position: relative;
}

.vr-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  border-radius: 8px;
}

.vr-placeholder-content {
  padding: 20px;
}

.vr-placeholder-content h3 {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 600;
}

.vr-placeholder-content p {
  margin: 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.vr-info {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.vr-info p {
  margin: 5px 0;
  font-size: 12px;
}
</style>