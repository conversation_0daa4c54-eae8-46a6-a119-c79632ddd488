// src/api/deviceTemplate.ts
import request from '/@/utils/request';

// 定义请求参数和响应数据类型

// 查询设备采集变量模板列表
export function listTemp(query: Record<string, any>) {
  return request({
    url: '/iot/temp/list',
    method: 'get',
    params: query,
  });
}

// 查询设备采集变量模板详细
export function getTemp(templateId: string) {
  return request({
    url: `/iot/temp/${templateId}`,
    method: 'get',
  });
}

// 新增设备采集变量模板
export function addTemp(data: Record<string, any>) {
  return request({
    url: '/iot/temp',
    method: 'post',
    data,
  });
}

// 修改设备采集变量模板
export function updateTemp(data: Record<string, any>) {
  return request({
    url: '/iot/temp',
    method: 'put',
    data,
  });
}

// 删除设备采集变量模板
export function delTemp(templateId: string) {
  return request({
    url: `/iot/temp/${templateId}`,
    method: 'delete',
  });
}

// 根据产品查询采集点关联
export function getDeviceTemp(params: Record<string, any>) {
  return request({
    url: 'iot/iotDevice/getTemp',
    method: 'get',
    params,
  });
}

// 根据产品查询采集点详细信息
export function getTempByPId(params: Record<string, any>) {
  return request({
    url: '/iot/temp/getTempByPid',
    method: 'get',
    params,
  });
}
