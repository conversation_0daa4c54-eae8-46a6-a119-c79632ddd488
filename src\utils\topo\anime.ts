import anime from 'animejs/lib/anime.es.js';

// 定义动画参数接口
interface AnimateOptions {
  targets: Element | Element[] | string; // 动画目标对象
  display?: string; // 控制显示样式，如 block / none
  rotate?: string | number; // 旋转角度
  scale?: string | number; // 缩放比例
  translates?: Array<Record<string, number>>; // 滑动动画帧数组
  duration?: number; // 动画持续时间（毫秒）
  autoplay?: boolean; // 是否自动播放
  loop?: boolean; // 是否循环播放
}

/**
 * 执行动画
 * @param options - 动画配置项
 */
export function animate(options: AnimateOptions): anime.AnimeInstance {
  return anime({
    targets: options.targets,
    display: options.display,
    rotate: options.rotate,
    scale: options.scale,
    keyframes: options.translates,
    duration: options.duration ?? 1000, // 默认 1s
    autoplay: options.autoplay ?? true,
    easing: 'linear',
    loop: options.loop ?? false,
  });
}

/**
 * 获取 anime 实例，用于直接调用其方法（如 set、timeline 等）
 */
export function getAnimate(): typeof anime {
  return anime;
}