<template>
  <div v-show="rulerToggle" :style="{ width: windowWidth + 'px', height: windowHeight + 'px', position: position } as CSSProperties"
       class="ScaleBox" onselectstart="return false;">
    <div id="levelRuler" class="ScaleRuler_h" @mousedown.stop="levelDragRuler">
      <span v-for="(item, index) in xScale" :key="index" :style="{ left: index * 50 + 2 + 'px' }" class="n">{{ item.id }}</span>
    </div>
    <div id="verticalRuler" class="ScaleRuler_v" @mousedown.stop="verticalDragRuler">
      <span v-for="(item, index) in yScale" :key="index" :style="{ top: index * 50 + 2 + 'px' }" class="n">{{ item.id }}</span>
    </div>
    <div id="levelDottedLine" :style="{ top: verticalDottedTop + 'px' }" class="RefDot_h" />
    <div id="verticalDottedLine" :style="{ left: levelDottedLeft + 'px' }" class="RefDot_v" />
    <div v-for="item in levelLineList" :id="item.id" :title="item.title" :style="{ top: item.top + 'px' }" :key="item.id" class="RefLine_h" @mousedown="dragLevelLine(item.id)" />
    <div v-for="item in verticalLineList" :id="item.id" :title="item.title" :style="{ left: item.left + 'px' }" :key="item.id" class="RefLine_v" @mousedown="dragVerticalLine(item.id)" />
    <div id="content" :style="{ left: contentLayout.left + 'px', top: contentLayout.top + 'px' }" style="padding: 18px">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import type { CSSProperties } from 'vue'
interface Line {
  id: string;
  title: string;
  top?: number;
  left?: number;
}

interface Scale {
  id: number;
}

// 状态定义
const position = ref('relative');
const isHotKey = ref(true);
const isScaleRevise = ref(false);
const presetLine = ref<{ type: string; site: number }[]>([]);
const contentLayout = ref({ top: 0, left: 0 });

const windowWidth = ref(0);
const windowHeight = ref(0);
const xScale = ref<Scale[]>([]);
const yScale = ref<Scale[]>([]);
const topSpacing = ref(0);
const leftSpacing = ref(0);
const isDrag = ref(false);
const dragFlag = ref('');
const levelLineList = ref<Line[]>([]);
const verticalLineList = ref<Line[]>([]);
const levelDottedLeft = ref(-999);
const verticalDottedTop = ref(-999);
const rulerWidth = ref(0);
const rulerHeight = ref(0);
const dragLineId = ref('');
const keyCode = ref({ r: 82 });
const rulerToggle = ref(true);

// 初始化方法
const init = () => {
  xScale.value = [];
  yScale.value = [];
  box();
  scaleCalc();
};

// 尺寸计算
const box = () => {
  if (isScaleRevise.value) {
    const content = document.getElementById('content');
    const contentLeft = content?.offsetLeft || 0;
    const contentTop = content?.offsetTop || 0;
    for (let i = 0; i < contentLeft; i += 1) {
      if (i % 50 === 0 && i + 50 <= contentLeft) {
        xScale.value.push({ id: i });
      }
    }
    for (let i = 0; i < contentTop; i += 1) {
      if (i % 50 === 0 && i + 50 <= contentTop) {
        yScale.value.push({ id: i });
      }
    }
  }
  const verticalRulerElement = document.getElementById('verticalRuler');
  const levelRulerElement = document.getElementById('levelRuler');
  rulerWidth.value = verticalRulerElement?.clientWidth || 0;
  rulerHeight.value = levelRulerElement?.clientHeight || 0;

  const rectLevel = levelRulerElement?.getBoundingClientRect();
  const rectVertical = verticalRulerElement?.getBoundingClientRect();

  topSpacing.value = rectLevel?.y || 0;
  leftSpacing.value = rectVertical?.x || 0;
};

const scaleCalc = () => {
  const xCount = Math.ceil(windowWidth.value / 50);
  const yCount = Math.ceil(windowHeight.value / 50);

  xScale.value = Array.from({ length: xCount }, (_, i) => ({ id: i * 50 }));
  yScale.value = Array.from({ length: yCount }, (_, i) => ({ id: i * 50 }));
};

// 拖拽相关方法
const newLevelLine = () => {
  isDrag.value = true;
  dragFlag.value = 'x';
};

const newVerticalLine = () => {
  isDrag.value = true;
  dragFlag.value = 'y';
};

const dottedLineMove = (event: MouseEvent) => {
  switch (dragFlag.value) {
    case 'x':
    case 'l':
      verticalDottedTop.value = event.pageY - topSpacing.value;
      break;
    case 'y':
    case 'v':
      levelDottedLeft.value = event.pageX - leftSpacing.value;
      break;
  }
};

const dottedLineUp = (event: MouseEvent) => {
  if (!isDrag.value) return;
  isDrag.value = false;

  switch (dragFlag.value) {
    case 'x':
      levelLineList.value.push({
        id: `levelLine${levelLineList.value.length + 1}`,
        title: `${event.pageY - topSpacing.value}px`,
        top: event.pageY - topSpacing.value,
      });
      break;
    case 'y':
      verticalLineList.value.push({
        id: `verticalLine${verticalLineList.value.length + 1}`,
        title: `${event.pageX - leftSpacing.value}px`,
        left: event.pageX - leftSpacing.value,
      });
      break;
    case 'l': {
      const idx = levelLineList.value.findIndex((item) => item.id === dragLineId.value);
      if (idx !== -1) {
        levelLineList.value[idx] = {
          id: dragLineId.value,
          title: `${event.pageY - topSpacing.value}px`,
          top: event.pageY - topSpacing.value,
        };
      }
      break;
    }
    case 'v': {
      const idx = verticalLineList.value.findIndex((item) => item.id === dragLineId.value);
      if (idx !== -1) {
        verticalLineList.value[idx] = {
          id: dragLineId.value,
          title: `${event.pageX - leftSpacing.value}px`,
          left: event.pageX - leftSpacing.value,
        };
      }
      break;
    }
  }

  verticalDottedTop.value = -999;
  levelDottedLeft.value = -999;
};

// 标尺点击
const levelDragRuler = newLevelLine;
const verticalDragRuler = newVerticalLine;

const dragLevelLine = (id: string) => {
  isDrag.value = true;
  dragFlag.value = 'l';
  dragLineId.value = id;
};

const dragVerticalLine = (id: string) => {
  isDrag.value = true;
  dragFlag.value = 'v';
  dragLineId.value = id;
};

// 快捷键与预设线
const handleKeyboard = (event: KeyboardEvent) => {
  if (isHotKey.value && event.keyCode === keyCode.value.r) {
    rulerToggle.value = !rulerToggle.value;
  }
};

const quickGeneration = (params: { type: string; site: number }[]) => {
  params.forEach((item) => {
    if (item.type === 'l') {
      levelLineList.value.push({
        id: `levelLine${levelLineList.value.length + 1}`,
        title: `${item.site}px`,
        top: item.site,
      });
    } else if (item.type === 'v') {
      verticalLineList.value.push({
        id: `verticalLine${verticalLineList.value.length + 1}`,
        title: `${item.site}px`,
        left: item.site,
      });
    }
  });
};
let resizeObserver: ResizeObserver;
resizeObserver = new ResizeObserver(() => {
  init(); // 当容器尺寸变化时重新初始化
});
// 生命周期
onMounted(async () => {
  await nextTick(); // 等待 DOM 渲染
  await nextTick(); // 确保动态内容也渲染完成

  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
  rulerToggle.value = true;

  const container = document.querySelector('.ScaleBox');

  window.addEventListener('resize', handleResize);

  // 绑定全局事件
  document.documentElement.addEventListener('mousemove', dottedLineMove, true);
  document.documentElement.addEventListener('mouseup', dottedLineUp, true);
  document.documentElement.addEventListener('keyup', handleKeyboard, true);

  setTimeout(() => {
    if (container) {
      resizeObserver = new ResizeObserver(() => {
        init();
      });
      resizeObserver.observe(container);
    }
    init(); // 延迟初始化标尺
    quickGeneration(presetLine.value);
  }, 200);
});

function handleResize() {
  windowWidth.value = window.innerWidth;
  windowHeight.value = window.innerHeight;
}
onBeforeUnmount(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }

  document.documentElement.removeEventListener('mousemove', dottedLineMove, true);
  document.documentElement.removeEventListener('mouseup', dottedLineUp, true);
  document.documentElement.removeEventListener('keyup', handleKeyboard, true);
});
</script>

<style scoped>
.ScaleBox {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  user-select: none;
  z-index: 999;
}

.ScaleRuler_h,
.ScaleRuler_v,
.RefLine_v,
.RefLine_h,
.RefDot_h,
.RefDot_v {
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  z-index: 999;
}

.ScaleRuler_h {
  width: calc(100% - 18px);
  height: 18px;
  left: 18px;
  opacity: 0.6;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAASCAMAAAAuTX21AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAlQTFRFMzMzAAAA////BqjYlAAAACNJREFUeNpiYCAdMDKRCka1jGoBA2JZZGshiaCXFpIBQIABAAplBkCmQpujAAAAAElFTkSuQmCC)
  repeat-x;
  /*./image/ruler_h.png*/
}

.ScaleRuler_v {
  width: 18px;
  height: calc(100% - 18px);
  top: 18px;
  opacity: 0.6;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAyCAMAAABmvHtTAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAlQTFRFMzMzAAAA////BqjYlAAAACBJREFUeNpiYGBEBwwMTGiAakI0NX7U9aOuHyGuBwgwAH6bBkAR6jkzAAAAAElFTkSuQmCC)
  repeat-y;
  /*./image/ruler_v.png*/
}

.ScaleRuler_v .n,
.ScaleRuler_h .n {
  position: absolute;
  font: 10px/1 Arial, sans-serif;
  color: #333;
  cursor: default;
}

.ScaleRuler_v .n {
  width: 8px;
  left: 3px;
  word-wrap: break-word;
}

.ScaleRuler_h .n {
  top: 1px;
}

.RefLine_v,
.RefLine_h,
.RefDot_h,
.RefDot_v {
  z-index: 998;
}

.RefLine_h {
  width: 100%;
  height: 3px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFSv//AAAAH8VRuAAAAA5JREFUeNpiYIACgAADAAAJAAE0lmO3AAAAAElFTkSuQmCC) repeat-x
  left center;
  /*./image/line_h.png*/
  cursor: n-resize;
  /*url(./image/cur_move_h.cur), move*/
}

.RefLine_v {
  width: 3px;
  height: 100%;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAAICAMAAAAPxGVzAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFSv//AAAAH8VRuAAAAA5JREFUeNpiYEAFAAEGAAAQAAGePof9AAAAAElFTkSuQmCC) repeat-y
  center top;
  /*./image/line_v.png*/
  cursor: w-resize;
  /*url(./image/cur_move_v.cur), move*/
}

.RefDot_h {
  width: 100%;
  height: 3px;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAMAAABFaP0WAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFf39/////F3PnHQAAAAJ0Uk5T/wDltzBKAAAAEElEQVR42mJgYGRgZAQIMAAADQAExkizYQAAAABJRU5ErkJggg==)
  repeat-x left 1px;
  /*./image/line_dot.png*/
  cursor: n-resize;
  /*url(./image/cur_move_h.cur), move*/
  top: -10px;
}

.RefDot_v {
  width: 3px;
  height: 100%;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAIAAAACCAMAAABFaP0WAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAZQTFRFf39/////F3PnHQAAAAJ0Uk5T/wDltzBKAAAAEElEQVR42mJgYGRgZAQIMAAADQAExkizYQAAAABJRU5ErkJggg==)
  repeat-y 1px top;
  /*./image/line_dot.png*/
  cursor: w-resize;
  /*url(./image/cur_move_v.cur), move*/
  left: -10px;
}

#content {
  position: absolute;
  height: 100%;
  width: 100%;
  overflow: auto;
}
</style>
