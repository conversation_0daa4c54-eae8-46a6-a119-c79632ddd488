<template>
  <el-dialog
      :title="!!id ? '修改' : '新增'"
      v-model="open"
      width="25%"
      append-to-body
  >
    <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
    <el-form v-loading="loading" :model="form" ref="formRef" :rules="rules" label-width="80px">
      <el-form-item label="文件类型" prop="categoryName">
        <el-select v-model="form.categoryName" placeholder="请选择文件类型" filterable clearable style="width: 100%">
          <el-option
              v-for="dict in categoryTypes"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="form.fileName" placeholder="请输入文件名称" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSave">保 存</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { getDicts } from '/@/api/system/dict/data'
import { ElMessage } from 'element-plus'
import { getGallery, updateGallery } from '/@/api/scada/gallery'

// Props
const props = defineProps<{
  id: number | null
}>()

interface TypeOption {
  dictValue: string;
  dictLabel: string;
  listClass: string;
  cssClass: string;
}
const categoryTypes = ref<TypeOption[]>([])
// Emits
const emit = defineEmits<{
  (e: 'save'): void
}>()

// 表单引用
const formRef = ref()
const loading = ref(false)
const open = ref(false)

// 表单数据
const form = ref({
  categoryName: '',
  fileName: ''
})

// 表单校验规则
const rules = {
  categoryName: [
    { required: true, message: '请选择文件类型', trigger: 'change' }
  ],
  fileName: [
    { required: true, message: '请输入文件名称', trigger: 'blur' }
  ]
}

// 获取详情数据
const getDetailData = () => {
  if (!props.id) return
  loading.value = true
  getGallery(props.id)
      .then(res => {
        if (res.data.code === 200) {
          form.value = res.data.data
        }
      })
      .finally(() => {
        loading.value = false
      })
}

// 保存逻辑
const handleSave = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      updateGallery(form.value).then(res => {
        if (res.data.code === 200) {
          ElMessage.success('修改成功')
          open.value = false
          emit('save')
        } else {
          ElMessage.error(res.data.msg || '保存失败')
        }
      })
    }
  })
}

// 取消弹窗
const handleCancel = () => {
  open.value = false
}

// 监听 id 变化
watch(
    () => props.id,
    (newId) => {
      if (newId) {
        getDetailData()
      }
    }
)

// 打开弹窗的方法（供父组件调用）
const openDialog = (id?: number) => {

  if (id !== undefined) {
    // 如果有传入 id，则加载数据
    getGallery(id).then(res => {
      if (res.data.code === 200) {
        form.value = res.data.data
      }
    })
  }
  getDatas()
  open.value = true
}
// 获取分类和列表
async function getDatas() {
  const dictType = 'scada_gallery_type'
  try {
    const response = await getDicts(dictType);
    const res = response.data
    if (res.code === 200) {
      categoryTypes.value = res.data
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取分类失败', error)
  }
}

// 暴露方法给父组件使用
defineExpose({ openDialog })
</script>