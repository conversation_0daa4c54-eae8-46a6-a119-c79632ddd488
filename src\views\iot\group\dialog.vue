<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="500">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="上级设备" prop="parentId" v-if="state.ruleForm.parentId != '0'">
                            <el-tree-select v-model="state.ruleForm.parentId" :props="defaultProps"
                                :data="state.groupData" :render-after-expand="true"
                                :show-count="true" @node-click="handleNodeClick" check-strictly>
                                <template #default="{ node, data }">
                                    <span>{{ data.groupName }}</span>
                                    <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
                                </template>
                            </el-tree-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="分组名称" prop="groupName">
                            <el-input v-model="state.ruleForm.groupName" placeholder="请输入分组名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="分组排序" prop="groupOrder">
                            <el-input v-model="state.ruleForm.groupOrder" placeholder="请输入分组排序" clearable></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="备注">
                        <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
                            maxlength="150"></el-input>
                    </el-form-item>
                </el-col>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { addCategory, getCategory, updateCategory } from '/@/api/iot/category';
import { addGroup, getGroup, listGroup, updateGroup } from '/@/api/iot/group';
import { handleTree } from '/@/utils/next';

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const defaultProps = reactive({
    children: "children",
    label: "groupName",
    value: 'groupId'
});
// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        groupName: '',
        groupOrder: 0,
        remark: '',
        groupId: '',
        parentId:''
    },
    groupData: [],
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    groupData: [...initialState.groupData],
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    groupName: [
        {
            required: true,
            message: '分组名称不能为空',
            trigger: 'blur',
        },
    ],
    groupOrder: [
        {
            required: true,
            message: '分组排序不能为空,最大值为99',
            trigger: 'blur',
        },
    ],

})
// 打开弹窗
const openDialog = (type: string, row: groupType, groupId: string) => {
    if (type === 'edit') {

        if (row != undefined) {
            getGroup(row.groupId).then(response => {
                state.ruleForm = response.data.data

            });

        } else {
            getGroup(groupId).then(response => {
                state.ruleForm = response.data.data

            });
        }
        state.dialog.title = '修改设备分组';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        if (row) {
			state.ruleForm.parentId = row.groupId
		}
        state.dialog.title = '新增设备分组';
        state.dialog.submitTxt = '新 增';
    }
    getGroupList()
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.groupId != '') {
                updateGroup(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addGroup(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 点击节点时更新父节点
const handleNodeClick = (node: any, data: any) => {
	// 判断是否是父节点
	// if (!node.isLeaf) {
	// 如果是父节点，更新模型值
	state.ruleForm.parentId = data.data.groupId;
	console.log(state.ruleForm.parentId);

	// }
};
// 初始化部门数据
const getGroupList = () => {
    listGroup({ groupName: undefined }).then(response => {
        state.groupData = handleTree(response.data.data, "groupId") as any;

    });
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
