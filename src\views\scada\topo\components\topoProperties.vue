<template>
  <div class="topo-properties">
    <div class="nav-bar-wrap">
      <template v-if="configObject && !isLayer">
        <img v-if="configObject.type === 'image'" class="img-wrap" :src="configObject.style.url" alt="Config Image" />
      </template>
    </div>
    <template v-if="configObject != null && isLayer == false">
      <div class="tabs-wrap">
        <div class="tab-item" @click="changeTab(0)" :class="{ 'tab-item-active': tabIndex === 0 }" >组件样式</div>
        <div class="tab-item" @click="changeTab(1)" :class="{ 'tab-item-active': tabIndex === 1 }" >数据绑定</div>
      </div>
      <div class="table-wrap">
        <!-- 组件样式 -->
        <div class="table-item" v-show="tabIndex == 0">
          <el-collapse class="collapse-wrap" v-model="styleCollapseActive">
            <el-collapse-item title="位置与尺寸" name="1">
              <el-form label-width="55px">
                <el-form-item label="X坐标">
                  <el-input-number
                      v-model="configObject.style.position.x"
                      placeholder="请填写X坐标"
                      controls-position="right"
                      size="small"
                      style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="Y坐标">
                  <el-input-number
                      v-model="configObject.style.position.y"
                      placeholder="请填写Y坐标"
                      controls-position="right"
                      size="small"
                      style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="宽度">
                  <el-input-number
                      v-model="configObject.style.position.w"
                      placeholder="请填写宽度"
                      controls-position="right"
                      size="small"
                      style="width: 100%"
                  />
                </el-form-item>
                <el-form-item label="高度">
                  <el-input-number
                      v-model="configObject.style.position.h"
                      placeholder="请填写高度"
                      controls-position="right"
                      size="small"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <el-collapse-item title="基本样式" name="2">
              <el-form label-width="72px">
                <el-form-item label="组件名称" v-if="configObject.name !== undefined">
                  <el-input placeholder="请填写组件名称" v-model="configObject.name" size="small" clearable />
                </el-form-item>
                <el-form-item label="最小值" v-if="configObject.dataBind.paramMin !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写最小值" v-model="configObject.dataBind.paramMin" size="small" />
                </el-form-item>
                <el-form-item label="最大值" v-if="configObject.dataBind.paramMax !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写最大值" v-model="configObject.dataBind.paramMax" size="small" />
                </el-form-item>
                <el-form-item label="刻度间隔" v-if="configObject.dataBind.interval !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写刻度间隔" v-model="configObject.dataBind.interval" size="small" />
                </el-form-item>
                <el-form-item label="天气样式" v-if="configObject.style.weatherModel !== undefined">
                  <el-select v-model="configObject.style.weatherModel" placeholder="请选择天气样式" style="width: 100%" size="small">
                    <el-option label="完整模式" value="完整模式">
                      <span style="font-size: 12px">完整模式</span>
                    </el-option>
                    <el-option label="简约模式" value="简约模式">
                      <span style="font-size: 12px">简约模式</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="地图主题" v-if="configObject.style.mapModel !== undefined">
                  <el-select v-model="configObject.style.mapModel" placeholder="请选择地图主题" style="width: 100%" size="small">
                    <el-option label="默认主题" value="normal">
                      <span style="font-size: 12px">默认主题</span>
                    </el-option>
                    <el-option label="清新蓝" value="light">
                      <span style="font-size: 12px">清新蓝</span>
                    </el-option>
                    <el-option label="黑夜" value="dark">
                      <span style="font-size: 12px">黑夜</span>
                    </el-option>
                    <el-option label="红色警戒" value="redalert">
                      <span style="font-size: 12px">红色警戒</span>
                    </el-option>
                    <el-option label="精简" value="googlelite">
                      <span style="font-size: 12px">精简</span>
                    </el-option>
                    <el-option label="自然绿" value="grassgreen">
                      <span style="font-size: 12px">自然绿</span>
                    </el-option>
                    <el-option label="午夜蓝" value="midnight">
                      <span style="font-size: 12px">午夜蓝</span>
                    </el-option>
                    <el-option label="浪漫粉" value="pink">
                      <span style="font-size: 12px">浪漫粉</span>
                    </el-option>
                    <el-option label="青春绿" value="darkgreen">
                      <span style="font-size: 12px">青春绿</span>
                    </el-option>
                    <el-option label="清新蓝绿" value="bluish">
                      <span style="font-size: 12px">清新蓝绿</span>
                    </el-option>
                    <el-option label="高端灰风" value="grayscale">
                      <span style="font-size: 12px">高端灰风</span>
                    </el-option>
                    <el-option label="强边界" value="hardedge">
                      <span style="font-size: 12px">强边界</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="图层高度">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写图层高度" v-model="configObject.style.zIndex" size="small" />
                </el-form-item>
                <el-form-item label="背景颜色" v-if="isLayer === false && configObject.type !== 'map'">
                  <div style="height: 36px">
                    <el-color-picker v-model="componentBackColor" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <template v-if="isLayer === false && configObject.type !== 'flow-bar' && configObject.type !== 'flow-bar-dynamic' && configObject.type !== 'map'">
                  <el-form-item label="透明度">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="0.5" v-model="configObject.style.opacity" size="small" />
                  </el-form-item>
                  <el-form-item label="边框圆角">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="10" v-model="configObject.style.borderRadius" size="small" />
                  </el-form-item>
                  <el-form-item label="阴影长度">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="20" v-model="configObject.style.boxShadowWidth" size="small" />
                  </el-form-item>
                  <el-form-item label="阴影颜色">
                    <div style="height: 36px">
                      <el-color-picker v-model="configObject.style.boxShadowColor" show-alpha :predefine="predefineColors" color-format="hex" />
                    </div>
                  </el-form-item>
                </template>
                <el-form-item label="统计类型" v-if="configObject.componentShow && configObject.componentShow.indexOf('设备统计') > -1">
                  <el-select v-model="configObject.style.pieType" placeholder="请选择统计类型" style="width: 100%" size="small">
                    <el-option label="设备状态" value="设备状态">
                      <span style="font-size: 12px">设备状态</span>
                    </el-option>
                    <el-option label="报警状态" value="报警状态">
                      <span style="font-size: 12px">报警状态</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="图片选择" v-if="configObject.style.url !== undefined && configObject.style.url !== null">
                  <el-button size="default" @click="handleGalleryClick('图片路径')">上传图片</el-button>
                </el-form-item>
                <el-form-item label="文字" v-if="configObject.style.text !== undefined">
                  <el-input placeholder="请填写文字" v-model="configObject.style.text" size="small" clearable />
                </el-form-item>
                <el-form-item label="对齐方式" v-if="configObject.style.textAlign !== undefined">
                  <el-select v-model="configObject.style.textAlign" placeholder="请选择对齐方式" style="width: 100%" size="small">
                    <el-option label="居左" value="left">
                      <span style="font-size: 12px">居左</span>
                    </el-option>
                    <el-option label="居中" value="center">
                      <span style="font-size: 12px">居中</span>
                    </el-option>
                    <el-option label="居右" value="right">
                      <span style="font-size: 12px">居右</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="字体类型" v-if="configObject.style.fontFamily !== undefined">
                  <el-select v-model="configObject.style.fontFamily" placeholder="请选择字体类型" style="width: 100%" size="small">
                    <el-option :label="item" :value="item" v-for="item in fontFamilyOptions" :key="item">
                      <span style="font-size: 12px">{{ item }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="字体大小" v-if="configObject.style.fontSize !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写字体大小" v-model="configObject.style.fontSize" size="small" />
                </el-form-item>
                <el-form-item label="滤镜/阴影" v-if="configObject.componentShow && configObject.componentShow.indexOf('滤镜渲染') > -1">
                  <el-radio-group v-model="configObject.style.isFilter" size="small">
                    <el-radio :value="true">滤镜</el-radio>
                    <el-radio :value="false">阴影</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="组件颜色" v-if="configObject.componentShow && configObject.componentShow.indexOf('组件颜色') > -1">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.foreColor" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="组件显隐">
                  <el-select v-model="configObject.style.visible" placeholder="请选择组件显隐" style="width: 100%" size="small">
                    <el-option label="显示" :value="true">
                      <span style="font-size: 12px">显示</span>
                    </el-option>
                    <el-option label="隐藏" :value="false">
                      <span style="font-size: 12px">隐藏</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="代码视图" v-if="configObject.componentShow && configObject.componentShow.indexOf('自定义echarts') > -1">
                  <el-button link size="small" @click="echartClick"><el-icon><EditPen /></el-icon>编辑</el-button>
                </el-form-item>
                <el-form-item label="地图选择" v-if="configObject.componentShow && configObject.componentShow.indexOf('地图文件') > -1">
                  <el-select v-model="configObject.dataBind.mapAddress" placeholder="请选择地图" filterable style="width: 100%" size="small">
                    <el-option label="安徽" value="安徽">
                      <span style="font-size: 12px">安徽</span>
                    </el-option>
                    <el-option label="澳门" value="澳门">
                      <span style="font-size: 12px">澳门</span>
                    </el-option>
                    <el-option label="北京" value="北京">
                      <span style="font-size: 12px">北京</span>
                    </el-option>
                    <el-option label="重庆" value="重庆">
                      <span style="font-size: 12px">重庆</span>
                    </el-option>
                    <el-option label="福建" value="福建">
                      <span style="font-size: 12px">福建</span>
                    </el-option>
                    <el-option label="甘肃" value="甘肃">
                      <span style="font-size: 12px">甘肃</span>
                    </el-option>
                    <el-option label="广东" value="广东">
                      <span style="font-size: 12px">广东</span>
                    </el-option>
                    <el-option label="广西" value="广西">
                      <span style="font-size: 12px">广西</span>
                    </el-option>
                    <el-option label="贵州" value="贵州">
                      <span style="font-size: 12px">贵州</span>
                    </el-option>
                    <el-option label="海南" value="海南">
                      <span style="font-size: 12px">海南</span>
                    </el-option>
                    <el-option label="河北" value="河北">
                      <span style="font-size: 12px">河北</span>
                    </el-option>
                    <el-option label="黑龙江" value="黑龙江">
                      <span style="font-size: 12px">黑龙江</span>
                    </el-option>
                    <el-option label="河南" value="河南">
                      <span style="font-size: 12px">河南</span>
                    </el-option>
                    <el-option label="湖北" value="湖北">
                      <span style="font-size: 12px">湖北</span>
                    </el-option>
                    <el-option label="湖南" value="湖南">
                      <span style="font-size: 12px">湖南</span>
                    </el-option>
                    <el-option label="江苏" value="江苏">
                      <span style="font-size: 12px">江苏</span>
                    </el-option>
                    <el-option label="江西" value="江西">
                      <span style="font-size: 12px">江西</span>
                    </el-option>
                    <el-option label="吉林" value="吉林">
                      <span style="font-size: 12px">吉林</span>
                    </el-option>
                    <el-option label="辽宁" value="辽宁">
                      <span style="font-size: 12px">辽宁</span>
                    </el-option>
                    <el-option label="内蒙古" value="内蒙古">
                      <span style="font-size: 12px">内蒙古</span>
                    </el-option>
                    <el-option label="宁夏" value="宁夏">
                      <span style="font-size: 12px">宁夏</span>
                    </el-option>
                    <el-option label="青海" value="青海">
                      <span style="font-size: 12px">青海</span>
                    </el-option>
                    <el-option label="山东" value="山东">
                      <span style="font-size: 12px">山东</span>
                    </el-option>
                    <el-option label="上海" value="上海">
                      <span style="font-size: 12px">上海</span>
                    </el-option>
                    <el-option label="山西" value="山西">
                      <span style="font-size: 12px">山西</span>
                    </el-option>
                    <el-option label="四川" value="四川">
                      <span style="font-size: 12px">四川</span>
                    </el-option>
                    <el-option label="台湾" value="台湾">
                      <span style="font-size: 12px">台湾</span>
                    </el-option>
                    <el-option label="天津" value="天津">
                      <span style="font-size: 12px">天津</span>
                    </el-option>
                    <el-option label="香港" value="香港">
                      <span style="font-size: 12px">香港</span>
                    </el-option>
                    <el-option label="新疆" value="新疆">
                      <span style="font-size: 12px">新疆</span>
                    </el-option>
                    <el-option label="西藏" value="西藏">
                      <span style="font-size: 12px">西藏</span>
                    </el-option>
                    <el-option label="云南" value="云南">
                      <span style="font-size: 12px">云南</span>
                    </el-option>
                    <el-option label="浙江" value="浙江">
                      <span style="font-size: 12px">浙江</span>
                    </el-option>
                    <el-option label="自定义" value="自定义">
                      <span style="font-size: 12px">自定义</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="地图数据" v-if="configObject.dataBind.mapAddress == '自定义'">
                  <el-input placeholder="请填写地图数据地址" v-model="configObject.dataBind.mapUrl" size="small" />
                </el-form-item>
                <el-form-item label="图表刷新" v-if="configObject.componentShow && (configObject.componentShow.indexOf('自定义echarts') > -1 || configObject.componentShow.indexOf('地图文件') > -1)">
                  <el-input placeholder="60 (秒)" v-model="configObject.dataBind.echartSecond" size="small" />
                </el-form-item>
                <el-form-item label="悬浮提示" v-if="configObject.componentShow && configObject.componentShow.indexOf('悬浮提示') > -1">
                  <el-radio-group v-model="configObject.style.tipDIs" size="small">
                    <el-radio :value="true">开启</el-radio>
                    <el-radio :value="false">关闭</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="悬浮模式" v-if="configObject.componentShow && configObject.componentShow.indexOf('悬浮提示') > -1">
                  <el-radio-group v-model="configObject.style.tipShow" size="small">
                    <el-radio :value="true">长显</el-radio>
                    <el-radio :value="false">短显</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="悬浮位置" v-if="configObject.componentShow && configObject.componentShow.indexOf('悬浮提示') > -1">
                  <el-select v-model="configObject.style.placement" placeholder="请选择悬浮位置" style="width: 100%" size="small">
                    <el-option label="上边" value="top">
                      <span style="font-size: 12px">上边</span>
                    </el-option>
                    <el-option label="上左" value="top-start">
                      <span style="font-size: 12px">上左</span>
                    </el-option>
                    <el-option label="上右" value="top-end">
                      <span style="font-size: 12px">上右</span>
                    </el-option>
                    <el-option label="下边" value="bottom">
                      <span style="font-size: 12px">下边</span>
                    </el-option>
                    <el-option label="下左" value="bottom-start">
                      <span style="font-size: 12px">下左</span>
                    </el-option>
                    <el-option label="下右" value="bottom-end">
                      <span style="font-size: 12px">下右</span>
                    </el-option>
                    <el-option label="左边" value="left">
                      <span style="font-size: 12px">左边</span>
                    </el-option>
                    <el-option label="左上" value="left-start">
                      <span style="font-size: 12px">左上</span>
                    </el-option>
                    <el-option label="左下" value="left-end">
                      <span style="font-size: 12px">左下</span>
                    </el-option>
                    <el-option label="右边" value="right">
                      <span style="font-size: 12px">右边</span>
                    </el-option>
                    <el-option label="右上" value="right-start">
                      <span style="font-size: 12px">右上</span>
                    </el-option>
                    <el-option label="右下" value="right-end">
                      <span style="font-size: 12px">右下</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="悬浮样式" v-if="configObject.componentShow && configObject.componentShow.indexOf('悬浮提示') > -1">
                  <el-select v-model="configObject.style.effect" placeholder="请选择悬浮样式" style="width: 100%" size="small">
                    <el-option label="暗黑" value="dark">
                      <span style="font-size: 12px">暗黑</span>
                    </el-option>
                    <el-option label="明亮" value="light">
                      <span style="font-size: 12px">明亮</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="统计栏目" v-if="configObject.componentShow && configObject.componentShow.indexOf('数统计') > -1">
                  <el-select v-model="configObject.dataBind.staticType" placeholder="请选择统计栏目" style="width: 100%" size="small">
                    <el-option v-for="item in paramNameList" :key="item" :label="item" :value="item">
                      <span style="font-size: 12px">{{ item }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="线条宽度" v-if="configObject.style.lineWidth">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写线条宽度" v-model="configObject.style.lineWidth" size="small" />
                </el-form-item>
                <!-- 新版水流样式设置开始 -->
                <template v-if="configObject.componentShow && configObject.componentShow.indexOf('水流') > -1">
                  <el-form-item label="线条高度">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="请填写线条高度" v-model="configObject.style.lineHeight" size="small" />
                  </el-form-item>
                  <el-form-item label="线条间隔">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="请填写线条间隔" v-model="configObject.style.lineInterval" size="small" />
                  </el-form-item>
                  <el-form-item label="线条形状">
                    <el-select v-model="configObject.style.lineType" placeholder="请选择线条形状" style="width: 100%" size="small">
                      <el-option label="矩形" value="butt">
                        <span style="font-size: 12px">矩形</span>
                      </el-option>
                      <el-option label="椭圆" value="round">
                        <span style="font-size: 12px">椭圆</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="流动方向">
                    <el-select v-model="configObject.style.animations" placeholder="请选择流动方向" style="width: 100%" size="small">
                      <el-option label="正向" value="正向">
                        <span style="font-size: 12px">正向</span>
                      </el-option>
                      <el-option label="反向" value="反向">
                        <span style="font-size: 12px">反向</span>
                      </el-option>
                      <el-option label="静止" value="静止">
                        <span style="font-size: 12px">静止</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="流动速度">
                    <el-select v-model="configObject.style.speed" placeholder="请选择流动速度" style="width: 100%" size="small">
                      <el-option label="快" value="快">
                        <span style="font-size: 12px">快</span>
                      </el-option>
                      <el-option label="中" value="中">
                        <span style="font-size: 12px">中</span>
                      </el-option>
                      <el-option label="慢" value="慢">
                        <span style="font-size: 12px">慢</span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="锚点个数">
                    <el-input-number style="width: 100%" controls-position="right" placeholder="请填写锚点个数" v-model="configObject.style.anchorPointNum" size="small" />
                  </el-form-item>
                </template>
                <!-- 水流样式设置结束 -->
                <!-- 水球图样式 -->
                <el-form-item label="液位形状" v-if="configObject.style.waterShape">
                  <el-select v-model="configObject.style.waterShape" placeholder="请选择液位形状" style="width: 100%" size="small">
                    <el-option label="容器" value="container">
                      <span style="font-size: 12px">容器</span>
                    </el-option>
                    <el-option label="圆形" value="circle">
                      <span style="font-size: 12px">圆形</span>
                    </el-option>
                    <el-option label="矩形" value="rect">
                      <span style="font-size: 12px">矩形</span>
                    </el-option>
                    <el-option label="圆角矩形" value="roundRect">
                      <span style="font-size: 12px">圆角矩形</span>
                    </el-option>
                    <el-option label="三角形" value="triangle">
                      <span style="font-size: 12px">三角形</span>
                    </el-option>
                    <el-option label="菱形" value="diamond">
                      <span style="font-size: 12px">菱形</span>
                    </el-option>
                    <el-option label="热气球形" value="pin">
                      <span style="font-size: 12px">热气球形</span>
                    </el-option>
                    <el-option label="倒三角形" value="arrow">
                      <span style="font-size: 12px">倒三角形</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="波浪颜色" v-if="configObject.style.waterShape">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.waterColor" show-alpha :predefine="predefineColors" />
                  </div>
                </el-form-item>
                <el-form-item label="字体大小" v-if="configObject.style.waterFontSize !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写字体大小" v-model="configObject.style.waterFontSize" size="small" />
                </el-form-item>
                <el-form-item label="边框宽度" v-if="configObject.style.waterBorderWidth !== undefined">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写边框宽度" v-model="configObject.style.waterBorderWidth" size="small" />
                </el-form-item>
                <el-form-item label="边框颜色" v-if="configObject.style.waterBorderColor !== undefined">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.waterBorderColor" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="边框背景色" v-if="configObject.style.waterBackColor !== undefined">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.waterBackColor" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 滚动表格开始 -->
            <el-collapse-item title="表格样式" name="3" v-if="configObject.componentShow && configObject.componentShow.indexOf('轮播表') > -1">
              <el-form label-width="84px">
                <el-form-item label="行数">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写行数" v-model="configObject.style.rowNum" size="small" />
                </el-form-item>
                <el-form-item label="字体颜色">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.foreColor" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="表头背景色">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.headerBGC" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="奇数行背景色">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.oddRowBGC" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="偶数行背景色">
                  <div style="height: 36px">
                    <el-color-picker v-model="configObject.style.evenRowBGC" show-alpha :predefine="predefineColors" color-format="hex" />
                  </div>
                </el-form-item>
                <el-form-item label="轮播时间间隔">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写轮播时间间隔" v-model="configObject.style.waitTime" size="small" :min="1000" />
                </el-form-item>
                <el-form-item label="表头高度">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写表头高度" v-model="configObject.style.headerHeight" size="small" />
                </el-form-item>
                <el-form-item label="表头宽度">
                  <el-input-number style="width: 100%" controls-position="right" placeholder="请填写表头宽度" v-model="configObject.style.columnWidth" size="small" />
                </el-form-item>
                <el-form-item label="是否显示行号">
                  <el-radio-group v-model="configObject.style.index" size="small">
                    <el-radio :value="true">显示</el-radio>
                    <el-radio :value="false">隐藏</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="轮播方式">
                  <el-select v-model="configObject.style.carousel" placeholder="请选择轮播方式" style="width: 100%" size="small">
                    <el-option label="行" value="single">
                      <span style="font-size: 12px">行</span>
                    </el-option>
                    <el-option label="页" value="page">
                      <span style="font-size: 12px">页</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 滚动表格结束 -->
          </el-collapse>
        </div>
        <!-- 数据绑定 -->
        <div class="table-item" v-show="tabIndex == 1">
          <el-collapse class="collapse-wrap" v-model="dataCollapseActive">
            <!-- 参数绑定开始 -->
            <el-collapse-item title="参数绑定" name="1" v-if="configObject.componentShow && configObject.componentShow.indexOf('参数绑定') > -1">
              <el-form label-width="65px">
                <el-form-item label="状态类型" v-if="configObject.componentShow && configObject.componentShow.indexOf('设备状态') > -1">
                  <el-select v-model="configObject.dataBind.activeName" placeholder="请选择状态" style="width: 100%" size="small">
                    <el-option label="变量状态" value="变量状态">
                      <span style="font-size: 12px">变量状态</span>
                    </el-option>
                    <el-option label="设备状态" value="设备状态">
                      <span style="font-size: 12px">设备状态</span>
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item label="设备状态" v-if="configObject.dataBind.activeName == '设备状态'">
                  <el-select v-model="configObject.dataBind.serialNumber" placeholder="请选择设备" style="width: 100%" size="small" filterable>
                    <el-option :label="item.deviceName" :value="item.serialNumber" v-for="item in deviceBindList" :key="item.serialNumber">
                      <span style="font-size: 12px">{{ item.deviceName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="变量状态" v-else>
                  <el-input
                      v-model="configObject.dataBind.modelName"
                      placeholder="请选择变量"
                      size="default"
                      readonly
                  >
                    <template #append>
                      <el-button size="default" @click="selectVariable('参数绑定')">选择</el-button>
                    </template>
                  </el-input>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 参数绑定结束 -->
            <!-- 组态界面开始 -->
            <el-collapse-item title="数据源" name="2" v-if="configObject && configObject.componentShow.indexOf('组态界面') > -1">
              <el-form label-width="65px">
                <el-form-item label="组态界面">
                  <el-select v-model="configObject.dataBind.ztPageData" placeholder="请选择组态界面" style="width: 100%" size="small">
                    <el-option v-for="item in ztOption" :key="item.id" :label="item.pageName" :value="item.id + '&' + item.guid">
                      <span style="font-size: 12px">{{ item.pageName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 组态界面结束 -->
            <!-- 萤石云开始 -->
            <el-collapse-item title="萤石云" name="3" v-if="configObject.componentShow && configObject.componentShow.indexOf('萤石云') > -1">
              <el-form label-width="72px">
                <el-form-item label="设备序列号">
                  <el-input placeholder="萤石云控制台获取" v-model="configObject.dataBind.serialNumber" size="small" clearable />
                </el-form-item>
                <el-form-item label="通道号">
                  <el-input placeholder="萤石云控制台获取" v-model="configObject.dataBind.channelNumber" size="small" clearable />
                </el-form-item>
                <el-form-item label="Token">
                  <el-input placeholder="萤石云控制台获取" v-model="configObject.dataBind.accessToken" size="small" clearable />
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 萤石云结束 -->
            <!-- 直播视频开始 -->
            <el-collapse-item title="直播视频" name="4" v-if="configObject.componentShow && configObject.componentShow.indexOf('直播视频') > -1">
              <el-form label-width="72px">
                <el-form-item label="视频流URL">
                  <el-input placeholder="Flv格式直播流" v-model="configObject.videoUrl" size="small" clearable />
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 直播视频结束 -->
            <!-- 通用视频开始 -->
            <el-collapse-item title="通用视频" name="5" v-if="configObject.componentShow && configObject.componentShow.indexOf('通用视频') > -1">
              <el-form label-width="65px">
                <el-form-item label="视频地址">
                  <el-input placeholder="MP4视频地址" v-model="configObject.videoUrl" size="small" clearable />
                </el-form-item>
                <el-form-item label="封面地址">
                  <el-input placeholder="视频封面地址" v-model="configObject.imageUrl" size="small" clearable />
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 通用视频结束 -->
            <!-- 通用视频开始 -->
            <el-collapse-item title="三维" name="6" v-if="configObject.componentShow && configObject.componentShow.indexOf('三维') > -1">
              <el-form label-width="65px">
                <el-form-item label="三维场景">
                  <el-select v-model="configObject.imageUrl" placeholder="请联系商务人员获取" @change="selectModelChange" style="width: 100%" size="small">
                    <el-option :label="item.modelName" :value="item.imageUrl" :key="item.id" v-for="item in ztModelOption">
                      <span style="font-size: 12px">{{ item.modelName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 通用视频结束 -->
            <!-- echarts图表地址开始 -->
            <el-collapse-item
                title="自定义echarts/地图文件"
                name="7"
                v-if="configObject.componentShow && (
            configObject.componentShow.includes('自定义echarts') ||
            configObject.componentShow.includes('地图文件')
          )"
            >
              <el-form label-width="65px" label-position="top">
                <el-form-item label="引擎地址">
                  <el-input
                      placeholder="目前仅支持GET请求"
                      v-model="configObject.dataBind.echartUrl"
                      size="small"
                      clearable
                  />
                </el-form-item>
                <el-form-item label="响应示例">
                  <JsonViewer :data="configObject.dataBind.echartData" />
                </el-form-item>
                <el-form-item style="float: right" v-if="configObject.dataBind.echartUrl">
                  <el-button
                      type="primary"
                      @click="echartHttpClick(configObject.dataBind.echartUrl)"
                      size="default"
                  >请求
                  </el-button>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- echarts图表地址结束 -->
            <!--事件点击开始 -->
            <el-collapse-item title="事件" name="8" v-if="configObject.componentShow && configObject.componentShow.indexOf('单击') > -1">
              <div class="ixd-wrap">
                <el-checkbox v-model="configObject.dataBind.djAction">单击</el-checkbox>
                <el-button link :disabled="!configObject.dataBind.djAction" @click="configClick('单击')"><el-icon><EditPen /></el-icon></el-button>
              </div>
            </el-collapse-item>
            <!-- 事件点击结束 -->
            <!-- 新版线条流动判断开始 -->
            <el-collapse-item title="流动" name="9" v-if="configObject.componentShow && configObject.componentShow.indexOf('水流') > -1">
              <el-form label-width="65px">
                <el-form-item label="流动条件">
                    <div class="input-wrap">
                      <el-input
                          type="number"
                          placeholder="请输入值"
                          v-model.number="configObject.dataAction.paramJudgeData1"
                          size="default"
                          style="width: 180px;"
                      >
                        <template #prepend>
                          <el-select v-model="configObject.dataAction.paramJudge1" placeholder="请选择" size="default" style="width: 66px;">
                            <el-option label=">" value="大于">
                              <span style="font-size: 12px">&gt;</span>
                            </el-option>
                            <el-option label=">=" value="大于等于">
                              <span style="font-size: 12px">&gt;=</span>
                            </el-option>
                            <el-option label="=" value="等于">
                              <span style="font-size: 12px">=</span>
                            </el-option>
                            <el-option label="<=" value="小于等于">
                              <span style="font-size: 12px">&lt;=</span>
                            </el-option>
                            <el-option label="<" value="小于">
                              <span style="font-size: 12px">&lt;</span>
                            </el-option>
                            <el-option label="!=" value="不等于">
                              <span style="font-size: 12px">!=</span>
                            </el-option>
                          </el-select>
                        </template>
                      </el-input>
                    </div>
                </el-form-item>
                <el-form-item label="流动方向">
                  <el-radio-group v-model="configObject.dataAction.direction" size="small">
                    <el-radio label="正向">正向</el-radio>
                    <el-radio label="反向">反向</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="流动条件">
                  <div class="input-wrap">
                    <el-input
                        type="number"
                        placeholder="请输入值"
                        v-model.number="configObject.dataAction.paramJudgeData2"
                        size="default"
                        style="width: 180px;"
                    >
                      <template #prepend>
                        <el-select v-model="configObject.dataAction.paramJudge2" placeholder="请选择" size="default" style="width: 66px;">
                          <el-option label=">" value="大于">
                            <span style="font-size: 12px">&gt;</span>
                          </el-option>
                          <el-option label=">=" value="大于等于">
                            <span style="font-size: 12px">&gt;=</span>
                          </el-option>
                          <el-option label="=" value="等于">
                            <span style="font-size: 12px">=</span>
                          </el-option>
                          <el-option label="<=" value="小于等于">
                            <span style="font-size: 12px">&lt;=</span>
                          </el-option>
                          <el-option label="<" value="小于">
                            <span style="font-size: 12px">&lt;</span>
                          </el-option>
                          <el-option label="!=" value="不等于">
                            <span style="font-size: 12px">!=</span>
                          </el-option>
                        </el-select>
                      </template>
                    </el-input>
                  </div>
                </el-form-item>
                <el-form-item label="流动方向">
                  <el-radio-group v-model="configObject.dataAction.direction01" size="small">
                    <el-radio label="正向">正向</el-radio>
                    <el-radio label="反向">反向</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-collapse-item>
            <!-- 线条流动判断结束 -->
            <!-- 闪烁、旋转、显隐动画 -->
            <el-collapse-item title="动画-唯一生效" name="10" v-if="configObject.componentShow && configObject.componentShow.includes('动画')">
              <!-- 显隐 -->
              <div class="ixd-wrap">
                <el-checkbox v-model="configObject.dataBind.xyAction">显隐</el-checkbox>
                <el-button link :disabled="!configObject.dataBind.xyAction" @click="configClick('显隐')">
                  <el-icon><EditPen /></el-icon>
                </el-button>
              </div>

              <!-- 旋转 -->
              <div class="ixd-wrap">
                <el-checkbox v-model="configObject.dataBind.xzAction">旋转</el-checkbox>
                <el-button link :disabled="!configObject.dataBind.xzAction" @click="configClick('旋转')">
                  <el-icon><EditPen /></el-icon>
                </el-button>
              </div>

              <!-- 闪烁 -->
              <div class="ixd-wrap">
                <el-checkbox v-model="configObject.dataBind.ssAction">闪烁</el-checkbox>
                <el-button link :disabled="!configObject.dataBind.ssAction" @click="configClick('闪烁')">
                  <el-icon><EditPen /></el-icon>
                </el-button>
              </div>

              <!-- 滑动 -->
              <div class="ixd-wrap">
                <el-checkbox v-model="configObject.dataBind.hdAction">滑动</el-checkbox>
                <el-button link :disabled="!configObject.dataBind.hdAction" @click="configClick('滑动')">
                  <el-icon><EditPen /></el-icon>
                </el-button>
              </div>
            </el-collapse-item>
            <!-- 状态开关开始 -->
            <el-collapse-item title="状态开关" name="11" v-if="configObject.componentShow && configObject.componentShow.indexOf('状态开关') > -1">
              <template v-if="configObject.dataBind.activeName == '变量状态'">
                <div class="btn-tools-wrap">
                  <span class="name">条件</span>
                  <div>
                    <el-button link icon="el-icon-plus" size="default" @click="addSwitch"></el-button>
                    <el-button link icon="el-icon-delete" style="color: #f56c6c; margin-left: 10px" size="default" @click="deleteSwitch"></el-button>
                  </div>
                </div>
                <div class="btn-tools-content-wrap" v-for="(item, index) in configObject.dataBind.stateList" :key="index">
                  <div class="input-wrap">
                    <el-input type="number" controls-position="right" class="input-with-select" placeholder="请输入值" v-model="item.paramData" size="small">
                      <template #prepend>
                        <el-select v-model="item.paramCondition" placeholder="请选择" size="small">
                          <el-option label=">" value="大于">
                            <span style="font-size: 12px">&gt;</span>
                          </el-option>
                          <el-option label=">=" value="大于等于">
                            <span style="font-size: 12px">&gt;=</span>
                          </el-option>
                          <el-option label="=" value="等于">
                            <span style="font-size: 12px">=</span>
                          </el-option>
                          <el-option label="<=" value="小于等于">
                            <span style="font-size: 12px">&lt;=</span>
                          </el-option>
                          <el-option label="<" value="小于">
                            <span style="font-size: 12px">&lt;</span>
                          </el-option>
                          <el-option label="!=" value="不等于">
                            <span style="font-size: 12px">!=</span>
                          </el-option>
                        </el-select>
                      </template>
                    </el-input>
                  </div>
                  <div class="img-wrap">
                    <el-image style="width: 30px; height: 30px" fit="fit" v-if="item.imageUrl" :src="item.imageUrl" @click="handleGalleryClick('状态开关-' + item.id)" />
                    <i v-else class="el-icon-plus" @click="handleGalleryClick('状态开关-' + item.id)"></i>
                  </div>
                  <div class="color-picker-wrap">
                    <el-color-picker v-model="item.foreColor" show-alpha :predefine="predefineColors" size="small" color-format="hex" />
                  </div>
                </div>
              </template>
              <!-- 设备状态判断在线、离线、报警 -->
              <div style="margin-top: 8px" v-if="configObject.dataBind.activeName == '设备状态'">
                <div class="form-item-wrap">
                  <span class="name">设备离线</span>
                  <div class="content">
                    <img v-if="configObject.dataBind.shutImageUrl" style="width: 30px; height: 30px" :src="configObject.dataBind.shutImageUrl" @click="handleGalleryClick('开关状态(关)')" />
                    <el-button v-else link size="default" @click="handleGalleryClick('开关状态(关)')"><el-icon><Plus /></el-icon></el-button>
                  </div>
                </div>
                <div class="form-item-wrap">
                  <span class="name">设备在线</span>
                  <div class="content">
                    <img v-if="configObject.dataBind.openImageUrl" style="width: 30px; height: 30px" :src="configObject.dataBind.openImageUrl" @click="handleGalleryClick('开关状态(开)')" />
                    <el-button v-else link size="default" @click="handleGalleryClick('开关状态(开)')"><el-icon><Plus /></el-icon></el-button>
                  </div>
                </div>
                <div class="form-item-wrap">
                  <span class="name">设备禁用</span>
                  <div class="content">
                    <img v-if="configObject.dataBind.warnImageUrl" style="width: 30px; height: 30px" :src="configObject.dataBind.warnImageUrl" @click="handleGalleryClick('开关状态(报警)')" />
                    <el-button v-else link  size="default" @click="handleGalleryClick('开关状态(报警)')"><el-icon><Plus /></el-icon></el-button>
                  </div>
                </div>
              </div>
            </el-collapse-item>
            <!-- 状态开关结束 -->
            <!-- 填充颜色开始 -->
            <el-collapse-item
                title="填充颜色"
                name="12"
                v-if="configObject.componentShow && configObject.componentShow.includes('组件填充')"
            >
              <template #default>
                <div class="btn-tools-wrap">
                  <span class="name">条件</span>
                  <div>
                    <el-button type="text" :icon="Plus" size="default" @click="addSwitch" />
                    <el-button type="text" :icon="Delete" size="default"
                        style="color: #f56c6c; margin-left: 10px" @click="deleteSwitch" />
                  </div>
                </div>
                <div
                    class="btn-tools-content-wrap"
                    v-for="(item, index) in configObject.dataBind.stateList"
                    :key="index"
                >
                  <div class="input-wrap">
                    <el-input
                        type="number"
                        placeholder="请输入值"
                        v-model.number="item.paramData"
                        size="default"
                    >
                      <template #prepend>
                        <el-select v-model="item.paramCondition" placeholder="请选择" size="default" style="width: 60px;">
                          <el-option label=">" value="大于">
                            <span style="font-size: 12px">&gt;</span>
                          </el-option>
                          <el-option label=">=" value="大于等于">
                            <span style="font-size: 12px">&gt;=</span>
                          </el-option>
                          <el-option label="=" value="等于">
                            <span style="font-size: 12px">=</span>
                          </el-option>
                          <el-option label="<=" value="小于等于">
                            <span style="font-size: 12px">&lt;=</span>
                          </el-option>
                          <el-option label="<" value="小于">
                            <span style="font-size: 12px">&lt;</span>
                          </el-option>
                          <el-option label="!=" value="不等于">
                            <span style="font-size: 12px">!=</span>
                          </el-option>
                        </el-select>
                      </template>
                    </el-input>
                  </div>
                  <div class="color-picker-wrap">
                    <el-color-picker
                        v-model="item.foreColor"
                        show-alpha
                        :predefine="predefineColors"
                        size="default"
                        format="hex"
                    />
                  </div>
                </div>
              </template>
            </el-collapse-item>
            <!-- 填充颜色结束 -->
            <!-- 天气组件开始 -->
            <el-collapse-item title="位置" name="13" v-if="configObject.componentShow && configObject.componentShow.indexOf('天气') > -1">
              <el-form label-width="30px">
                <el-form-item label="省">
                  <el-select v-model="configObject.dataBind.provinceCode" placeholder="请选择省" style="width: 100%" size="small" filterable @change="provinceChange">
                    <el-option v-for="pro in provinceList" :key="pro.code" :label="pro.name" :value="pro.code">
                      <span style="font-size: 12px">{{ pro.name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="市">
                  <el-select v-model="configObject.dataBind.cityCode" placeholder="请选择市" style="width: 100%" size="small" filterable @change="cityChange">
                    <el-option v-for="city in cityList" :key="city.code" :label="city.name" :value="city.code">
                      <span style="font-size: 12px">{{ city.name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="区">
                  <el-select v-model="configObject.dataBind.districtCode" placeholder="请选择区" style="width: 100%" size="small" filterable>
                    <el-option v-for="dis in districtList" :key="dis.code" :label="dis.name" :value="dis.code">
                      <span style="font-size: 12px">{{ dis.name }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-form>
              <el-alert title="温馨提示" type="warning" description="同一组态界面只能存在一个天气组件"></el-alert>
            </el-collapse-item>
            <!-- 天气组件结束 -->
          </el-collapse>
        </div>
      </div>
    </template>
    <!-- 界面背景设置 -->
    <template v-if="isLayer">
      <el-form class="bg-set-wrap" label-width="65px">
        <el-form-item label="背景名称">
          <el-input placeholder="请填写背景名称" v-model="topoData.name" size="small"></el-input>
        </el-form-item>
        <el-form-item label="分辨率">
          <el-select v-model="resolution" placeholder="请选择" style="width: 100%" size="small">
            <el-option
                v-for="dict in sys_page_size"
                :key="dict.dictValue"
                :label="dict.dictLabel"
                :value="dict.dictValue"
            >
              <template #default>
                <span style="font-size: 12px">{{ dict.dictLabel}}</span>
              </template>
            </el-option>
            <el-option key="custom" label="自定义" value="custom">
              <span style="font-size: 12px">自定义</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分辨率宽" v-if="resolution === 'custom'">
          <el-input-number style="width: 100%" controls-position="right" placeholder="请填写分辨率宽" v-model="topoData.layer.width" size="small" :min="100" />
        </el-form-item>
        <el-form-item label="分辨率高" v-if="resolution === 'custom'">
          <el-input-number style="width: 100%" controls-position="right" placeholder="请填写分辨率高" v-model="topoData.layer.height" size="small" :min="100" />
        </el-form-item>
        <el-form-item label="背景颜色">
          <el-color-picker v-model="layerBackColor" show-alpha :predefine="predefineColors" color-format="hex" />
        </el-form-item>
        <el-form-item label="背景图片">
          <div class="bg-img-wrap" @click="handleGalleryClick('背景图片')">
            <img v-if="topoData.layer.backgroundImage" :src="topoData.layer.backgroundImage" class="img-wrap"/>
            <el-icon v-else class="ico-wrap">
              <Plus />
            </el-icon>
            <div v-if="topoData.layer.backgroundImage" class="img-tools" @click.stop>
              <el-icon class="ico-wrap" @click.stop="handleGalleryClick('背景图片')">
                <Plus /> </el-icon>
              <el-icon class="ico-wrap" @click.stop="topoData.layer.backgroundImage = ''">
                <Delete /> </el-icon>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="拖拽缩放">
          <el-checkbox v-model="topoData.layer.dragZoom" size="small">开启</el-checkbox>
        </el-form-item>
      </el-form>
    </template>
    <!-- 图库对话框 -->
    <el-dialog title="图库" v-model="isGalleryDialog" width="1100px" :close-on-click-modal="false">
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
      <TopoSelectImage ref="topoSelectImage" />
      <template #footer>
        <span class="dialog-footer">
                <el-button @click="isGalleryDialog = false">取 消</el-button>
                <el-button type="primary" @click="selectImageClick">确 定</el-button>
            </span>
      </template>
    </el-dialog>
    <!--图片动画设置  -->
    <el-dialog
        v-if="configObject && configObject.componentShow.includes('动画')"
        title="动画设置"
        v-model="animationDialog"
        width="550px"
        :close-on-click-modal="false"
        draggable
    >
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px;"></div>
      <el-form label-width="80px">
        <!-- 变量名称 -->
        <el-form-item label="变量名称">
          <el-input placeholder="请选择变量" v-model="configObject.dataAction.modelName" readonly>
            <template #append>
              <el-button @click="selectVariable">选择</el-button>
            </template>
          </el-input>
        </el-form-item>
        <!-- 判断条件 -->
        <el-form-item label="判断条件" prop="paramJudge" >
          <div class="condition-container">
            <el-select v-model="configObject.dataAction.paramJudge" placeholder="请选择" style="width: 100px;">
              <el-option label=">" value="大于">
                <span style="font-size: 12px">&gt;</span>
              </el-option>
              <el-option label=">=" value="大于等于">
                <span style="font-size: 12px">&gt;=</span>
              </el-option>
              <el-option label="=" value="等于">
                <span style="font-size: 12px">=</span>
              </el-option>
              <el-option label="<=" value="小于等于">
                <span style="font-size: 12px">&lt;=</span>
              </el-option>
              <el-option label="<" value="小于">
                <span style="font-size: 12px">&lt;</span>
              </el-option>
              <el-option label="!=" value="不等于">
                <span style="font-size: 12px">!=</span>
              </el-option>
            </el-select>
            <el-input
                type="number"
                placeholder="请输入值"
                v-model.number="configObject.dataAction.paramJudgeData"
                style="width: calc(100% - 100px);"
            />
          </div>
        </el-form-item>
        <!-- 动画效果：旋转 -->
        <el-form-item v-if="clickText === '旋转'" label="动画效果" prop="rotationSpeed">
          <el-radio-group v-model="configObject.dataAction.rotationSpeed">
            <el-radio label="快">快</el-radio>
            <el-radio label="中">中</el-radio>
            <el-radio label="慢">慢</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 滑动配置 -->
        <div v-if="clickText === '滑动'">
          <el-form-item label="滑动配置">
            <el-button type="primary" :icon="Edit" plain circle @click="addTranslate"></el-button>
            <el-button type="danger" :icon="Delete" plain circle @click="deleteTranslate"></el-button>
          </el-form-item>
          <el-form-item label="滑动周期">
            <el-input-number
                v-model="configObject.dataAction.duration"
                controls-position="right"
                clearable
                placeholder="请输入滑动周期(秒)"
                style="width: 100%"
            />
          </el-form-item>
          <el-form-item
              v-for="(item, index) in configObject.dataAction.translateList"
              :key="index"
              :label="`滑动位置 ${index + 1}`"
          >
            <div class="condition-container">
                <el-select v-model="item.direction" placeholder="请选择" style="width: 100px;">
                  <el-option label="水平" value="水平" />
                  <el-option label="竖直" value="竖直" />
                </el-select>
                <el-input
                    type="number"
                    placeholder="请输入偏移度"
                    v-model.number="item.position"
                    style="width: calc(100% - 100px);"
                ></el-input>
            </div>
          </el-form-item>
        </div>
      </el-form>
      <!-- 对话框底部按钮 -->
      <template #footer>
        <el-button @click="animationDialog = false">取 消</el-button>
        <el-button type="primary" @click="animationSubmit">确 定</el-button>
      </template>
    </el-dialog>
    <el-dialog v-model="variableDialog" title="变量选择" width="1100px" :append-to-body="true" :close-on-click-modal="false" >
      <el-divider style="margin-top: -25px;" />
      <TopoVariable ref="topoVariable" :multiple="multiple" :textStatic="textStatic" />
      <template #footer>
    <span class="dialog-footer">
      <el-button @click="variableDialog = false">取 消</el-button>
      <el-button type="primary" @click="selectVariableClick">确 定</el-button>
    </span>
      </template>
    </el-dialog>
    <!--事件单击设置  -->
    <el-dialog
        v-if="configObject && configObject.componentShow?.includes('单击')"
        v-model="singleClickDialog"
        title="单击设置"
        width="530px"
    >
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>

      <el-form label-width="80px">
        <!-- 动作选择 -->
        <el-form-item label="动作">
          <el-radio-group v-model="configObject.dataBind.action">
            <el-radio label="操作变量">操作变量</el-radio>
            <el-radio label="外部链接">外部链接</el-radio>
            <el-radio label="组态界面">组态界面</el-radio>
            <el-radio v-if="configObject.type === 'imageSwitch'" label="开关控制">开关控制</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 变量名称 -->
        <el-form-item
            label="变量名称"
            v-show="configObject.dataBind.action === '操作变量' || configObject.dataBind.action === '开关控制'"
        >
          <el-input placeholder="请选择变量" v-model="configObject.dataBind.modelName" readonly>
            <template #append>
              <el-button @click="selectVariable">选择</el-button>
            </template>
          </el-input>
        </el-form-item>

        <!-- 写入值 -->
        <el-form-item
            label="写入值"
            v-show="configObject.dataBind.action === '操作变量'"
        >
          <el-input v-model="configObject.dataBind.modelValue" placeholder="为空即为可变值" />
        </el-form-item>

        <!-- 提示信息 -->
        <el-form-item
            label="提示信息"
            v-show="configObject.dataBind.action === '操作变量'"
        >
          <el-input v-model="configObject.dataBind.tipMsg" placeholder="请输入提示信息" clearable />
        </el-form-item>

        <!-- 跳转链接 -->
        <el-form-item
            label="跳转链接"
            v-show="configObject.dataBind.action === '外部链接'"
        >
          <el-input v-model="configObject.dataBind.redirectUrl" placeholder="请填写要跳转的链接" clearable />
        </el-form-item>

        <!-- 开关控制 -->
        <el-form-item
            label="开关控制"
            v-show="configObject.dataBind.action === '开关控制'"
        >
          <el-radio-group v-model="configObject.dataBind.controValue">
            <el-radio label="0关1开">0关1开</el-radio>
            <el-radio label="0开1关">0开1关</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 组态界面 -->
        <el-form-item
            label="组态界面"
            v-show="configObject.dataBind.action === '组态界面'"
        >
          <el-select v-model="configObject.dataBind.ztPage" clearable placeholder="请选择组态界面" style="width: 100%" @change="selectZtPage">
            <el-option
                v-for="item in ztOption"
                :key="item.id"
                :label="item.pageName"
                :value="item.guid"
            />
          </el-select>
        </el-form-item>

        <!-- 打开方式 -->
        <el-form-item
            label="打开方式"
            v-show="configObject.dataBind.action === '组态界面'"
        >
          <el-radio-group v-model="configObject.dataBind.openModel">
            <el-radio :label="1">当前窗口打开</el-radio>
            <el-radio :label="2">打开新窗口</el-radio>
            <el-radio :label="3">弹出小窗口</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 窗口宽度 -->
        <el-form-item
            label="窗口宽度"
            v-show="configObject.dataBind.action === '组态界面' && configObject.dataBind.openModel === 3"
        >
          <el-input v-model="configObject.dataBind.windowWidth" placeholder="请填写窗口宽度" clearable />
        </el-form-item>

        <!-- 窗口高度 -->
        <el-form-item
            label="窗口高度"
            v-show="configObject.dataBind.action === '组态界面' && configObject.dataBind.openModel === 3"
        >
          <el-input v-model="configObject.dataBind.windowHeight" placeholder="请填写窗口高度" clearable />
        </el-form-item>
      </el-form>

      <!-- Footer -->
      <template #footer>
      <span class="dialog-footer">
        <el-button @click="singleClickDialog = false">取 消</el-button>
        <el-button type="primary" @click="singleClickSubmit">确 定</el-button>
      </span>
      </template>
    </el-dialog>
    <!-- 代码视图 -->
    <el-dialog class="data-engine-dialog" v-if="configObject" title="数据引擎"
        v-model:visible="isCodeViewDialog" width="50%" append-to-body :close-on-click-modal="false" >
      <!-- 自定义标题栏并绑定拖拽指令 -->
      <template #header>
        <div v-dialogDrag style="cursor: move; user-select: none;">数据引擎</div>
      </template>
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
      <div class="title-wrap">
        <a href="https://www.isqqw.com/" target="_blank">Echarts图表合集-仅支持vue语法糖、数据引擎-echartData</a>
        <i class="el-icon-info" @click="isEchartExplainDialog = true" style="color: #e6a23c; margin-left: 5px"></i>
      </div>
      <el-input class="action-wrap" type="textarea" :autosize="{ minRows: 3 }" placeholder="请输入响应示例" v-model="configObject.dataBind.echartData"></el-input>
      <monaco-editor ref="monaco" height="70vh" :value="configObject.dataBind.echartOption"
                     @change="handleMonacoChange" language="json" theme="vs-dark"/>
      <template #footer>
        <span class="dialog-footer">
                <el-button type="primary" @click="echartRunClick">运 行</el-button>
                <el-button @click="isCodeViewDialog = false">关 闭</el-button>
            </span>
      </template>
    </el-dialog>
    <el-dialog title="数据引擎示例" v-model:visible="isEchartExplainDialog" width="40%" append-to-body :close-on-click-modal="false">
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
      <div class="box-message" v-html="topoUtil.getEchartExplain()"></div>
      <template #footer>
        <span class="dialog-footer">
                <el-button type="primary" @click="isEchartExplainDialog = false">关 闭</el-button>
            </span>
      </template>
    </el-dialog>
    <div class="setup-angle-wrap" v-show="configObject" @mouseenter="mouseEnterAngle" @mouseleave="mouseLeaveAngle" :style="angleStyle">
      <setup-angle ref="setupAngle" @angle="onAngle" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import JsonViewer from '/@/components/JsonView/index.vue'
import TopoSelectImage from './topoSelectImage.vue'
import TopoVariable from './topoVariable.vue'
import SetupAngle from './topoAngle.vue'
import { listCenter } from '/@/api/scada/center';
import { listModel } from '/@/api/scada/model';
import { listDeviceBind } from '/@/api/scada/topo'
import cityDatas from '/@/assets/json/citylist.json'
import { useCounterStore } from '/@/stores/counterStore';
import router from '/@/router';
// import { useRoute } from 'vue-router';
import topoUtil from "/@/utils/topo/topo-util";
import {useDictStore} from "/@/stores/dictStore";
import MonacoEditor from 'monaco-editor-vue3'
import {
  Delete,
  Edit,
  Plus,
} from '@element-plus/icons-vue'

// const route = useRoute();
const counterStore = useCounterStore();
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)

// 定义子组件向父组件传值/事件
const emit = defineEmits(['saveHistory'])

// 计算属性：确保组件背景颜色始终是字符串
const componentBackColor = computed({
  get() {
    const backColor = configObject.value?.style?.backColor;
    if (typeof backColor === 'string') {
      return backColor;
    } else if (typeof backColor === 'object' && backColor !== null) {
      // 如果是对象，转换为透明色
      console.warn('组件背景颜色是对象，已转换为透明色:', backColor);
      return 'transparent';
    }
    return 'transparent';
  },
  set(value) {
    if (configObject.value?.style) {
      configObject.value.style.backColor = value;
    }
  }
});

// 计算属性：确保图层背景颜色始终是字符串
const layerBackColor = computed({
  get() {
    const backColor = topoData.value?.layer?.backColor;
    if (typeof backColor === 'string') {
      return backColor;
    } else if (typeof backColor === 'object' && backColor !== null) {
      // 如果是对象，转换为空字符串
      console.warn('图层背景颜色是对象，已转换为空字符串:', backColor);
      return '';
    }
    return '';
  },
  set(value) {
    if (topoData.value?.layer) {
      topoData.value.layer.backColor = value;
    }
  }
});
const tabIndex = ref(0)
const resolutionTemp = ref<string>('')
const styleCollapseActive = ref(['1', '2', '3'])
const dataCollapseActive = ref(['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13'])

const predefineColors = [
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  'rgba(255, 69, 0, 0.68)',
  'rgb(255, 120, 0)',
  'hsv(51, 100, 98)',
  'hsva(120, 40, 94, 0.5)',
  'hsl(181, 100%, 37%)',
  'hsla(209, 100%, 56%, 0.73)',
  '#c7158577',
  '#ffffff',
  '#000',
]

const fontFamilyOptions = ['Arial', 'Helvetica', 'sans-serif', '宋体', '黑体', '微软雅黑']
// const textAlignOptions = ['left', 'right', 'center', 'justify']
// const borderStyleOptions = ['solid', 'dashed', 'dotted']

const isGalleryDialog = ref(false)
const imgProperty = ref('')
const provinceList = ref(cityDatas)
const cityList = ref([])
const districtList = ref([])
const ztModelOption = ref([])

const isCodeViewDialog = ref(false)
const isEchartExplainDialog = ref(false)
const deviceBindList = ref([])
// const bindParams = ref([])
const animationDialog = ref(false)
// const animationForm = reactive({})
const ztOption = ref([])
const singleClickDialog = ref(false)
// const deviceImei = ref(route.query.deviceImei) // 假设路由已挂载到 store 或通过 useRoute 获取
const variableDialog = ref(false)
const multiple = ref(false)
const textStatic = ref('')
const clickText = ref('')
const paramNameList = [
  '设备总数',
  '设备在线数',
  '设备离线数',
  '设备报警数',
  '在线比率',
  '日报警总数',
  '日报警已处理',
  '日报警未处理',
  '日报警处理比率',
  '周报警总数',
  '周报警已处理',
  '周报警未处理',
  '周报警处理比率',
  '月报警总数',
  '月报警已处理',
  '月报警未处理',
  '月报警处理比率',
  '年报警已处理',
  '年报警未处理',
  '年报警处理比率',
  '日工单总数',
  '日工单已处理',
  '日工单未处理',
  '日工单处理比率',
  '周工单总数',
  '周工单已处理',
  '周工单未处理',
  '周工单处理比率',
  '月工单总数',
  '月工单已处理',
  '月工单未处理',
  '月工单处理比率',
  '年工单总数',
  '年工单已处理',
  '年工单未处理',
  '年工单处理比率',
]
const angleStyle = ref({ opacity: 0.1 })

// ========== Computed Properties ==========
const topoData = computed(() => counterStore.topoData)
const selectedComponents = computed(() => counterStore.selectedComponents)
const selectedComponentMap = computed(() => counterStore.selectedComponentMap)
const isLayer = computed(() => counterStore.selectedIsLayer)
const configObject = computed(() => counterStore.selectedComponent)

const historyData = computed(() => counterStore.historyData)
const animations = computed(() => {
  return configObject.value?.direction === 'vertical'
      ? [
        { label: '向上', value: 'up' },
        { label: '向下', value: 'down' },
      ]
      : [
        { label: '向右', value: 'right' },
        { label: '向左', value: 'left' },
      ]
})

const resolution = computed({
  get() {

    if (resolutionTemp.value === 'custom'){
      return 'custom'
    }if (resolutionTemp.value === '') {
      return topoData.value.layer.width + '×' + topoData.value.layer.height;
    }else{
      return resolutionTemp.value
    }
  },
  set(val) {
    resolutionTemp.value = val
    if (val !== 'custom') {
      const [width, height] = val.split('×').map(Number)
      topoData.value.layer.width = width
      topoData.value.layer.height = height
    }
  },
})

onMounted(async () => {
  await Promise.all([getZtPage(), getZtModel(), getBindDeviceList(),getdictdata()])
})

const dictStore = useDictStore();
interface TypeOption {
  dictValue: string;
  dictLabel: string;
}
const sys_page_size = ref<TypeOption[]>([])
// 获取字典数据
const getdictdata = async () => {
  try {
    sys_page_size.value = await dictStore.fetchDict('sys_page_size')
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取公告数据失败:', error);
  }
};
async function getZtModel() {
  const params = { pageNum: 1, pageSize: 9999 }
  try {
    const ress = await listModel(params)
    const res = ress.data
    if (res.code === 200) {
      ztModelOption.value = res.rows
    }
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取模型失败:', error)
  }
}

// 变化日志
function changeLog(newVal) {
  counterStore.historyData.push(newVal)
}

// 增加滑动位置
function addTranslate() {
  const id = configObject.value.dataAction.translateList.length
  const state = {
    id: id + 1,
    direction: '',
    position: 0,
  }
  configObject.value.dataAction.translateList.push(state)
}

// 删除滑动位置
function deleteTranslate() {
  const list = configObject.value.dataAction.translateList
  for (let i = 0; i < list.length; i++) {
    if (list[i].id === list.length) {
      list.splice(i, 1)
      return
    }
  }
}

// ECharts 代码视图
function echartClick() {
  isCodeViewDialog.value = true
}

// Monaco 编辑器变化
function handleMonacoChange(data:any) {
  configObject.value.dataBind.echartOption = data
}

// ECharts 运行
function echartRunClick() {
  configObject.value.dataBind.echartRun = Date.now()
  isCodeViewDialog.value = false
}
import axios from 'axios'
// 请求图表数据
let loadingInstance = null

function showLoading() {
  loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '正在加载，请稍候...',
    background: 'rgba(0, 0, 0, 0.1)'
  })
}

function closeLoading() {
  if (loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
}

// 请求图表数据
async function echartHttpClick(dataUrl) {
  if (!dataUrl) {
    ElMessage.warning('请输入有效的 URL')
    return
  }

  showLoading()

  try {
    const res = await axios.get(dataUrl)
    // 处理并格式化返回的图表数据
    configObject.value.dataBind.echartData = JSON.stringify(res.data, null, '\t')
    closeLoading()
  } catch (err) {
    ElMessage.error('请输入正确的 URL!')
    closeLoading()
  }
}

// 添加状态开关
function addSwitch() {
  const id = configObject.value.dataBind.stateList.length
  const state = {
    id: id + 1,
    paramCondition: '',
    paramData: '',
    imageUrl: '',
  }
  configObject.value.dataBind.stateList.push(state)
}

// 删除状态开关
function deleteSwitch() {
  const list = configObject.value.dataBind.stateList
  for (let i = 0; i < list.length; i++) {
    if (list[i].id === list.length) {
      list.splice(i, 1)
      return
    }
  }
}

// 获取绑定设备列表
function getBindDeviceList() {
  const params = {
    pageNum: 1,
    pageSize: 9999,
    scadaGuid: router.currentRoute.value.query.guid,
  }
  listDeviceBind(params).then((res) => {
    res = res.data
    if (res.code === 200) {
      deviceBindList.value = res.rows
    }
  })
}

// 省变化获取市
function provinceChange(e) {
  const filtered = provinceList.value.filter((item) => item.code === e)
  cityList.value = filtered.length > 0 ? filtered[0].children : []
  configObject.value.dataBind.cityCode = ''
  configObject.value.dataBind.districtCode = ''
  districtList.value = []
}

// 市变化获取区
function cityChange(e) {
  const filtered = cityList.value.filter((item) => item.code === e)
  districtList.value = filtered.length > 0 ? filtered[0].children : []
  configObject.value.dataBind.districtCode = ''
}

// 模型选择变化
function selectModelChange() {
  ztModelOption.value.forEach((element) => {
    if (element.imageUrl === configObject.value.imageUrl) {
      configObject.value.modelUrl = element.modelUrl
    }
  })
}

// 层级操作：置顶、置底、上一层、下一层
function stack(val:string) {
  if (!configObject.value) return

  let zIndex = 0
  if (val === '置顶') {
    zIndex = topoData.value.zIndexTop === undefined ? 0 : topoData.value.zIndexTop + 1
    topoData.value.zIndexTop = zIndex
  } else if (val === '置底') {
    zIndex = topoData.value.zIndexBottom === undefined ? 0 : topoData.value.zIndexBottom - 1
    topoData.value.zIndexBottom = zIndex
  } else if (val === '上一层') {
    zIndex = configObject.value.style.zIndex === undefined ? 1 : configObject.value.style.zIndex + 1
  } else if (val === '下一层') {
    zIndex = configObject.value.style.zIndex === undefined ? 1 : configObject.value.style.zIndex - 1
  }

  for (const key in selectedComponentMap.value) {
    const component = selectedComponentMap.value[key]
    component.style.zIndex = zIndex
  }

  // 通知父组件保存历史记录
  emit('saveHistory')
}

// 获取组态页面列表
async function getZtPage() {
  try {
    const res = await listCenter()
    ztOption.value = res.data.rows
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取组态列表失败:', error)
  }
}

// 选中组态页面
function selectZtPage() {
  ztOption.value.forEach((element) => {
    console.log(element)
    if (element.guid === configObject.value.dataBind.ztPage) {
      configObject.value.dataBind.ztId = element.id
      configObject.value.dataBind.ztPageName = element.pageName
    }
  })
}

// 打开变量选择窗口
const topoVariable = ref() // 子组件 ref

// 方法：选择变量
function selectVariable(val:any) {
  if (val === '参数绑定') {
    if (configObject.value?.type === 'realData') {
      multiple.value = true
      clickText.value = '多参数绑定'
      textStatic.value = ''
    } else if (configObject.value?.type === 'textStatic') {
      multiple.value = true
      clickText.value = '多参数绑定'
      textStatic.value = '数统计'
    } else {
      multiple.value = false
      clickText.value = val
    }
  }
  variableDialog.value = true
  // 调用子组件方法前判断是否存在
  if (topoVariable.value && typeof topoVariable.value.clearSelection === 'function') {
    topoVariable.value.clearSelection()
  }
}

// configObject 类型定义（示例）
interface VariableItem {
  serialNumber: string
  identifier: string
  modelName: string
  unit?: string
  productId?: string
  type?: string
}

// interface ConfigObject {
//   dataAction: {
//     serialNumber: string
//     identifier: string
//     modelName: string
//   }
//   dataBind: {
//     identifier: string
//     modelName: string
//     serialNumber: string
//     productId?: string
//     unit?: string
//     type?: string
//   }
//   style: {
//     header: VariableItem | VariableItem[]
//   }
// }

// const configObject = ref<ConfigObject>({
//   dataAction: {
//     serialNumber: '',
//     identifier: '',
//     modelName: ''
//   },
//   dataBind: {
//     identifier: '',
//     modelName: '',
//     serialNumber: '',
//     productId: '',
//     unit: '',
//     type: ''
//   },
//   style: {
//     header: []
//   }
// })
// 变量选择事件
function selectVariableClick() {
  variableDialog.value = false
  let selectRowData: VariableItem | VariableItem[] | null = null

  if (clickText.value === '多参数绑定') {
    selectRowData = topoVariable.value.selectRowsDataClick()
  } else {
    selectRowData = topoVariable.value.selectRowDataClick()
  }
  if (!selectRowData) return
  if (
      clickText.value === '闪烁' ||
      clickText.value === '旋转' ||
      clickText.value === '显隐' ||
      clickText.value === '滑动'
  ) {
    const item = selectRowData as VariableItem
    configObject.value.dataAction.serialNumber = item.serialNumber
    configObject.value.dataAction.identifier = item.identifier
    configObject.value.dataAction.modelName = item.modelName
  } else if (clickText.value === '参数绑定' || clickText.value === '单击') {
    const item = selectRowData as VariableItem
    configObject.value.dataBind.identifier = item.identifier
    configObject.value.dataBind.modelName = item.modelName
    configObject.value.dataBind.serialNumber = item.serialNumber
    configObject.value.dataBind.productId = item.productId
    configObject.value.dataBind.type = item.type
    if (item.unit) {
      configObject.value.dataBind.unit = item.unit
    }
  } else if (clickText.value === '多参数绑定') {
    const items = selectRowData as VariableItem[]
    configObject.value.style.header = items
    configObject.value.dataBind.modelName = items.map(i => i.modelName).join(',')
  }
}

// 配置点击事件
function configClick(val) {
  if (val === '单击') {
    singleClickDialog.value = true
  } else {
    animationDialog.value = true
  }
  clickText.value = val
}

// 单击提交
function singleClickSubmit() {
  singleClickDialog.value = false
}

// 动画提交
function animationSubmit() {
  animationDialog.value = false
}

// 图库选择触发
function handleGalleryClick(label:any) {
  imgProperty.value = label
  isGalleryDialog.value = true
}

import {ElLoading, ElMessage} from 'element-plus';
const topoSelectImage = ref()
// 选择图片回调
const selectImageClick = () => {
  const imgs = topoSelectImage.value?.handleChoice()
  if (!imgs || imgs.length === 0) {
    ElMessage.warning('请选择图片')
    return
  }
  const selectImage = imgs[imgs.length - 1]
  const imageUrl = baseApi.value + selectImage.resourceUrl
  switch (imgProperty.value) {
    case '背景图片':
      topoData.value.layer.backgroundImage = imageUrl
      break
    case '图片路径':
      configObject.value.style.url = imageUrl
      break
    case '开关状态(关)':
      configObject.value.dataBind.shutImageUrl = imageUrl
      break
    case '开关状态(开)':
      configObject.value.dataBind.openImageUrl = imageUrl
      break
    case '开关状态(报警)':
      configObject.value.dataBind.warnImageUrl = imageUrl
      break
    default:
      if (imgProperty.value.indexOf('状态开关') > -1) {
        const id = imgProperty.value.split('-')[1]
        configObject.value.dataBind.stateList.forEach((element) => {
          if (element.id === id) {
            element.imageUrl = imageUrl
          }
        })
      }
      break
  }
  ElMessage.success('添加成功')
  isGalleryDialog.value = false
  topoSelectImage.value?.clearChoice()
}

// Tab切换
function changeTab(tabIndexVal) {
  tabIndex.value = tabIndexVal
}

// 鼠标进入角度区域
function mouseEnterAngle() {
  angleStyle.value = { opacity: 0.8 }
}

// 鼠标离开角度区域
function mouseLeaveAngle() {
  angleStyle.value = { opacity: 0.1 }
}

// 自定义角度变化
function onAngle(angle:any) {
  transform('自定义旋转角度', angle)
}

// 旋转操作核心逻辑
function transform(val:any, value:any) {
  if (!configObject.value) return

  let transformDeg = 0
  let transformType = ''

  if (val === '顺时针旋转') {
    transformDeg = (configObject.value.style.transform || 0) + 90
    transformType = `rotate(${transformDeg}deg)`
  } else if (val === '逆时针旋转') {
    transformDeg = (configObject.value.style.transform || 0) - 90
    transformType = `rotate(${transformDeg}deg)`
  } else if (val === '水平镜像') {
    transformType = `rotateY(180deg)`
  } else if (val === '垂直镜像') {
    transformType = `rotateX(180deg)`
  } else if (val === '自定义角度' && value) {
    transformDeg = (configObject.value.style.transform || 0) + parseInt(value)
    transformType = `rotate(${transformDeg}deg)`
  } else if (val === '自定义旋转角度' && value) {
    transformDeg = parseInt(value)
    transformType = `rotate(${transformDeg}deg)`
  }

  for (const key in selectedComponentMap.value) {
    const component = selectedComponentMap.value[key]
    component.style.transform = transformDeg
    component.style.transformType = transformType
  }

  // 通知父组件保存历史记录
  emit('saveHistory')
}

// 暴露方法给父组件调用
defineExpose({ stack,transform})
</script>

<style lang="scss" scoped>
.topo-properties {
  border: #e6e6e6 solid 1px;
  background-color: #f1f3f4;
  width: 100%;
  height: 100%;

  .nav-bar-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 10px 0 10px;

    .img-wrap {
      width: 60px;
      height: 60px;
    }
  }

  .tabs-wrap {
    height: 35px;
    display: flex;
    border-bottom: #ccc solid 1px;
    background-color: #f1f3f4;

    .tab-item {
      height: 35px;
      text-align: center;
      line-height: 35px;
      flex: 1;
      color: #666;
      font-size: 14px;
    }

    .tab-item:hover {
      cursor: pointer;
    }

    .tab-item-active {
      color: #3388ff;
      border-bottom: #3388ff solid 2px;
    }
  }

  .table-wrap {
    overflow-x: hidden;
    overflow-y: auto;
    height: calc(100% - 105px);

    .table-item {
      padding: 2px 10px 10px 10px;

      .collapse-wrap {
        border-top: unset;
        border-bottom: unset;
      }

      .ixd-wrap {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;
        font-size: 12px;
      }

      .btn-tools-wrap {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        .name {
          font-size: 12px;
          color: #606266;
        }
      }

      .btn-tools-content-wrap {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 10px;

        .img-wrap {
          width: 39px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          margin-left: 5px;
        }

        .color-picker-wrap {
          height: 32px;
          width: 32px;
          margin-left: 5px;
        }
      }

      .form-item-wrap {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-top: 5px;

        .name {
          font-size: 12px;
          color: #606266;
        }

        .content {
          margin-left: 13px;
          height: 32px;
          line-height: 32px;
        }
      }
    }
  }

  .bg-set-wrap {
    padding: 10px;

    .bg-img-wrap {
      position: relative;
      background-color: #fbfdff;
      border: 1px dashed #c0ccda;
      border-radius: 6px;
      box-sizing: border-box;
      width: 100%;
      height: 128px;
      cursor: pointer;
      line-height: 126px;
      vertical-align: top;
      text-align: center;

      &:hover {
        border-color: #409eff;

        .img-tools {
          opacity: 1;
        }
      }

      .ico-wrap {
        font-size: 26px;
        color: #8c939d;
      }

      .img-wrap {
        width: 100%;
        height: 100%;
        padding: 10px;
        background-size: cover;
      }

      .img-tools {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        cursor: default;
        text-align: center;
        color: #fff;
        font-size: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: opacity 0.3s;
        cursor: pointer;
        opacity: 0;

        i {
          padding: 0 8px;
        }
      }
    }
  }

  // 使用 :deep() 替换 ::v-deep
  :deep(.el-form-item__label) {
    font-size: 12px;
    font-weight: normal;
  }

  :deep(.el-form--label-top .el-form-item__label) {
    padding: 0;
  }

  :deep(.el-input) {
    font-size: 12px;
  }

  :deep(.el-checkbox__label) {
    font-size: 12px;
  }

  :deep(.el-collapse-item__header) {
    background-color: transparent;
    font-size: 12px;
    height: 42px;
    line-height: 42px;
  }

  :deep(.el-collapse-item__wrap) {
    background-color: transparent;
    border-bottom: unset;
  }

  :deep(.el-radio__label) {
    font-size: 12px;
  }

  :deep(.el-input-group__append) {
    padding: 0 10px;
  }

  :deep(.el-input-group--prepend .el-input__inner) {
    padding: 0 10px;
  }

  .input-with-select :deep(.el-input-group__prepend) {
    background-color: #fff;
    padding: 0 10px;
    min-width: 75px;

    :deep(.el-select) {
      margin: -10px;
    }
  }

  .setup-angle-wrap {
    position: fixed;
    top: 100px;
    right: 350px;
    height: 100px;
    width: 100px;
    z-index: 1000;
    background-color: transparent;
  }

  .json-view-wrap {
    border-radius: 4px;
    border: 1px solid #dcdfe6;

    :deep(.jv-button) {
      font-size: 12px;
      padding: 0;
    }
  }
}

.data-engine-dialog {
  .title-wrap {
    font-size: 16px;
    margin-bottom: 20px;
  }

  .action-wrap {
    margin-bottom: 15px;
  }
}

.box-message {
  padding: 20px;
  border: 1px solid #f4dfb6;
  background: #fffbf4;
  text-align: left;
  height: 400px;
  overflow-y: auto;
}
.custom-input-container {
  width: 100%; /* 确保输入框撑满父容器 */
}
.condition-container{
  width: 100%;
}

</style>