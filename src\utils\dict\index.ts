import Vue, { App  } from 'vue';
import Dict from './Dict';
import { mergeOptions } from './DictOptions';

interface DictPluginOptions {
  onCreated?: (dict: Dict) => void;
  onReady?: (dict: Dict) => void;
}

export default function(Vue: App , options: DictPluginOptions) {
  mergeOptions(options);

  Vue.mixin({
    data() {
      if (this.$options === undefined || this.$options.dicts === undefined || this.$options.dicts === null) {
        return {};
      }
      const dict = new Dict();
      dict.owner = this;
      return {
        dict,
      };
    },
    created() {
      if (!(this.dict instanceof Dict)) {
        return;
      }

      // Optionally call onCreated callback if provided
      options.onCreated && options.onCreated(this.dict);

      // Initialize the dict
      this.dict.init(this.$options.dicts).then(() => {
        // Optionally call onReady callback if provided
        options.onReady && options.onReady(this.dict);

        this.$nextTick(() => {
          // Emit an event 'dictReady' when dict is ready
          this.$emit('dictReady', this.dict);

          // Optionally invoke onDictReady method if exists
          if (this.$options.methods && this.$options.methods.onDictReady instanceof Function) {
            this.$options.methods.onDictReady.call(this, this.dict);
          }
        });
      });
    },
  });
}
