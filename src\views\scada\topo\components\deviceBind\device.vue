<template>
  <div>
    <el-form
        @submit.native.prevent
        :model="queryParams"
        ref="queryFormRef"
        :inline="true"
        label-width="68px"
    >
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
             size="default"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备编号" prop="serialNumber">
        <el-input
            v-model="queryParams.serialNumber"
            placeholder="请输入设备编号"
             size="default"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
        <el-button size="default" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
        v-loading="loading"
        ref="tableRef"
        :row-key="(row:any) => row.serialNumber"
        :data="deviceList"
        @selection-change="handleSelectionChange"
        size="default"
    >
      <el-table-column :reserve-selection="true" type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="产品名称" align="center" prop="productName" />
      <el-table-column label="设备状态" align="center" prop="status" width="80">
        <template #default="{ row }">
          <dict-tag :options="device_status_list" :value="row.status" />
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-show="total > 0" size="small" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                   layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 30]"
                   :pager-count="5" background class="mt15" style="justify-content: flex-end;"
                   @size-change="handleSizeChange" @current-change="handleCurrentChange" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { listDeviceShort } from '/@/api/iot/device'
import {useDictStore} from "/@/stores/dictStore";

// 类型定义
interface DeviceItem {
  deviceName: string
  serialNumber: string
  productName: string
  status: number
}

interface QueryParams {
  pageNum: number
  pageSize: number
  deviceName: string
  serialNumber: string
}

// 响应式状态
const loading = ref<boolean>(true)
const total = ref<number>(0)
const deviceList = ref<DeviceItem[]>([])
const queryParams = ref<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  deviceName: '',
  serialNumber: ''
})
const selectDevices = ref<DeviceItem[]>([])

// 引用子组件
const queryFormRef = ref()
const tableRef = ref()
interface TypeOption {
  dictValue: string;
  dictLabel: string;
  listClass: string;
  cssClass: string;
}
const dictStore = useDictStore();
const device_status_list = ref<TypeOption[]>([]);
// 获取状态数据
const getdictdata = async () => {
  try {
    device_status_list.value = await dictStore.fetchDict('iot_device_status')
    // 处理参数数据
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取参数数据失败:', error);
  }
};

// 生命周期钩子
onMounted(() => {
  getList();
  getdictdata();
})

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.value.pageSize = size
  getList()
}

// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.value.pageNum = page
  getList()
}

// 获取设备列表
function getList() {
  loading.value = true
  listDeviceShort(queryParams.value)
      .then(res => {
        res = res.data
        if (res.code === 200) {
          // 过滤掉状态为 1 或 2 的设备
          deviceList.value = res.rows.filter((item: DeviceItem) => ![1, 2].includes(item.status))
          total.value = res.total
        }
        loading.value = false
      })
      .catch(err => {
        // eslint-disable-next-line no-console
        console.error(err)
        loading.value = false
      })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 重置按钮操作
function resetQuery() {
  if (queryFormRef.value?.resetFields) {
    queryFormRef.value.resetFields()
  }
  handleQuery()
}

// 表格多选事件
function handleSelectionChange(selection: DeviceItem[]) {
  selectDevices.value = selection
}

// 对外暴露的方法（给父组件使用）
function selectRowDataClick(): string[] {
  return selectDevices.value.map(item => item.serialNumber)
}

function clearSelection() {
  tableRef.value?.clearSelection()
}

function rowKey(row: DeviceItem) {
  return row.serialNumber
}

// 暴露方法给父组件调用
defineExpose({ selectRowDataClick, clearSelection, rowKey })
</script>

<style scoped>
.el-form--inline .el-form-item {
  margin-right: 10px;
}
</style>