<template>
    <div class="device-timer-wrap">
        <el-form :model="state.tableData.param" ref="DialogFormRef" :inline="true" v-show="showSearch"
            label-width="70px">
            <el-form-item label="定时名称" prop="jobName">
                <el-input v-model="state.tableData.param.jobName" placeholder="请输入定时名称" clearable size="default" />
            </el-form-item>
            <el-form-item label="定时状态" prop="status" style="width: 250px;">
                <el-select v-model="state.tableData.param.status" placeholder="请选择定时状态" clearable size="default">
                    <el-option v-for="dict in job_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="getTableData"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
            <el-form-item style="float: right">
                <el-button type="primary" plain size="default" @click="handleAdd"
                    v-auths="['iot:device:timer:add']"><el-icon><ele-Plus /></el-icon>新增</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="state.tableData.loading" :data="state.tableData.jobList"
            @selection-change="handleSelectionChange" border style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
            <!-- <el-table-column type="selection" width="55" align="center" /> -->
            <el-table-column label="名称" align="center" prop="jobName" :show-overflow-tooltip="true" />
            <el-table-column label="描述" align="center" prop="cronText">
                <template #default="scope">
                    <div v-html="formatCronDisplay(scope.row)"></div>
                </template>
            </el-table-column>
            <el-table-column label="CRON表达式" align="center" prop="cronExpression" :show-overflow-tooltip="true" />
            <el-table-column label="动作" align="center" prop="actions" :show-overflow-tooltip="true" width="260">
                <template #default="scope">
                    <div v-html="formatActionsDisplay(scope.row.actions)" style="overflow: hidden; white-space: nowrap">
                    </div>
                </template>
            </el-table-column>

            <el-table-column label="状态" align="center">
                <template #default="scope">
                    <el-switch v-model="scope.row.status" active-value="0" inactive-value="1" active-text="启用"
                        @change="handleStatusChange(scope.row)"></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="default-padding fixed-width">
                <template #default="scope">
                    <el-button size="default" text type="primary" @click="handleUpdate(scope.row)"
                        v-auths="['iot:device:timer:query']"><el-icon><ele-View /></el-icon>查看</el-button>
                    <el-button size="default" text type="primary" @click="handleView(scope.row)"
                        v-auths="['iot:device:timer:query']"><el-icon><ele-CaretRight /></el-icon>定时详细</el-button>
                    <br />
                    <el-button size="default" text type="primary" @click="handleDelete(scope.row)"
                        v-auths="['iot:device:timer:remove']"><el-icon><ele-Delete /></el-icon>删除</el-button>
                    <el-button size="default" text type="primary" @click="handleRun(scope.row)"
                        v-auths="['iot:device:timer:execute']"><el-icon><ele-CaretRight /></el-icon>执行一次</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
            style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
            v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <!-- 添加或修改定时定时对话框 -->
        <el-dialog class="device-timer-config-dialog" style="position: absolute; top: 100px;"
            :title="dialogstate.dialog.title" v-model="dialogstate.dialog.isShowDialog" width="800px" append-to-body
            :close-on-click-modal="false" :close-on-press-escape="false">
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="DialogFormRef" :model="dialogstate.form" :rules="rules" label-width="100px">
                <el-form-item label="定时名称" prop="jobName">
                    <el-input v-model="dialogstate.form.jobName" placeholder="请输入定时名称" style="width: 280px" />
                </el-form-item>
                <el-form-item label="执行时间" prop="timerTimeValue">
                    <el-time-picker v-model="timerTimeValue" value-format="HH:mm" format="HH:mm" placeholder="选择时间"
                        style="width: 280px" @change="timeChange"
                        :disabled="dialogstate.form.isAdvance == 1"></el-time-picker>
                </el-form-item>
                <el-form-item label="选择星期" prop="timerWeek">
                    <el-row>
                        <el-col :span="18">
                            <el-select v-model="timerWeekValue" placeholder="请选择" multiple style="width: 500px"
                                @change="weekChange" :disabled="dialogstate.form.isAdvance == 1">
                                <el-option v-for="item in timerWeeks" :key="item.value" :label="item.label"
                                    :value="item.value"></el-option>
                            </el-select>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="cron表达式" prop="cron">
                    <el-row>
                        <el-col :span="18">
                            <el-input v-model="dialogstate.form.cronExpression" placeholder="cron执行表达式"
                                :disabled="dialogstate.form.isAdvance == 0">
                                <template #append>
                                    <el-button type="primary" @click="handleShowCron"
                                        :disabled="dialogstate.form.isAdvance == 0">
                                        生成表达式
                                        <i class="el-icon-time el-icon--right"></i>
                                    </el-button>
                                </template>
                            </el-input>
                        </el-col>
                        <el-col :span="4" :offset="1">
                            <el-checkbox v-model="dialogstate.form.isAdvance" :true-value="1" :false-value="0"
                                @change="customerCronChange">自定义表达式</el-checkbox>
                        </el-col>
                    </el-row>
                </el-form-item>
                <el-form-item label="定时状态" prop="status">
                    <el-radio-group v-model="dialogstate.form.status">
                        <el-radio v-for="dict in job_status_list" :key="dict.dictValue" :value="dict.dictValue">{{
                            dict.dictLabel }}</el-radio>
                    </el-radio-group>
                </el-form-item>
                <div style="height: 1px; background-color: #ddd; margin: 0 0 20px 0"></div>
                <el-form-item class="action-wrap" label="执行动作" prop="actions">
                    <div class="item-wrap" v-for="(actionItem, index) in actionList" :key="index + 'action'">
                        <el-row :gutter="16">
                            <el-col :span="5">
                                <el-select v-model="actionItem.type" placeholder="请选择类型" size="default"
                                    @change="handleActionTypeChange($event, index)">
                                    <el-option v-for="(subItem, subIndex) in modelTypes" :key="subIndex + 'type'"
                                        :label="subItem.label" :value="subItem.value"></el-option>
                                </el-select>
                            </el-col>
                            <el-col :span="10">
                                <el-select style="width: 100%" v-model="actionItem.parentId" placeholder="请选择父级物模型"
                                    v-if="actionItem.type == 1" size="default"
                                    @change="handleActionParentModelChange($event, index)">
                                    <el-option v-for="(subItem, subIndex) in thingsModel.properties"
                                        :key="subIndex + 'property'" :label="subItem.name"
                                        :value="subItem.id"></el-option>
                                </el-select>
                                <el-select style="width: 100%" v-model="actionItem.parentId" placeholder="请选择父级物模型"
                                    v-else-if="actionItem.type == 2" size="default"
                                    @change="handleActionParentModelChange($event, index)">
                                    <el-option v-for="(subItem, subIndex) in thingsModel.functions"
                                        :key="subIndex + 'func'" :label="subItem.name" :value="subItem.id"></el-option>
                                </el-select>
                            </el-col>
                            <div class="delete-wrap">
                                <el-button v-if="index !== 0" size="default" plain type="danger" style="padding: 5px"
                                    @click="handleRemoveActionItem(index)"><el-icon>
                                        <Delete />
                                    </el-icon>删除</el-button>
                            </div>
                        </el-row>
                        <!-- 数组索引/物模型/值 -->
                        <!-- {{ actionItem.parentModel }} -->
                        <el-row :gutter="16" v-if="actionItem.parentModel">
                                <!-- <el-col :span="5"
                                    v-if="actionItem.parentModel.datatype.type && actionItem.parentModel.datatype.type === 'array'">
                                    <el-select v-model="actionItem.arrayIndex" placeholder="请选择" size="default"
                                        @change="handleActionIndexChange($event, index)">
                                        <el-option v-for="subItem in actionItem.parentModel.datatype.arrayModel"
                                            :key="subItem.id" :label="subItem.name" :value="subItem.id"></el-option>
                                    </el-select>
                                </el-col> -->                                 
                                <el-col :span="5" v-if="actionItem.parentModel.datatype && actionItem.parentModel.datatype.type == 'array'">
                                    <el-select v-model="actionItem.id" placeholder="请选择" size="default"
                                        @change="handleActionModelChange($event, index)">
                                        <el-option v-for="(subItem, subIndex) in actionItem.parentModel.datatype.params"
                                            :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                    </el-select>
                                </el-col>
                                <el-col :span="5" v-if="actionItem.parentModel.datatype && actionItem.parentModel.datatype.type == 'object'">
                                    <el-select v-model="actionItem.id" placeholder="请选择" size="default"
                                        @change="handleActionModelChange($event, index)">
                                        <el-option v-for="(subItem, subIndex) in actionItem.parentModel.datatype.params"
                                            :key="subIndex" :label="subItem.name" :value="subItem.id"></el-option>
                                    </el-select>
                                </el-col>

                                <el-col :span="10" v-if="actionItem.model">
                                    <div
                                        v-if="actionItem.model.datatype && (actionItem.model.datatype.type == 'integer' || actionItem.model.datatype.type == 'decimal')">
                                        <el-input style="vertical-align: baseline" v-model="actionItem.value"
                                            placeholder="值" :max="actionItem.model.datatype.max"
                                            :min="actionItem.model.datatype.min" type="number" size="default">
                                            <template slot="append">{{ actionItem.model.datatype.unit }}</template>
                                        </el-input>
                                    </div>
                                    <div
                                        v-else-if="actionItem.model.datatype && (actionItem.model.datatype.type == 'bool')">
                                        <el-switch style="vertical-align: baseline" v-model="actionItem.value"
                                            :active-text="actionItem.model.datatype.trueText"
                                            :inactive-text="actionItem.model.datatype.falseText" :active-value="1"
                                            :inactive-value="0"></el-switch>
                                    </div>
                                    <div
                                        v-else-if="actionItem.model.datatype && (actionItem.model.datatype.type == 'enum')">
                                        <el-select v-model="actionItem.value" placeholder="请选择" style="width: 100%"
                                            size="default">
                                            <el-option v-for="(subItem, subIndex) in actionItem.model.datatype.enumList"
                                                :key="subIndex + 'things'" :label="subItem.text"
                                                :value="subItem.value"></el-option>
                                        </el-select>
                                    </div>
                                    <div
                                        v-else-if="actionItem.model.datatype && (actionItem.model.datatype.type == 'string')">
                                        <el-input v-model="actionItem.value" placeholder="请输入字符串"
                                            :max="actionItem.model.datatype.maxLength" size="default" />
                                    </div>
                                </el-col>
                        </el-row>
                    </div>
                    <div>
                        +
                        <a style="color: #409eff" @click="handleAddActionItem()">添加执行动作</a>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="handleCancel">取 消</el-button>
                <!-- {{ dialogstate.form.jobId == undefined }} -->
                <el-button v-if="dialogstate.form.jobId == undefined" type="primary"
                    @click="handleSubmitForm(DialogFormRef)" :loading="submitButtonLoading"
                    v-auths="['iot:device:timer:edit']">新 增</el-button>
                <el-button v-else type="primary" @click="handleSubmitForm(DialogFormRef)" :loading="submitButtonLoading"
                    v-auths="['iot:device:timer:edit']">修 改</el-button>

            </template>
        </el-dialog>
        <el-dialog title="Cron表达式生成器" v-model="openCron" append-to-body destroy-on-close class="scrollbar">
            <crontab @hide="openCron = false" @fill="crontabFill" :expression="expression" style="padding-bottom: 80px">
            </crontab>
        </el-dialog>

        <!-- 定时日志详细 -->
        <el-dialog style="position: absolute; top: 100px;" title="定时详细" v-model="dialogstate.dialog.openView"
            width="700px" append-to-body>
            <el-form ref="form" :model="dialogstate.form" label-width="120px" size="default">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="定时编号：">{{ dialogstate.form.jobId }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="定时分组：">{{ jobGroupFormat(dialogstate.form, '') }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="定时名称：">{{ dialogstate.form.jobName }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="创建时间：">{{ dialogstate.form.createTime }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="是否并发：">
                            <div v-if="dialogstate.form.concurrent == 0">允许</div>
                            <div v-else-if="dialogstate.form.concurrent == 1">禁止</div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="cron表达式：">{{ dialogstate.form.cronExpression }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="执行策略：">
                            <div v-if="dialogstate.form.misfirePolicy == 0">默认策略</div>
                            <div v-else-if="dialogstate.form.misfirePolicy == 1">立即执行</div>
                            <div v-else-if="dialogstate.form.misfirePolicy == 2">执行一次</div>
                            <div v-else-if="dialogstate.form.misfirePolicy == 3">放弃执行</div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="下次执行时间：">{{ parseTime(dialogstate.form.nextValidTime) }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="定时状态：">
                            <div v-if="dialogstate.form.status == 0">正常</div>
                            <div v-else-if="dialogstate.form.status == 1">暂停</div>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="执行动作：">
                            <div v-html="formatActionsDisplay(dialogstate.form.actions)"
                                style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; width: 465px"></div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="dialogstate.dialog.openView = false">关 闭</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts" name="">
import { nextTick, onMounted, reactive, ref, watch } from 'vue';
import { selectDictLabel } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addJob, changeJobStatus, delJob, getJob, listJob, runJob, updateJob } from '/@/api/iot/deviceJob';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import Crontab from '/@/components/Crontab/index.vue';
import { parseTime } from '/@/utils/next'
const dictStore = useDictStore();  // 使用 Pinia store
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
// 定义 props
const props = defineProps({
    device: {
        type: Object
    }
})
// 定义变量内容
const DialogFormRef = ref();
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref()
// 设备信息
const deviceInfo = ref<any>({
    chartList: [],
})
let thingsModel = reactive({
    functions: '' as any,
    properties: '' as any

})  // 物模型JSON
const showSearch = ref(true)
const job_group_list = ref<Option[]>([]);
const job_status_list = ref<Option[]>([]);
const state = reactive({
    tableData: {
        jobList: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: '' as any,
            jobName: '' as any,
            deviceId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
});
const dialogstate = reactive({
    form: {
        jobName: '',
        cronExpression: '',
        jobId: undefined,
        status: '' as any,
        jobGroup: 'DEFAULT', // 定时分组
        misfirePolicy: 2, // 1=立即执行，2=执行一次，3=放弃执行
        concurrent: 1, // 是否并发，1=禁止，0=允许
        isAdvance: 0, // 是否详细cron表达式
        jobType: 1, // 任务类型 1=设备定时，2=设备告警，3=场景联动
        productId: 0,
        productName: '',
        sceneId: 0, //场景ID
        alertId: 0, // 告警ID
        actions: [],
        nextValidTime: '',
        createTime: '',
        deviceId: 0, // Add deviceId property
        deviceName: '', // Add deviceName property
        serialNumber: '' // Add serialNumber property
    },
    dialog: {
        isShowDialog: false,
        title: '',
        openView: false
    },
})
const expression = ref('')
const openCron = ref(false)
// 校验规则
const rules = reactive({
    jobName: [
        {
            required: true,
            message: '定时名称不能为空',
            trigger: 'blur',
        },
    ],

})
const timerWeeks = reactive([
    {
        value: 1,
        label: '周一',
    },
    {
        value: 2,
        label: '周二',
    },
    {
        value: 3,
        label: '周三',
    },
    {
        value: 4,
        label: '周四',
    },
    {
        value: 5,
        label: '周五',
    },
    {
        value: 6,
        label: '周六',
    },
    {
        value: 7,
        label: '周日',
    },
])
let actionList = ref([
    {
        id: '',
        name: '',
        value: '',
        valueName: '',
        type: 1,
        parentId: '',
        parentName: '',
        arrayIndex: '',
        arrayIndexName: '',
        model: {} as any,
        parentModel: [
            {
                datatype: {
                    type: '',
                }
            }
        ] as any,
    },
])
const submitButtonLoading = ref(false)// 提交按钮加载
let timerWeekValue = ref<any>([1, 2, 3, 4, 5, 6, 7])
const timerTimeValue = ref('')
const modelTypes = reactive([
    {
        label: '属性',
        value: 1,
    },
    {
        label: '功能',
        value: 2,
    },
])
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        listJob(state.tableData.param).then((response) => {
            state.tableData.jobList = response.data.rows;
            state.tableData.total = response.data.total;
        })

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { jobId: string; }) => item.jobId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 定时状态修改
const handleStatusChange = (row: any) => {
    let text = row.status === '0' ? '启用' : '停用';
    ElMessageBox.confirm(
        `确认要"${text}" "${row.jobName}" 定时吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
        }
    )
        .then(() => {
            // 调用 changeJobStatus 函数
            return changeJobStatus(row.jobId, row.status);
        })
        .then(() => {
            // 操作成功后显示消息
            ElMessage.success(`${text} 成功`);
        })
        .catch(() => {
            // 如果用户取消，则恢复状态
            row.status = row.status === '0' ? '1' : '0';
        });
}
const reset = () => {
    dialogstate.form = {
        jobName: '',
        cronExpression: '',
        jobId: undefined,
        status: '0',
        jobGroup: 'DEFAULT', // 定时分组
        misfirePolicy: 2, // 1=立即执行，2=执行一次，3=放弃执行
        concurrent: 1, // 是否并发，1=禁止，0=允许
        isAdvance: 0, // 是否详细cron表达式
        jobType: 1, // 任务类型 1=设备定时，2=设备告警，3=场景联动
        productId: 0,
        productName: '',
        sceneId: 0, //场景ID
        alertId: 0, // 告警ID
        actions: [],
        nextValidTime: '',
        createTime: '',
        deviceId: 0, // Add deviceId property
        deviceName: '', // Add deviceName property
        serialNumber: '' // Add serialNumber property
    };
    submitButtonLoading.value = false;
    timerWeekValue.value = [1, 2, 3, 4, 5, 6, 7] as any;
    timerTimeValue.value = '';
    actionList.value = [
        {
            id: '',
            name: '',
            value: '',
            valueName: '',
            type: 1,
            parentId: '',
            parentName: '',
            arrayIndex: '',
            arrayIndexName: '',
            model: {} as any,
            parentModel: [
                {
                    datatype: {
                        type: '',
                    }
                }
            ] as any,
        },
    ];
}
// 修改按钮操作
const handleUpdate = (row: any) => {
    reset();
    const jobId = row.jobId || ids;
    getJob(jobId).then((response) => {
        dialogstate.form = response.data.data;
        console.log(dialogstate.form);

        // actionList.value赋值
        actionList.value = JSON.parse(dialogstate.form.actions as any);
        for (let i = 0; i < actionList.value.length; i++) {
            if (actionList.value[i].type == 1) {
                setParentAndModelData(actionList.value[i], thingsModel.properties);
            } else if (actionList.value[i].type == 2) {
                setParentAndModelData(actionList.value[i], thingsModel.functions);
            }
        }
        if (dialogstate.form.isAdvance == 0) {
            let arrayValue = dialogstate.form.cronExpression.substring(12).split(',').map(Number);
            timerWeekValue.value = arrayValue;
            timerTimeValue.value = dialogstate.form.cronExpression.substring(5, 7) + ':' + dialogstate.form.cronExpression.substring(2, 4);
        }
        dialogstate.dialog.isShowDialog = true;
        dialogstate.dialog.title = '修改定时';
    });
}
// 设置父级物模型和物模型值
const setParentAndModelData = (sceneScript: any, sceneScripts: any) => {
    for (let i = 0; i < sceneScripts.length; i++) {
        if (sceneScript.parentId == sceneScripts[i].id) {
            sceneScript.parentModel = sceneScripts[i];
            if (sceneScript.parentModel.datatype.type === 'object') {
                // 对象类型，物模型赋值
                for (let j = 0; j < sceneScript.parentModel.datatype.params.length; j++) {
                    if (sceneScript.id == sceneScript.parentModel.datatype.params[j].id) {
                        sceneScript.model = sceneScript.parentModel.datatype.params[j];
                    }
                }
            } else if (sceneScript.parentModel.datatype.arrayType === 'object' && sceneScript.parentModel.datatype.type === 'array') {
                // 对象数组类型，物模型集合移除索引前缀
                if (sceneScript.id.indexOf('array_') != -1) {
                    sceneScript.id = sceneScript.id.substring(9);
                }
                // 物模型赋值
                for (let j = 0; j < sceneScript.parentModel.datatype.params.length; j++) {
                    if (sceneScript.id == sceneScript.parentModel.datatype.params[j].id) {
                        sceneScript.model = sceneScript.parentModel.datatype.params[j];
                    }
                }
            } else if (sceneScript.parentModel.datatype.arrayType !== 'object' && sceneScript.parentModel.datatype.type === 'array') {
                // 普通数组类型，物模型集合移除索引前缀
                if (sceneScript.id.indexOf('array_') != -1) {
                    sceneScript.id = sceneScript.id.substring(9);
                }
                sceneScript.model = {
                    datatype: {
                        type: sceneScript.parentModel.datatype.arrayType,
                        maxLength: -1,
                        min: -1,
                        max: -1,
                        unit: '无单位',
                    },
                };
            } else {
                // 普通类型
                sceneScript.model = sceneScript.parentModel;
            }
            console.log(sceneScript.model, 'sceneScript.model');

            break;
        }
    }
}
/* 立即执行一次 */
const handleRun = (row: any) => {
    ElMessageBox.confirm(
        `确认要立即执行一次 "${row.jobName}" 定时吗？`,
        '提示',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
        }
    )
        .then(() => {
            // 执行定时任务
            return runJob(row.jobId, row.jobGroup);
        })
        .then(() => {
            // 执行成功后提示
            ElMessage.success('执行成功');
        })
        .catch(() => {
            // 用户取消操作时什么都不做
        });
};
// 删除按钮操作
const handleDelete = (row: any) => {
    const jobIds = row.jobId || ids;
    ElMessageBox.confirm(`是否确认删除定时定时编号为${jobIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delJob(jobIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });

}
/** 定时详细信息 */
const handleView = (row: any) => {
    getJob(row.jobId).then((response) => {
        dialogstate.form = response.data.data;
        dialogstate.dialog.openView = true;
    });
}
/** cron表达式按钮操作 */
const handleShowCron = () => {
    expression.value = dialogstate.form.cronExpression;
    openCron.value = true;
}
/** 确定后回传值 */
const crontabFill = (value: any) => {
    dialogstate.form.cronExpression = value;
}
// 新增按钮操作
const handleAdd = () => {
    reset();
    dialogstate.dialog.isShowDialog = true;
    dialogstate.dialog.title = '添加定时';
}
// 提交按钮
const handleSubmitForm = async (formEl: FormInstance | undefined) => {
    console.log(formEl);

    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            let actions = [];
            if (dialogstate.form.isAdvance == 0) {
                if (timerTimeValue.value == '' || timerTimeValue.value == null) {
                    ElMessage.error('执行时间不能空');
                    return;
                }
                console.log(timerWeekValue.value);

                if (timerWeekValue.value.length == 0) {
                    ElMessage.error('请选择要执行的星期');
                    return;
                }
            } else if (dialogstate.form.isAdvance == 1) {
                if (dialogstate.form.cronExpression == '') {
                    ElMessage.error('cron表达式不能为空');
                    return;
                }
            }
            for (let i = 0; i < actionList.value.length; i++) {
                if (actionList.value[i].value === '') {
                    ElMessage.error('执行动作中的选项和值不能为空');
                    return;
                }
                // 数据重组
                let item = actionList.value[i];
                // id拼接array索引
                let id = '';
                if (item.arrayIndex != '') {
                    id = 'array_' + item.arrayIndex + '_' + item.id;
                } else {
                    id = item.id;
                }
                // 获取valueName
                let valueName = '';
                if (item.model.datatype.type === 'bool') {
                    valueName = item.value == '1' ? item.model.datatype.trueText : item.model.datatype.falseText;
                } else if (item.model.datatype.type === 'enum') {
                    valueName = item.model.datatype.enumList.find((subItem: any) => subItem.value === item.value).text;
                } else {
                    valueName = '';
                }
                // 只传需要的数据
                actions[i] = {
                    type: item.type,
                    id: item.id,
                    name: item.name,
                    value: item.value,
                    valueName: valueName,
                    parentId: item.parentId,
                    parentName: item.parentName,
                    arrayIndex: item.arrayIndex,
                    arrayIndexName: item.arrayIndexName,
                    deviceId: deviceInfo.value.deviceId,
                    deviceName: deviceInfo.value.deviceName,
                };
            }
            dialogstate.form.actions = JSON.stringify(actions) as any;
            // 设备信息
            dialogstate.form.deviceId = deviceInfo.value.deviceId;
            dialogstate.form.deviceName = deviceInfo.value.deviceName;
            dialogstate.form.serialNumber = deviceInfo.value.serialNumber;
            dialogstate.form.productId = deviceInfo.value.productId;
            dialogstate.form.productName = deviceInfo.value.productName;
            // 按钮等待后端加载完
            submitButtonLoading.value = true;
            if (dialogstate.form.jobId != undefined) {
                updateJob(dialogstate.form).then(() => {
                    ElMessage.success('修改成功');
                    submitButtonLoading.value = false;
                    dialogstate.dialog.isShowDialog = false;
                    getTableData();
                });
            } else {
                addJob(dialogstate.form).then(() => {
                    ElMessage.success('新增成功');
                    submitButtonLoading.value = false;
                    dialogstate.dialog.isShowDialog = false;
                    getTableData();
                });
            }
        } else {
            console.log('error submit!', fields)
        }
    })
};
const handleCancel = () => {
    dialogstate.dialog.isShowDialog = false;
    reset();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        status: '' as any,
        jobName: '' as any,
        deviceId: '' as any
    }
}
// 定时组名字典翻译
const jobGroupFormat = (row: any, column: any) => {
    return selectDictLabel(job_group_list.value, row.jobGroup);
}
/** 星期改变事件 **/
const weekChange = (data: any) => {
    gentCronExpression();
}
/** 时间改变事件 **/
const timeChange = (data: any) => {
    gentCronExpression();
}
/**自定义cron表达式选项改变事件 */
const customerCronChange = (data: any) => {
    if (data == 0) {
        gentCronExpression();
    }
}
/** 生成cron表达式**/
const gentCronExpression = () => {
    let hour = '00';
    let minute = '00';
    if (timerTimeValue.value != null && timerTimeValue.value != '') {
        hour = timerTimeValue.value.substring(0, 2);
        minute = timerTimeValue.value.substring(3);
    }
    let week = '*' as any;
    console.log(timerWeekValue.value);

    if (timerWeekValue.value.length > 0) {
        week = timerWeekValue.value.sort();
    }
    dialogstate.form.cronExpression = '0 ' + minute + ' ' + hour + ' ? * ' + week;
}
// 格式化显示CRON描述
const formatCronDisplay = (item: any) => {
    let result = '';
    if (item.isAdvance == 0) {
        let time = '<br /><span style="color:#F56C6C">时间 ' + item.cronExpression.substring(5, 7) + ':' + item.cronExpression.substring(2, 4) + '</span>';
        let week = item.cronExpression.substring(12);
        if (week == '1,2,3,4,5,6,7') {
            result = '每天 ' + time;
        } else {
            let weekArray = week.split(',');
            for (let i = 0; i < weekArray.length; i++) {
                if (weekArray[i] == '1') {
                    result = result + '周一、';
                } else if (weekArray[i] == '2') {
                    result = result + '周二、';
                } else if (weekArray[i] == '3') {
                    result = result + '周三、';
                } else if (weekArray[i] == '4') {
                    result = result + '周四、';
                } else if (weekArray[i] == '5') {
                    result = result + '周五、';
                } else if (weekArray[i] == '6') {
                    result = result + '周六、';
                } else if (weekArray[i] == '7') {
                    result = result + '周日、';
                }
            }
            result = result.substring(0, result.length - 1) + ' ' + time;
        }
    } else {
        result = '自定义Cron表达式';
    }
    return result;
}
// 格式化显示动作
const formatActionsDisplay = (json: any) => {
    if (json == null || json == '') {
        return;
    }
    let actions = JSON.parse(json);
    let result = '';
    for (let i = 0; i < actions.length; i++) {
        if (actions[i].arrayIndexName) {
            result =
                result +
                `${actions[i].parentName} >> ${actions[i].arrayIndexName} >> ${actions[i].name} <span style="color:#F56C6C"> ${actions[i].valueName ? actions[i].valueName : actions[i].value}</span><br />`;
        } else {
            if (actions[i].parentName !== actions[i].name) {
                result = result + `${actions[i].parentName} >> ${actions[i].name} <span style="color:#F56C6C">${actions[i].valueName ? actions[i].valueName : actions[i].value}</span><br />`;
            } else {
                result = result + `${actions[i].name} <span style="color:#F56C6C">${actions[i].valueName ? actions[i].valueName : actions[i].value}</span><br />`;
            }
        }
    }
    return result == '' ? '无' : result;
}

// 物模型格式化处理
const formatArrayIndex = (data: any) => {
    let obj = { ...data };
    for (let o in obj) {
        obj[o] = obj[o].map((item: any) => {
            if (item.datatype.type === 'array') {
                let arrayModel = [];
                for (let k = 0; k < item.datatype.arrayCount; k++) {
                    let index = k > 9 ? String(k) : '0' + k;
                    if (item.datatype.arrayType === 'object') {
                        arrayModel.push({
                            id: index,
                            name: item.name + ' ' + (k + 1),
                        });
                    } else {
                        arrayModel.push({
                            id: index,
                            name: item.name + ' ' + (k + 1),
                        });
                    }
                }
                item.datatype.arrayModel = arrayModel;
            }
            return item;
        });
    }
    return obj;
}
// 添加执行动作
const handleAddActionItem = () => {
    actionList.value.push({
        id: '',
        name: '',
        value: '',
        valueName: '',
        type: 1, // 1=属性，2=功能，3=事件，5=设备上线，6=设备下线
        parentId: '',
        parentName: '',
        arrayIndex: '',
        arrayIndexName: '',
        model: null,
        parentModel: undefined
    });
    console.log(actionList.value, 'actionList.value');

}
// 删除执行动作
const handleRemoveActionItem = (index: any) => {
    actionList.value.splice(index, 1);
}
// 动作类型改变事件
const handleActionTypeChange = (data: any, index: any) => {
    actionList.value[index].id = '';
    actionList.value[index].name = '';
    actionList.value[index].value = '';
    actionList.value[index].valueName = '';
    actionList.value[index].parentId = '';
    actionList.value[index].parentName = '';
    actionList.value[index].arrayIndex = '';
    actionList.value[index].arrayIndexName = '';
    actionList.value[index].parentModel = null;
    actionList.value[index].model = null;
}
// 执行动作物模型数组索引选择
const handleActionIndexChange = (id: any, index: any) => {
    actionList.value[index].arrayIndexName = actionList.value[index].parentModel.datatype.arrayModel.find((x: any) => x.id == id).name;
    actionList.value[index].value = '';
    // 数组类型保留id和name
    if (actionList.value[index].parentModel.datatype.arrayType === 'object') {
        actionList.value[index].id = '';
        actionList.value[index].name = '';
    }
}
// 动作物模型项改变事件
const handleActionParentModelChange = (identifier: any, index: any) => {
    actionList.value[index].model = null;
    actionList.value[index].value = '';
    actionList.value[index].arrayIndex = '';
    actionList.value[index].arrayIndexName = '';

    let scripts = [];
    if (actionList.value[index].type == 1) {
        //属性
        scripts = thingsModel.properties;
    } else if (actionList.value[index].type == 2) {
        //功能
        scripts = thingsModel.functions;
    }
    for (let i = 0; i < scripts.length; i++) {
        if (scripts[i].id == identifier) {
            actionList.value[index].parentName = scripts[i].name;
            actionList.value[index].parentModel = scripts[i];
            if (scripts[i].datatype.type === 'object') {
                // 对象类型
                actionList.value[index].id = '';
                actionList.value[index].name = '';
            } else if (scripts[i].datatype.type === 'array' && scripts[i].datatype.arrayType === 'object') {
                // 对象数组类型
                actionList.value[index].id = '';
                actionList.value[index].name = '';
            } else if (scripts[i].datatype.type === 'array' && scripts[i].datatype.arrayType !== 'object') {
                // 普通类型，数组类
                actionList.value[index].id = scripts[i].id;
                actionList.value[index].name = scripts[i].name;
                // 用于减少界面判断
                actionList.value[index].model = {
                    datatype: {
                        type: scripts[i].datatype.arrayType,
                        maxLength: -1,
                        min: -1,
                        max: -1,
                        unit: '无单位',
                    },
                };
            } else {
                // 普通类型,不包含数组类型
                actionList.value[index].id = scripts[i].id;
                actionList.value[index].name = scripts[i].name;
                actionList.value[index].model = scripts[i];
            }
            break;
        }
    }
}
// 执行动作物模型选择
const handleActionModelChange = (id: any, index: any) => {
    actionList.value[index].value = '';
    let model = null;
    if (actionList.value[index].parentModel.datatype.type === 'array' || actionList.value[index].parentModel.datatype.type === 'object') {
        model = actionList.value[index].parentModel.datatype.params.find((item: any) => item.id == id);
        actionList.value[index].name = model.name;
        actionList.value[index].model = model;
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        job_group_list.value = await dictStore.fetchDict('sys_job_group')
        job_status_list.value = await dictStore.fetchDict('sys_job_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 监听props的变化
watch(() => props.device, (newVal) => {
    console.log(newVal);

    try {
        if (newVal && newVal.deviceId != 0) {
            console.log(newVal, 'newVal');
            deviceInfo.value = newVal;
            if (deviceInfo.value && deviceInfo.value.deviceId !== 0) {
                thingsModel = formatArrayIndex(newVal.cacheThingsModel);

                // 过滤监测数据和只读数据
                if (thingsModel.properties && thingsModel.properties.length !== 0) {
                    console.log(thingsModel.properties, 'thingsModel.properties');

                    thingsModel.properties = thingsModel.properties.filter((item: any) => {
                        if (item.datatype.params && item.datatype.params.length !== 0) {
                            item.datatype.params = item.datatype.params.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
                            // item.datatype.params = item.datatype.params.filter((item: any) => item.type == 1 && item.subType == 3);
                        }
                        return item.isMonitor == 0 && item.isReadonly == 0;
                    });
                }
                if (thingsModel.properties && thingsModel.properties.length !== 0) {
                    thingsModel.properties = thingsModel.properties.filter((item: any) => {
                        if (item.datatype.params && item.datatype.params.length !== 0) {
                            // item.datatype.params = item.datatype.params.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
                            item.datatype.params = item.datatype.params.filter((item: any) => item.type == 1 && item.subType == 3);
                            item.datatype.params = item.datatype.params.filter((item: any) => item.type == 1 && item.subType == 3);
                        }
                        return item.type == 1 && item.subType == 3;
                    });
                }

                // if (thingsModel.functions && thingsModel.functions.length !== 0) {
                //     thingsModel.functions = thingsModel.functions.filter((item: any) => {
                //         if (item.datatype.params && item.datatype.params.length !== 0) {
                //             // item.datatype.params = item.datatype.params.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
                //             item.datatype.params = item.datatype.params.filter((item: any) => item.type == 1 && item.subType == 3);
                //         }
                //         return item.type == 1 && item.subType == 3;
                //     });
                // }

                if (thingsModel.functions && thingsModel.functions.length !== 0) {
                    thingsModel.functions = thingsModel.functions.filter((item: any) => {
                        if (item.datatype.params && item.datatype.params.length !== 0) {
                            item.datatype.params = item.datatype.params.filter((item: any) => item.isMonitor == 0 && item.isReadonly == 0);
                            // item.datatype.params = item.datatype.params.filter((item: any) => item.type == 1 && item.subType == 3);
                        }
                        return item.isMonitor == 0 && item.isReadonly == 0;
                    });
                }
                state.tableData.param.deviceId = deviceInfo.value.deviceId;
                getTableData()
                getdictdata()
            }
            // mqttCallback();
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
</script>
<style lang="scss" scoped>
.device-timer-wrap {
    padding-left: 20px;
}

.device-timer-config-dialog {
    .action-wrap {
        position: relative;

        .item-wrap {
            width: 540px;
            margin-bottom: 15px;
            padding: 10px;
            background-color: #d9e5f6;
            border-radius: 5px;

            .delete-wrap {
                position: absolute;
                right: 10px;
                top: 0;
            }
        }
    }
}
</style>