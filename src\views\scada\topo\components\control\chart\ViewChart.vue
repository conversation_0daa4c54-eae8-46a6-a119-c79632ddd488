<template>
    <div>
        <div v-show="false">{{ height }}{{ width }}{{ foreColor }}</div>
        <div class="view-chart" ref="chartView" :id="detail.identifier" v-show="detail.dataBind.lData.length > 0" @dblclick="handleDblclick" />
        <div
            v-show="detail.dataBind.lData.length === 0"
            :style="{
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
                'text-align': 'center',
                'line-height': detail.style.position.h + 'px',
                'font-size': '30px',
                color: '#368a42',
            }"
            @dblclick="handleDblclick"
        >
            双击绑定变量
        </div>
        <el-dialog title="变量绑定" v-model="dialogVisible" width="40%" append-to-body>
            <div style="min-height: 400px" v-loading="loading" v-if="paramNameList.length > 0">
                <div style="margin: 0 15px 15px; color: red">多属性对比时，多个属性上报时间需一样！</div>
                <el-form label-width="85px">
                    <el-form-item v-for="item in paramNameList" :key="item.id" :label="item.name">
                        <el-checkbox-group v-model="checkList">
                            <el-checkbox v-for="chil in item.children" :key="`${item.id}-${chil.id}`" :value="`${item.id}-${chil.id}-${chil.type}`">{{ chil.name }}</el-checkbox>
                        </el-checkbox-group>
                    </el-form-item>
                </el-form>
            </div>
            <el-empty v-else description="请先绑定设备或为设备创建物模型"></el-empty>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">关 闭</el-button>
                    <el-button type="primary" @click="handleClick">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
// import { useCounterStore } from '/@/stores/counterStore';
import topoUtil from '/@/utils/topo/topo-util';
import { listDeviceThingsModel, getThingsModelHistory } from '/@/api/scada/topo';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Composables
const route = useRoute();
// const counterStore = useCounterStore();

// Refs
const chartView = ref<HTMLElement>();
const loading = ref(false);
const paramNameList = ref<any[]>([]);
const paramList = ref<any[]>([]);
const checkList = ref<any[]>([]);
const dialogVisible = ref(false);
const myChart = ref<any>(null);
const timer = ref<any>(null);

// Reactive data
const option = ref({
  title: {
    text: '',
  },
  tooltip: {
    trigger: 'item',  // 改为 item 触发，只在悬停到具体数据点时显示
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: '#333',
    borderWidth: 1,
    textStyle: {
      color: '#fff',
      fontSize: 12
    },
    formatter: function (params: any) {
      const color = params.color;
      const seriesName = params.seriesName;
      const value = params.value;
      const axisValue = params.name;

      return `<div style="padding: 5px;">
        <div style="margin-bottom: 5px; font-weight: bold; color: #ffeb3b;">${axisValue}</div>
        <div style="display: flex; align-items: center;">
          <span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>
          <span style="font-weight: bold;">${seriesName}:</span>
          <span style="color: #ffeb3b; margin-left: 5px; font-size: 14px; font-weight: bold;">${value}</span>
        </div>
      </div>`;
    },
    extraCssText: 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);'
  },
  legend: {
    textStyle: { color: props.detail.style.foreColor },
    data: [],
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  toolbox: {
    feature: {
      saveAsImage: {},
    },
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: [],
    axisLabel: {
      formatter: function (param: string) {
        let time = param.split(' ')[1];
        return time;
      },
      inside: false,
    },
  },
  yAxis: {
    type: 'value',
  },
  series: [],
});

// Computed
const width = computed(() => {
  nextTick(() => {
    if (myChart.value) {
      myChart.value.resize({
        width: props.detail.style.position.w,
        height: props.detail.style.position.h,
      });
    }
  });
  return props.detail.style.position.w;
});

const height = computed(() => {
  nextTick(() => {
    if (myChart.value) {
      myChart.value.resize({
        width: props.detail.style.position.w,
        height: props.detail.style.position.h,
      });
    }
  });
  return props.detail.style.position.h;
});

const foreColor = computed(() => {
  nextTick(() => {
    if (option.value.legend.textStyle) {
      option.value.legend.textStyle.color = props.detail.style.foreColor;
      setOption(option.value);
    }
  });
  return props.detail.style.foreColor;
});
// Methods
const handleDblclick = () => {
  getParam();
  dialogVisible.value = true;
};

const handleClick = () => {
  getChartsData();
};

const setOption = (optionData: any) => {
  if (myChart.value) {
    myChart.value.setOption(optionData);
  }
};
//获取设备变量
const getParam = async () => {
  // 如果已经有数据，直接返回，不重复获取
  if (paramNameList.value.length > 0) {
    return;
  }

  loading.value = true;
  const params = {
    pageNum: 1,
    pageSize: 999,
    scadaGuid: route.query.guid,
  };

  try {
    const ress = await listDeviceThingsModel(params);
    const res = ress.data;
    if (res.code == 200) {
      paramList.value = res.rows;
      paramNameList.value = formatParamName(res.rows);
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.log(err);
  } finally {
    loading.value = false;
  }
};
// 格式化属性数据
const formatParamName = (list: any[]) => {
  let datas: any[] = [];
  if (list.length !== 0) {
    list.forEach((item, i) => {
      if (i === 0) {
        let par = {
          id: item.serialNumber,
          name: item.deviceName,
          children: [
            {
              id: item.identifier,
              name: item.modelName,
              type: item.type,
            },
          ],
        };
        datas.push(par);
      } else {
        let par = datas.find((d) => d.id === item.serialNumber);
        if (par) {
          let chil = {
            id: item.identifier,
            name: item.modelName,
            type: item.type,
          };
          par.children.push(chil);
        } else {
          let par = {
            id: item.serialNumber,
            name: item.deviceName,
            children: [
              {
                id: item.identifier,
                name: item.modelName,
                type: item.type,
              },
            ],
          };
          datas.push(par);
        }
      }
    });
  }
  return datas;
};
// 获取属性数据
const getChartsData = async () => {
  if (checkList.value.length !== 0) {
    let serialNumber = checkList.value[0].split('-')[0];
    let res = checkList.value.find((item: string) => item.indexOf(serialNumber) === -1);
    if (res) {
      ElMessage.error('请选择同一个设备的属性！');
      return;
    }
    let identifiers = checkList.value.map((item: string) => ({
      identifier: item.split('-')[1],
      type: item.split('-')[2],
    }));
    let query = {
      serialNumber: serialNumber,
      beginTime: topoUtil.getTime(1),
      endTime: topoUtil.getNowTime(),
      thingsModelList: identifiers,
    };

    try {
      const ress = await getThingsModelHistory(query);
      const res = ress.data;
      if (res.code == 200) {
        let ldata: any[] = [];
        let xdata: any[] = [];
        let ydata: any[] = [];
        const modelIdens = Object.keys(res.data) || [];
        if (modelIdens.length !== 0) {
          // 获取第一个设备的数据用于排序
          const firstDeviceData = res.data[modelIdens[0]];

          // 按时间升序排序，创建索引映射
          const sortedIndices = firstDeviceData
            .map((item: any, index: number) => ({ time: item.time, index }))
            .sort((a: any, b: any) => new Date(a.time).getTime() - new Date(b.time).getTime())
            .map((item: any) => item.index);

          // 按排序后的索引重新组织数据
          ldata = modelIdens.map((item) => paramList.value.find((p) => p.identifier === item).modelName);
          xdata = sortedIndices.map((index: number) => firstDeviceData[index].time);
          ydata = modelIdens.map((item) => ({
            name: paramList.value.find((p) => p.identifier === item).modelName,
            data: sortedIndices.map((index: number) => res.data[item][index].value),
          }));
          props.detail.dataBind.lData = ldata;
          props.detail.dataBind.xData = xdata;
          props.detail.dataBind.yData = ydata;
          setChartDatas(ldata, xdata, ydata);
          nextTick(() => {
            setOption(option.value);
          });
          ElMessage.success('数据绑定成功！');
        } else {
          ElMessage.warning('暂无数据！');
        }
        dialogVisible.value = false;
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
      ElMessage.error('获取数据失败！');
    }
  }
};
// 设置表格数据
const setChartDatas = (ldata: any[], xdata: any[], ydata: any[]) => {
  // 确保时间数据按升序排列
  if (xdata.length > 0) {
    // 创建时间和数据的映射，然后按时间排序
    const timeDataMap = xdata.map((time, index) => ({
      time,
      index,
      timestamp: new Date(time).getTime()
    })).sort((a, b) => a.timestamp - b.timestamp);

    // 重新排列 x 轴数据
    const sortedXData = timeDataMap.map(item => item.time);
    const sortedIndices = timeDataMap.map(item => item.index);

    // 重新排列 y 轴数据
    const sortedYData = ydata.map(series => ({
      ...series,
      data: sortedIndices.map(index => series.data[index])
    }));

    option.value.legend.data = ldata;
    option.value.xAxis.data = sortedXData;
    option.value.series = [];

    sortedYData.forEach((element) => {
      let data: any = {};
      if (props.detail.type == 'chart-line') {
        data = {
          name: element.name,
          type: 'line',
          stack: 'Total',
          data: element.data,
          itemStyle: {},
          label: {
            show: false,  // 默认不显示标签
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            label: {
              show: true,                // 悬停时显示标签
              position: 'top',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333',
              formatter: '{c}',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderColor: '#666',
              borderWidth: 1,
              borderRadius: 4,
              padding: [3, 6],
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true
        };
      } else if (props.detail.type == 'chart-line-step') {
        data = {
          name: element.name,
          type: 'line',
          stack: 'Total',
          step: 'start',
          data: element.data,
          itemStyle: {},
          label: {
            show: false,  // 默认不显示标签
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            label: {
              show: true,                // 悬停时显示标签
              position: 'top',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333',
              formatter: '{c}',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderColor: '#666',
              borderWidth: 1,
              borderRadius: 4,
              padding: [3, 6],
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          symbol: 'circle',
          symbolSize: 8,
          showSymbol: true
        };
      } else {
        data = {
          name: element.name,
          type: 'bar',
          stack: 'Total', //有代表是一个柱形图堆叠，无这属性柱形分散排列
          barWidth: 17,
          data: element.data,
          itemStyle: {},
          label: {
            show: false,  // 默认不显示标签
          },
          emphasis: {
            focus: 'series',
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            label: {
              show: true,                // 悬停时显示标签
              position: 'top',
              fontSize: 12,
              fontWeight: 'bold',
              color: '#333',
              formatter: '{c}',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              borderColor: '#666',
              borderWidth: 1,
              borderRadius: 4,
              padding: [3, 6],
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        };
      }
      option.value.series.push(data);
    });
  } else {
    // 如果没有数据，直接设置
    option.value.legend.data = ldata;
    option.value.xAxis.data = xdata;
    option.value.series = [];
  }
};

// Lifecycle
onMounted(() => {
  myChart.value = echarts.init(document.getElementById(props.detail.identifier));
  const { lData, xData, yData } = props.detail.dataBind;
  if (lData.length !== 0 && xData.length !== 0 && yData.length !== 0) {
    setChartDatas(lData, xData, yData);
  }

  // 设置1分钟定时更新
  timer.value = setInterval(() => {
    // 只有在已经绑定了数据的情况下才自动更新
    if (checkList.value.length > 0) {
      getChartsData();
    }
  }, 60000); // 60秒 = 1分钟
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (myChart.value) {
    myChart.value.dispose();
  }
});
</script>

<style lang="scss">
.view-chart {
    height: 100%;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
