<template>
	<!-- 左边 -->
    <div class="big-data-down-left">
					<!-- 左1 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测范围</div>
						</div>
					</div>
					<!-- 左2 -->
					<div class="flex-warp-item">
						
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备监测动态</div>
							<div class="flex-content">
								<div style="height: 100%;width: 100%;" ref="chartsLeftTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 左3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">设备故障分类对比</div>
							<div class="flex-content">
								<div style="height: 100%;" ref="chartsLeftThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 左4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">采集齐全率分析</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsLeftFourRef"></div>
							</div>
						</div>
					</div>
				</div>

				<!-- 中间 -->
				<div class="big-data-down-center">
					<div class="big-data-down-center-one">
						<div class="big-data-down-center-one-content border">
							<div style="height: 100%" ref="chartsCenterOneRef"></div>
						</div>
					</div>
					<div class="big-data-down-center-two">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备故障清单</span>
							</div>
							<div class="flex-content">
								
							</div>
						</div>
					</div>
				</div>

				<!-- 右边 -->
				<div class="big-data-down-right">
					<!-- 右1 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备周故障对比</span>
							</div>

							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightOneRef"></div>
							</div>
						</div>
					</div>
					<!-- 右2 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border" style="position: relative;">
							<div class="flex-title">
								<span>设备运行统计</span>
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightTwoRef"></div>
							</div>
						</div>
					</div>
					<!-- 右3 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备运维考核</span>
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightThreeRef"></div>
							</div>
						</div>
					</div>
					<!-- 右4 -->
					<div class="flex-warp-item">
						<div class="flex-warp-item-box border">
							<div class="flex-title">
								<span>设备故障区域排名</span>
								<!-- <span class="flex-title-small">单位：件</span> -->
							</div>
							<div class="flex-content">
								<div style="height: 100%" ref="chartsRightFourRef"></div>
							</div>
						</div>
					</div>
				</div>
</template>

<style lang="scss" scoped> 
@use './chart.scss' as *;
.title{
    color: #ffffff;
}
</style>

