<template>
    <div class="system-dic-dialog-container" style="padding: 20px;">
        <el-dialog style="position: absolute; top: 100px; padding: 30px;" :title="state.tableData.title"
            v-model="isShowDialog" width="900">
            <el-divider style="margin: 0 0 10px 0;" />
            <el-form ref="queryForm" :inline="true">
                <el-form-item label="名称" prop="templateName">
                    <el-input v-model="state.tableData.queryParams.templateName" placeholder="请选择模型类别" clearable
                        size="default" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item label="类别" prop="type" style="width: 200px">
                    <el-select v-model="state.tableData.queryParams.type" placeholder="请选择模型类别" clearable
                        size="default">
                        <el-option v-for="dict in things_type_list" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="default" @click="handleQuery">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        搜索
                    </el-button>
                    <el-button size="small" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                </el-form-item>
            </el-form>
            <el-table v-loading="state.tableData.loading" :data="state.tableData.data"
                @selection-change="handleSelectionChange" ref="multipleTable" size="default" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="名称" align="center" prop="templateName" />
                <el-table-column label="标识符" align="center" prop="identifier" />
                <el-table-column label="物模型类别" align="center" prop="type">
                    <template #default="scope">
                        <dictTag :options="things_type_list" :value="scope.row.type" />
                    </template>
                </el-table-column>
                <el-table-column label="图表展示" align="center" prop="isChart">
                    <template #default="scope">
                        <dict-tag :options="statuslist" :value="scope.row.isChart" />
                    </template>
                </el-table-column>
                <el-table-column label="实时监测" align="center" prop="isMonitor">
                    <template #default="scope">
                        <dict-tag :options="statuslist" :value="scope.row.isMonitor" />
                    </template>
                </el-table-column>
                <el-table-column label="只读" align="center" prop="isReadonly">
                    <template #default="scope">
                        <dict-tag :options="statuslist" :value="scope.row.isReadonly" />
                    </template>
                </el-table-column>
                <el-table-column label="历史存储" align="center" prop="isHistory">
                    <template #default="scope">
                        <dict-tag :options="statuslist" :value="scope.row.isHistory" />
                    </template>
                </el-table-column>
                <el-table-column label="数据类型" align="center" prop="datatype">
                    <template #default="scope">
                        <dict-tag :options="data_type_list" :value="scope.row.datatype" />
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.queryParams.pageNum" background
                v-model:page-size="state.tableData.queryParams.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total" size="small">
            </el-pagination>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="handleDeviceSelected" size="default">导 入</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts" name="NoticeDialogRef">
import { nextTick, reactive, ref } from 'vue';
import { ElMessage, ElTable } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listTemplate,importModel } from '/@/api/iot/template';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const multipleTable = ref<InstanceType<typeof ElTable> | null>(null);
// 初始化 state
const state = reactive({
    tableData: {
        data: [] as any,
        total: 0,
        loading: false,
        title: '',
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            templateName: null,
            type: null,
        },
    },
});
const productInfo = ref({
    productId: '' as any,
    productName: '' as any,
    templateIds: '' as any,
})
const isShowDialog = ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref<number[]>([]); //groupId

interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<TypeOption[]>([]);
const things_type_list = ref<TypeOption[]>([]);
const data_type_list = ref<TypeOption[]>([]);
// 打开弹窗
const openDialog = (queryParams: any,) => {
    isShowDialog.value = true;
    state.tableData.title = '导入通用物模型'
    getTableData();
    getdictdata()
    productInfo.value.productId = queryParams.productId
    productInfo.value.productName = queryParams.productName
    // deviceGroup = row
    // // 获取分组下的设备
    // state.tableData.queryParams.userId = deviceGroup.userId;
    // state.tableData.queryParams.pageNum = 1;
    // getDeviceIds(deviceGroup.groupId).then(response => {
    //     ids.value = response.data.data;

    //     isShowDialog.value = true;
    // });
};
// 初始化表格数据
const getTableData = async () => {

    try {
        state.tableData.loading = true;
        const response = await listTemplate(state.tableData.queryParams);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
        // 设置分组关联的设备选中
        state.tableData.data.forEach((row: { deviceId: number; }) => {
            nextTick(() => {
                if (ids.value.includes(row.deviceId)) {
                    multipleTable.value?.toggleRowSelection(row, true)
                }
            })
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('iot_yes_no')
        things_type_list.value = await dictStore.fetchDict('iot_things_type')
        data_type_list.value = await dictStore.fetchDict('iot_data_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.queryParams.pageNum = 1;
    getTableData();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.queryParams.pageNum = 1;
    state.tableData.queryParams = {
        pageNum: 1,
        pageSize: 10,
        templateName: null,
        type: null,
    }
    getTableData();
}
// 关闭弹窗
const closeDialog = () => {
    isShowDialog.value = false;
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item) => item.templateId);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
}
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const handleDeviceSelected = () => {
    if (ids.value != null && ids.value.length > 0) {
        var importData = {
            productId: productInfo.value.productId,
            productName: productInfo.value.productName,
            templateIds: ids.value,
        };
        importModel(importData).then((response) => {
            ElMessage.success(response.data.msg);
            // this.$refs.productSelectTemplate.$refs.selectTemplateTable.clearSelection();
            emit('refresh');
            closeDialog();
        });
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.queryParams.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.queryParams.pageNum = val;
    getTableData();
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
<style scoped>
:deep(.el-overlay .el-overlay-dialog .el-dialog .el-dialog__body) {
    padding: 0 !important;
}
</style>