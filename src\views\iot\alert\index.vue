<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="告警名称" prop="alertName">
                        <el-input v-model="state.tableData.param.alertName" clearable size="default"
                            placeholder="请输入告警名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="告警级别" prop="alertLevel">
                        <el-select v-model="state.tableData.param.alertLevel" placeholder="请选择告警级别" clearable
                            size="default" style="width: 240px">
                            <el-option v-for="dict in alert_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:alert:add']" size="default" type="primary" class="ml5"
                            @click="handleAdd">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }"
                :row-style="{ cursor: 'pointer' }"
                @row-click="handleRowClick"
                highlight-current-row>
                <el-table-column label="告警名称" align="center" prop="alertName" />
                <el-table-column label="状态" align="center" prop="status">
                    <template #default="scope">
                        <el-tag type="success" size="default" v-if="scope.row.status == 1">启动</el-tag>
                        <el-tag type="danger" size="default" v-if="scope.row.status == 0">暂停</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="告警级别" align="center" prop="alertLevel">
                    <template #default="scope">
                        <dict-tag :options="alert_level_list" :value="scope.row.alertLevel" size="default" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注信息" align="center" prop="remark" />
                <el-table-column label="操作" align="center" class-name="default-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default" type="primary" text @click.stop="handleUpdate(scope.row)"
                                v-auths="['iot:alert:query']"><el-icon
                                    size="default"><ele-View /></el-icon>查看</el-button>
                            <el-button size="default" type="primary" text @click.stop="handleDelete(scope.row)"
                                v-auths="['iot:alert:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <!-- 添加或修改设备告警对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogData.tableData.dialog.title"
            v-model="dialogData.tableData.dialog.isShowDialog" width="900px" append-to-body
            :close-on-click-modal="false" :close-on-press-escape="false">
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <el-form ref="DialogFormRef" :model="dialogData.tableData.ruleForm" :rules="rules" label-width="90px">
                <el-row :gutter="50">
                    <el-col :span="12">
                        <el-form-item label="告警名称" prop="alertName">
                            <el-input text v-model="dialogData.tableData.ruleForm.alertName" placeholder="请输入告警名称" />
                        </el-form-item>
                        <el-form-item label="告警级别" prop="alertLevel">
                            <el-select text v-model="dialogData.tableData.ruleForm.alertLevel" placeholder="请选择告警级别"
                                style="width: 100%">
                                <el-option v-for="dict in alert_level_list" :key="dict.dictValue"
                                    :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注信息" prop="remark">
                            <el-input v-model="dialogData.tableData.ruleForm.remark" type="textarea" placeholder="请输入内容"
                                :rows="1" />
                        </el-form-item>
                        <el-form-item label="告警状态">
                            <el-switch v-model="dialogData.tableData.ruleForm.status" :active-value="1"
                                :inactive-value="0" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <el-tabs v-model="activeName" style="padding: 10px">
                <el-tab-pane label="关联场景" name="relateScene">
                    <!-- 加载状态 -->
                    <div v-if="dialogData.tableData.dialog.sceneLoading" class="loading-state">
                        <el-skeleton :rows="3" animated />
                    </div>
                    <!-- 有数据时显示表格 -->
                    <el-table v-else-if="dialogData.tableData.ruleForm.scenes && dialogData.tableData.ruleForm.scenes.length > 0"
                        :data="dialogData.tableData.ruleForm.scenes" border
                        size="default"
                        :row-style="{ cursor: 'pointer' }"
                        highlight-current-row>
                        <el-table-column prop="sceneName" align="center" label="场景名称"></el-table-column>
                        <el-table-column label="状态" align="center" prop="enable">
                            <template #default="scope">
                                <el-tag type="success" size="default" v-if="scope.row.enable == 1">启动</el-tag>
                                <el-tag type="danger" size="default" v-if="scope.row.enable == 2">暂停</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="触发条件" align="center" prop="cond">
                            <template #default="scope">
                                <span v-if="scope.row.cond == 1">任意条件</span>
                                <span v-if="scope.row.cond == 2">所有条件</span>
                                <span v-if="scope.row.cond == 3">不满足条件</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="执行方式" align="center" prop="executeMode">
                            <template #default="scope">
                                <span v-if="scope.row.executeMode == 1">串行</span>
                                <span v-if="scope.row.executeMode == 2">并行</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" width="120">
                            <template #default="scope">
                                <el-button size="default" text type="primary" @click="handleAlertSceneRemove(scope.row)"
                                    v-auths="['iot:alert:remove']"><el-icon>
                                        <Delete />
                                    </el-icon>移除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 无数据时显示空状态 -->
                    <div v-else class="empty-state">
                        <el-empty description="暂无关联场景数据">
                            <el-button type="primary" @click="addAlertScenes" v-auths="['iot:alert:add']">添加场景</el-button>
                        </el-empty>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="消息通知" name="notify">
                    <!-- 加载状态 -->
                    <div v-if="dialogData.tableData.dialog.notifyLoading" class="loading-state">
                        <el-skeleton :rows="3" animated />
                    </div>
                    <!-- 有数据时显示表格 -->
                    <el-table v-else-if="dialogData.tableData.ruleForm.notifyTemplateList && dialogData.tableData.ruleForm.notifyTemplateList.length > 0"
                        :data="dialogData.tableData.ruleForm.notifyTemplateList" border
                        size="default"
                        :row-style="{ cursor: 'pointer' }"
                        highlight-current-row>
                        <el-table-column prop="name" align="center" label="模板名称"></el-table-column>
                        <el-table-column label="状态" align="center" prop="status">
                            <template #default="scope">
                                <el-tag type="success" size="default" v-if="scope.row.status == '1'">启动</el-tag>
                                <el-tag type="danger" size="default" v-if="scope.row.status == '0'">暂停</el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="渠道类型" align="center" prop="channelType">
                            <template #default="scope">
                                <dict-tag :options="channel_type_list" :value="scope.row.channelType" />
                            </template>
                        </el-table-column>
                        <el-table-column label="渠道账号" align="center" prop="channelName"></el-table-column>
                        <el-table-column label="服务商" align="center" prop="provider"></el-table-column>
                        <el-table-column label="操作" align="center" width="120">
                            <template #default="scope">
                                <el-button size="default" type="primary" text
                                    @click="handleAlertNotifyTempRemove(scope.row)"><el-icon>
                                        <Delete />
                                    </el-icon>移除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 无数据时显示空状态 -->
                    <div v-else class="empty-state">
                        <el-empty description="暂无消息通知模板数据">
                            <el-button type="primary" @click="addAlertNotifyTemp" v-auths="['iot:alert:add']">添加模板</el-button>
                        </el-empty>
                    </div>
                </el-tab-pane>
                <!-- 用于设置间距 -->
                <el-tab-pane disabled>
                    <template #label>
                        <div style="margin-left: 400px;"></div>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="sceneButton" disabled v-if="activeName == 'relateScene'">
                    <template #label>
                        <el-button type="" plain size="small" @click="getScenesByAlertIds"
                            v-auths="['iot:alert:add']">刷新</el-button>
                        <el-button type="" plain size="small" @click="addAlertScenes"
                            v-auths="['iot:alert:add']">添加场景</el-button>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="notifyButton" disabled v-else>
                    <template #label>
                        <el-button type="" plain size="small" @click="getNotifyTempsByAlertId"
                            v-auths="['iot:alert:add']">刷新</el-button>
                        <el-button type="" plain size="small" @click="addAlertNotifyTemp"
                            v-auths="['iot:alert:add']">添加模板</el-button>
                    </template>
                </el-tab-pane>
            </el-tabs>

            <template #footer>
                <div class="dialog-footer">
                    <!-- <el-button type="primary" @click="handleSubmitForm">确 定</el-button> -->
                    <el-button @click="handleCancel">取 消</el-button>
                    <el-button type="primary" @click="handleSubmitForm(DialogFormRef)" v-auths="['iot:alert:edit']"
                        v-show="dialogData.tableData.ruleForm.alertId">修
                        改</el-button>
                    <el-button type="primary" @click="handleSubmitForm(DialogFormRef)" v-auths="['iot:alert:add']"
                        v-show="!dialogData.tableData.ruleForm.alertId">新
                        增</el-button>

                </div>
            </template>
        </el-dialog>
        <!-- <SceneDialog ref="SceneDialogRef" @refresh="getTableData()" /> -->
        <!-- 选择场景对话框 -->
        <scene-list ref="sceneListRef" @sceneEvent="getSceneData($event)" />
        <!-- 选择通知模板 -->
        <notify-temp-list ref="notifyTempListRef" @notifyEvent="getNotifyTempData($event)" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { parseTime } from '/@/utils/next'
import { addAlert, delAlert, getAlert, getScenesByAlertId, listAlert, listNotifyTemplate, updateAlert } from '/@/api/iot/alert';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const sceneList = defineAsyncComponent(() => import('/@/views/iot/alert/scene-list.vue'));
const notifyTempList = defineAsyncComponent(() => import('/@/views/iot/alert/notify-temp-list.vue'));
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
// 定义变量内容
const sceneListRef = ref();
const notifyTempListRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            alertId: '' as any,
            alertName: '' as any,
            alertLevel: '',

        },
    },
});
const dialogData = reactive({
    tableData: {
        ruleForm: {
            scenes: [] as any[],
            notifyTemplateList: [] as any[],
            status: 2,
            alertName: '',
            alertLevel: '',
            remark: '',
            alertId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
            sceneLoading: false,
            notifyLoading: false,
        },
    },
})
// 校验规则
const rules = reactive({
    alertName: [
        {
            required: true,
            message: '告警名称不能为空',
            trigger: 'blur',
        },
    ],
    alertLevel: [
        {
            required: true,
            message: '告警级别不能为空',
            trigger: 'change',
        },
    ],

})
// 定义变量内容
const DialogFormRef = ref();
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //alertId
const alert_level_list = ref<Option[]>([]);
const job_status_list = ref<Option[]>([]);
const channel_type_list = ref<Option[]>([]);
const activeName = ref('relateScene')
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listAlert(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        alertId: '' as any,
        alertName: '' as any,
        alertLevel: ''
    }
}
/**获取告警关联场景列表*/
const getScenesByAlertIds = () => {
    const alertId = dialogData.tableData.ruleForm.alertId;

    if (alertId) {
        dialogData.tableData.dialog.sceneLoading = true;
        getScenesByAlertId(alertId).then((response) => {
            // 确保数据结构正确
            if (response && response.data && response.data.rows) {
                dialogData.tableData.ruleForm.scenes = response.data.rows;
            } else {
                dialogData.tableData.ruleForm.scenes = [];
                console.warn('获取关联场景数据格式异常:', response);
            }
        }).catch((error) => {
            console.error('获取关联场景数据失败:', error);
            dialogData.tableData.ruleForm.scenes = [];
            ElMessage.error('获取关联场景数据失败');
        }).finally(() => {
            dialogData.tableData.dialog.sceneLoading = false;
        });
    } else {
        // 如果没有alertId，清空场景数据
        dialogData.tableData.ruleForm.scenes = [];
        dialogData.tableData.dialog.sceneLoading = false;
    }
}
/**获取告警通知模板列表*/
const getNotifyTempsByAlertId = () => {
    if (dialogData.tableData.ruleForm.alertId) {
        dialogData.tableData.dialog.notifyLoading = true;
        listNotifyTemplate(dialogData.tableData.ruleForm.alertId).then((response) => {
            // 确保数据结构正确
            if (response && response.rows) {
                dialogData.tableData.ruleForm.notifyTemplateList = response.rows;
            } else {
                dialogData.tableData.ruleForm.notifyTemplateList = [];
                console.warn('获取通知模板数据格式异常:', response);
            }
        }).catch((error) => {
            console.error('获取通知模板数据失败:', error);
            dialogData.tableData.ruleForm.notifyTemplateList = [];
            ElMessage.error('获取通知模板数据失败');
        }).finally(() => {
            dialogData.tableData.dialog.notifyLoading = false;
        });
    } else {
        // 如果没有alertId，清空通知模板数据
        dialogData.tableData.ruleForm.notifyTemplateList = [];
    }
}
/**添加场景*/
const addAlertScenes = () => {
    sceneListRef.value.openDialog()
    if (dialogData.tableData.ruleForm.scenes) {
        console.log(dialogData.tableData.ruleForm.scenes);

        let sceneList = JSON.parse(JSON.stringify(dialogData.tableData.ruleForm.scenes));
        sceneListRef.value.selectScenes = sceneList;
        sceneListRef.value.ids = sceneList.map((item: any) => item.sceneId);
    }
}
/**添加通知模板*/
const addAlertNotifyTemp = () => {
    notifyTempListRef.value.openDialog();
    if (dialogData.tableData.ruleForm.notifyTemplateList) {
        let list = JSON.parse(JSON.stringify(dialogData.tableData.ruleForm.notifyTemplateList));
        notifyTempListRef.value.selectNotifyTemps = list;
        notifyTempListRef.value.ids = list.map((item: any) => item.id);
    }
}
/**获取场景数据*/
const getSceneData = (data: any) => {
    dialogData.tableData.ruleForm.scenes = data;
}
/**获取通知模板数据*/
const getNotifyTempData = (data: any) => {
    dialogData.tableData.ruleForm.notifyTemplateList = data;
}
/** 移除告警场景项*/
const handleAlertSceneRemove = (row: any) => {
    for (let i = 0; i < dialogData.tableData.ruleForm.scenes.length; i++) {
        if (row.sceneId == dialogData.tableData.ruleForm.scenes[i].sceneId) {
            dialogData.tableData.ruleForm.scenes.splice(i, 1);
        }
    }
}
/** 移除告警通知项*/
const handleAlertNotifyTempRemove = (row: any) => {
    for (let i = 0; i < dialogData.tableData.ruleForm.notifyTemplateList.length; i++) {
        if (row.id == dialogData.tableData.ruleForm.notifyTemplateList[i].id) {
            dialogData.tableData.ruleForm.notifyTemplateList.splice(i, 1);
        }
    }
}
// 清空弹框数据
const rest = () => {
    dialogData.tableData = {
        ruleForm: {
            scenes: [] as any[],
            notifyTemplateList: [] as any[],
            status: 2,
            alertName: '',
            alertLevel: '',
            remark: '',
            alertId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
            sceneLoading: false,
            notifyLoading: false,
        },
    }
    activeName.value = 'relateScene'
}
// 表格行点击事件
const handleRowClick = (row: any) => {
    handleUpdate(row);
};

// 打开新增弹窗
const handleAdd = () => {
    rest()
    dialogData.tableData.dialog.isShowDialog = true
    dialogData.tableData.dialog.title = '添加告警配置'
};

// 打开修改弹窗
const handleUpdate = async (row: any | undefined) => {
    rest()
    const alertId = row.alertId || ids.value;

    try {
        // 先获取告警详情
        const response = await getAlert(alertId);
        dialogData.tableData.ruleForm = response.data.data;

        // 确保alertId被正确设置
        if (alertId) {
            dialogData.tableData.ruleForm.alertId = alertId;
        }

        // 显示对话框
        dialogData.tableData.dialog.isShowDialog = true;
        dialogData.tableData.dialog.title = '修改设备告警';

        // 异步加载关联数据，但不阻塞对话框显示
        if (alertId) {
            // 立即开始加载场景数据
            setTimeout(() => {
                getScenesByAlertIds();
                getNotifyTempsByAlertId();
            }, 100); // 给对话框一点时间渲染
        }
    } catch (error) {
        console.error('获取告警详情失败:', error);
        ElMessage.error('获取告警详情失败');
    }
};
// 删除分类
const handleDelete = (row: any) => {
    const alertIds = row.alertId || ids.value;
    ElMessageBox.confirm(`是否确认删除设备告警编号为${alertIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delAlert(alertIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 取消按钮
const handleCancel = () => {
    dialogData.tableData.dialog.isShowDialog = false;
    resetQuery();
}
// 提交
const handleSubmitForm = async (formEl: FormInstance | undefined) => {
    console.log(formEl);

    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogData.tableData.ruleForm.alertId != '') {
                updateAlert(dialogData.tableData.ruleForm).then(() => {
                    ElMessage.success('修改成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            } else {
                addAlert(dialogData.tableData.ruleForm).then(() => {
                    ElMessage.success('新增成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        alert_level_list.value = await dictStore.fetchDict('iot_alert_level')
        job_status_list.value = await dictStore.fetchDict('sys_job_status')
        channel_type_list.value = await dictStore.fetchDict('notify_channel_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>

<style scoped>
/* 空状态容器样式 */
.empty-state {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    margin: 10px 0;
}

/* 加载状态容器样式 */
.loading-state {
    padding: 20px;
    background-color: #fafafa;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    margin: 10px 0;
}

/* 空状态样式优化 */
:deep(.el-empty) {
    padding: 40px 0;
}

:deep(.el-empty__description) {
    margin: 20px 0;
    color: #909399;
    font-size: 14px;
}

:deep(.el-empty__image) {
    width: 80px;
    height: 80px;
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
    background-color: #f5f7fa !important;
}

/* 表格行点击效果 */
:deep(.el-table__row.current-row) {
    background-color: #ecf5ff !important;
}

/* 标签页内容区域样式 */
:deep(.el-tabs__content) {
    padding: 0;
}

/* 表格边框优化 */
:deep(.el-table--border) {
    border: 1px solid #ebeef5;
}

:deep(.el-table--border .el-table__cell) {
    border-right: 1px solid #ebeef5;
}

/* 骨架屏样式优化 */
:deep(.el-skeleton) {
    padding: 0;
}

:deep(.el-skeleton__item) {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0 50%;
    }
}
</style>
