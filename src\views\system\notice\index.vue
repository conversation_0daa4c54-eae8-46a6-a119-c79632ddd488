<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="公告标题" prop="noticeTitle">
                        <el-input v-model="state.tableData.param.noticeTitle" clearable aria-label="First Name"
                            placeholder="请输入公告标题" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="操作人员" prop="createBy">
                        <el-input v-model="state.tableData.param.createBy" clearable placeholder="请输入操作人员"
                            style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="类型" prop="noticeType">
                        <el-select v-model="state.tableData.param.noticeType" placeholder="公告类型" clearable
                            style="width: 240px">
                            <el-option v-for="notice in noticeTypelist" :key="notice.dictValue"
                                :label="notice.dictLabel" :value="notice.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['system:notice:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['system:notice:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['system:notice:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="序号" align="center" prop="noticeId" width="100" />
                <el-table-column label="公告标题" align="center" prop="noticeTitle" :show-overflow-tooltip="true" />
                <el-table-column label="公告类型" align="center">
                    <template #default="scope">
                        <!-- <el-tag :type="scope.row.noticeType == '2' ? 'success' : 'warning'">{{ scope.row.noticeType == '2'
                            ? '公告' :
                            '通知'
                            }}</el-tag> -->
                            <DictTag :options="noticeTypelist" :value="scope.row.noticeType"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center">
                    <template #default="scope">
                        <!-- <el-tag :type="scope.row.status == '0' ? 'success' : 'danger'">{{ scope.row.status == '0'
                            ? '正常' :
                            '关闭'
                            }}</el-tag> -->
                            <DictTag :options="statuslist" :value="scope.row.status"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="创建者" align="center" prop="createBy" width="100" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" icon="el-icon-edit"
                            @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['system:notice:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
                        <el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
                            v-auths="['system:notice:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <NoticeDialog ref="NoticeDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { delNotice, listNotice } from '/@/api/system/notice';
import { parseTime } from '/@/utils/next'
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const NoticeDialog = defineAsyncComponent(() => import('/@/views/system/notice/dialog.vue'));

// 定义变量内容
const NoticeDialogRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            noticeTitle: undefined, //公告标题
            createBy: undefined,//操作人员
            noticeType: '' //类型
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
interface noticeTypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const noticeTypelist = ref<noticeTypeOption[]>([]); //状态列表
const statuslist = ref<noticeTypeOption[]>([]); //状态列表
    
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //noticeId
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listNotice(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        noticeType: ''
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        noticeTypelist.value = await dictStore.fetchDict('sys_notice_type')
        statuslist.value = await dictStore.fetchDict('sys_notice_status')
        // 处理公告数据
    } catch (error) {
        console.error('获取公告数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { noticeId: string; }) => item.noticeId);
    console.log(ids.value);

    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 打开新增公告弹窗
const onOpenAddDic = (type: string) => {
    NoticeDialogRef.value.openDialog(type);
};
// 打开修改公告弹窗
const onOpenEditDic = (type: string, row: RowNoticeType | undefined) => {
    var noticeId = ''
    if (!row) {
        noticeId = ids.value
    } else {
        noticeId = row.noticeId
    }
    NoticeDialogRef.value.openDialog(type, row, noticeId);
};
// 删除公告
const onRowDel = (row: RowNoticeType) => {
    const noticeIds = row.noticeId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除公告序号：“${noticeIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delNotice(noticeIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
