<template>
    <div class="system-user-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="用户名称" prop="userName">
                        <el-input v-model="state.tableData.param.userName" clearable size="default"
                            placeholder="请输入用户名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="手机号码" prop="phonenumber">
                        <el-input v-model="state.tableData.param.phonenumber" clearable size="default"
                            placeholder="请输入手机号码" style="width: 240px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['system:role:add']" size="default" type="primary" class="ml5"
                            @click="openSelectUser">
                            <el-icon><ele-Plus /></el-icon>
                            添加用户
                        </el-button>
                        <el-button v-auths="['system:role:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="cancelAuthUserAll">
                            <el-icon><ele-CircleCloseFilled /></el-icon>
                            批量取消授权
                        </el-button>
                        <el-button size="default" type="warning" class="ml10"
                            @click="close">
                            <el-icon><ele-CloseBold /></el-icon>
                            关闭
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <div class="app-container">
                <el-table :data="state.tableData.data" v-loading="state.tableData.loading" ref="tableRef"
                    @selection-change="handleSelectionChange" border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column label="用户名称" align="center" prop="userName" />
                    <el-table-column label="用户昵称" prop="nickName" :show-overflow-tooltip="true" />
                    <el-table-column label="邮箱" prop="email" :show-overflow-tooltip="true" />
                    <el-table-column label="手机" prop="phonenumber" :show-overflow-tooltip="true" />
                    <el-table-column label="状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="statuslist" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-button size="default" text type="primary" icon="el-icon-delete"
                                @click="cancelAuthUser(scope.row)"
                                v-auths="['system:role:remove']"><el-icon><ele-CircleCloseFilled /></el-icon>取消授权</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                    style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>
            </div>
        </el-card>
        <SelectUserDialog ref="SelectUserDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemUser">

import { reactive, onMounted, ref, defineAsyncComponent } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { useRoute } from 'vue-router';
import router from '/@/router';
import { allocatedUserList, authUserCancel, authUserCancelAll } from '/@/api/system/role';
const dictStore = useDictStore();  // 使用 Pinia store
// 引入组件
const SelectUserDialog = defineAsyncComponent(() => import('/@/views/system/role/SelectUser.vue'));
// 定义变量内容
const SelectUserDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            roleId: '' as any,
            userName: undefined,
            phonenumber: undefined
        },
    },
});
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<statusOption[]>([]);
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

const ids = ref() //roleId
const tableRef = ref();  // 引用表格组件实例
// 初始化表格数据
const getTableData = async () => {
    try {

        state.tableData.loading = true;
        const response = await allocatedUserList(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
// 打开新增新闻弹窗
const openSelectUser = () => {
    SelectUserDialogRef.value.openDialog(state.tableData.param.roleId);
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param.pageNum = 1
    state.tableData.param.pageSize = 10
    state.tableData.param.userName = undefined
    state.tableData.param.phonenumber = undefined

}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_normal_disable')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { userId: string; }) => item.userId);
    console.log(selection);
    
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 取消授权按钮操作 */
const cancelAuthUser = (row: { userName: string; userId: any; }) => {
    const roleId = state.tableData.param.roleId;  
    ElMessageBox.confirm('确认要取消该用户"' + row.userName + '"角色吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            authUserCancel({ roleId: roleId, userId: row.userId }).then(() => {
                getTableData();
                ElMessage.success('取消授权成功');
            })
        })
        .catch(() => { });
}
/** 批量取消授权按钮操作 */
const cancelAuthUserAll = () => {
    const roleId = state.tableData.param.roleId;
    const userIds = ids.value.join(",");
    console.log(roleId,userIds);
    
    ElMessageBox.confirm('是否取消选中用户授权数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            authUserCancelAll({ roleId: roleId, userIds: userIds }).then(() => {
                getTableData();
                ElMessage.success('取消授权成功');
            })
        })
        .catch(() => { });
}
/** 关闭按钮 */
const close = () => {
    const obj = { path: "/system/role" };
    router.push(obj);
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
}
// 页面加载时
onMounted(() => {
    const route = useRoute();
    const roleId = route.params.roleId;
    state.tableData.param.roleId = roleId
    if (roleId) {
        getTableData()
    }

    getdictdata()
});
</script>