// apis/mediaServer.js (Vue 3 兼容写法)
import request from '/@/utils/request'

// 查询流媒体服务器配置列表
export function listmediaServer(query: any) {
  return request({
    url: '/sip/mediaserver/list',
    method: 'get',
    params: query
  })
}

// 查询流媒体服务器配置详细
export function getmediaServer() {
  return request({
    url: '/sip/mediaserver/',
    method: 'get'
  })
}

// 新增流媒体服务器配置
export function addmediaServer(data: any) {
  return request({
    url: '/sip/mediaserver',
    method: 'post',
    data: data
  })
}

// 修改流媒体服务器配置
export function updatemediaServer(data: any) {
  return request({
    url: '/sip/mediaserver',
    method: 'put',
    data: data
  })
}

// 删除流媒体服务器配置
export function delmediaServer(id: number) {
  return request({
    url: '/sip/mediaserver/' + id,
    method: 'delete'
  })
}

// 检查流媒体服务状态
export function checkmediaServer(query: any) {
  return request({
    url: '/sip/mediaserver/check',
    method: 'get',
    params: query
  })
}