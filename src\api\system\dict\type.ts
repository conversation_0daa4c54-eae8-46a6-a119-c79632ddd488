import request from '/@/utils/request';

// 定义字典类型的数据接口
interface DictType {
  dictId: string;
  dictName: string;
  dictType: string;
  status: string;
  [key: string]: any; // 可以根据需要扩展其他字段
}

// 定义查询字典类型列表时的参数类型
interface QueryParams {
  dictName?: string;
  dictType?: string;
  [key: string]: any;
}

// 查询字典类型列表
export function listType(query: QueryParams) {
  return request({
    url: '/system/dict/type/list',
    method: 'get',
    params: query
  });
}

// 查询字典类型详细
export function getType(dictId: any) {
  return request({
    url: '/system/dict/type/' + dictId,
    method: 'get'
  });
}

// 新增字典类型
export function addType(data: DictType) {
  return request({
    url: '/system/dict/type',
    method: 'post',
    data: data
  });
}

// 修改字典类型
export function updateType(data: DictType) {
  return request({
    url: '/system/dict/type',
    method: 'put',
    data: data
  });
}

// 删除字典类型
export function delType(dictId: string) {
  return request({
    url: '/system/dict/type/' + dictId,
    method: 'delete'
  });
}

// 刷新字典缓存
export function refreshCache() {
  return request({
    url: '/system/dict/type/refreshCache',
    method: 'delete'
  });
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url: '/system/dict/type/optionselect',
    method: 'get'
  });
}
