<template>
	<div class="split-container">
		<el-card id="devicePosition" style="width: 100vw; height: 91vh" :body-style="{ padding: '0px' }">
			<el-container v-loading="loading" style="height: 91vh" :element-loading-text="$t('sip.splitview.998531-0')">
				<el-aside width="250px" style="background-color: #ffffff; border: 1px solid #f0f0f0; margin-right: 10px; height: 100%">
					<!-- 假设 DeviceTree 是一个设备树组件 -->
					<DeviceTree :click-event="clickEvent" />
				</el-aside>
				<el-main style="padding: 0">
					<div height="5vh" style="text-align: left; font-size: 17px; line-height: 5vh; margin-bottom: 10px">
						{{ "分屏：" }}
						<el-button type="success" style="margin-left: 10px" :class="{ active: spilt === 1 }" @click="spilt = 1" plain size="mini">
							<el-icon><FullScreen /></el-icon> 单屏
						</el-button>

						<el-button type="info" style="margin-left: 10px" :class="{ active: spilt === 4 }" @click="spilt = 4" size="mini" plain>
							<el-icon><Menu /></el-icon> 四屏
						</el-button>

						<el-button type="warning" style="margin-left: 10px" :class="{ active: spilt === 9 }" @click="spilt = 9" plain size="mini">
							<el-icon><Grid /></el-icon> 九屏
						</el-button>
					</div>
					<div style="height: 85vh; display: flex; flex-wrap: wrap">
						<div
							v-for="i in spilt"
							:key="i"
							class="play-box"
							:style="liveStyle"
							:class="{ redborder: playerIdx === i - 1 }"
							@click="playerIdx = i - 1"
						>
							<div v-if="!videoUrl[i - 1]" style="color: #ffffff; font-size: 30px; font-weight: bold">{{ i }}</div>
							<player v-else :video-url="videoUrl[i - 1]" fluent autoplay @screenshot="shot" @destroy="(idx) => destroy(idx)" class="player-wrap" />
						</div>
					</div>
				</el-main>
			</el-container>
		</el-card>
	</div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
// import player from '/@/views/components/player/jessibuca.vue'
 import DeviceTree from '/@/views/components/player/DeviceTree.vue'

// ====== State ======
const route = useRoute();

const videoUrl = ref(['']);
const spilt = ref(1);
const playerIdx = ref(0);
const loading = ref(false);

// ====== Computed ======
const liveStyle = computed(() => {
	let style = { width: '81%', height: '99%' };
	switch (spilt.value) {
		case 4:
			style = { width: '40%', height: '49%' };
			break;
		case 9:
			style = { width: '27%', height: '32%' };
			break;
	}
	return style;
});

// ====== Watchers ======
watch(
	() => spilt.value,
	(newValue) => {
		console.log('切换画幅：' + newValue);
		window.localStorage.setItem('split', newValue);
	}
);

watch(
	() => route.fullPath,
	() => checkPlayByParam()
);

// ====== Methods ======

function clickEvent(data) {
	if (data.channelSipId) {
		sendDevicePush(data);
	}
}

function setPlayUrl(url, idx) {
	videoUrl.value[idx] = url;
	localStorage.setItem('videoUrl', JSON.stringify(videoUrl.value));
}

function shot(e) {
	const base64ToBlob = (code) => {
		const parts = code.split(';base64,');
		const contentType = parts[0].split(':')[1];
		const raw = atob(parts[1]);
		const uInt8Array = new Uint8Array(raw.length);
		for (let i = 0; i < raw.length; ++i) {
			uInt8Array[i] = raw.charCodeAt(i);
		}
		return new Blob([uInt8Array], { type: contentType });
	};

	const aLink = document.createElement('a');
	const blob = base64ToBlob(e);
	aLink.download = '截图.png';
	aLink.href = URL.createObjectURL(blob);
	aLink.click();
}

function save(item) {
	const dataStr = localStorage.getItem('playData') || '[]';
	const data = JSON.parse(dataStr);
	data[playerIdx.value] = item;
	localStorage.setItem('playData', JSON.stringify(data));
}

function clear(idx) {
	const dataStr = localStorage.getItem('playData') || '[]';
	const data = JSON.parse(dataStr);
	data[idx - 1] = null;
	localStorage.setItem('playData', JSON.stringify(data));
}

function destroy(idx) {
	clear(idx);
	videoUrl.value[idx] = '';
}

// 模拟发送推流请求（假数据）
function sendDevicePush(itemData) {
	loading.value = true;
	// 模拟异步请求
	setTimeout(() => {
		const fakePlayUrl = 'https://example.com/stream/' + Math.random();
		setPlayUrl(fakePlayUrl, playerIdx.value);
		save(itemData);
		loading.value = false;
	}, 1000);
}

function checkPlayByParam() {
	const { deviceId, channelId } = route.query;
	if (deviceId && channelId) {
		sendDevicePush({ deviceId, channelId });
	}
}

// 初始化时检查路由参数
onMounted(() => {
	checkPlayByParam();
});
</script>

<style lang="scss" scoped>
.split-container {
	padding: 20px;
}

.btn {
	margin: 0 10px;
}

.btn:hover {
	color: #409eff;
}

.btn.active {
	color: #409eff;
}

.redborder {
	border: 2px solid red !important;
}

.play-box {
	background-color: #55565f;
	border: 1px solid #505050;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 10px;
	position: relative;
	border-radius: 5px;
}

.player-wrap {
	position: absolute;
	top: 0px;
	height: 100% !important;
}
</style>