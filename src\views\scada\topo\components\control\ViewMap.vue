<template>
    <div>
        <BaiduMap
            :center="center"
            @ready="ready"
            @marker-click="clickHandler"
            :zoom="zoom"
            :scroll-wheel-zoom="true"
            :markers="mapMarkers"
            :map-style="{
                width: detail.style.position.w + 'px',
                height: detail.style.position.h + 'px',
            }"
        />
        <div v-show="false">{{ mapModel }}</div>
    </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted, onBeforeUnmount, watch, reactive} from 'vue';
import onlineImg from '/@/assets/images/marker-online.png';
import offlineImg from '/@/assets/images/marker-offline.png';
import inactiveImg from '/@/assets/images/marker-inactive.png';
import forbiddenImg from '/@/assets/images/marker-forbidden.png';
import { listDeviceShort } from '/@/api/iot/device';

// Props
interface Props {
  detail?: any;
}
const props = defineProps<Props>();

// 使用 base.json 中地图的默认配置
const detail = ref(props.detail || {
  style: {
    position: {
      x: 0,
      y: 0,
      w: 1200,
      h: 600
    },
    zIndex: 1,
    mapModel: "normal",
    transform: 0,
    transformType: "rotate(0deg)"
  }
});

// Refs
const center = ref({ lng: 116.40605, lat: 39.915879 });
const zoom = ref(10);
const points = ref<any[]>([]); // 地图点 集合
const currIndex = ref(-1);
const timer = ref<any>(null);
const map = ref<any>(null);
const changeModel = ref('');

// 计算地图标记点
const mapMarkers = computed(() => {
  return points.value.map(item => ({
    ...item,
    longitude: item.longitude,
    latitude: item.latitude,
    icon: {
      url: judge(item.status),
      size: { width: 40, height: 40 }
    },
    infoWindow: {
      content: `
        <div style="padding: 10px;">
          <div style="margin-bottom: 8px;">
            <span style="font-weight: bold;">设备编号：</span>
            <span>${item.serialNumber}</span>
          </div>
          <div style="margin-bottom: 8px;">
            <span style="font-weight: bold;">设备名称：</span>
            <span>${item.deviceName}</span>
          </div>
          <div>
            <span style="font-weight: bold;">设备状态：</span>
            <span style="color: ${getStatusColor(item.status)};">${getStatusText(item.status)}</span>
          </div>
        </div>
      `,
      width: 230,
      height: 180
    }
  }))
});

// Computed
const mapModel = computed(() => {
  changeModel.value = detail.value.style.mapModel;
  return detail.value.style.mapModel;
});

// Watch
watch(changeModel, () => {
  if (map.value) {
    map.value.setMapStyle({ style: detail.value.style.mapModel });
  }
});

// Methods
const ready = ({ map: baiduMap }: any) => {
  map.value = baiduMap;
  if (detail.value.style.mapModel != 'normal') {
    map.value.setMapStyle({ style: detail.value.style.mapModel });
  }
};
const state = reactive({
  tableData: {
    data: [],
    total: 0,
    loading: false,
    param: {
      pageNum: 1,
      pageSize: 10,
      deviceName: undefined,
      serialNumber: undefined,
      status: '',
      userId: undefined,
      productId: null,
      groupId: null,

    },
  },
});
const getList = async () => {
  try {
    const ress = await listDeviceShort(state.tableData.param);
    const res = ress.data
    if (res.code == 200) {
      setZoom(res.rows);
      points.value = res.rows;
    }
  } catch (error) {
    console.error('获取设备列表失败:', error);
  }
};
//设备图标
const judge = (status: number) => {
  if (status == 3) {
    return onlineImg;
  } else if (status == 4) {
    return offlineImg;
  } else if (status == 1) {
    return inactiveImg;
  } else {
    return forbiddenImg;
  }
};

// 获取状态颜色
const getStatusColor = (status: number) => {
  switch (status) {
    case 1: return '#faad14'; // 未激活 - 橙色
    case 2: return '#ff4d4f'; // 禁用 - 红色
    case 3: return '#52c41a'; // 在线 - 绿色
    case 4: return '#d9d9d9'; // 离线 - 灰色
    default: return '#d9d9d9';
  }
};

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 1: return '未激活';
    case 2: return '禁用';
    case 3: return '在线';
    case 4: return '离线';
    default: return '未知';
  }
};

// 设置中心点和zoom的值
const setZoom = (sdata: any[]) => {
  if (sdata.length > 0) {
    let maxLng = sdata[0].longitude;
    let minLng = sdata[0].longitude;
    let maxLat = sdata[0].latitude;
    let minLat = sdata[0].latitude;

    for (let i = 0; i < sdata.length; i++) {
      if (sdata[i].longitude > maxLng) {
        maxLng = sdata[i].longitude;
      }
      if (sdata[i].longitude < minLng) {
        minLng = sdata[i].longitude;
      }
      if (sdata[i].latitude > maxLat) {
        maxLat = sdata[i].latitude;
      }
      if (sdata[i].latitude < minLat) {
        minLat = sdata[i].latitude;
      }
    }
    let cenLng = (parseFloat(maxLng) + parseFloat(minLng)) / 2;
    let cenLat = (parseFloat(maxLat) + parseFloat(minLat)) / 2;
    center.value.lng = cenLng;
    center.value.lat = cenLat;
    let zoomLevels = ['50', '100', '200', '500', '1000', '2000', '5000', '10000', '20000', '25000', '50000', '100000', '200000', '500000', '1000000', '2000000'];
    let averLng = rad(maxLng) - rad(minLng);
    let averLat = rad(maxLat) - rad(minLat);
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(averLat / 2), 2) + Math.cos(rad(maxLat)) * Math.cos(rad(minLat)) * Math.pow(Math.sin(averLng / 2), 2)));
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;

    for (let i = 0; i < zoomLevels.length; i++) {
      if (parseFloat(zoomLevels[i]) - s > 0) {
        zoom.value = 8 - i + 3;
        break;
      }
    }
  } else {
    // 没有坐标，显示全中国
    center.value.lng = 103.388611;
    center.value.lat = 35.563611;
    zoom.value = 5;
  }
};
const rad = (d: number) => {
  return (d * Math.PI) / 180.0;
};

// 控制info弹窗显示/隐藏
const clickHandler = (markerData: any) => {
  currIndex.value = markerData.deviceId;
};



// Lifecycle
onMounted(() => {
  getList();
  // 移除定时器，只获取一次数据
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>

<style lang="scss">
.proSize {
    font-size: 13px;
    margin: 10px 0;
}
</style>
