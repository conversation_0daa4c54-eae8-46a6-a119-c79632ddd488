<template>
  <!-- 组态预览容器 - 只在当前组件内显示 -->
  <div class="topo-preview-container">
    <div id="render-app" class="fullscreen-preview" @mousewheel="mouseWheel">
    <div
        v-if="configData.layer"
        id="render-box"
        class="topo-render"
        ref="imageTofile"
        :style="layerStyle"
        v-loading="loadingContro"
        :element-loading-text="loadingText"
        @contextmenu.prevent="onContextmenu"
        @mousemove="mouseMove"
        @mousedown="mouseDown"
        @mouseup="mouseUp"
        @mouseenter="mouseEnter"
        @mouseleave="mouseLeave"
    >
      <template v-for="(component, index) in configData.components" :key="index">
        <div
            class="topo-render-wrapper"
            @click="throttle(component)"
            @dblclick="doDbClickComponent(component)"
            :class="{ 'topo-render-wrapper-clickable': component.action?.length > 0 }"
            v-show="component.style.visible == undefined ? true : component.style.visible"
            :style="{
            left: component.style.position.x + 'px',
            top: component.style.position.y + 'px',
            width: component.style.position.w + 'px',
            height: component.style.position.h + 'px',
            backgroundColor: component.type == 'flow-bar' || component.type == 'flow-bar-dynamic' ? 'transparent' : component.style.backColor,
            zIndex: component.style.zIndex,
            transform: component.style.transformType,
            opacity: component.style.opacity,
            'border-radius': component.style.borderRadius + 'px',
            'box-shadow': '0 0 ' + component.style.boxShadowWidth + 'px 0 ' + component.style.boxShadowColor,
          }"
        >
          <!-- 使用统一的动态组件渲染 -->
          <component v-bind:is="parseView(component)" :detail="component" ref="spirit" />
        </div>
      </template>
    </div>
    <!-- 组态预览小窗口 -->
    <el-dialog :title="windowPage" v-model="childPage" :close-on-click-modal="false" :width="windowWidth + 'px'">
      <iframe :style="{ width: windowWidth - 40 + 'px', height: windowHeight + 'px', border: '#91939a 1px solid' }" :src="childUrl"></iframe>
    </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElNotification } from 'element-plus';
import html2canvas from 'html2canvas';
import { getByGuid } from '/@/api/scada/topo';
import topoUtil from '/@/utils/topo/topo-util';
// 导入所有组态组件 - 基于 topoBase.vue 的完整导入
// 基础组件
import ViewText from './control/ViewText.vue';
import View3DModel from './control/View3DModel.vue';
import ViewImage from './control/ViewImage.vue';
import ViewImageSwitch from './control/ViewImageSwitch.vue';
// Canvas 形状组件
import ViewCircular from './control/canvas/ViewCircular.vue';
import ViewLine from './control/canvas/ViewLine.vue';
import ViewLineArrow from './control/canvas/ViewLineArrow.vue';
import ViewBizierCurveArrow from './control/canvas/ViewBizierCurveArrow.vue';
import ViewRect from './control/canvas/ViewRect.vue';
import ViewTriangle from './control/canvas/ViewTriangle.vue';
// 图表组件
import ViewChart from './control/chart/ViewChart.vue';
import ViewChartPie from './control/chart/ViewChartPie.vue';
import ViewChartGauge from './control/chart/ViewChartGauge.vue';
import ViewChartWater from './control/chart/ViewChartWater.vue';
import ViewChartTemp from './control/chart/ViewChartTemp.vue';
import ViewChartMap from './control/chart/ViewChartMap.vue';
import ViewChartWrapper from './control/chart/ViewChartWrapper.vue';
// 媒体组件
import ViewVideo from './control/ViewVideo.vue';
import ViewVideoPlay from './control/ViewVideoPlay.vue';
import ViewVideoMp4 from './control/ViewVideoMp4.vue';
// 3D 组件
import ViewVR from './control/ViewVR.vue';
// 数据组件
import ViewFlowBarDynamic from './control/ViewFlowBarDynamic.vue';
// 功能组件
import ViewMap from './control/ViewMap.vue';
import ViewPanel from './control/ViewPanel.vue';
import ViewTimer from './control/ViewTimer.vue';
import ViewWeather from './control/ViewWeather.vue';
import ViewWarn from './control/ViewWarn.vue';
import ViewOrder from './control/ViewOrder.vue';
import ViewComponent from './control/ViewComponent.vue';
// 完整的组件映射表 - 基于 topoBase.vue 的完整映射
const componentMap: any = {
  // 基础组件
  'text': ViewText,
  // 'text-static': ViewTextStatic,
  'image': ViewImage,
  'imageSwitch': ViewImageSwitch,
  'image-switch': ViewImageSwitch,
  // switch: ViewSwitch,
  // 'knob-switch': ViewKnobSwitch,
  '3d-model': View3DModel,
  '3D-model': View3DModel,

  // Canvas 形状组件
  'circular': ViewCircular,
  'rect': ViewRect,
  'triangle': ViewTriangle,
  'line': ViewLine,
  'line-arrow': ViewLineArrow,
  // 'line-wave': ViewLineWave,
  'bizier-curve-arrow': ViewBizierCurveArrow,
  // dashed: ViewDashed,

  // 图表组件
  'chart-line': ViewChart,
  'chart-line-step': ViewChart,
  'chart-bar': ViewChart,
  'chart-pie': ViewChartPie,
  'chart-gauge': ViewChartGauge,
  'chart-water': ViewChartWater,
  'chart-temp': ViewChartTemp,
  'chart-map': ViewChartMap,
  'chart-wrapper': ViewChartWrapper,

  // SVG 组件
  // 'svg-image': ViewSvgImage,
  // 'svg-static': ViewSvgStatic,

  // 媒体组件
  'video': ViewVideo,
  'video-play': ViewVideoPlay,
  'video-mp4': ViewVideoMp4,

  // 3D 组件
  // threejs: ViewThreeJs,
  // 'relief-ball': ViewReliefBall,
  'VR': ViewVR,
  'vr': ViewVR,

  // 数据组件
  // 'real-data': ViewRealData,
  // history: ViewHistory,
  // 'flow-bar': ViewFlowBar,
  'flow-bar-dynamic': ViewFlowBarDynamic,

  // 功能组件
  'map': ViewMap,
  'panel': ViewPanel,
  'timer': ViewTimer,
  'weather': ViewWeather,
  'warn': ViewWarn,
  'order': ViewOrder,
  // 'luck-draw': ViewLuckDraw,
  'component': ViewComponent,
};

// Props
interface Props {
  defaultValue?: number;
  isShare?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  defaultValue: 100,
  isShare: false
});

// Vue 实例和路由
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance() as any;

// 使用 Pinia store
import { useCounterStore } from '/@/stores/counterStore';
const counterStore = useCounterStore();

// 响应式数据
const scadaGuid = ref('');
const configData = ref<any>({
  layer: {
    backColor: '',
    backgroundImage: '',
    width: 1920,
    height: 1080,
    dragZoom: false
  },
  components: []
});
const bindDeviceList = ref<any[]>([]);
const selectedValue = ref(props.defaultValue);
const loadingContro = ref(false);
const loadingText = ref('指令下发中...');
const childPage = ref(false);
const childUrl = ref('');
const windowWidth = ref(960);
const windowHeight = ref(600);
const windowPage = ref('子界面');
const newZoom = ref(0.2);
const displacement = reactive({
  scale: 1,
  pageX: 0,
  pageY: 0,
  pageX2: 0,
  pageY2: 0,
  moveable: false,
  originScale: 1
});
const throttleTimer = ref<any>(null);
const deviceTimer = ref<any>(null);
const ztTimer = ref<any>(null);
const mouseOperate = ref('default');
const x = ref(0);
const y = ref(0);
const l = ref(0);
const t = ref(0);

// 计算属性
const layerStyle = computed(() => {
  const styles = [];
  styles.push(`transform:scale(${selectedValue.value / 100})`);
  if (configData.value.layer?.backColor) {
    styles.push(`background-color: ${configData.value.layer.backColor}`);
  }
  if (configData.value.layer?.backgroundImage) {
    styles.push(`background-image: url("${configData.value.layer.backgroundImage}")`);
  }
  if (configData.value.layer?.width > 0) {
    styles.push(`width: ${configData.value.layer.width}px`);
  }
  if (configData.value.layer?.height > 0) {
    styles.push(`height: ${configData.value.layer.height}px`);
  }
  styles.push('overflow:hidden');
  return styles.join(';');
});

// 生命周期
onMounted(() => {
  ElNotification({
    title: '温馨提示',
    message: '键盘按下F11键或右键开启全屏，按下Esc退出全屏。',
    position: 'top-left',
    duration: 4500,
  });
  scadaGuid.value = route.query.guid as string;
  getZtData();
  connectMqtt();
});

onBeforeUnmount(() => {
  mqttUnSubscribe(bindDeviceList.value);
  if (ztTimer.value) {
    clearInterval(ztTimer.value);
    ztTimer.value = null;
  }
  if (deviceTimer.value) {
    clearInterval(deviceTimer.value);
    deviceTimer.value = null;
  }
});

// 方法

// 连接mqtt消息服务器
const connectMqtt = async () => {
  if (mqttTool?.client == null) {
    await mqttTool?.connect();
  }
  mqttCallback();
};
import mqttTool from '/@/utils/mqttTool';
// mqtt回调处理
const mqttCallback = () => {
  if (mqttTool.client) {
    mqttTool.client.on('message', (topic: string, message: any) => {
      let topics = topic.split('/');
      let productId = topics[1];
      let deviceNum = topics[2];
      try {
        message = JSON.parse(message.toString());
      } catch (error) {
        console.error('❌ 解析 MQTT 消息失败:', error);
        return;
      }

      if (!message) {
        console.warn('⚠️ MQTT 消息为空');
        return;
      }
      if (topics[3] == 'status') {
        console.log('📊 接收到【设备状态】主题：', topic);
        console.log('📊 接收到【设备状态】内容：', message);
      }
      if (topics[4] == 'reply') {
        proxy.$modal?.notifySuccess(message);
      }
      if (topic.endsWith('ws/service')) {
        console.log('🎯 接收到【物模型】主题：', topic);
        console.log('🎯 接收到【物模型】内容：', message);
        const data = {
          productId: productId,
          serialNumber: deviceNum,
          message: message,
        };
        counterStore.setMqttData(data);
        console.log('📊 MQTT数据已更新到store:', data);
      }
      if (topic.endsWith('monitor/post')) {
        console.log('🎯 接收到【物模型】主题：', topic);
        console.log('🎯 接收到【物模型】内容：', message);
        const data = {
          productId: productId,
          serialNumber: deviceNum,
          message: message,
        };
        counterStore.setMqttData(data);
        console.log('📊 MQTT数据已更新到store:', data);
      }
    });

    // 添加连接状态监听
    mqttTool.client.on('connect', () => {
      console.log('✅ MQTT 客户端连接成功');
    });

    mqttTool.client.on('disconnect', () => {
      console.log('❌ MQTT 客户端断开连接');
    });

    mqttTool.client.on('error', (error: any) => {
      console.error('❌ MQTT 连接错误:', error);
    });
  } else {
    console.error('❌ MQTT 客户端不存在');
  }
};

// mqtt订阅主题
const mqttSubscribe = (list: any[]) => {
  const topics = getSubscribeTopic(list);
  mqttTool.subscribe(topics);
};

// mqtt取消订阅主题
const mqttUnSubscribe = (list: any[]) => {
  const topics = getSubscribeTopic(list);
 mqttTool.unsubscribe(topics);
};

// 获取订阅主题
const getSubscribeTopic = (list: any[]) => {
  let topics: string[] = [];
  console.log('📡 准备生成订阅主题，设备列表:', list);

  if (list && list.length !== 0) {
    list.forEach((item) => {
      console.log(`📡 为设备 ${item.serialNumber} 生成订阅主题`);
      const topicStatus = '/' + item.productId + '/' + item.serialNumber + '/status/post';
      const topicService = '/' + item.productId + '/' + item.serialNumber + '/ws/service';
      const topicMonitor = '/' + item.productId + '/' + item.serialNumber + '/monitor/post';
      const topicReply = '/' + item.productId + '/' + item.serialNumber + '/service/reply';

      topics.push(topicStatus);
      topics.push(topicService);
      topics.push(topicMonitor);
      topics.push(topicReply);

      console.log(`  📡 设备 ${item.serialNumber} 的主题:`, {
        status: topicStatus,
        service: topicService,
        monitor: topicMonitor,
        reply: topicReply
      });
    });
  }

  console.log('📡 最终订阅主题列表:', topics);
  return topics;
};

// 从组态组件中提取实际使用的设备序列号
const getUsedDevicesFromComponents = (components: any[]) => {
  const usedDevices = new Set<string>();

  if (!components || components.length === 0) {
    console.log('🔍 没有组件，返回空设备列表');
    return [];
  }

  components.forEach((component, index) => {
    console.log(`🔍 检查组件 ${index + 1}:`, {
      type: component.type,
      identifier: component.identifier,
      dataBind: component.dataBind
    });

    // 检查组件的数据绑定配置
    if (component.dataBind && component.dataBind.serialNumber) {
      console.log(`  📡 组件 ${component.type} 使用设备: ${component.dataBind.serialNumber}`);
      usedDevices.add(component.dataBind.serialNumber);
    } else {
      console.log(`  ⚠️ 组件 ${component.type} 没有设备绑定`);
    }
  });

  const deviceArray = Array.from(usedDevices);
  console.log('🔍 提取到的设备列表:', deviceArray);
  return deviceArray;
};

// 获取组态数据
const getZtData = () => {
  getByGuid(scadaGuid.value).then((res) => {
    const { data } = res.data;

    // 安全解析JSON数据
    if (data.scadaData && data.scadaData !== 'undefined' && data.scadaData !== 'null') {
      try {
        configData.value = JSON.parse(data.scadaData);
      } catch (error) {
        configData.value = getDefaultConfig(data.pageName);
        ElMessage.warning('组态数据格式错误，已使用默认配置');
      }
    } else {
      configData.value = getDefaultConfig(data.pageName);
      ElMessage.info('该组态还没有设计内容，显示空白画布');
    }

    document.title = data.pageName || '组态预览';
    bindDeviceList.value = data.bindDeviceList || [];

    console.log('🔧 组态绑定设备列表:', bindDeviceList.value);
    console.log('🔧 设备数量:', bindDeviceList.value.length);

    // 分析每个设备
    bindDeviceList.value.forEach((device, index) => {
      console.log(`🔧 设备 ${index + 1}:`, {
        productId: device.productId,
        serialNumber: device.serialNumber,
        deviceName: device.deviceName || '未命名'
      });
    });

    // 检查是否包含温度计期望的设备
    const hasExpectedDevice = bindDeviceList.value.some(device => device.serialNumber === 'HN11V2785');
    console.log('🔧 是否包含期望设备 HN11V2785:', hasExpectedDevice);

    if (!hasExpectedDevice) {
      console.warn('⚠️ 绑定设备列表中没有找到 HN11V2785，温度计可能无法正常工作');
    }

    // 智能过滤：只订阅组态中实际使用的设备
    const usedDevices = getUsedDevicesFromComponents(configData.value.components);
    console.log('🎯 组态中实际使用的设备:', usedDevices);

    // 过滤绑定设备列表，只保留实际使用的设备
    const filteredDeviceList = bindDeviceList.value.filter(device =>
      usedDevices.includes(device.serialNumber)
    );

    console.log('🎯 过滤后的设备列表:', filteredDeviceList);

    if (filteredDeviceList.length > 0) {
      mqttSubscribe(filteredDeviceList);
    } else {
      console.warn('⚠️ 没有找到组态中使用的设备，将订阅所有绑定设备');
      mqttSubscribe(bindDeviceList.value);
    }

    // 处理缩放和移动端适配
    handleScaling();
  }).catch((error) => {
    ElMessage.error('获取组态数据失败，请检查网络连接');
    configData.value = getDefaultConfig('--');
  });
};

// 获取默认配置
const getDefaultConfig = (pageName: string = '--') => ({
  name: pageName,
  layer: {
    backColor: '',
    backgroundImage: '',
    widthHeightRatio: '',
    width: 1920,
    height: 1080,
    dragZoom: false
  },
  components: []
});

// 处理缩放逻辑
const handleScaling = () => {
  if (window.screen.width < 1366) {
    // 移动端处理
    selectedValue.value = 100;
    let rate = window.screen.width / 1920;
    newZoom.value = rate;

    if (isMobileDevice().ios) {
      document.body.style.transform = 'scale(' + rate + ')';
    } else {
      document.body.style.zoom = rate.toString();
    }

    // 添加触摸事件处理
    addTouchEvents();
  } else {
    // 桌面端处理
    const savedValue = sessionStorage.getItem('selectedValue-' + scadaGuid.value);
    if (savedValue && savedValue !== 'undefined') {
      const parsedValue = Number(savedValue);
      selectedValue.value = parsedValue < 30 ? 100 : parsedValue;
    } else if (props.defaultValue) {
      selectedValue.value = props.defaultValue;
    }

    setTimeout(() => {
      initLeftTop();
    }, 200);
  }
};

// 工具函数
const isMobileDevice = () => {
  const u = navigator.userAgent;
  return {
    trident: u.indexOf('Trident') > -1,
    presto: u.indexOf('Presto') > -1,
    webKit: u.indexOf('AppleWebKit') > -1,
    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1,
    mobile: !!u.match(/AppleWebKit.*Mobile/i) || !!u.match(/MIDP|SymbianOS|NOKIA|SAMSUNG|LG|NEC|TCL|Alcatel|BIRD|DBTEL|Dopod|PHILIPS|HAIER|LENOVO|MOT-|Nokia|SonyEricsson|SIE-|Amoi|ZTE/),
    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),
    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,
    iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1,
    iPad: u.indexOf('iPad') > -1,
    webApp: u.indexOf('Safari') == -1,
  };
};

const addTouchEvents = () => {
  document.body.addEventListener('touchstart', handleTouchStart);
  document.addEventListener('touchmove', handleTouchMove);
};

const handleTouchStart = (event: TouchEvent) => {
  const touches = event.touches;
  const events = touches[0];
  const events2 = touches[1];

  displacement.pageX = events.pageX;
  displacement.pageY = events.pageY;
  displacement.moveable = true;

  if (events2) {
    displacement.pageX2 = events2.pageX;
    displacement.pageY2 = events2.pageY;
  }
  displacement.originScale = displacement.scale || 1;
};

const handleTouchMove = (event: TouchEvent) => {
  if (!displacement.moveable) return;

  event.preventDefault();
  const touches = event.touches;
  const events = touches[0];
  const events2 = touches[1];

  if (events2) {
    if (!displacement.pageX2) displacement.pageX2 = events2.pageX;
    if (!displacement.pageY2) displacement.pageY2 = events2.pageY;

    const zoom = getDistance(
        { x: events.pageX, y: events.pageY },
        { x: events2.pageX, y: events2.pageY }
    ) / getDistance(
        { x: displacement.pageX, y: displacement.pageY },
        { x: displacement.pageX2, y: displacement.pageY2 }
    );

    if (ztTimer.value) clearTimeout(ztTimer.value);

    ztTimer.value = setTimeout(() => {
      if (zoom > 1) {
        newZoom.value = Math.min(newZoom.value + 0.1, 0.8);
      } else {
        newZoom.value = Math.max(newZoom.value - 0.1, 0.2);
      }

      if (isMobileDevice().ios) {
        document.body.style.transform = 'scale(' + newZoom.value + ')';
      } else {
        document.body.style.zoom = newZoom.value.toString();
      }
    }, 100);
  }
};

const getDistance = (start: { x: number; y: number }, stop: { x: number; y: number }) => {
  return Math.hypot(stop.x - start.x, stop.y - start.y);
};

// 初始化左上角样式
const initLeftTop = () => {
  const renderApp = document.getElementById('render-app');
  if (!renderApp) return;

  if (!configData.value.layer?.dragZoom) {
    renderApp.style.overflow = 'auto';
    return;
  }

  renderApp.style.overflow = 'hidden';
  const savedLeft = sessionStorage.getItem('boxLeft-' + scadaGuid.value);
  const savedTop = sessionStorage.getItem('boxTop-' + scadaGuid.value);

  if (savedLeft !== 'undefined' && savedTop !== 'undefined') {
    const point = document.getElementById('render-box');
    if (point) {
      point.style.left = savedLeft || '0px';
      point.style.top = savedTop || '0px';
    }
  }
};

// 鼠标事件处理
const mouseLeave = () => {
  mouseOperate.value = 'default';
};

const mouseEnter = () => {
  mouseOperate.value = 'default';
};

const mouseDown = (e: MouseEvent) => {
  e.stopPropagation();
  e.preventDefault();
  if ((e.target as HTMLElement).id === 'render-box') {
    const idDown = document.getElementById('render-box');
    if (idDown) {
      idDown.style.cursor = 'pointer';
      x.value = e.clientX;
      y.value = e.clientY;
      l.value = idDown.offsetLeft;
      t.value = idDown.offsetTop;
      mouseOperate.value = 'render-box';
    }
  }
};

const mouseMove = (e: MouseEvent) => {
  e.preventDefault();
  if (!configData.value.layer?.dragZoom) return;

  const point = document.getElementById('render-box');
  if (mouseOperate.value === 'render-box' && point) {
    const nx = e.clientX;
    const ny = e.clientY;
    const nl = nx - (x.value - l.value);
    const nt = ny - (y.value - t.value);

    point.style.left = nl + 'px';
    point.style.top = nt + 'px';
    sessionStorage.setItem('boxLeft-' + scadaGuid.value, point.style.left);
    sessionStorage.setItem('boxTop-' + scadaGuid.value, point.style.top);
    e.stopImmediatePropagation();
  }
};

const mouseUp = (e: MouseEvent) => {
  mouseOperate.value = 'default';
  if ((e.target as HTMLElement).id === 'render-box') {
    const idDoc = document.getElementById((e.target as HTMLElement).id);
    if (idDoc) {
      idDoc.style.cursor = 'default';
    }
  }
};

const mouseWheel = (e: any) => {
  if (!configData.value.layer?.dragZoom) return;

  if (e.wheelDelta >= 120) {
    selectedValue.value = selectedValue.value + 10;
  } else if (e.wheelDelta <= -120) {
    if (selectedValue.value > 30) {
      selectedValue.value = selectedValue.value - 10;
    }
  }
  saveSf();
};

// 右键菜单
const onContextmenu = (event: MouseEvent) => {
  if (proxy.$contextmenu) {
    proxy.$contextmenu({
      items: [
        {
          label: '图片生成',
          divided: true,
          icon: 'el-icon-download',
          onClick: () => generateImage(),
        },
        {
          label: '全屏展示',
          icon: 'el-icon-full-screen',
          onClick: () => clickFullscreen(),
        },
        {
          label: '重新加载',
          divided: true,
          icon: 'el-icon-refresh',
          onClick: () => router.go(0),
        },
      ],
      event,
      customClass: 'custom-class',
      zIndex: 9999,
      minWidth: 230,
    });
  }
  return true;
};

// 生成图片
const generateImage = () => {
  if (proxy.$modal) {
    proxy.$modal.loading('正在生成，请稍候...');
  }

  const canvas = document.createElement('canvas');
  const canvasBox = document.getElementById('render-box');

  if (canvasBox) {
    const width = parseInt(window.getComputedStyle(canvasBox).width);
    const height = parseInt(window.getComputedStyle(canvasBox).height);
    canvas.width = width * 2;
    canvas.height = height * 2;
    canvas.style.width = width + 'px';
    canvas.style.height = height + 'px';

    const options = {
      backgroundColor: null,
      canvas: canvas,
      useCORS: true,
    };

    html2canvas(canvasBox, options).then((canvas) => {
      const dataURL = canvas.toDataURL('image/png');
      downloadImage(dataURL);
      if (proxy.$modal) {
        proxy.$modal.closeLoading();
      }
    });
  }
};

const downloadImage = (url: string) => {
  const a = document.createElement('a');
  a.href = url;
  a.download = document.title;
  a.click();
};

// 全屏展示
const clickFullscreen = () => {
  const element = document.getElementById('app');
  if (element) {
    if (element.requestFullscreen) {
      element.requestFullscreen();
    } else if ((element as any).webkitRequestFullScreen) {
      (element as any).webkitRequestFullScreen();
    } else if ((element as any).mozRequestFullScreen) {
      (element as any).mozRequestFullScreen();
    } else if ((element as any).msRequestFullscreen) {
      (element as any).msRequestFullscreen();
    }
  }
};

// 保存缩放
const saveSf = () => {
  sessionStorage.setItem('selectedValue-' + route.query.guid, selectedValue.value.toString());
};

const parseView = (component: any) => {
  const type = component.type;
  let viewComponent = componentMap[type];
  if (viewComponent) {
    return viewComponent;
  } else {
    // 如果没有找到组件，返回默认的组件名
    const viewName = topoUtil.parseViewName(component);
    console.warn(`[Topo] 未找到 ${type} 的组件映射，将使用默认组件名：${viewName}`);
    return viewName;
  }
};

// 点击节流
const throttle = (component: any) => {
  if (throttleTimer.value) {
    clearTimeout(throttleTimer.value);
  }
  throttleTimer.value = setTimeout(() => {
    doClickComponent(component);
  }, 300);
};

// 双击事件
const doDbClickComponent = (component: any) => {
  if (component.action?.length > 0) {
    for (let i = 0; i < component.action.length; i++) {
      const action = component.action[i];
      if (action.type === 'dblclick') {
        // console.log('组件双击了', component);
      }
    }
  }
};

// 点击事件
const doClickComponent = (_component: any) => {
  // console.log('🖱️ 组件点击:', _component);
  // 简化版本，只记录点击事件
};
</script>

<style lang="scss" scoped>
/* 组态预览容器 - 确保样式只在当前组件内生效 */
.topo-preview-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #fff;
  overflow: hidden;
}

/* 使用更安全的样式，避免影响其他页面 */
.fullscreen-preview {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  z-index: 1000 !important;
  background: #fff;
  margin: 0 !important;
  padding: 0 !important;
}

#render-app {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transform-origin: center top;
  z-index: 1000;
  background: #fff;
  margin: 0;
  padding: 0;
}

.topo-render {
  overflow: auto;
  background-color: #ffffff;
  background-clip: padding-box;
  background-origin: padding-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 100%;
  width: 100%;
  position: relative;

  .topo-render-wrapper {
    position: absolute;
  }

  .topo-render-wrapper-clickable {
    cursor: pointer;
  }
}

.simple-image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
</style>