<template>
    <div class="system-user-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="app-container">
                <h4 class="form-header h4">基本信息</h4>
                <el-divider />
                <el-form ref="form" label-width="80px">
                    <el-row>
                        <el-col :span="8" :offset="2">
                            <el-form-item label="用户昵称" prop="nickName">
                                <el-input v-model="state.tableData.user.nickName" disabled />
                            </el-form-item>
                        </el-col>
                        <el-col :span="8" :offset="2">
                            <el-form-item label="登录账号" prop="userName">
                                <el-input v-model="state.tableData.user.userName" disabled />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <h4 class="form-header h4">角色信息</h4>
                <el-divider />
                <el-table :data="state.tableData.data" v-loading="state.tableData.loading" ref="tableRef"
                    @selection-change="handleSelectionChange" border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column label="序号" type="index" align="center" width="100">
                        <template #default="scope">
                            <span>{{ (state.tableData.param.pageNum - 1) * state.tableData.param.pageSize + scope.$index
                                + 1 }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column label="角色编号" align="center" prop="roleId" />
                    <el-table-column label="角色名称" align="center" prop="roleName" />
                    <el-table-column label="权限字符" align="center" prop="roleKey" />
                    <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                        <template #default="scope">
                            <span>{{ scope.row.createTime }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                    style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>

                <el-form label-width="100px">
                    <el-form-item style="text-align: center;margin-top:30px; position: relative; left: 40%">
                        <el-button type="primary" @click="submitForm()">提交</el-button>
                        <el-button @click="close()">返回</el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-card>
    </div>
</template>

<script setup lang="ts" name="systemUser">

import { reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage, ElUpload } from 'element-plus';
import { listUser, getUser, delUser, addUser, updateUser, resetUserPwd, changeUserStatus, deptTreeSelect, getAuthRole, updateAuthRole } from "/@/api/system/user";
// import router from '/@/router';
import { useRoute } from 'vue-router';
import router from '/@/router';

// 定义变量内容
const userDialogRef = ref();
declare type dataType = {
    flag: boolean;

};
const state = reactive({
    tableData: {
        data: [] as dataType[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
        },
        user: {
            nickName: '',
            userName: '',
            userId:''
        }
    },
});


// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)

const ids = ref() //userId
const tableRef = ref();  // 引用表格组件实例
// 初始化表格数据
const getTableData = async () => {

    try {
        const route = useRoute();
        const userId = route.params.userId;
        if (!userId) return
        state.tableData.loading = true;
        const response = await getAuthRole(userId);
        state.tableData.data = response.data.roles;
        state.tableData.user = response.data.user;
        console.log(state.tableData.data);
        state.tableData.total = response.data.roles.length;
        nextTick(() => {
            state.tableData.data.forEach((row) => {
                if (row.flag) {
                    tableRef.value.toggleRowSelection(row);  // 使用 ref 获取组件实例
                }
            });
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};

// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { roleId: string; }) => item.roleId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 提交按钮 */
const submitForm = () => {
    const userId = state.tableData.user.userId;
    const roleIds = ids.value.join(",");
    updateAuthRole({ userId: userId, roleIds: roleIds }).then((response) => {
        ElMessage.success("授权成功");
        close();
    });
}
/** 关闭按钮 */
const close = () => {
    const obj = { path: "/system/user" };
    router.push(obj);
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
}
// 页面加载时
onMounted(() => {
    getTableData()
});
</script>