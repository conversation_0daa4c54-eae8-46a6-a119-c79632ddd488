<template>
	<div class="system-dic-dialog-container">
		<el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" width="500px">
			<el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
				size="default" label-width="90px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="参数名称" prop="configName">
							<el-input v-model="state.ruleForm.configName" placeholder="请输入参数名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="参数键名" prop="configKey">
							<el-input v-model="state.ruleForm.configKey" placeholder="请输入参数键名" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="参数键值" prop="configValue">
							<el-input v-model="state.ruleForm.configValue" placeholder="请输入参数键值" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="系统内置">
							<el-radio-group v-model="state.ruleForm.configType">
								<el-radio v-for="item in configTypelist" :key="item.dictValue"
									:label="item.dictValue" :value="item.dictValue">{{
										item.dictLabel
									}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注">
							<el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
						state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemDicDialog">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addConfig, getConfig, updateConfig } from '/@/api/system/config';

const dictStore = useDictStore();  // 使用 Pinia store

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
	ruleForm: {
		configName: '', // 参数名称
		configKey: '', // 字段名
		configValue: '',
		configType: 'Y', // 参数状态
		remark: '', // 参数描述
		configId: ''
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
}
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	// deptData: [...initialState.deptData],
	dialog: { ...initialState.dialog },
});
interface configTypeOption {
	dictValue: string;
	dictLabel: string;
}
const configTypelist = ref<configTypeOption[]>([]); //状态
// 校验规则
const rules = reactive({
	configName: [
		{ required: true, message: "参数名称不能为空", trigger: "blur" }
	],
	configKey: [
		{ required: true, message: "参数键名不能为空", trigger: "blur" }
	],
	configValue: [
		{ required: true, message: "参数键值不能为空", trigger: "blur" }
	]

})
// 打开弹窗
const openDialog = (type: string, row: RowConfigType, configId: string) => {
	if (type === 'edit') {
		if (row != undefined) {
			getConfig(row.configId).then(response => {
				state.ruleForm = response.data.data
			});
		} else {
			getConfig(configId).then(response => {
				state.ruleForm = response.data.data
			});
		}
		state.dialog.title = '修改参数';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();
		state.dialog.title = '新增参数';
		state.dialog.submitTxt = '新 增';
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	DialogFormRef.value.resetFields();
		// });
	}
	state.dialog.isShowDialog = true;
	getdictdata()
};
// 清空弹框
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm }
	state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.configId != '') {
				updateConfig(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				// const data = addDateRange(state.ruleForm)
				// console.log(data,'data');
				// return
				// console.log(state.ruleForm,'state.ruleForm');

				addConfig(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
};
// 获取状态数据
const getdictdata = async () => {
	try {
		configTypelist.value = await dictStore.fetchDict('sys_yes_no')
		// 处理参数数据
	} catch (error) {
		console.error('获取参数数据失败:', error);
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
