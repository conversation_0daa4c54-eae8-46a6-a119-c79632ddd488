<template>
    <div class="topo-editor-wrap">
        <el-container class="container-wrap">
            <el-header class="header-wrap">
                <div class="left-wrap">
                    <el-button class="btn-wrap" text @click="handelClick(3)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-CopyDocument /></el-icon>
                            <span class="name">复制</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(4)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Delete /></el-icon>
                            <span class="name">删除</span>
                        </span>
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button class="btn-wrap" text @click="handelClick(13)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Top /></el-icon>
                            <span class="name">置顶</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(14)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Bottom /></el-icon>
                            <span class="name">置底</span>
                        </span>
                    </el-button>
                    <el-dropdown class="ml10" @command="handleCommand">
                        <el-button text class="btn-wrap">
                            <span style="display: flex; flex-direction: column;align-items: flex-end;">
                                <el-icon><ele-Refresh /></el-icon>
                                <span class="name">旋转</span>
                            </span>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    command="顺时针90°"><el-icon><ele-Plus /></el-icon>顺时针90°</el-dropdown-item>
                                <el-dropdown-item
                                    command="逆时针90°"><el-icon><ele-Minus /></el-icon>逆时针90°</el-dropdown-item>
                                <el-dropdown-item
                                    command="水平镜像"><el-icon><ele-DArrowLeft /></el-icon>水平镜像</el-dropdown-item>
                                <el-dropdown-item
                                    command="垂直镜像"><el-icon><ele-DArrowRight /></el-icon>垂直镜像</el-dropdown-item>
                                <el-dropdown-item
                                    command="自定义角度"><el-icon><ele-EditPen /></el-icon>自定义角度</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-dropdown class="ml10" @command="handleCommandAlign">
                        <el-button text class="btn-wrap">
                            <span style="display: flex; flex-direction: column;align-items: flex-end;">
                                <el-icon><ele-Operation /></el-icon>
                                <span class="name">对齐</span>
                            </span>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item
                                    command="左对齐"><el-icon><ele-ArrowLeft /></el-icon>左对齐</el-dropdown-item>
                                <el-dropdown-item
                                    command="右对齐"><el-icon><ele-ArrowRight /></el-icon>右对齐</el-dropdown-item>
                                <el-dropdown-item command="上对齐"><el-icon><ele-ArrowUp /></el-icon>上对齐</el-dropdown-item>
                                <el-dropdown-item
                                    command="下对齐"><el-icon><ele-ArrowDown /></el-icon>下对齐</el-dropdown-item>
                                <el-dropdown-item
                                    command="水平等间距"><el-icon><ele-ArrowDown /></el-icon>水平等间距</el-dropdown-item>
                                <el-dropdown-item
                                    command="垂直等间距"><el-icon><ele-ArrowDown /></el-icon>垂直等间距</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-dropdown class="ml10" @command="handleCommandMakeUP">
                        <el-button text class="btn-wrap">
                            <span style="display: flex; flex-direction: column;align-items: flex-end;">
                                <el-icon><ele-Money /></el-icon>
                                <span class="name">组合</span>
                            </span>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="组合"><el-icon><ele-Connection /></el-icon>组合</el-dropdown-item>
                                <el-dropdown-item command="取消组合"><el-icon><ele-Link /></el-icon>取消组合</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <el-divider direction="vertical"></el-divider>
                    <el-button v-if="isLock" class="btn-wrap" text @click="handleLock('解锁')">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Unlock /></el-icon>
                            <span class="name">解锁</span>
                        </span>
                    </el-button>
                    <el-button v-else class="btn-wrap" text @click="handleLock('锁定')">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Lock /></el-icon>
                            <span class="name">锁定</span>
                        </span>
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button class="btn-wrap" text @click="handelClick(6)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Picture /></el-icon>
                            <span class="name">图库</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(8)">
                        <span style="display: flex; flex-direction: column;align-items: center;">
                            <el-icon><ele-Paperclip /></el-icon>
                            <span class="name">设备绑定</span>
                        </span>
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button class="btn-wrap" text @click="handelClick(18)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Upload /></el-icon>
                            <span class="name">导入</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(19)">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Download /></el-icon>
                            <span class="name">导出</span>
                        </span>
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-button class="btn-wrap" text @click="handelClick(16)" :disabled="isRevoke">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-RefreshLeft /></el-icon>
                            <span class="name">撤销</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(17)" :disabled="isRecovery">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-RefreshRight /></el-icon>
                            <span class="name">恢复</span>
                        </span>
                    </el-button>
                    <el-divider direction="vertical"></el-divider>
                    <el-dropdown @command="handleCommandZoom">
                        <el-button class="btn-wrap" text type="primary">
                            <span style="display: flex; flex-direction: column;margin-top: 2px;">
                                <div>{{ zoom }}%</div>
                                <span class="name">缩放</span>
                            </span>
                        </el-button>
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="25">25%</el-dropdown-item>
                                <el-dropdown-item command="50">50%</el-dropdown-item>
                                <el-dropdown-item command="75">75%</el-dropdown-item>
                                <el-dropdown-item command="100">100%</el-dropdown-item>
                                <el-dropdown-item command="125">125%</el-dropdown-item>
                                <el-dropdown-item command="150">150%</el-dropdown-item>
                                <el-dropdown-item command="175">175%</el-dropdown-item>
                                <el-dropdown-item command="200">200%</el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                </div>
                <div class="right-wrap">
                    <el-button class="btn-wrap" text @click="handelClick(1)" v-auths="['scada:center:edit']">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-DocumentChecked /></el-icon>
                            <span class="name">保存</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelClick(2)" v-auths="['scada:center:preview']">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-View /></el-icon>
                            <span class="name">预览</span>
                        </span>
                    </el-button>
                    <el-button class="btn-wrap" text @click="handelHelpClick">
                        <span style="display: flex; flex-direction: column;align-items: flex-end;">
                            <el-icon><ele-Warning /></el-icon>
                            <span class="name">帮助</span>
                        </span>
                    </el-button>
                </div>
            </el-header>
            <el-container>
                <el-aside class="aside-wrap left-aside-open" :class="{ 'aside-close': !leftAsideStatus }"
                    :style="{ height: asideHeight + 'px' }">
                    <!-- { height: asideHeight + 'px' } -->
                    <topo-toolbox ref="topoToolbox" class="topoToolbox-item" />
                </el-aside>
                <el-container>
                    <el-main class="main-wrap">
                        <!-- 侧边框按钮 -->
                        <div class="left-aside-close-btn" @click="leftAsideChange">
                            <i class="el-icon-arrow-left" v-if="leftAsideClose"></i>
                            <i class="el-icon-arrow-right" v-else></i>
                        </div>
                        <div class="right-aside-close-btn" @click="rightAsideChange">
                            <i class="el-icon-arrow-right" v-if="rightAsideClose"></i>
                            <i class="el-icon-arrow-left" v-else></i>
                        </div>
                        <TopoMain ref="topoMain" @menuClick="menuClick" @lockStatus="lockStatus"
                            @recoveryFlagClick="recoveryFlagClick" @revokeFlagClick="revokeFlagClick" />
                    </el-main>
                </el-container>
                <el-aside class="aside-wrap right-aside-open" :class="{ 'aside-close': !rightAsideStatus }"
                    :style="{ height: asideHeight + 'px' }">
                    <TopoProperties ref="topoProperties" class="topoProperties" @saveHistory="handleSaveHistory" />
                </el-aside>
            </el-container>
        </el-container>
         <el-dialog title="图库" v-model="isImgDialog" width="1100px" :close-on-click-modal="false">
           <!-- 拖动区域 -->
           <template #header>
             <div v-dialogDrag style="font-size: 18px;">图库</div>
           </template>
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
            <TopoSelectImage ref="topoSelectImage" />
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="isImgDialog = false">取 消</el-button>
                    <el-button type="primary"@click="selectImageClick">选 择</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog title="设备绑定" v-model="isDeviceBindDialog" width="700px" :close-on-click-modal="false">
            <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
             <DeviceBind ref="deviceBind"/>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus';
import {ref} from 'vue';
import TopoToolbox from './components/topoToolbox.vue';
import TopoMain from './components/topoMain.vue';
import TopoProperties from './components/topoProperties.vue';
import DeviceBind from './components/deviceBind/index.vue';
import TopoSelectImage from './components/topoSelectImage.vue';
// const testDialogVisible = ref(false)
const deviceBind = ref()
// 定义组件状态
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
const leftAsideStatus = ref(true)
const leftAsideClose = ref(true)
const rightAsideStatus = ref(true)
const rightAsideClose = ref(true)
const asideHeight = ref(1000)
const isLock = ref(false) // 锁定/解锁
const isImgDialog = ref(false)  // 图库对话框
const isDeviceBindDialog = ref(false) // 设备对话框
const isRevoke = ref(true) // 撤销
const isRecovery = ref(true) // 恢复
const zoom = ref(100) // 缩放大小
const topoMain = ref()
const topoProperties = ref()
const topoSelectImage = ref()

// 侧边栏收缩与展开
const leftAsideChange = () => {
    leftAsideStatus.value = !leftAsideStatus.value;
    if (leftAsideStatus.value) {
        setTimeout(() => {
            leftAsideClose.value = true;
            topoMain.value.initRuler();
        }, 500);
    } else {
        setTimeout(() => {
            leftAsideClose.value = false;
            topoMain.value.initRuler();
        }, 500);
    }
}
const rightAsideChange = () => {
    rightAsideStatus.value = !rightAsideStatus.value;
    if (rightAsideStatus.value) {
        setTimeout(() => {
            rightAsideClose.value = true;
            topoMain.value.initRuler();
        }, 500);
    } else {
        setTimeout(() => {
            rightAsideClose.value = false;
            topoMain.value.initRuler();
        }, 500);
    }
}
// 获取侧边高度
const calculateAsideHeight = () => {
    let originalHeight = document.getElementsByClassName('app-main')[0].offsetHeight;
    asideHeight.value = parseFloat(originalHeight - 60);
}
// 工具点击事件
const handelClick = (index: number) => {
    if (index == 1) {
        //保存
        topoMain.value.printData();
    } else if (index == 2) {
        //预览
        topoMain.value.screenPreview();
    } else if (index == 3) {
        //复制
        topoMain.value.copyItem();
        topoMain.value.pasteItem();
    } else if (index == 4) {
        //删除
        topoMain.value.removeItem();
    } else if (index == 5) {
        //测试
        topoMain.value.alignClick();
    } else if (index == 6) {
        //图库
        isImgDialog.value = true;
    } else if (index == 7) {
        //全屏
        this.clickFullscreen();
    } else if (index == 8) {
        //设备绑定
        isDeviceBindDialog.value = true;
    } else if (index == 9) {
        //顺时针旋转
        topoProperties.value.transform('顺时针旋转');
    } else if (index == 10) {
        //逆时针旋转
        topoProperties.value.transform('逆时针旋转');
    } else if (index == 11) {
        //水平镜像
        topoProperties.value.transform('水平镜像');
    } else if (index == 12) {
        //垂直镜像
        topoProperties.value.transform('垂直镜像');
    } else if (index == 13) {
        //置顶
        topoProperties.value.stack('置顶');
    } else if (index == 14) {
        //置底
        topoProperties.value.stack('置底');
    } else if (index == 15) {
        //自定义角度
        ElMessageBox.prompt('', '自定义旋转角度', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            closeOnPressEscape: false,
            inputValue: '0',
            inputErrorMessage: '旋转角度不能为空',
        })
            .then(({ value }) => {
                topoProperties.value.transform('自定义角度', value);
            });
    } else if (index == 16) {
        //撤销
        topoMain.value.revoke();
    } else if (index == 17) {
        //恢复
        topoMain.value.recovery();
    } else if (index == 18) {
        //导入
        topoMain.value.handleImport();
    } else if (index == 19) {
        //导出
        topoMain.value.handleDownLoad();
    }
}
// 旋转
const handleCommand = (command: any) => {
    if (command == '顺时针90°') {
        handelClick(9);
    } else if (command == '逆时针90°') {
        handelClick(10);
    } else if (command == '水平镜像') {
        handelClick(11);
    } else if (command == '垂直镜像') {
        handelClick(12);
    } else if (command == '自定义角度') {
        handelClick(15);
    }
}
// 对齐
const handleCommandAlign = (command: any) => {
    topoMain.value.alignClick(command);
}
// 组合
const handleCommandMakeUP = (command: any) => {
    topoMain.value.makeUpClick(command);
}
// 锁定/解锁
const handleLock = (command: any) => {
    topoMain.value.handleLock(command);
}
// 锁定/解锁状态
const lockStatus = (val: any) => {
    isLock.value = val;
}
// 图库选择
const selectImageClick = () => {
    let selectImage = topoSelectImage.value.handleChoice();
    if (selectImage.length === 0) {
        ElMessage.warning('请选择图片')
        return;
    } else {
        selectImage.forEach((element: any) => {
            topoMain.value.addImageData(baseApi.value + element.resourceUrl);
        });
        ElMessage.success('添加成功')
        isImgDialog.value = false;
        topoSelectImage.value.clearChoice();
    }
}
// 撤销标志位
const revokeFlagClick = (flag: any) => {
    isRevoke.value = flag;
}
// 恢复标志位
const recoveryFlagClick = (flag: any) => {
    isRecovery.value = flag;
}
// 缩放
const handleCommandZoom = (command: any) => {
    zoom.value = command;
    topoMain.value.handleZoom(parseInt(command));
}
// 帮助
const handelHelpClick = () => {
    window.open('https://fastbee.cn/doc/');
}
// 处理保存历史记录事件
const handleSaveHistory = () => {
    // 调用主组件的保存历史记录方法
    topoMain.value.saveHistory();
}

// 右键菜单点击了
const menuClick = (val: any) => {
    if (val == '顺时针90°') {
        handelClick(9);
    } else if (val == '逆时针90°') {
        handelClick(10);
    } else if (val == '水平镜像') {
        handelClick(11);
    } else if (val == '垂直镜像') {
        handelClick(12);
    } else if (val == '自定义角度') {
        handelClick(15);
    } else if (val == '置顶') {
        handelClick(13);
    } else if (val == '置底') {
        handelClick(14);
    } else if (val == '预览') {
        handelClick(2);
    } else if (val == '保存') {
        handelClick(1);
    } else if (val == '图库') {
        handelClick(6);
    }
}
</script>

<style scoped>
.el-button--large {
  padding: 5px 5px;
}
.topo-editor-wrap {
  margin: 0;
  padding: 0;
  height: calc(100vh - 84px);
  width: 100%;

  .container-wrap {
    height: 100%;
    background: #f1f3f4;

    .header-wrap {
      height: 60px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #d8dce5;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

      .left-wrap,
      .right-wrap {
        display: flex;
        flex-direction: row;
        align-items: center;

        .btn-wrap {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #495060;

          :deep(span) {
            margin: 0;
          }

          .name {
            display: inline-block;
            margin-top: 5px;
            font-size: 12px;
          }
        }
      }
    }
    .aside-wrap {
      margin-bottom: 0;
      padding: 0;
      background: #f1f3f4;
    }

    .left-aside-open {
      width: 233px !important;
      transition: width 0.5s;
    }


    .right-aside-open {
      width: 260px !important;
      transition: width 0.5s;
    }

    .aside-close {
      width: 0px !important;
    }

    .main-wrap {
      position: relative;
      margin: 0;
      padding: 0;

      .left-aside-close-btn {
        position: absolute;
        left: 0;
        top: 50%;
        width: 14px;
        height: 50px;
        line-height: 50px;
        background-color: #c0c4cc8f;
        color: #fff;
        border-radius: 0 6px 6px 0;
        z-index: 1000;
        cursor: pointer;
        font-size: 14px;
      }

      .left-aside-close-btn:hover {
        color: #ccc;
        background-color: #f1f3f4;
      }

      .right-aside-close-btn {
        position: absolute;
        right: 0;
        top: 50%;
        width: 14px;
        height: 50px;
        line-height: 50px;
        background-color: #c0c4cc8f;
        color: #fff;
        border-radius: 6px 0 0 6px;
        z-index: 1000;
        cursor: pointer;
        font-size: 14px;
      }

      .right-aside-close-btn:hover {
        color: #ccc;
        background-color: #f1f3f4;
      }
    }
  }
}

</style>