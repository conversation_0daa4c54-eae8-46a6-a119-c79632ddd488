.chart-scrollbar {
	position: relative;
	width: 100%;
	height: 100vh;
	min-height: 600px;
	overflow: hidden;
	background: url(/images/bg01.png) no-repeat;
	background-size: cover;

	/* 确保在开发者工具打开时页面能正确适应 */
	@media screen and (max-height: 800px) {
		height: 100vh;
		min-height: 500px;
	}

	@media screen and (max-height: 600px) {
		height: 100vh;
		min-height: 400px;
	}
	// padding: 0 0.2vw 0.2vw 0.2vw;
	// background: none;
	// &-view {
	// 	background: var(--el-color-white);
	// 	width: 100%;
	// 	height: 100%;
	// 	border-radius: 4px;
	// 	border: 1px solid var(--el-border-color-light, #ebeef5);
	// 	overflow: hidden;
	// }

	.chart-warp {
		display: flex;
		flex-direction: column;
		height: 100%;

		.chart-warp-bottom {
			flex: 1;
			overflow: hidden;
			display: flex;

			.big-data-down-left,
			.big-data-down-right {
				width: 25%;
				display: flex;
				flex-direction: column;

				.flex-warp-item {
					padding: 0 7.5px 15px 15px;
					width: 100%;
					height: 33.33%;
					overflow: hidden;

					.flex-warp-item-box {
						width: 100%;
						height: 100%;
						// background: var(--el-color-white);
						// border: 1px solid var(--el-border-color-lighter);
						// border:0.008rem solid rgba(14,253,255,0.5);
						// background: url('./images/chooseKuang.png') center center no-repeat;
						// border-radius: 4px;
						// padding: 15px;
						position: relative;
						border: 1px solid #0bc4e9;
						background: rgba(0, 72, 115, 0.28);
						transition: all 1s;
						display: flex;
						flex-direction: column;
						transition: all ease 0.3s;

						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}

						.flex-title {
							padding: 10px 10px 10px 25px;
							border-bottom: 1px solid #0bc4e9;
							font-size: 16px;
							font-weight: 500;
							position: relative;
							// margin-bottom: 10px;
						}
						.shebeitongji {
							padding: 10px 1px 10px 25px;
							// border-bottom: 1px solid #0bc4e9;
							font-size: 12px;
							font-weight: 400;
							position: relative;
							// margin-bottom: 10px;
						}
						.shebeitongji span {
							color: #0bc4e9;
						}

						.flex-title:before {
							position: absolute;
							content: "";
							width: 6px;
							height: 6px;
							background: #0bc4e9;
							box-shadow: 0 0 5px rgba(22, 214, 255, .9);
							border-radius: 10px;
							left: 10px;
							top: 18px;
						}

						.flex-title:after {
							width: 100%;
							height: 1px;
							content: "";
							position: absolute;
							left: 0;
							bottom: -1px;
							background: linear-gradient(to right, #076ead, #4ba6e0, #076ead);
							box-shadow: 0 0 5px rgba(131, 189, 227, 1);
							opacity: .6
						}

						// .flex-title {
						// 	padding-bottom: 15px;
						// 	padding-top: 5px;
						// 	display: flex;
						// 	justify-content: space-between;
						// 	position: relative;
						// 	padding-left: 20px;

						// 	.flex-title-small {
						// 		font-size: 12px;
						// 	}
						// }


						// .flex-title:before {
						// 	width: 5px;
						// 	height: 25px;
						// 	position: absolute;
						// 	left: 5px;	
						// 	top: 2px;	
						// 	content: "";
						// 	background: #59ebe8;
						// 	border-radius: 20px;
						// 	// left: 0;
						// }

						.flex-content {
							flex: 1;
							font-size: 12px;
							width: 100%;
							// height: calc(100% - 40px);
							overflow: hidden;
						}

						.flex-content-overflow {
							overflow: hidden;
						}
					}

					/*边框公共样式*/
					.border:before {
						content: '';
						position: absolute;
						width: 80%;
						height: 100%;
						bottom: -1px;
						top: -1px;
						left: 10%;
						border-bottom: 1px solid #007297;
						border-top: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:after {
						content: '';
						position: absolute;
						width: 100%;
						height: 80%;
						left: -1px;
						right: -1px;
						top: 10%;
						border-left: 1px solid #007297;
						border-right: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:hover::before {
						width: 0%;
					}

					.border:hover::after {
						height: 0%;
					}

					.border:hover {
						box-shadow: 0 0 0 rgba(255, 255, 255, 0.1),
							/*左边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*上边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*右边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1);
						background: rgba(255, 255, 255, 0.1);
					}
				}
			}

			.big-data-down-left {
				color: var(--el-color-white);

				.sky {
					display: flex;
					align-items: center;

					.sky-left {
						font-size: 30px;
					}

					.sky-center {
						flex: 1;
						overflow: hidden;
						padding: 0 10px;

						font {
							margin-right: 15px;
						}

						.span {
							background: #22bc76;
							border-radius: 2px;
							padding: 0 5px;
							color: var(--el-color-white);
						}
					}

					.sky-right {
						span {
							font-size: 30px;
						}

						font {
							font-size: 20px;
						}
					}
				}

				.sky-dd {
					.sky-dl {
						display: flex;
						align-items: center;
						height: 28px;
						overflow: hidden;

						div {
							flex: 1;
							overflow: hidden;

							i {
								font-size: 14px;
							}
						}

						.tip {
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
						}
					}

					.sky-dl-first {
						color: var(--el-color-white);
					}
				}

				.d-states {
					display: flex;

					.d-states-item {
						flex: 1;
						display: flex;
						align-items: center;
						overflow: hidden;

						i {
							font-size: 20px;
							height: 33px;
							width: 33px;
							line-height: 33px;
							text-align: center;
							border-radius: 100%;
							flex-shrink: 1;
							color: var(--el-color-white);
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.i-bg1 {
							background: #22bc76;
						}

						.i-bg2 {
							background: #e2356d;
						}

						.i-bg3 {
							background: #43bbef;
						}

						.d-states-flex {
							overflow: hidden;
							padding: 0 10px 0;

							.d-states-item-label {
								color: var(--el-color-white);
								overflow: hidden;
								white-space: nowrap;
								text-overflow: ellipsis;
							}

							.d-states-item-value {
								font-size: 14px;
								text-align: center;
								margin-top: 3px;
								color: var(--el-color-white);
							}
						}
					}
				}

				.d-btn {
					margin-top: 5px;

					.d-btn-item {
						border: 1px solid var(--el-color-white);
						display: flex;
						width: 100%;
						border-radius: 35px;
						align-items: center;
						padding: 5px;
						margin-top: 15px;
						cursor: pointer;
						transition: all ease 0.3s;
						color: var(--el-color-white);

						.d-btn-item-left {
							font-size: 20px;
							border: 1px solid var(--el-color-white);
							width: 25px;
							height: 25px;
							line-height: 25px;
							border-radius: 100%;
							text-align: center;
							font-size: 14px;
						}

						.d-btn-item-center {
							padding: 0 10px;
							flex: 1;
						}

						.d-btn-item-eight {
							text-align: right;
							padding-right: 10px;
						}
					}
				}
			}

			.big-data-down-center {
				width: 50%;
				display: flex;
				flex-direction: column;

				.big-data-down-center-one {
					height: 62%;
					padding: 0 7.5px 15px;

					.big-data-down-center-one-content {
						height: 100%;
						// background: var(--el-color-white);
						// padding: 15px;
						border: 1px solid #0bc4e9;
						// border-radius: 10px;
						transition: all ease 0.3s;
						position: relative;

						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}


					}

					/*边框公共样式*/
					.border:before {
						content: '';
						position: absolute;
						width: 80%;
						height: 100%;
						bottom: -1px;
						top: -1px;
						left: 10%;
						border-bottom: 1px solid #007297;
						border-top: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:after {
						content: '';
						position: absolute;
						width: 100%;
						height: 80%;
						left: -1px;
						right: -1px;
						top: 10%;
						border-left: 1px solid #007297;
						border-right: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:hover::before {
						width: 0%;
					}

					.border:hover::after {
						height: 0%;
					}

					.border:hover {
						box-shadow: 0 0 0 rgba(255, 255, 255, 0.1),
							/*左边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*上边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*右边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1);
						background: rgba(255, 255, 255, 0.1);
					}
				}

				.big-data-down-center-two {
					padding: 0 7.5px 15px;
					height: 48%;
					width: 100%;
					// position: relative;
					overflow: hidden;

					.flex-warp-item-box {
						width: 100%;
						height: 100%;
						display: flex;
						flex-direction: column;
						// padding: 15px;
						border: 1px solid #0bc4e9;
						// border-radius: 4px;
						transition: all ease 0.3s;
						position: relative;

						&:hover {
							box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
							transition: all ease 0.3s;
						}

						.flex-title {
							padding: 10px 10px 10px 25px;
							border-bottom: 1px solid #0bc4e9;
							font-size: 16px;
							font-weight: 500;
							position: relative;
							color: var(--el-color-white);
						}


						.flex-title:before {
							position: absolute;
							content: "";
							width: 6px;
							height: 6px;
							background: #0bc4e9;
							box-shadow: 0 0 5px rgba(22, 214, 255, .9);
							border-radius: 10px;
							left: 10px;
							top: 18px;
						}

						.flex-title:after {
							width: 100%;
							height: 1px;
							content: "";
							position: absolute;
							left: 0;
							bottom: -1px;
							background: linear-gradient(to right, #076ead, #4ba6e0, #076ead);
							box-shadow: 0 0 5px rgba(131, 189, 227, 1);
							opacity: .6
						}

						// .flex-title {
						// 	margin-bottom: 15px;
						// 	color: var(--el-color-white);
						// 	display: flex;
						// 	justify-content: space-between;

						// 	.flex-title-small {
						// 		font-size: 12px;
						// 	}
						// }

						.flex-content {
							flex: 1;
							font-size: 12px;
							display: flex;
							flex-direction: column;
							color: #ffffff;
							position: relative;
							height: calc(100% - 40px);
							overflow: hidden;

							.col:nth-child(1) {
								width: 5%;
							}

							.col:nth-child(2) {
								width: 9%;
							}

							.col:nth-child(3) {
								width: 9%;
							}

							.col:nth-child(4) {
								width: 11%;
							}

							.col:nth-child(5) {
								width: 9%;
							}

							.col:nth-child(6) {
								width: 21%;
								margin: 0 1%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;
							}

							.col:nth-child(7) {
								width: 21%;
								margin: 0 1%;
								white-space: nowrap;
								overflow: hidden;
								text-overflow: ellipsis;

							}

							.col:nth-child(8) {
								width: 11%;
							}

							.head {
								font-size: 1rem;
								// padding: 0.5rem 1.5rem;
								color: #0bc4e9;
								display: flex;
								justify-content: space-between;
								line-height: 1.05;
								width: 100%;
								//height: 2vh;
								text-align: center;
								line-height: 2vh;
								border-bottom: 1px solid #25a8db;
							}

							.scroll-view {
								// flex: 1;
								height: 100%;
								width: 100%;
								overflow-y: auto;
							}

							.list {
								width: 100%;
								padding: 0 20px;
								box-sizing: border-box;
							}

							.marquee-view {
								width: 100%;
								// position: absolute;
								// top: 1.85rem;
								// bottom: 0;
								overflow: hidden;

								/* 调用动画 */
								.marquee {
									/* //infinite永久调用动画 */
									// animation: row 3s linear infinite;

									.row {
										line-height: 0.75rem;
										padding: 0.75rem 0;
										color: #ffffff;
										font-size: 0.8rem;
										display: flex;
										// position: relative;
										// justify-content: space-between;
										text-align: center;
										height: 100%;
									}

									.row:hover {
										color: #0bc4e9;
										background: rgba(255, 255, 255, 0.1);
									}

									.row:hover .icon-dot {
										opacity: 1;
									}

									// .icon-dot {
									// 	position: absolute;
									// 	left: 0.64rem;
									// 	opacity: 0;
									// }

									// /* ------------------------------------------------------------动画 */
									// @keyframes row {
									// 	0% {
									// 		// transform: translateY(0)
									// 	}

									// 	100% {
									// 		transform: translateY(-50%);
									// 	}

									// }
								}

								/*鼠标划入 停止动画  */
								// .marquee:hover {
								// 	animation-play-state: paused;
								// }
							}

							// height: calc(100% - 30px);

							// .flex-content-left {
							// 	display: flex;
							// 	flex-wrap: wrap;
							// 	width: 120px;
							// 	height: 100%;

							// 	.monitor-item {
							// 		width: 50%;
							// 		display: flex;
							// 		align-items: center;

							// 		.monitor-wave {
							// 			cursor: pointer;
							// 			width: 40px;
							// 			height: 40px;
							// 			position: relative;
							// 			background-color: var(--el-color-white);
							// 			border-radius: 50%;
							// 			overflow: hidden;
							// 			text-align: center;

							// 			&::before,
							// 			&::after {
							// 				content: '';
							// 				position: absolute;
							// 				left: 50%;
							// 				width: 40px;
							// 				height: 40px;
							// 				background: #f4f4f4;
							// 				animation: roateOne 10s linear infinite;
							// 				transform: translateX(-50%);
							// 				z-index: 1;
							// 			}

							// 			&::before {
							// 				bottom: 10px;
							// 				border-radius: 60%;
							// 			}

							// 			&::after {
							// 				bottom: 8px;
							// 				opacity: 0.7;
							// 				border-radius: 37%;
							// 			}

							// 			.monitor-z-index {
							// 				position: relative;
							// 				z-index: 2;
							// 				color: var(--el-color-white);
							// 				display: flex;
							// 				align-items: center;
							// 				height: 100%;
							// 				justify-content: center;
							// 			}
							// 		}

							// 		@keyframes roateOne {
							// 			0% {
							// 				transform: translate(-50%, 0) rotateZ(0deg);
							// 			}

							// 			50% {
							// 				transform: translate(-50%, -2%) rotateZ(180deg);
							// 			}

							// 			100% {
							// 				transform: translate(-50%, 0%) rotateZ(360deg);
							// 			}
							// 		}

							// 		.monitor-active {
							// 			background-color: #22bc76;

							// 			.monitor-z-index {
							// 				color: #22bc76;
							// 			}
							// 		}
							// 	}
							// }

							// .flex-content-right {
							// 	flex: 1;
							// }
						}
					}

					/*边框公共样式*/
					.border:before {
						content: '';
						position: absolute;
						width: 80%;
						height: 100%;
						bottom: -1px;
						top: -1px;
						left: 10%;
						border-bottom: 1px solid #007297;
						border-top: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:after {
						content: '';
						position: absolute;
						width: 100%;
						height: 80%;
						left: -1px;
						right: -1px;
						top: 10%;
						border-left: 1px solid #007297;
						border-right: 1px solid #007297;
						transition: all 0.5s;
					}

					.border:hover::before {
						width: 0%;
					}

					.border:hover::after {
						height: 0%;
					}

					.border:hover {
						box-shadow: 0 0 0 rgba(255, 255, 255, 0.1),
							/*左边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*上边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1),
							/*右边阴影*/
							0 0 0 rgba(255, 255, 255, 0.1);
						background: rgba(255, 255, 255, 0.1);
					}
				}
			}

			.big-data-down-right {
				.flex-warp-item {
					padding: 0 15px 15px 7.5px;

					.flex-title {
						color: var(--el-color-white);
					}

					.flex-content {
						display: flex;
						flex-direction: column;

						.task {
							display: flex;
							height: 45px;

							.task-item {
								flex: 1;
								// color: var(--el-color-white);
								display: flex;
								justify-content: center;

								.task-item-box {
									position: relative;
									width: 45px;
									height: 45px;
									overflow: hidden;
									border-radius: 100%;
									z-index: 0;
									display: flex;
									align-items: center;
									flex-direction: column;
									justify-content: center;
									box-shadow: 0 10px 12px 0 rgba(0, 0, 0, 0.3);

									&::before {
										content: '';
										position: absolute;
										z-index: -2;
										left: -50%;
										top: -50%;
										width: 200%;
										height: 200%;
										background-repeat: no-repeat;
										background-size: 50% 50%, 50% 50%;
										background-position: 0 0, 100% 0, 100% 100%, 0 100%;
										background-image: linear-gradient(#19d4ae, #19d4ae), linear-gradient(#5ab1ef, #5ab1ef), linear-gradient(#fa6e86, #fa6e86),
											linear-gradient(#ffb980, #ffb980);
										animation: rotate 2s linear infinite;
									}

									&::after {
										content: '';
										position: absolute;
										z-index: -1;
										left: 1px;
										top: 1px;
										width: calc(100% - 2px);
										height: calc(100% - 2px);
										border-radius: 100%;
									}

									.task-item-value {
										text-align: center;
										font-size: 14px;
										font-weight: bold;
									}

									.task-item-label {
										text-align: center;
									}
								}

								.task1 {
									&::after {
										background: #5492be;
									}
								}

								.task2 {
									&::after {
										background: #43a177;
									}
								}

								.task3 {
									&::after {
										background: #a76077;
									}
								}
							}

							.task-first-item {
								flex-direction: column;
								text-align: center;
								color: var(--el-color-white);

								.task-first {
									font-size: 20px;
								}
							}
						}

						.progress {
							color: var(--el-color-white);
							display: flex;
							flex-direction: column;
							flex: 1;
							justify-content: space-between;
							margin-top: 15px;

							.progress-item {
								height: 33.33%;
								display: flex;
								align-items: center;

								.progress-box {
									flex: 1;
									width: 100%;
									margin-left: 10px;

									:deep(.el-progress__text) {
										color: var(--el-color-white);
										font-size: 12px !important;
										text-align: right;
									}

									:deep(.el-progress-bar__outer) {
										background-color: rgba(0, 0, 0, 0.1) !important;
									}

									:deep(.el-progress-bar) {
										margin-right: -22px !important;
									}
								}
							}
						}
					}
				}
			}
		}
	}
}
  