<template>
    <div>
        <template v-for="(item, index) in options" :key="item.dictValue">
            <template v-if="values.includes(item.dictValue)">
                <span v-if="item.listClass == 'default' || item.listClass === '' "  :index="index" :class="item.cssClass"
                    :size="size"> 
                    {{ item.dictLabel }}
                </span>
                <el-tag v-else :disable-transitions="true" :index="index"
                    :class="item.listClass === 'primary' ? 'primary-tag' : ''"
                    :type="item.listClass === 'primary' ? 'success' : item.listClass" :size="size">
                    {{ item.dictLabel }}
                </el-tag>
            </template>
        </template>
    </div>
</template>

<script lang="ts">
import {  defineComponent } from 'vue';

interface Option {
    dictValue: string | number;
    dictLabel: string;
    listClass: string;
    cssClass: string;

}

export default defineComponent({
    name: 'DictTag',
    props: {
        options: {
            type: Array as () => Option[], // Array of options
            required: true,
        },
        value: {
            type: [String, Number, Array] as unknown as () => string | number | Array<string | number>, // Value can be string, number, or an array
            default: () => [],
        },
        size: {
            type: String,
            default: 'default',
        },
    },
    computed: {
        values(): (string | number)[] {
            if (this.value !== null && typeof this.value !== 'undefined') {
                return Array.isArray(this.value) ? this.value : [String(this.value)];
            } else {
                return [];
            }
        },
    },
});

</script>

<style scoped>
.el-tag+.el-tag {
    margin-left: 10px;
}

.primary-tag {
    background-color: #ebf5ff;
    /* 设置类似 primary 的蓝色背景 */
    color: #1890ff;
}
</style>
