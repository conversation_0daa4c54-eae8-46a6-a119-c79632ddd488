import request from '/@/utils/request'

// 查询设备告警列表
export function listTicket(query: any) {
  return request({
    url: '/iot/ticket/list',
    method: 'get',
    params: query
  })
}

// 查询设备告警详细
export function getTicket(ticketId: string) {
  return request({
    url: '/iot/ticket/' + ticketId,
    method: 'get'
  })
}

// 新增设备告警
export function addTicket(data: any) {
  return request({
    url: '/iot/ticket',
    method: 'post',
    data: data
  })
}

// 修改设备告警
export function updateTicket(data: any) {
  return request({
    url: '/iot/ticket',
    method: 'put',
    data: data
  })
}

// 删除设备告警
export function delTicket(ticketId: string) {
  return request({
    url: '/iot/ticket/' + ticketId,
    method: 'delete'
  })
}
