<template>
	<div id="mediaServer" class="iot-media-server">
		<el-card v-show="showSearch" style="margin-bottom: 10px">
			<el-form @submit.prevent :model="queryParams" ref="queryForm" :inline="true" size="mini" label-width="68px" style="margin-bottom: -18px">
				<el-form-item label="配置名称" prop="serverId">
					<el-input v-model="queryParams.serverId" placeholder="请输入配置名称" clearable @keyup.enter="handleQuery" />
				</el-form-item>

				<el-form-item style="float: right">
					<el-button type="primary" size="mini" @click="handleQuery"
						><el-icon> <ele-Search /> </el-icon>{{ '搜索' }}</el-button
					>
					<el-button size="mini" @click="resetQuery"
						><el-icon><ele-Refresh /></el-icon>{{ '重置' }}</el-button
					>
				</el-form-item>
			</el-form>
		</el-card>

		<el-card>
			<el-row :gutter="10" style="margin-bottom: 15px">
				<el-col :span="1.5">
					<el-button type="primary" size="mini" @click="add" v-hasPermi="['iot:video:add']"
						><el-icon><Plus /></el-icon>{{ '新增节点' }}</el-button
					>
				</el-col>
				<right-toolbar v-model:showSearch="showSearch" @queryTable="getServerList"></right-toolbar>
			</el-row>
			<el-row :gutter="20" v-loading="loading" style="min-height: 200px">
				<el-col
					:xs="24"
					:sm="12"
					:md="12"
					:lg="8"
					:xl="6"
					v-for="(item, index) in mediaServerList"
					:key="index"
					style="margin-bottom: 20px; text-align: center"
				>
					<el-card :body-style="{ padding: '20px' }" shadow="always">
						<el-row :gutter="10">
							<el-col :span="15">
								<el-descriptions :column="1" size="mini" style="white-space: nowrap">
									<el-descriptions-item label="服务器名称:">
										{{ item.serverId }}
									</el-descriptions-item>
									<el-descriptions-item label="服务器IP:">
										{{ item.ip || '未配置' }}
									</el-descriptions-item>
									<el-descriptions-item label="播放协议:">
										{{ item.protocol || '未配置' }}
									</el-descriptions-item>
									<el-descriptions-item label="创建时间:">
										{{ new Date(item.createTime).toLocaleDateString() }}
									</el-descriptions-item>
								</el-descriptions>
							</el-col>
							<el-col :span="8">
								<div style="margin-top: 10px">
									<el-image :src="zlmLogo" fit="fit">
										<template #error>
											<div class="image-error">图片加载失败</div>
										</template>
									</el-image>
								</div>
							</el-col>
						</el-row>
						<el-button-group style="margin-top: 10px">
							<el-button type="danger" size="mini" style="padding: 5px 10px" v-hasPermi="['iot:video:remove']" @click="del(item)"
								><el-icon><Delete /></el-icon>{{ '删除' }}</el-button
							>
							<el-button type="primary" size="mini" style="padding: 5px 15px" @click="view(item)" v-hasPermi="['iot:video:query']"
								><el-icon><View /></el-icon>{{ '查看' }}</el-button
							>
							<el-button v-if="!istrue" type="success" size="mini" style="padding: 5px 15px" @click="edit(item)" v-hasPermi="['iot:video:edit']">
								<el-icon><Odometer /></el-icon>{{ '编辑' }}
							</el-button>
							<el-button v-else type="success" size="mini" style="padding: 5px 15px" icon="el-icon-odometer" :loading="true" disabled>{{
								'sdfsdf'
							}}</el-button>
						</el-button-group>
					</el-card>
				</el-col>
			</el-row>
			<!-- <el-empty :description="$t('sip.mediaServer.998535-6')" v-if="total == 0"></el-empty> -->
			<!-- <pagination
				style="margin: 0 0 20px 0"
				v-show="total > 0"
				:total="total"
				:page.sync="queryParams.pageNum"
				:limit.sync="queryParams.pageSize"
				:pageSizes="[12, 24, 36, 60]"
				@pagination="getServerList"
			/> -->
		</el-card>

		<!-- 添加媒体服务器编辑组件 -->
		<media-server-edit
			v-if="showDialog" 
			ref="mediaServerEditRef"
			v-model:edit-flag="editFlag"
			:key="dialogKey"
		/>
	</div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue';
import { delmediaServer, listmediaServer } from '/@/api/iot/mediaServer';
import { ElMessage, ElMessageBox } from 'element-plus';
import MediaServerEdit from './sip-edit.vue';
import zlmLogo from '/@/assets/images/zlm-logo.png';

// 弹窗控制
const mediaServerEditRef = ref(null);
const showEdit = ref(false);
const editFlag = ref(false);
const btnLoading = ref(false);
// 添加对话框控制变量
const showDialog = ref(false);
const dialogKey = ref(0); // 用于强制重新创建组件

const mediaServerForm = ref({
	serverId: '',
	ip: '',
	portHttp: '',
	portHttps: '',
	portRtmp: '',
	portRtsp: '',
	protocol: 'http',
	secret: '',
	domain: '',
	hookurl: '',
	autoConfig: false,
	rtpEnable: false,
	rtpProxyPort: '',
	recordPort: '',
	createTime: '',
});

// 弹出层标题
const title = ref('');

// 数据定义
const showSearch = ref(true);
const loading = ref(true);
const istrue = ref(false);
const mediaServerList = ref([]);
const total = ref(0);
const queryParams = reactive({
	serverId: '',
	pageNum: 1,
	pageSize: 10,
});
const queryForm = ref(null);

// 方法定义
const getServerList = () => {
	loading.value = true;
	console.log('开始获取服务器列表', queryParams);
	listmediaServer(queryParams)
		.then((response) => {
			console.log('获取服务器列表成功', response);
			// 从response.data获取实际数据
			const responseData = response.data || {};
			if (responseData.code === 200 && responseData.rows) {
				console.log('API返回数据:', responseData.rows);
				// 确保数据结构正确
				const rows = responseData.rows || [];
				mediaServerList.value = Array.isArray(rows) ? rows : [rows];
				// 确保每个item都有必要字段
				mediaServerList.value = mediaServerList.value.map((item) => ({
					serverId: item.serverId || '',
					ip: item.ip || '',
					portHttp: item.portHttp || '',
					portRtmp: item.portRtmp || '',
					portRtsp: item.portRtsp || '',
					status: item.status || 0,
					...item,
				}));
				total.value = responseData.total || 0;
				console.log('mediaServerList赋值后:', mediaServerList.value);
				// 强制更新视图
				nextTick(() => {
					console.log('强制视图更新');
				});
			} else {
				console.warn('API返回数据异常:', responseData);
				mediaServerList.value = [];
				total.value = 0;
			}
		})
		.catch((error) => {
			console.error('获取服务器列表失败', error);
			ElMessage.error('获取服务器列表失败: ' + error.message);
		})
		.finally(() => {
			loading.value = false;
		});
};

// 添加一个辅助方法，用于强制刷新组件引用
const refreshComponentRef = () => {
	return new Promise((resolve) => {
		// 先尝试直接使用引用
		if (mediaServerEditRef.value) {
			console.log('组件引用已存在，无需刷新');
			resolve(true);
			return;
		}
		
		console.log('尝试刷新组件引用');
		// 强制组件重新渲染
		showEdit.value = true;
		nextTick(() => {
			showEdit.value = false;
			nextTick(() => {
				if (mediaServerEditRef.value) {
					console.log('组件引用已刷新成功');
					resolve(true);
				} else {
					console.error('组件引用刷新失败');
					resolve(false);
				}
			});
		});
	});
};

// 编辑方法
const edit = (row) => {
	console.log('edit传入的row:', row);
	
	// 关闭旧对话框
	showDialog.value = false;
	
	// 增加key值，强制重新创建组件
	dialogKey.value += 1;
	
	// 设置编辑状态为true
	editFlag.value = true;
	
	// 准备好数据后再显示对话框
	setTimeout(() => {
		showDialog.value = true;
		
		// 等组件创建后再传递数据
		nextTick(() => {
			if (mediaServerEditRef.value) {
				const safeData = {
					id: row?.id || undefined,
					serverId: row?.serverId || '',
					ip: row?.ip || '',
					portHttp: row?.portHttp || '',
					portHttps: row?.portHttps || '',
					portRtmp: row?.portRtmp || '',
					portRtsp: row?.portRtsp || '',
					protocol: row?.protocol || 'http',
					secret: row?.secret || '',
					domain: row?.domain || '',
					hookurl: row?.hookurl || '',
					autoConfig: !!row?.autoConfig,
					rtpEnable: !!row?.rtpEnable,
					rtpProxyPort: row?.rtpProxyPort || '',
					recordPort: row?.recordPort || ''
				};
				
				mediaServerEditRef.value.openDialog(safeData, () => {
					getServerList();
				});
			} else {
				console.error('mediaServerEditRef is not initialized');
				ElMessage.error('组件初始化失败，请刷新页面重试');
			}
		});
	}, 100);
};

// 查看方法
const view = (row) => {
	console.log('view传入的row:', row);
	
	// 关闭旧对话框
	showDialog.value = false;
	
	// 增加key值，强制重新创建组件
	dialogKey.value += 1;
	
	// 查看模式不设置editFlag为true
	editFlag.value = false;
	
	setTimeout(() => {
		showDialog.value = true;
		
		nextTick(() => {
			if (mediaServerEditRef.value) {
				const safeData = {
					id: row?.id || undefined,
					serverId: row?.serverId || '',
					ip: row?.ip || '',
					portHttp: row?.portHttp || '',
					portHttps: row?.portHttps || '',
					portRtmp: row?.portRtmp || '',
					portRtsp: row?.portRtsp || '',
					protocol: row?.protocol || 'http',
					secret: row?.secret || '',
					domain: row?.domain || '',
					hookurl: row?.hookurl || '',
					autoConfig: !!row?.autoConfig,
					rtpEnable: !!row?.rtpEnable,
					rtpProxyPort: row?.rtpProxyPort || '',
					recordPort: row?.recordPort || ''
				};
				
				mediaServerEditRef.value.openDialog(safeData);
			} else {
				console.error('mediaServerEditRef is not initialized');
				ElMessage.error('组件初始化失败，请刷新页面重试');
			}
		});
	}, 100);
};

// 添加方法
const add = () => {
	console.log('添加新服务器');
	
	// 关闭旧对话框
	showDialog.value = false;
	
	// 增加key值，强制重新创建组件
	dialogKey.value += 1;
	
	// 设置编辑状态为true
	editFlag.value = true;
	
	setTimeout(() => {
		showDialog.value = true;
		
		nextTick(() => {
			if (mediaServerEditRef.value) {
				// 创建一个空的安全对象
				const safeData = {
					id: undefined,
					serverId: '',
					ip: '',
					portHttp: '',
					portHttps: '',
					portRtmp: '',
					portRtsp: '',
					protocol: 'http',
					secret: '',
					domain: '',
					hookurl: '',
					autoConfig: false,
					rtpEnable: false,
					rtpProxyPort: '',
					recordPort: ''
				};
				
				mediaServerEditRef.value.openDialog(safeData, () => {
					getServerList();
				});
			} else {
				console.error('mediaServerEditRef is not initialized');
				ElMessage.error('组件初始化失败，请刷新页面重试');
			}
		});
	}, 100);
};

const close = () => {
	showEdit.value = false;
	mediaServerForm.value = {
		serverId: '',
		ip: '',
		portHttp: '',
		portHttps: '',
		portRtmp: '',
		portRtsp: '',
		protocol: 'http',
		secret: '',
		domain: '',
		hookurl: '',
		autoConfig: false,
		rtpEnable: false,
		rtpProxyPort: '',
		recordPort: '',
		createTime: '',
	};
};

const del = (row) => {
	const ids = row.id;
	ElMessageBox.confirm('确认要删除该服务器吗？', '提示', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			delmediaServer(ids).then(() => {
				getServerList();
				ElMessage.success('删除成功');
			});
		})
		.catch(() => {});
};

const handleQuery = () => {
	queryParams.pageNum = 1;
	getServerList();
};

const resetQuery = () => {
	if (queryForm.value?.resetFields) {
		queryForm.value.resetFields();
	}
	handleQuery();
};

const initData = () => {
	getServerList();
};

// 生命周期钩子
onMounted(() => {
	// 确保组件初始化完成后再进行数据加载
	nextTick(() => {
		initData();
		console.log('媒体服务器组件已挂载，引用状态:', !!mediaServerEditRef.value);
	});
});

onUnmounted(() => {
	// 清理工作
	console.log('媒体服务器组件已卸载');
});
</script>

<style lang="scss" scoped>
.iot-media-server {
	.image-error {
		width: 100%;
		height: 60px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f5f5f5;
		color: #999;
	}
	padding: 20px;

	/* 确保卡片可见 */
	.el-card {
		background: white;
		margin-bottom: 20px;
	}

	/* 确保行可见 */
	.el-row {
		background: transparent;
	}
}
</style>
