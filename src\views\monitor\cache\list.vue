<template>
    <div class="layout-padding">
        <el-row :gutter="10">
            <el-col :span="8">
                <el-card style="height: calc(100vh - 125px)">
                    <div>
                        <div class="title">
                            <span>缓存列表</span>
                            <el-button style="padding: 3px 0" text type="primary" @click="refreshCacheNames()">
                                <el-icon style="margin-right: 10px;">
                                    <RefreshRight />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div style="width: 100%; padding: 0 20px;">
                        <el-table border v-loading="loading" :data="cacheNames" :height="tableHeight"
                            highlight-current-row @row-click="getCacheKeys"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <el-table-column label="序号" width="80" align="center" type="index"></el-table-column>
                            <el-table-column label="缓存名称" align="center" prop="cacheName" :show-overflow-tooltip="true"
                                :formatter="nameFormatter"></el-table-column>
                            <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
                            <el-table-column label="操作" width="80" align="center"
                                class-name="small-padding fixed-width">
                                <template #default="scope">
                                    <el-button size="default" text type="primary" plain @click="handleClearCacheName(scope.row)"
                                        v-auths="['monitor:cache:remove']">
                                        <el-icon><ele-DeleteFilled /></el-icon>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="8">
                <el-card style="height: calc(100vh - 125px)">
                    <div>
                        <div class="title">
                            <span>键名列表</span>
                            <el-button style="padding: 3px 0" text type="primary" @click="refreshCacheKeys()">
                                <el-icon style="margin-right: 10px;">
                                    <RefreshRight />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                    <div style="width: 100%; padding: 0 20px;">
                        <el-table border v-loading="subLoading" :data="cacheKeys" :height="tableHeight"
                            highlight-current-row @row-click="handleCacheValue"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <el-table-column label="序号" align="center" width="80" type="index"></el-table-column>
                            <el-table-column label="缓存键名" align="center" :show-overflow-tooltip="true"
                                :formatter="keyFormatter">
                            </el-table-column>
                            <el-table-column label="操作" width="90" align="center"
                                class-name="small-padding fixed-width">
                                <template #default="scope">
                                    <el-button size="default" text type="primary" plain @click="handleClearCacheKey(scope.row)"
                                        v-auths="['monitor:cache:remove']">
                                        <el-icon><ele-DeleteFilled /></el-icon>
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="8">
                <el-card :bordered="false" style="height: calc(100vh - 125px)">
                    <div>
                        <div class="title">
                            <span>缓存内容</span>
                            <!-- <el-button style="padding: 3px 0 ;margin-right: 10px;" text type="primary"
                                @click="handleClearCacheAll()">
                                <el-icon>
                                    <RefreshRight />
                                </el-icon>
                                清理全部
                            </el-button> -->
                        </div>
                    </div>
                    <el-form :model="cacheForm" label-position="top">
                        <el-row :gutter="32">
                            <el-col :offset="1" :span="22"  style="margin-bottom: 30px;">
                                <el-form-item label="缓存名称:" prop="cacheName">
                                    <el-input v-model="cacheForm.cacheName" readonly />
                                </el-form-item>
                            </el-col>
                            <el-col :offset="1" :span="22"  style="margin-bottom: 30px;">
                                <el-form-item label="缓存键名:" prop="cacheKey">
                                    <el-input v-model="cacheForm.cacheKey" readonly />
                                </el-form-item>
                            </el-col>
                            <el-col :offset="1" :span="22">
                                <el-form-item label="缓存内容:" prop="cacheValue">
                                    <el-input v-model="cacheForm.cacheValue" type="textarea" :rows="8" readonly />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { clearCacheAll, clearCacheKey, clearCacheName, getCacheValue, listCacheKey, listCacheName } from '/@/api/monitor/cache';
import { ElMessage } from 'element-plus';

const cacheNames = ref([])
const cacheKeys = ref([])
const cacheForm = ref({
    cacheName: '',
    cacheKey: '',
    cacheValue: '',
})
const loading = ref(true)
const subLoading = ref(false)
const nowCacheName = ref('')
const tableHeight = ref(window.innerHeight - 200)

/** 查询缓存名称列表 */
const getCacheNames = () => {
    loading.value = true;
    listCacheName().then(response => {
        cacheNames.value = response.data.data;
        loading.value = false;
    });
}
/** 刷新缓存名称列表 */
const refreshCacheNames = () => {
    getCacheNames();
    ElMessage.success("刷新缓存列表成功");
}
/** 刷新缓存键名列表 */
const refreshCacheKeys = () => {
    getCacheKeys();
    ElMessage.success("刷新键名列表成功");
}
/** 查询缓存键名列表 */
const getCacheKeys = (row?: any) => {
    const cacheName = row !== undefined ? row.cacheName : nowCacheName.value;
    if (cacheName === "") {
        return;
    }
    subLoading.value = true;
    listCacheKey(cacheName).then(response => {
        cacheKeys.value = response.data.data;
        subLoading.value = false;
        nowCacheName.value = cacheName;
    });
}
/** 查询缓存内容详细 */
const handleCacheValue = (cacheKey: any) => {
    getCacheValue(nowCacheName.value, cacheKey).then(response => {
        cacheForm.value = response.data.data;
    });
}
/** 列表前缀去除 */
const nameFormatter = (row: any) => {
    return row.cacheName.replace(":", "");
}
/** 键名前缀去除 */
const keyFormatter = (cacheKey: any) => {
    return cacheKey.replace(nowCacheName.value, "");
}
/** 清理指定名称缓存 */
const handleClearCacheName = (row: any) => {
    clearCacheName(row.cacheName).then(response => {
        ElMessage.success("清理缓存名称[" + nowCacheName.value + "]成功");
        getCacheKeys();
    });
}
/** 清理指定键名缓存 */
const handleClearCacheKey = (cacheKey: any) => {
    clearCacheKey(cacheKey).then(response => {
        ElMessage.success("清理缓存键名[" + cacheKey + "]成功");
        getCacheKeys();
    });
}
/** 清理全部缓存 */
const handleClearCacheAll = () => {
    clearCacheAll().then(response => {
        ElMessage.success("清理全部缓存成功");
    });
}
// 页面加载时
onMounted(() => {
    getCacheNames();
});
</script>
<style scoped>
.card-box {
    margin-bottom: 20px;
}

:deep(.el-card__body) {
    padding: 15px 0 20px 0 !important;
}

.title {
    height: 30px;
    padding-left: 20px;
    border-bottom: 1px solid #e6ebf5;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

}
</style>