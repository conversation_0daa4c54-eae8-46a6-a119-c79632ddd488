<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="标题" prop="title">
                        <el-input v-model="state.tableData.param.title" clearable size="default" placeholder="请输入标题"
                            style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="分类" prop="categoryName">
                        <el-input v-model="state.tableData.param.categoryName" placeholder="请输入分类名称" clearable
                            size="default" />
                    </el-form-item>
                    <el-form-item label="置顶" prop="isTop">
                        <el-select v-model="state.tableData.param.isTop" placeholder="是否置顶" clearable size="default"
                            style="width:100px;">
                            <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="轮播" prop="isBanner">
                        <el-select v-model="state.tableData.param.isBanner" placeholder="是否轮播" clearable size="default"
                            style="width:100px;">
                            <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>

                    <el-form-item label="发布" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="选择状态" clearable size="default"
                            style="width:100px;">
                            <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['iot:news:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['iot:news:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['iot:news:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['iot:news:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="图片" align="center" prop="imgUrl" width="140px;">
                    <template #default="scope">
                        <el-image style="border-radius:5px;height:80px;width:120px;margin-bottom:-5px;" lazy
                            :preview-src-list="[baseUrl + scope.row.imgUrl]" :src="baseUrl + scope.row.imgUrl"
                            fit="cover"></el-image>
                    </template>
                </el-table-column>
                <el-table-column label="标题" align="center" prop="title" />
                <el-table-column label="分类" align="center" prop="categoryName">
                    <template #default="scope">
                        <el-tag type="info">{{ scope.row.categoryName }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="作者" align="center" prop="author" width="100" />
                <el-table-column label="置顶" align="center" prop="isTop" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.isTop == 1 ? 'success' : 'danger'">{{ scope.row.isTop == 1 ? '是' :
                            '否'
                            }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="轮播" align="center" prop="isBanner" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.isBanner == 1 ? 'success' : 'danger'">{{ scope.row.isBanner == 1 ? '是'
                            :
                            '否'
                            }}</el-tag> </template>
                </el-table-column>
                <el-table-column label="发布" align="center" prop="status" width="80">
                    <template #default="scope">
                        <el-tag :type="scope.row.status == 1 ? 'success' : 'danger'">{{ scope.row.status == 1 ? '是' :
                            '否'
                            }}</el-tag> </template>
                </el-table-column>

                <el-table-column label="创建时间" align="center" prop="createTime" width="120">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="备注" align="center" prop="remark" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary"
                            @click="openDetailDialog(scope.row.newsId)"><el-icon><ele-View /></el-icon>查看</el-button>
                        <el-button size="default" text type="primary" @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:news:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
                        <el-button size="default" text type="primary" @click="onRowDel(scope.row)"
                            v-auths="['iot:news:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <NewsDialog ref="NewsDialogRef" @refresh="getTableData()" />
        <!--通知公告详情 -->
        <el-dialog style="position: absolute; top: 100px;" :title="form.title" v-model="openDetail" width="800px"
            append-to-body>
            <div style="margin-top:-10px;margin-bottom:10px;">
                <el-tag size="default" effect="dark" type="success">{{ form.categoryName }}</el-tag>
                <span style="margin-left:20px;">{{ form.createTime }}</span>
            </div>
            <div v-loading="loadingDetail" class="content">
                <div v-html="form.content"></div>
            </div>
            <!-- <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="closeDetail"> 关 闭 </el-button>
            </div> -->
            <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" @click="closeDetail" size="default">关 闭 </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listNews, getNews, delNews, addNews, updateNews } from "/@/api/iot/news";
import { download } from '/@/utils/request';
import { parseTime } from '/@/utils/next'

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const NewsDialog = defineAsyncComponent(() => import('/@/views/iot/news/dialog.vue'));

// 定义变量内容
const NewsDialogRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            title: undefined,//标题
            categoryName: undefined, //分类名称
            isTop: undefined,//是否置顶
            isBanner: undefined,//是否轮播
            status: ''//状态
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //newsId
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<statusOption[]>([]);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
const openDetail = ref()
const loadingDetail = ref()
let form = reactive({
    title: '',
    categoryName: '',
    createTime: '',
    content: '',
})
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listNews(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
    }
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { newsId: string; }) => item.newsId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('iot_yes_no')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 打开信息详情
const openDetailDialog = (newsId: any) => {
    loadingDetail.value = true;
    getNews(newsId).then(response => {
        form = response.data.data;
        openDetail.value = true;
        loadingDetail.value = false;
    });

}
// 取消按钮
const closeDetail = () => {
    // titleDetail = "详情";
    openDetail.value = false;
    form = reactive({
        title: '',
        categoryName: '',
        createTime: '',
        content: '',
    })
}
// 打开新增新闻弹窗
const onOpenAddDic = (type: string) => {
    NewsDialogRef.value.openDialog(type);
};
// 打开修改新闻弹窗
const onOpenEditDic = (type: string, row: RowNewsType | undefined) => {
    var newsId = ''
    if (!row) {
        newsId = ids.value
    } else {
        newsId = row.newsId
    }
    NewsDialogRef.value.openDialog(type, newsId);
};
// 删除新闻
const onRowDel = (row: RowNewsType) => {
    const newsIds = row.newsId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除新闻资讯编号：“${newsIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delNews(newsIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('iot/news/export', {
        ...state.tableData.param
    }, `news_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
<style lang="scss">
.content {
    line-height: 24px;
    padding:30px 10px;
    border: 1px solid #eee;
    border-radius: 10px;
}
</style>
