<script setup>
import { ref, onMounted } from 'vue';
import { listProduct } from '/@/api/iot/product';

const emit = defineEmits(['productEvent']);

// 字典数据（假设你在全局挂载了 dict.type）
// 如果没有，可以单独引入或通过接口获取
const iotNetworkMethodOptions = ref([]); // 可根据实际 dict.type.iot_network_method 获取

// 表单查询参数
const queryParams = ref({
    pageNum: 1,
    pageSize: 10,
    productName: null,
    categoryId: null,
    categoryName: null,
    tenantId: null,
    tenantName: null,
    isSys: null,
    status: 2, // 已发布
    deviceType: 3, // 监控设备
    networkMethod: null,
});

// 数据状态
const loading = ref(true);
const total = ref(0);
const productList = ref([]);
const product = ref({});
const open = ref(false);

// props 中的 productId（默认值）
const props = defineProps({
    productId: {
        type: Number,
        default: 0
    }
});

// 获取产品列表
const getList = () => {
    loading.value = true;
    console.log('请求参数:', queryParams.value);
    listProduct(queryParams.value).then(response => {
        console.log('接口响应:', response);
        // 初始化 isSelect 属性
        const rows = response?.rows || [];
        rows.forEach(row => {
            row.isSelect = false;
        });
        productList.value = rows;
        total.value = response?.total || 0;

        if (props.productId !== 0) {
            setRadioSelected(props.productId);
        }

        loading.value = false;
    }).catch(error => {
        console.error('获取产品列表失败:', error);
        loading.value = false;
    });
};

// 搜索按钮操作
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

// 重置按钮操作
const resetQuery = () => {
    queryParams.value.productName = '';
    handleQuery();
};

// 单击行触发选中
const rowClick = (row) => {
    if (row) {
        setRadioSelected(row.productId);
        product.value = row;
    }
};

// 设置单选选中
const setRadioSelected = (productId) => {
    productList.value.forEach(item => {
        item.isSelect = item.productId === productId;
    });
};

// 确认选择产品
const confirmSelectProduct = () => {
    emit('productEvent', product.value);
    open.value = false;
};

// 关闭对话框
const closeDialog = () => {
    open.value = false;
};

// 初始加载
onMounted(() => {
    getList();
});

// 暴露方法给父组件
defineExpose({
    open,
    closeDialog
});
</script>

<template>
    <el-dialog
        title="选择产品"
        v-model="open"
        width="600px"
        append-to-body
    >
        <div style="margin-top: -45px;">
            <el-divider style="margin-top: -30px;"></el-divider>
            <el-form :model="queryParams" inline label-width="68px">
                <el-form-item label="产品名称">
                    <el-input
                        v-model="queryParams.productName"
                        placeholder="请输入产品名称"
                        clearable
                        size="small"
                        @keyup.enter="handleQuery"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary"  size="mini" @click="handleQuery"><el-icon><Search /></el-icon>
                        {{ "搜索" }}
                    </el-button>
                    <el-button  size="mini" @click="resetQuery"><el-icon><Refresh /></el-icon>
                        {{ "重置" }}
                    </el-button>
                </el-form-item>
            </el-form>

            <el-table
                v-loading="loading"
                :data="productList"
                @row-click="rowClick"
                highlight-current-row
                size="mini"
            >
                <el-table-column label="选择" width="50" align="center">
                    <template #default="{ row }">
                        <input type="radio" :checked="row.isSelect" name="product" />
                    </template>
                </el-table-column>
                <el-table-column label="产品名称" prop="productName" align="center" />
                <el-table-column label="分类名称" prop="categoryName" align="center" />
                <el-table-column label="租户名称" prop="tenantName" align="center" />
                <el-table-column label="联网方式" align="center">
                    <template #default="{ row }">
                        <dict-tag :options="iotNetworkMethodOptions" :value="row.networkMethod" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" prop="createTime" align="center" width="100">
                    <template #default>
                        <span>{{ new Date(item.createTime).toLocaleDateString() }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0" 
                :total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="getList"
            />
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="confirmSelectProduct" type="primary">{{ "确定" }}</el-button>
                <el-button @click="closeDialog" type="info">{{"关闭" }}</el-button>
            </div>
        </template>
    </el-dialog>
</template>
