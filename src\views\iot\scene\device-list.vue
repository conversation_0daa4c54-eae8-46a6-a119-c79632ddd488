<template>
    <el-dialog title="选择设备" v-model="open" width="900px" append-to-body style="position: absolute; top: 100px;">
        <el-form :model="state.tableData.param" ref="queryForm" :inline="true" label-width="68px">
            <el-form-item label="设备名称" prop="deviceName">
                <el-input v-model="state.tableData.param.deviceName" placeholder="请输入设备名称" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="设备编号" prop="serialNumber">
                <el-input v-model="state.tableData.param.serialNumber" placeholder="请输入设备编号" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"> <el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-table ref="multipleTable" v-loading="state.tableData.loading" :data="state.tableData.deviceList"
            @select="handleSelectionChange" row-key="serialNumber" size="default">
            <el-table-column type="selection" width="30" align="center" />
            <el-table-column label="设备名称" align="center" prop="deviceName" />
            <el-table-column label="设备编号" align="center" prop="serialNumber" />
            <el-table-column label="产品名称" align="center" prop="productName" />
            <el-table-column label="所属名称" align="center" prop="deviceBelongName" />
            <el-table-column label="所属单位" align="center" prop="groupName" />
        
            <el-table-column label="设备状态" align="center" prop="status" width="80">
                <template #default="scope">
                    <dict-tag :options="device_status_list" :value="scope.row.status" />
                </template>
            </el-table-column>
        </el-table>

        <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
            class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
            v-model:current-page="state.tableData.param.pageNum" background
            v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
            :total="state.tableData.total">
        </el-pagination>
        <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="confirmSelectDevice">确 定</el-button>
                <el-button @click="closeSelectDeviceList">取 消</el-button>
            </span>
        </template>

    </el-dialog>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { listDeviceShort } from '/@/api/iot/device';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { log } from 'console';
const dictStore = useDictStore();  // 使用 Pinia store
const emit = defineEmits(); // 定义 emit
const state = reactive({
    tableData: {
        deviceList: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            deviceName: '',
            productId: '' as any,
            serialNumber: '' as any,
            productName: '' as any
        },
    },
});
const selectDeviceNums = ref<string[]>([]);
const multipleTable = ref();
const productId = ref(0);
const productName = ref('');
const open = ref(false);
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const location_way_list = ref<TypeOption[]>([]);
const device_status_list = ref<TypeOption[]>([]);
/** 查询产品列表 */
const getList = () => {
    state.tableData.loading = true;
    state.tableData.deviceList = []
    try {
        listDeviceShort(state.tableData.param).then((response) => {
            state.tableData.deviceList = response.data.rows;
            state.tableData.total = response.data.total;
            // 设置选中
            if (selectDeviceNums.value) {
                state.tableData.deviceList.forEach((row) => {
                    nextTick(() => {
                        if (selectDeviceNums.value.some((x) => x === row.serialNumber)) {
                            multipleTable.value.toggleRowSelection(row, true);
                        }
                    });
                });
            } else {
                // 初始化
                selectDeviceNums.value = [];
            }
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        deviceName: '',
        productId: '' as any,
        serialNumber: '' as any,
        productName: '' as any
    }
    handleQuery();
}
/** 多选框选中数据 */
const handleSelectionChange = (selection: any, row: any) => {
    // 设备ID是否存在于原始设备ID数组中
    let index = selectDeviceNums.value.indexOf(row.serialNumber);
    // 是否选中
    let value = selection.indexOf(row);
    if (index == -1 && value != -1) {
        // 不存在且选中
        selectDeviceNums.value.push(row.serialNumber);
        productId.value = row.productId;
        productName.value = row.productName;
    } else if (index != -1 && value == -1) {
        // 存在且取消选中
        selectDeviceNums.value.splice(index, 1);
    }

    // 筛选产品下的设备比
    if (selectDeviceNums.value.length == 0) {
        state.tableData.param.productId = null;
        state.tableData.param.productName = ''
        getList();
        productId.value = 0 as any
        productName.value = ''

    } else if (selectDeviceNums.value.length == 1) {
        state.tableData.param.productId = row.productId;
        getList();
    }
}
// 全选事件处理
const handleSelectionAll = (selection: any) => {
    for (let i = 0; i < state.tableData.deviceList.length; i++) {
        // 设备ID是否存在于原始设备ID数组中
        let index = selectDeviceNums.value.indexOf(state.tableData.deviceList[i].serialNumber);
        // 是否选中
        let value = selection.indexOf(state.tableData.deviceList[i]);
        if (index == -1 && value != -1) {
            // 不存在且选中
            selectDeviceNums.value.push(state.tableData.deviceList[i].serialNumber);
        } else if (index != -1 && value == -1) {
            // 存在且取消选中
            selectDeviceNums.value.splice(index, 1);
        }
    }
}
// 关闭选择设备列表
const closeSelectDeviceList = () => {
    open.value = false;
}
/**确定选择设备，设备传递给父组件 */
const confirmSelectDevice = () => {
    // if (selectDeviceNums.value.length > 0) {
    var data = {
        productId: productId.value,
        productName: productName.value,
        deviceNums: selectDeviceNums.value,
    };
    emit('deviceEvent', data);
    // }
    closeDialog()
}
/**关闭对话框 */
const closeDialog = () => {
    productId.value = 0
    productName.value = ''
    selectDeviceNums.value = []
    open.value = false;

}
// 打开弹窗
const openDeviceList = () => {
    open.value = true
    getList();
    getdictdata();
};
// 获取状态数据
const getdictdata = async () => {
    try {
        location_way_list.value = await dictStore.fetchDict('iot_location_way ')
        device_status_list.value = await dictStore.fetchDict('iot_device_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getList();
};
// 暴露变量
defineExpose({
    openDeviceList,
    state,
    productId,
    productName,
    selectDeviceNums
});
</script>
<style lang="scss" scoped>
/***隐藏全选，避免选中不同产品的设备**/
:deep(.el-table__header-wrapper .el-checkbox) {
    display: none;
}
</style>