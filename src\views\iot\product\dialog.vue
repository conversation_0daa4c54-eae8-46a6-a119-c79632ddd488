<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px; " :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="1000px" :close-on-click-modal="false">
            <!-- 步骤条 -->
            <div style="display: flex;justify-content: center;">
                <el-steps style="width: 800px;" :active="active" finish-status="success">
                    <el-step @click="changeStep(DialogFormRef, 1)" title="Step 1" />
                    <el-step @click="changeStep(DialogFormRef, 2)" title="Step 2" />
                    <el-step @click="changeStep(DialogFormRef, 3)" title="Step 3" />
                </el-steps>
            </div>
            <!-- 第一步 -->
            <div v-if="active == 1" style="width: 100%;">
                <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
                    size="default" label-width="150px">
                    <el-row :gutter="20">
                        <el-col :span="12" class="mb20">
                            <el-form-item label="产品名称" prop="productName">
                                <el-input size="large" v-model="state.ruleForm.productName"
                                    :readonly="state.ruleForm.status == 2" placeholder="请输入产品名称" clearable></el-input>
                            </el-form-item>
                            <el-form-item size="large" label="产品分类" prop="categoryId">
                                <el-tree-select v-model="state.ruleForm.categoryId" :data="treeState.tableData.data"
                                    :props="defaultSelectProps" @node-click="handleTreeClick"
                                    :disabled="state.ruleForm.status == 2" check-strictly :render-after-expand="true"
                                    :show-count="true" />
                            </el-form-item>
                            <el-form-item label="设备类型" prop="deviceType">
                                <el-select size="large" v-model="state.ruleForm.deviceType" placeholder="请选择设备类型"
                                    :disabled="state.ruleForm.status == 2" style="width:100%">
                                    <el-option v-for="dict in device_type_list" :key="dict.dictValue"
                                        :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="备注">
                                <el-input size="large" v-model="state.ruleForm.remark" type="textarea"
                                    placeholder="请输入内容" maxlength="150"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="产品图片">
                                <!-- {{ state.ruleForm.imgUrl  }}//{{ state.ruleForm.deviceType }}
                                <div v-if="state.ruleForm.imgUrl == ''">
                                    <el-image style="width:100%;height:145px;border-radius:10px;"
                                        :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                        v-if="state.ruleForm.deviceType == 2"></el-image>
                                    <el-image style="width:100%;height:145px;border-radius:10px;"
                                        :preview-src-list="[video]" :src="video" fit="cover"
                                        v-else-if="state.ruleForm.deviceType == 3"></el-image>
                                    <el-image style="width:100%;height:145px;border-radius:10px;"
                                        :preview-src-list="[product]" :src="product" fit="cover" v-else></el-image>
                                </div> -->
                                <div>
                                    <imageUpload v-if="state.ruleForm.status == 0 || state.ruleForm.status != 2"
                                        ref="image-upload" v-model:model-value="state.ruleForm.imgUrl"
                                        :limit="state.ruleForm.status == 2 ? 0 : 1" :fileSize="1">
                                        <!-- @input="getImagePath($event)"> -->
                                    </imageUpload>
                                    <el-image v-else-if="state.ruleForm.imgUrl != ''" @click="dialogVisible = true"
                                        fit="cover" :src="baseUrl + state.ruleForm.imgUrl" style="width: 145px;
                                        height: 145px;border-radius: 5px; object-fit: cover !important; 
                                        object-position: center; cursor: pointer;"></el-image>
                                    <div v-else>
                                        <el-image style="width:100%;height:145px;border-radius:10px;"
                                            :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                            v-if="state.ruleForm.deviceType == 2"></el-image>
                                        <el-image style="width:100%;height:145px;border-radius:10px;"
                                            :preview-src-list="[video]" :src="video" fit="cover"
                                            v-else-if="state.ruleForm.deviceType == 3"></el-image>
                                        <el-image style="width:100%;height:145px;border-radius:10px;"
                                            :preview-src-list="[product]" :src="product" fit="cover" v-else></el-image>
                                    </div>
                                    <div class="el-upload__tip" style="color:#f56c6c"
                                        v-if="state.ruleForm.productId == null || state.ruleForm.productId == 0">
                                        提示：上传后需要提交保存</div>
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <!-- 第二步 -->
            <div class="edit_dev custom-transfer" v-if="active == 2"
                style="padding: 30px 0; display: flex;justify-content: center;">
                <div class="transfer-header">
                    <el-tree-select v-model="selectedValue" :data="treedata" :props="defaultProps"
                        @node-click="handleNodeClick" check-strictly :render-after-expand="true" style="width: 600px"
                        :show-count="true" />
                </div>
                <el-transfer disabled="true" v-model="state.ruleForm.modelIds" filterable
                    filter-placeholder="物模型属性查询" :data="transferData" :format="{
                        noChecked: '${total}',
                        hasChecked: '${checked}/${total}',
                    }" :titles="['物模型名称', state.ruleForm.productName]" @change="handleChange">
                    <template #left-empty>
                        <el-empty :image-size="60" description="无数据" />
                    </template>
                    <template #right-empty>
                        <el-empty :image-size="60" description="无数据" />
                    </template>
                    <!-- <template #left-footer>
                        <el-select v-model="selectedValue" placeholder="选择项">
                            <el-option v-for="item in selectOptions" :key="item.value" :label="item.label"
                                :value="item.value" />
                        </el-select>
                    </template> -->
                </el-transfer>
            </div>
            <!-- 第三步 -->
            <div v-if="active == 3" style="padding: 30px 75px 0 75px;">
                <el-row :gutter="20">
                    <el-col :span="24" class="mb20">
                        <el-tabs addable v-model="activeName" type="card" class="custom-tabs" @tab-click="handleClick">
                            <template #add-icon>
                                <el-button v-if="state.ruleForm.status == 1" v-auths="['iot:product:add']"
                                    type="success" class="publish" size="default" @click="ChangeProductStatus(2)">
                                    发布产品
                                </el-button>
                                <el-button v-if="state.ruleForm.status == 2" v-auths="['iot:product:edit']"
                                    type="danger" class="publish" size="default"
                                    @click="ChangeProductStatus(1)">取消发布</el-button>
                            </template>
                            <el-tab-pane label="基础信息" name="onetabs">
                                <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm"
                                    size="default" label-width="100px">
                                    <el-row :gutter="20">
                                        <el-col :span="12" class="mb20">
                                            <el-form-item label="产品名称：" prop="productName">
                                                <div>{{ state.ruleForm.productName }}</div>
                                            </el-form-item>
                                            <el-form-item size="large" label="产品分类：" prop="categoryId">
                                                <div>{{ categoryLabel }}</div>
                                            </el-form-item>
                                            <el-form-item label="设备类型：" prop="deviceType">
                                                <div>{{ deviceTypeLabel }}</div>
                                            </el-form-item>
                                            <!-- <el-form-item label="备注：">
                                                <div>{{ state.ruleForm.remark }}</div>
                                            </el-form-item> -->
                                        </el-col>
                                        <el-col :span="12">
                                            <el-form-item label="产品图片：">
                                                <div v-if="state.ruleForm.imgUrl == ''">
                                                    <el-image style="height:145px;height:145px;border-radius:10px;"
                                                        :preview-src-list="[gateway]" :src="gateway" fit="cover"
                                                        v-if="state.ruleForm.deviceType == 2"></el-image>
                                                    <el-image style="height:145px;height:145px;border-radius:10px;"
                                                        :preview-src-list="[video]" :src="video" fit="cover"
                                                        v-else-if="state.ruleForm.deviceType == 3"></el-image>
                                                    <el-image style="height:145px;height:145px;border-radius:10px;"
                                                        :preview-src-list="[product]" :src="product" fit="cover"
                                                        v-else></el-image>
                                                </div>
                                                <div v-else>
                                                    <el-image @click="dialogVisible = true" fit="cover"
                                                        :src="baseUrl + state.ruleForm.imgUrl" style="width: 145px;
                                        height: 145px;border-radius: 5px; object-fit: cover !important; 
                                        object-position: center; cursor: pointer;"></el-image>
                                                </div>
                                            </el-form-item>
                                            <el-form-item label="备注：">
                                                <div>{{ state.ruleForm.remark }}</div>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form>
                            </el-tab-pane>
                            <el-tab-pane class="contain" label="属性" name="twotabs">
                                <el-table :data="filteredData" border
                                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                                    <el-table-column label="属性名称" prop="name" width="100" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.name }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="物模型名称" prop="children" align="center">
                                        <template #default="{ row }">
                                            <el-table v-if="row.children && row.children.length > 0"
                                                :data="row.children" border>
                                                <el-table-column label="名称" prop="modelName" align="center">
                                                    <template #default="{ row }">
                                                        <span>{{ row.modelName }}</span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column label="标识符" prop="modelName" align="center">
                                                    <template #default="{ row }">
                                                        <span>{{ row.identifier }}</span>
                                                    </template>
                                                </el-table-column>
                                                <el-table-column label="创建时间" prop="modelName" align="center">
                                                    <template #default="{ row }">
                                                        <span>{{ row.createTime }}</span>
                                                    </template>
                                                </el-table-column>
                                            </el-table>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!-- <el-tabs v-model="propertyName" class="demo-tabs" @tab-click="handlePropertyClick">
                                    <el-tab-pane label="固有属性" name="first">
                                        <div v-for="(item, index) in IntrinsicAttribute" :key="index">
                                            {{ item.modelName }}
                                        </div>
                                    </el-tab-pane>
                                    <el-tab-pane label="运维属性" name="second">
                                        <div v-for="(item, index) in OperationAttribute" :key="index">
                                            {{ item.modelName }}
                                        </div>
                                    </el-tab-pane>
                                    <el-tab-pane label="遥测属性" name="third">
                                        <div v-for="(item, index) in telemeterAttribute" :key="index">
                                            {{ item.modelName }}
                                        </div>
                                    </el-tab-pane>
                                    <el-tab-pane label="通讯属性" name="fourth">
                                        <div v-for="(item, index) in communicatAttribute" :key="index">
                                            {{ item.modelName }}
                                        </div>
                                    </el-tab-pane>
                                    <el-tab-pane label="虚拟属性" name="fifth">
                                        <div v-for="(item, index) in dummyAttribute" :key="index">
                                            {{ item.modelName }}
                                        </div>
                                    </el-tab-pane>
                                </el-tabs> -->
                            </el-tab-pane>
                            <el-tab-pane label="功能" name="threetabs">
                                <el-table :data="functions" border>
                                    <el-table-column label="名称" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.modelName }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="标识符" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.identifier }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="创建时间" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.createTime }}</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!-- <div v-for="(item, index) in functions" :key="index">
                                    {{ item.modelName }}
                                </div> -->
                            </el-tab-pane>
                            <el-tab-pane label="事件" name="fourtabs">
                                <el-table :data="events" border>
                                    <el-table-column label="名称" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.modelName }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="标识符" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.identifier }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="创建时间" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.createTime }}</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!-- <div v-for="(item, index) in events" :key="index">
                                    {{ item.modelName }}
                                </div> -->

                            </el-tab-pane>
                            <el-tab-pane label="关系" name="fivetabs">
                                <el-table :data="relationship" border>
                                    <el-table-column label="名称" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.modelName }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="标识符" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.identifier }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="创建时间" prop="modelName" align="center">
                                        <template #default="{ row }">
                                            <span>{{ row.createTime }}</span>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <!-- <div v-for="(item, index) in relationship" :key="index">
                                    {{ item.modelName }}
                                </div> -->
                            </el-tab-pane>
                        </el-tabs>
                    </el-col>
                </el-row>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button v-if="active != 1" type="primary" @click="changeStep(DialogFormRef, active - 1)"
                        size="default">上一步</el-button>
                    <el-button v-if="active != 3" type="primary" @click="changeStep(DialogFormRef, active + 1)"
                        size="default">下一步</el-button>
                    <el-button v-if="active == 3" type="primary" size="default" @click="onSubmit">提 交</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { computed, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import imageUpload from '/@/components/ImageUpload/index.vue'
import gateway from '/@/assets/images/gateway.png';
import video from '/@/assets/images/video.png';
import product from '/@/assets/images/product.png';
import { listTemplate } from '/@/api/iot/template';
import { addProduct, changeProductStatus, deviceCount, getProduct, updateProduct } from '/@/api/iot/product';
import { listCategory } from '/@/api/iot/category';
import { handleTree } from '/@/utils/next';
const dictStore = useDictStore();  // 使用 Pinia store


interface DataItem {
    modelId: number; // 假设 modelId 是 number 类型
}
interface productOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const defaultProps = {
    children: 'children',
    label: 'label',
    value: 'value',
}
const defaultSelectProps = reactive({
    children: "children",
    label: "categoryName",
    value: 'categoryId',
});
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const active = ref(1) //步骤
const activeName = ref('onetabs')  //全部信息tabs标签
const propertyName = ref('first')  //属性信息tabs标签
const dialogVisible = ref(false)  //预览状态
const selectedValue = ref('全部')  //树状图选择状态
const allData = ref<DataItem[]>([]); //所有列表数据
const tableData = ref<DataItem[]>([]); //其他列表数据
const baseUrl = import.meta.env.VITE_APP_BASE_API as string;
// 下拉框内容
const treedata: any = [
    {
        label: '全部',
        value: '全部',  // 添加 value 字段
        children: [
            {
                label: '属性',
                value: '属性',  // 添加 value 字段
                type: 1,
                children: [
                    {
                        label: '固有属性',
                        value: '固有属性',  // 添加 value 字段
                        subType: 1,
                        type: 1,
                    },
                    {
                        label: '运维属性',
                        value: '运维属性',  // 添加 value 字段
                        subType: 2,
                        type: 1,
                    },
                    {
                        label: '遥测属性',
                        value: '遥测属性',  // 添加 value 字段
                        subType: 3,
                        type: 1,
                    },
                    {
                        label: '通讯属性',
                        value: '通讯属性',  // 添加 value 字段
                        subType: 4,
                        type: 1,
                    },
                    {
                        label: '虚拟属性',
                        value: '虚拟属性',  // 添加 value 字段
                        subType: 5,
                        type: 1,
                    },
                ],
            },
            {
                label: '功能',
                value: '功能',  // 添加 value 字段
                type: 2,
            },
            {
                label: '事件',
                value: '事件',  // 添加 value 字段
                type: 3,
            },
            {
                label: '关系',
                value: '关系',  // 添加 value 字段
                type: 4,
            },
        ],
    },
];

const transferData = ref([])  //穿梭框左侧内容
const transferValue = ref<number[]>([]) //穿梭框右侧内容
const saveTransferValue = ref<number[]>([]) //穿梭框右侧内容
const device_type_list = ref<productOption[]>([]);//联网方式

// 定义变量内容
const DialogFormRef = ref();
const treeState = reactive({
    tableData: {
        data: [],
        param: {

        },
    },
});
const initialState = {
    ruleForm: {
        productName: '', // 产品分类标题
        remark: '', // 状态
        categoryId: '' as any,
        deviceType: '' as any,
        status: 0,
        imgUrl: '',
        productId: '' as any,
        modelIds: [] as number[]

    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    productName: [{
        required: true,
        message: "产品名称不能为空",
        trigger: "blur"
    }],
    categoryId: [{
        required: true,
        message: "产品分类ID不能为空",
        trigger: "blur"
    }],
    deviceType: [{
        required: true,
        message: "请选择设备类型",
        trigger: "blur"
    }],
    protocolCode: [{
        required: true,
        message: "设备协议不能为空",
        trigger: "blur"
    }],
    transport: [{
        required: true,
        message: "传输协议不能为空",
        trigger: 'blur'
    }]

})

// 改变步骤
const changeStep = async (formEl: FormInstance | undefined, index: any) => {
    if (active.value == 1) {
        if (!formEl) return
        await formEl.validate(async (valid, fields) => {
            if (valid) {
                active.value = index
                return
            } else {
                console.log('error submit!', fields)
            }
        })
    } else {
        active.value = index
    }

}
/**获取上传图片的路径 */
const getImagePath = (data: string) => {
    state.ruleForm.imgUrl = data;
}
// 打开弹窗
const openDialog = async (type: string, row: any, productId: string) => {
    if (type === 'edit') {
        if (row != undefined) {
            getProduct(row.productId).then(response => {
                state.ruleForm = response.data.data
            })
        } else {
            getProduct(productId).then(response => {
                state.ruleForm = response.data.data
            })
        }

        resetState()
        state.dialog.title = '修改产品';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '新增产品';
        state.dialog.submitTxt = '新 增';
    }
    state.dialog.isShowDialog = true;
    getdictdata()
    generateData()
};
// 当选项改变时触发
const handleChange = (newValue: any, type: any) => {
    if (type == 'right') {
        state.ruleForm.modelIds = [...saveTransferValue.value, ...newValue] as any
        state.ruleForm.modelIds = [...new Set(state.ruleForm.modelIds)];

    }
    saveTransferValue.value = state.ruleForm.modelIds


    filterTableData();

};
//当下第二步拉框数据改变时
const handleNodeClick = (data: any) => {
    if (data.type == 1 && data.subType) {
        generateData(1, data.subType)
    } else {
        generateData(data.type)
    }
}
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
    active.value = 1
    selectedValue.value = '全部'
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
/** 获取产品信息 */
const getProductMsg = () => {
    getProduct(state.ruleForm.productId).then(response => {
        state.ruleForm = response.data.data;
        // changeProductCode(state.ruleForm.protocolCode);
    });
}
/**同步获取产品下的设备数量**/
const getDeviceCountByProductId = (productId: number) => {
    return new Promise((resolve, reject) => {
        deviceCount(productId).then(res => {
            resolve(res);
        }).catch(error => {
            reject(error);
        })
    })
}
/** 更新产品状态 */
const ChangeProductStatus = async (status: any) => {
    let message = "确定取消发布？";
    if (status == 2) {
        message = "产品发布后，可以创建对应的设备";
    } else if (status == 1) {
        let result = await getDeviceCountByProductId(state.ruleForm.productId) as any;
        if (result.data.data > 0) {
            message = "重要提示：产品下已有 " + result.data.data + " 个设备，取消发布可以修改产品信息和模型，重新发布后对应设备状态将会被重置！"
        }
    }
    ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
    })
        .then(() => {
            let data = {
                productId: '',
                status: '',
                deviceType: '' as any
            };
            data.productId = state.ruleForm.productId;
            data.status = status;
            data.deviceType = state.ruleForm.deviceType;
            changeProductStatus(data).then(response => {
                ElMessage.success(response.data.msg);
                emit('refresh');
                closeDialog();
                // activeName.value = "basic";
                // getProductMsg();
            }).catch(() => {
                if (status == 2) {
                    // activeName.value = "basic";
                } else {
                    // goBack();
                }
            });
        })
        .catch(() => {
            activeName.value = "basic";
        });
}
// 提交
const onSubmit = async (formEl: FormInstance | undefined, index: any) => {
    if (state.ruleForm.productId != '') {
        updateProduct(state.ruleForm).then(response => {
            //  刷新页面
            emit('refresh');
            closeDialog();
            if (response.data.code != 200) {
                ElMessage.error(response.data.msg);
            } else {
                ElMessage.success(response.data.msg);
            }

        });
    } else {
        addProduct(state.ruleForm).then(response => {
            //  刷新页面
            emit('refresh');
            ElMessage.success('新增成功');
            state.ruleForm = response.data.data
            closeDialog();
        });
    }
};
// 树形控件节点单击事件
const handleTreeClick = (data: { categoryId: any; }) => {

    state.ruleForm.categoryId = data.categoryId;
}
// 获取状态数据
const getdictdata = async () => {
    try {
        device_type_list.value = await dictStore.fetchDict('iot_device_type') //获取设备类型列表
        const data = {
            pageNum: 1,
            pageSize: 100
        }
        const statess = await listTemplate(data)//获取穿梭框列表
        allData.value = statess.data.rows
        filterTableData()
        const response = await listCategory(treeState.tableData.param); //获取产品分类树形列表
        treeState.tableData.data = handleTree(response.data.data, "categoryId") as any;

    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 获取穿梭框左侧列表数据
const generateData = async (type?: any, subType?: any) => {
    try {
        const data = {
            pageNum: 1,
            pageSize: 100,
            type: type,
            subType: subType
        }
        const states = await listTemplate(data)
        const formattedData = [] as any
        states.data.rows.forEach((item: any, index: any) => {
            formattedData.push({
                label: item.modelName,
                key: item.modelId,
                disabled: state.ruleForm.status == 2 ? true : false
            })
        })
        transferData.value = formattedData
        saveTransferValue.value = state.ruleForm.modelIds
    }
    catch (error) {
        console.error('Error fetching data:', error);
    }
}
// 根据穿梭框选择的内容modelId生成一个新的数组
const filterTableData = () => {
    tableData.value = allData.value.filter((item: any) => state.ruleForm.modelIds.includes(item.modelId));
    console.log(tableData.value, '第三步反显数据');


};

// 点击更改全部tabs信息
const handleClick = (tab: any, event: Event) => {
    console.log(tab.props.name)
}
// 点击更改属性tabs信息
const handlePropertyClick = (tab: any, event: Event) => {
    console.log(tab.props.name)
}
// 固有属性
const IntrinsicAttribute = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 1 && item.subType == 1) {
            listData.push(item)
        }
    })
    return listData
});
// 运维属性
const OperationAttribute = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 1 && item.subType == 2) {
            listData.push(item)
        }
    })
    return listData
});
// 遥测属性
const telemeterAttribute = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 1 && item.subType == 3) {
            listData.push(item)
        }
    })
    return listData
});
// 通讯属性
const communicatAttribute = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 1 && item.subType == 4) {
            listData.push(item)
        }
    })
    return listData
});
// 虚拟属性
const dummyAttribute = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 1 && item.subType == 5) {
            listData.push(item)
        }
    })
    return listData
});
// 功能
const functions = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 2) {
            listData.push(item)
        }
    })
    return listData
});
// 事件
const events = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 3) {
            listData.push(item)
        }
    })
    return listData
});
// 关系
const relationship = computed(() => {
    const listData = [] as any
    tableData.value.forEach((item: any) => {
        if (item.type == 4) {
            listData.push(item)
        }
    })
    return listData
});
// 设备类型
const deviceTypeLabel = computed(() => {
    const item = device_type_list.value.find((d: any) => d.dictValue == state.ruleForm.deviceType) as any
    return item ? item.dictLabel : '暂无数据';
});
// 设备类型treeState.tableData.data
const categoryLabel = computed(() => {
    const item = treeState.tableData.data.find((d: any) => d.categoryId == state.ruleForm.categoryId) as any
    return item ? item.categoryName : '暂无数据';
});
const tableDatas = ref([
    {
        "id": 1,
        "name": "固有属性",
        "children": IntrinsicAttribute
    },
    {
        "id": 2,
        "name": "运维属性",
        "children": OperationAttribute
    },
    {
        "id": 3,
        "name": "遥测属性",
        "children": telemeterAttribute
    },
    {
        "id": 4,
        "name": "通讯属性",
        "children": communicatAttribute
    },
    {
        "id": 5,
        "name": "虚拟属性",
        "children": dummyAttribute
    },
])
const filteredData = computed(() => {
    return tableDatas.value.filter(item => item.children && item.children.length > 0);
})
// 暴露变量
defineExpose({
    openDialog,
});

</script>
<style scoped>
:deep(.el-step__icon-inner) {
    font-size: 15px !important;
    cursor: pointer;
}

.el-transfer-panel {
    width: 400px;
    height: 500px;
}

.el-transfer-panel__list.is-filterable {
    height: 400px;
}

:deep(.edit_dev .el-transfer-panel) {
    width: 300px;
}

:deep(.edit_dev .el-transfer-panel__body) {
    height: 400px;
}

.custom-transfer {
    position: relative;
}

.transfer-header {
    position: absolute;
    top: 15px;
    left: 71px;
    /* width: 32%; */
    z-index: 1;
    background: white;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.el-transfer {
    margin-top: 60px;
    /* 调整间距以适应顶部内容 */
}

.transfer-header {
    z-index: 2 !important;
}

:deep(.el-tabs__new-tab) {
    display: block;
    border: none;
}

:deep(.el-icon .is-icon-close) {
    display: none;
}

.publish {
    width: 80px;
    position: relative;
    right: 60px;
    bottom: 5px;
    /* color: #ffffff; */
}

.contain {
    max-height: 600px;
    /* 限制容器的高度 */
    overflow: auto;
    /* 当内容超出时显示滚动条 */
}
</style>
