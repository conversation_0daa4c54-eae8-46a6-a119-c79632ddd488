<template>
    <el-form size="small">
        <!-- 小时，允许的通配符 -->
        <el-form-item>
            <el-radio v-model="radioValue" :label="1">
                小时，允许的通配符[, - * /]
            </el-radio>
        </el-form-item>

        <!-- 周期从 -->
        <el-form-item>
            <el-radio v-model="radioValue" :label="2">
                周期从
                <el-input-number v-model="cycle01" :min="0" :max="22" /> -
                <el-input-number v-model="cycle02" :min="cycle01 ? cycle01 + 1 : 1" :max="23" /> 小时
            </el-radio>
        </el-form-item>

        <!-- 从开始，每N小时执行一次 -->
        <el-form-item>
            <el-radio v-model="radioValue" :label="3">
                从
                <el-input-number v-model="average01" :min="0" :max="22" /> 小时开始，每
                <el-input-number v-model="average02" :min="1" :max="23 - average01 || 0" /> 小时执行一次
            </el-radio>
        </el-form-item>

        <!-- 指定 -->
        <el-form-item>
            <el-radio v-model="radioValue" :label="4">
                指定
                <el-select clearable v-model="checkboxList" placeholder="可多选" multiple style="width:100%">
                    <el-option v-for="item in 24" :key="item" :value="item - 1">{{ item - 1 }}</el-option>
                </el-select>
            </el-radio>
        </el-form-item>
    </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch, PropType } from 'vue';

// 定义props
const props = defineProps({
    check: {
        type: Function as PropType<(value: any, min: any, max: any) => any>,
        required: true
    },
    cron: Object as () => {
        week: string;
    }
});

// 定义emit事件
const emit = defineEmits<{
    (event: 'update', field: string, value: string, from: string): void;
}>();

// 定义响应式数据
const radioValue = ref<any>(1);
const cycle01 = ref<any>(0);
const cycle02 = ref<any>(1);
const average01 = ref<any>(0);
const average02 = ref<any>(1);
const checkboxList = ref<any[]>([]);

// 计算属性
const cycleTotal = computed(() => {
    const cycle01Value = props.check(cycle01.value, 0, 22);
    const cycle02Value = props.check(cycle02.value, cycle01Value ? cycle01Value + 1 : 1, 23);
    return `${cycle01Value}-${cycle02Value}`;
});

const averageTotal = computed(() => {
    const average01Value = props.check(average01.value, 0, 22);
    const average02Value = props.check(average02.value, 1, 23 - average01.value || 0);
    return `${average01Value}/${average02Value}`;
});

const checkboxString = computed(() => checkboxList.value.length ? checkboxList.value.join() : '*');

// 动态更新 emit
const updateHour = (value: string) => {
    emit('update', 'hour', value, 'hour');
};

// 观察响应式数据并处理相应逻辑
watch(radioValue, () => {
    switch (radioValue.value) {
        case 1:
            updateHour('*');
            break;
        case 2:
            updateHour(cycleTotal.value);
            break;
        case 3:
            updateHour(averageTotal.value);
            break;
        case 4:
            updateHour(checkboxString.value);
            break;
    }
});

watch(cycleTotal, () => {
    if (radioValue.value === 2) {
        updateHour(cycleTotal.value);
    }
});

watch(averageTotal, () => {
    if (radioValue.value === 3) {
        updateHour(averageTotal.value);
    }
});

watch(checkboxString, () => {
    if (radioValue.value === 4) {
        updateHour(checkboxString.value);
    }
});
defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList
});
</script>