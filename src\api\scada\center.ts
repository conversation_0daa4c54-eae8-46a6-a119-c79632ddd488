import request from '/@/utils/request';

// 查询组态中心列表
export function listCenter(query?: any) {
    return request({
        url: '/scada/center/list',
        method: 'get',
        params: query,
    });
}

// 查询组态中心详细
export function getCenter(id: string) {
    return request({
        url: '/scada/center/' + id,
        method: 'get',
    });
}

// 新增组态中心
export function addCenter(data: any) {
    return request({
        url: '/scada/center',
        method: 'post',
        data: data,
    });
}

// 修改组态中心
export function updateCenter(data: any) {
    return request({
        url: '/scada/center',
        method: 'put',
        data: data,
    });
}

// 删除组态中心
export function delCenter(id: string) {
    return request({
        url: '/scada/center/' + id,
        method: 'delete',
    });
}
