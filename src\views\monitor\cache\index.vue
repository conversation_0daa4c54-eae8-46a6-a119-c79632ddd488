<template>
    <div class="layout-padding">
        <el-row>
            <el-col :span="24" class="card-box">
                <el-card>
                    <div class="title"><span>基本信息</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium">
                        <table cellspacing="0" style="width: 100%; padding: 10px 20px;">
                            <tbody>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">Redis版本</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.redis_version }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">运行模式</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.redis_mode == "standalone" ?
                                            "单机" : "集群" }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">端口</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.tcp_port }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">客户端数</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.connected_clients }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">运行时间(天)</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.uptime_in_days }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">使用内存</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.used_memory_human }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">使用CPU</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{
                                            parseFloat(cache.info.used_cpu_user_children).toFixed(2) }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">内存配置</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.maxmemory_human }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">AOF是否开启</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.aof_enabled == "0" ? "否" : "是"
                                            }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">RDB是否成功</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.rdb_last_bgsave_status }}
                                        </div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">Key数量</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.dbSize">{{ cache.dbSize }} </div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">网络入口/出口</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="cache.info">{{ cache.info.instantaneous_input_kbps
                                            }}kps/{{ cache.info.instantaneous_output_kbps }}kps</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </el-col>
            <div class="card-box deep" style="display: flex;justify-content: space-between;width: 100%;">
                <el-card style="height: 500px; width: calc(49% + 10px);">
                    <div class="title">
                        <span>命令统计</span>
                    </div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <div ref="commandstats" style="height: 420px" />
                    </div>
                    <div style="height: 1px; border-bottom: 1px solid #ecf0f7; margin: 0 20px;"></div>
                </el-card>
                <el-card style="height: 500px; width: calc(49% + 10px);">
                    <div class="title"><span>内存信息</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <div ref="usedmemory" style="height: 420px" />
                    </div>
                    <div style="height: 1px; border-bottom: 1px solid #ecf0f7; margin: 0 20px;"></div>
                </el-card>
            </div>
        </el-row>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ElLoading } from 'element-plus';
import { getCache } from '/@/api/monitor/cache';
import * as echarts from "echarts";

const loadingInstance = ref<any | null>(null);
const cache = ref<any>({});
const commandstats = ref(null) as any // 统计命令信息
const usedmemory = ref(null) as any   // 使用内存
// 初始化表格数据
const getTableData = async () => {
    await getCache().then(response => {
        cache.value = response.data.data;
        loadingInstance.value.close()
        commandstats.value = echarts.init(commandstats.value, "macarons");
        commandstats.value.setOption({
            tooltip: {
                trigger: "item",
                formatter: "{a} <br/>{b} : {c} ({d}%)",
            },
            series: [
                {
                    name: "命令",
                    type: "pie",
                    roseType: "radius",
                    radius: [15, 95],
                    center: ["50%", "38%"],
                    data: cache.value.commandStats,
                    animationEasing: "cubicInOut",
                    animationDuration: 1000,
                }
            ]
        });
        usedmemory.value = echarts.init(usedmemory.value, "macarons");
        usedmemory.value.setOption({
            tooltip: {
                formatter: "{b} <br/>{a} : " + cache.value.info.used_memory_human,
            },
            series: [
                {
                    name: "峰值",
                    type: "gauge",
                    min: 0,
                    max: 1000,
                    detail: {
                        formatter: cache.value.info.used_memory_human,
                    },
                    data: [
                        {
                            value: parseFloat(cache.value.info.used_memory_human),
                            name: "内存消耗",
                        }
                    ]
                }
            ]
        });
    });
}
const openLoading = () => {
    loadingInstance.value = ElLoading.service({
        text: '正在加载缓存监控数据，请稍候！',
        background: 'rgba(0, 0, 0, 0.7)', // 自定义背景色
    });
};
// 页面加载时
onMounted(() => {
    getTableData();
    openLoading()
});
</script>

<style scoped>
.card-box {
    margin-bottom: 20px;
}

:deep(.el-card__body) {
    padding: 15px 0 20px 0 !important;
}

.padd {
    padding: 0 20px;
}

.title {
    height: 30px;
    padding-left: 20px;
    border-bottom: 1px solid #e6ebf5;
    margin-bottom: 10px;
}
</style>