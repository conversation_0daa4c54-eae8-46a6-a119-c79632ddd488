<template>
    <div class="full-screen-wrap" :style="{ height: contentHeight + 'px' }">
        <topo-render :defaultValue="selectedValue" :isShare="isShare" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import TopoRender from './components/topoRender.vue';

const route = useRoute();
const contentHeight = ref(500);
const selectedValue = ref(100);
const isShare = ref(false);

onMounted(() => {
    if (route.query.isShare) {
        isShare.value = true;
    }
    calculateContentHeight();
    window.addEventListener('resize', calculateContentHeight, true);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateContentHeight, true);
});

// 获取窗体高度
const calculateContentHeight = () => {
    const appElement = document.getElementById('app');
    if (appElement) {
        contentHeight.value = appElement.offsetHeight;
    }
};
</script>

<style scoped>
.full-screen-wrap {
    position: relative;
    width: 100%;
    height: 500px;
}
</style>
