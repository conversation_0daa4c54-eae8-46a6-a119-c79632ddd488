<template>
  <div class="view-image-common" :style="animationClass" :id="detail.identifier">
    <img
        :style="[filterClass]"
        :src="imageURL"
        @dragstart.prevent
        @dragover.prevent
        @drop.prevent
    />
    <svg id="svg">
      <defs>
        <filter :id="`${detail.identifier}_svg`">
          <feColorMatrix
              color-interpolation-filters="sRGB"
              type="matrix"
              :values="hexTofeColorMatrix(detail.style.foreColor)"
          />
        </filter>
      </defs>
    </svg>
    <!-- 非渲染依赖 -->
    <div v-show="false">{{ colorChange }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch ,watchEffect,defineOptions } from 'vue'
import topoUtil from '/@/utils/topo/topo-util'
import { getAnimate } from '/@/utils/topo/anime'
defineOptions({
  name: 'ViewImage'
})
import { useCounterStore } from '/@/stores/counterStore';
const counterStore = useCounterStore();
// Store
const mqttData = computed(() => counterStore.mqttData)

// Props
const props = defineProps<{
  detail: {
    identifier: string
    style: {
      url?: string
      foreColor?: string
      isFilter?: boolean
      hdClassName?: string
    }
    dataAction: {
      serialNumber?: string
      identifier?: string
      paramJudge?: string
      paramJudgeData?: any
      translateList?: Array<{ direction: string; position: number }>
      duration?: number
      hdAction?: boolean
    }
    dataBind: {
      serialNumber?: string
      identifier?: string
      xyAction?: boolean
      hdAction?: boolean
      stateList?: Array<{ paramCondition?: string; paramData?: any; foreColor?: string }>
    }
  }
  editMode?: boolean
}>()

// Refs to expose
const animateView = ref<any>(null)

// Computed
const filterClass = ref({
  width: '100%',
  height: '100%',
  filter: '',
  position: 'absolute',
  animation: props.detail.style.hdClassName + ' 5s infinite',
})

const animationClass = ref<Record<string, string>>({})

const imageURL = computed(() => props.detail.style.url || null)

const colorChange = computed(() => {
  return props.detail.style.foreColor
})

watch(
    () => props.detail.style,
    (newStyle) => {
      if (newStyle.foreColor && newStyle.isFilter) {
        filterClass.value.marginLeft = ''
        filterClass.value.filter = `url(#${props.detail.identifier}_svg)`
        animationClass.value = {}
      } else if (newStyle.foreColor && newStyle.isFilter === false) {
        filterClass.value.marginLeft = '-10000px'
        filterClass.value.filter = `drop-shadow(5000px 0px ${newStyle.foreColor})`
        animationClass.value = {
          overflow: 'hidden',
          position: 'relative',
        }
      } else {
        animationClass.value = {}
        filterClass.value.marginLeft = ''
        filterClass.value.filter = ''
      }
    },
    { deep: true, immediate: true }
)

watchEffect(() => {
  if (
      props.editMode &&
      props.detail.dataAction.translateList &&
      props.detail.dataBind.hdAction
  ) {
    const translates = []
    for (const element of props.detail.dataAction.translateList) {
      if (element.direction === '竖直') {
        translates.push({ translateY: -element.position })
      } else {
        translates.push({ translateX: element.position })
      }
    }

    const domId = document.getElementById(props.detail.identifier)
    removeAnimate(domId)
    translateAnimate(domId, translates, props.detail.dataAction.duration * 1000, true, false)
  }
})

watch(
    () => mqttData.value,
    (newMqttData) => {
      if (!newMqttData) return

      // 动作初始化逻辑
      const dataAction = props.detail.dataAction
      if (
          dataAction.serialNumber &&
          dataAction.identifier &&
          dataAction.paramJudge &&
          dataAction.paramJudgeData !== undefined &&
          newMqttData.serialNumber === dataAction.serialNumber
      ) {
        const message = newMqttData.message.find((item) => item.id === dataAction.identifier)
        if (message) {
          const val = message.value
          const isGd = topoUtil.judgeSize(dataAction.paramJudge, val, dataAction.paramJudgeData)
          const dom = document.getElementById(props.detail.identifier)

          if (isGd) {
            if (props.detail.dataBind.xyAction && dom) {
              getAnimate().set(dom, { display: 'block' })
            }
            if (animateView.value) {
              animateView.value.play()
            }
          } else {
            if (props.detail.dataBind.xyAction && dom) {
              getAnimate().set(dom, { display: 'none' })
            }
            if (animateView.value) {
              animateView.value.pause()
            }
          }
        }
      }

      // 颜色初始化逻辑
      const dataBind = props.detail.dataBind
      if (
          dataBind.identifier &&
          newMqttData &&
          newMqttData.serialNumber === dataBind.serialNumber
      ) {
        const message = newMqttData.message.find((item) => item.id === dataBind.identifier)
        if (message) {
          const val = message.value
          for (const element of dataBind.stateList ?? []) {
            const isSure = topoUtil.judgeSize(element.paramCondition, val, element.paramData)
            if (isSure) {
              detail.style.foreColor = element.foreColor
            }
          }
        }
      }
    },
    { deep: true, immediate: true }
)

// Methods
function hexTofeColorMatrix(rgba: string): string {
  if (!rgba) return ''

  const hex =
      '#' +
      rgba
          .match(/\d+/g)
          ?.map((d) => (+d).toString(16))
          .join('')

  if (!hex) return ''

  const hexCleaned = hex.replace('#', '')
  const RGB = []
  const numberList = [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0]

  for (let i = 0; i < hexCleaned.length; i += 2) {
    const firstDigit = parseInt(hexCleaned[i], 16)
    const firstDigitPartial = firstDigit * 16
    let RGBValue = parseInt(hexCleaned[i + 1], 16) + firstDigitPartial
    RGBValue /= 255
    RGB.push(RGBValue.toFixed(2))
  }

  numberList[0] = RGB[0]
  numberList[6] = RGB[1]
  numberList[12] = RGB[2]

  return numberList.join(' ')
}

function removeAnimate(dom) {
  if (dom && dom._animeInstance) {
    dom._animeInstance.pause()
    dom._animeInstance = null
  }
}

function translateAnimate(dom, transforms, duration, loop, reverse) {
  if (!dom) return
  const anime = getAnimate()
  const timeline = anime.timeline({
    duration,
    loop,
    direction: reverse ? 'alternate' : 'normal',
  })

  transforms.forEach((transform) => {
    timeline.add({ targets: dom, ...transform })
  })

  dom._animeInstance = timeline
}

// Expose methods to parent component
defineExpose({
  animateView,
})
</script>

<style lang="scss" scoped>
.view-image-common {
  height: 100%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>