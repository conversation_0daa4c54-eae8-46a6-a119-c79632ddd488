<template>
    <div style="padding-left:20px;">
        <el-form :model="state.tableData.param" ref="queryForm" :inline="true" v-show="state.tableData.showSearch"
            label-width="68px">
            <el-form-item label="日志类型" prop="logType">
                <el-select style="width: 200px;" v-model="state.tableData.param.logType" placeholder="请选择类型" clearable
                    size="default">
                    <el-option v-for="dict in event_typelist" :key="dict.dictValue" :label="dict.dictLabel"
                        :value="dict.dictValue" />
                </el-select>
            </el-form-item>
            <el-form-item label="标识符" prop="identity">
                <el-input v-model="state.tableData.param.identity" placeholder="请输入标识符" clearable size="default"
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="时间范围">
                <el-date-picker v-model="daterangeTime" style="width: 240px" size="default"
                    date-format="YYYY-MM-DD" value-format="YYYY-MM-DD" type="daterange" range-separator="-"
                    start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="default"
                    @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
                <el-button size="default" @click="resetQuery"> <el-icon><ele-Refresh /></el-icon>重置</el-button>
            </el-form-item>
        </el-form>

        <el-table v-loading="state.tableData.loading" :data="state.tableData.data" size="default"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" border>
            <el-table-column label="类型" align="center" prop="logType" width="120">
                <template #default="scope">
                    <dict-tag :options="event_type_list" :value="scope.row.logType" />
                </template>
            </el-table-column>
            <el-table-column label="模式" align="center" prop="logType" width="120">
                <template #default="scope">
                    <el-tag type="primary" v-if="scope.row.mode == 1">影子模式</el-tag>
                    <el-tag type="success" v-else-if="scope.row.mode == 2">在线模式</el-tag>
                    <el-tag type="info" v-else>其他信息</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="时间" align="center" prop="createTime" width="150">
                <template #default="scope">
                    <span>{{ scope.row.createTime }}</span>
                </template>
            </el-table-column>
            <el-table-column label="标识符" align="center" prop="identity" />
            <el-table-column label="动作" align="center" header-align="center" prop="logValue">
                <template #default="scope">
                    <div v-html="formatValueDisplay(scope.row)"></div>
                </template>
            </el-table-column>

            <el-table-column label="备注" header-align="center" align="center" prop="remark">
                <template #default="scope">
                    {{ scope.row.remark == null ? "无" : scope.row.remark }}
                </template>
            </el-table-column>

        </el-table>
        <div style="height:40px;">
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </div>

    </div>
</template>
<script setup lang="ts" name="">
import { reactive, ref, watch } from 'vue';
import { listEventLog } from '/@/api/iot/eventLog';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addDateRange } from '/@/utils/next';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义 props
const props = defineProps({
    device: {
        type: Object
    }
})
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        showSearch: true,
        param: {
            pageNum: 1,
            pageSize: 10,
            logType: '',
            identity: '',
            serialNumber: ''
        },
    },
});
const daterangeTime = ref<[string, string]>(['', '']); //时间范围
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const event_typelist = ref<Option[]>([]);
const event_type_list = ref<Option[]>([]);
let thingsModel = reactive({
    properties: [] as any,
    functions: [] as any,
    events: [] as any,
});
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const data = addDateRange(state.tableData.param, daterangeTime.value)
        const response = await listEventLog(data);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getTableData();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        logType: '',
        identity: '',
        serialNumber: ''
    };
    daterangeTime.value = ['', ''];
    handleQuery();
}
/** 格式化显示数据定义 */
const formatValueDisplay = (row: any) => {
    // 类型（1=属性上报，2=调用功能，3=事件上报，4=设备升级，5=设备上线，6=设备离线）
    if (row.logType == 1) {
        let propertyItem = getThingsModelItem(1, row.identity);
        if (propertyItem != "") {
            return (propertyItem.parentName ? '[' + propertyItem.parentName + (propertyItem.arrayIndex ? propertyItem.arrayIndex : '') + '] ' : '') +
                propertyItem.name +
                '： <span style="color:#409EFF;">' + getThingsModelItemValue(propertyItem, row.logValue) + ' ' +
                (propertyItem.datatype.unit != undefined ? propertyItem.datatype.unit : '') + '</span>';
        }
    } else if (row.logType == 2) {
        let functionItem = getThingsModelItem(2, row.identity);
        if (functionItem != "") {
            return (functionItem.parentName ? '[' + functionItem.parentName + (functionItem.arrayIndex ? functionItem.arrayIndex : '') + '] ' : '') +
                functionItem.name +
                '： <span style="color:#409EFF">' + getThingsModelItemValue(functionItem, row.logValue) + ' ' +
                (functionItem.datatype.unit != undefined ? functionItem.datatype.unit : '') + '</span>';
        }
    } else if (row.logType == 3) {
        let eventItem = getThingsModelItem(3, row.identity);
        if (eventItem != "") {
            return (eventItem.parentName ? '[' + eventItem.parentName + (eventItem.arrayIndex ? eventItem.arrayIndex : '') + '] ' : '') +
                eventItem.name +
                '： <span style="color:#409EFF">' + getThingsModelItemValue(eventItem, row.logValue) + ' ' +
                (eventItem.datatype.unit != undefined ? eventItem.datatype.unit : '') + '</span>';
        } else {
            return row.logValue;
        }
    } else if (row.logType == 4) {
        return '<span style="font-weight:bold">设备升级</span>';
    } else if (row.logType == 5) {
        return '<span style="font-weight:bold">设备上线</span>';
    } else if (row.logType == 6) {
        return '<span style="font-weight:bold">设备离线</span>';
    }
    return "";
}
/** 获取物模型项中的值*/
const getThingsModelItemValue = (item: any, oldValue: any) => {
    // 枚举和布尔转换为文字
    if (item.datatype.type == "bool") {
        if (oldValue == "0") {
            return item.datatype.falseText;
        } else if (oldValue == "1") {
            return item.datatype.trueText;
        }
    } else if (item.datatype.type == "enum") {
        for (let i = 0; i < item.datatype.enumList.length; i++) {
            if (oldValue == item.datatype.enumList[i].value) {
                return item.datatype.enumList[i].text;
            }
        }
    }
    return oldValue;
}
/** 获取物模型中的项*/
const getThingsModelItem = (type: any, identity: any) => {
    if (type == 1 && thingsModel.properties) {
        for (let i = 0; i < thingsModel.properties.length; i++) {
            //普通类型 integer/decimal/string/emum//bool
            if (thingsModel.properties[i].id == identity) {
                return thingsModel.properties[i];
            }
            // 对象 object
            if (thingsModel.properties[i].datatype.type == "object") {
                for (let j = 0; j < thingsModel.properties[i].datatype.params.length; j++) {
                    if (thingsModel.properties[i].datatype.params[j].id == identity) {
                        thingsModel.properties[i].datatype.params[j].parentName = thingsModel.properties[i].name;
                        return thingsModel.properties[i].datatype.params[j];
                    }
                }
            }
            let realIdentity = identity;
            let arrayIndex = 0;
            // 数组 array
            if (thingsModel.properties[i].datatype.type == "array" && thingsModel.properties[i].datatype.arrayType) {
                if (thingsModel.properties[i].datatype.arrayType == "object") {
                    // 数组元素格式：array_01_parentId_humidity,array_01_前缀终端上报时加上，物模型中没有

                    if (identity.indexOf("array_") > -1) {
                        arrayIndex = identity.substring(6, 8);
                        realIdentity = identity.substring(9);
                    }
                    for (let j = 0; j < thingsModel.properties[i].datatype.params.length; j++) {
                        if (thingsModel.properties[i].datatype.params[j].id == realIdentity) {
                            // 标注索引和父级名称
                            thingsModel.properties[i].datatype.params[j].arrayIndex = Number(arrayIndex) + 1;
                            thingsModel.properties[i].datatype.params[j].parentName = thingsModel.properties[i].name;
                            return thingsModel.properties[i].datatype.params[j];
                        }
                    }
                } else {
                    // 普通类型
                    for (let j = 0; j < thingsModel.properties[i].datatype.arrayCount.length; j++) {
                        if (thingsModel.properties[i].id == realIdentity) {
                            thingsModel.properties[i].arrayIndex = Number(arrayIndex) + 1;
                            thingsModel.properties[i].parentName = "元素";
                            return thingsModel.properties[i];
                        }
                    }
                }

            }
        }
    } else if (type == 2 && thingsModel.functions) {
        for (let i = 0; i < thingsModel.functions.length; i++) {
            //普通类型 integer/decimal/string/emum/bool
            if (thingsModel.functions[i].id == identity) {
                return thingsModel.functions[i];
            }
            // 对象 object
            if (thingsModel.functions[i].datatype.type == "object") {
                for (let j = 0; j < thingsModel.functions[i].datatype.params.length; j++) {
                    if (thingsModel.functions[i].datatype.params[j].id == identity) {
                        thingsModel.functions[i].datatype.params[j].parentName = thingsModel.functions[i].name;
                        return thingsModel.functions[i].datatype.params[j];
                    }
                }
            }
            // 数组 array
            if (thingsModel.functions[i].datatype.type == "array" && thingsModel.functions[i].datatype.arrayType) {
                // 数组元素格式：array_01_parentId_humidity,array_01_前缀终端上报时加上，物模型中没有
                let realIdentity = identity;
                let arrayIndex = 0;
                if (identity.indexOf("array_") > -1) {
                    arrayIndex = identity.substring(6, 8);
                    realIdentity = identity.substring(9);
                }
                if (thingsModel.functions[i].datatype.arrayType == "object") {
                    for (let j = 0; j < thingsModel.functions[i].datatype.params.length; j++) {
                        if (thingsModel.functions[i].datatype.params[j].id == realIdentity) {
                            // 标注索引和父级名称
                            thingsModel.functions[i].datatype.params[j].arrayIndex = Number(arrayIndex) + 1;
                            thingsModel.functions[i].datatype.params[j].parentName = thingsModel.functions[i].name;
                            return thingsModel.functions[i].datatype.params[j];
                        }
                    }
                } else {
                    // 普通类型
                    for (let j = 0; j < thingsModel.functions[i].datatype.arrayCount.length; j++) {
                        if (thingsModel.functions[i].id == realIdentity) {
                            thingsModel.functions[i].arrayIndex = Number(arrayIndex) + 1;
                            thingsModel.functions[i].parentName = "元素";
                            return thingsModel.functions[i];
                        }
                    }
                }

            }
        }
    } else if (type == 3 && thingsModel.events) {
        for (let i = 0; i < thingsModel.events.length; i++) {
            if (thingsModel.events[i].id == identity) {
                return thingsModel.events[i];
            }
        }
    }
    return "";
}
// 获取状态数据
const getdictdata = async () => {
    try {
        event_typelist.value = await dictStore.fetchDict('iot_event_type')
        event_type_list.value = await dictStore.fetchDict('iot_event_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 监听props的变化
watch(() => props.device, (newVal) => {
    console.log(newVal, 'newValnewValnewVal');

    try {
        if (newVal && newVal.deviceId != 0) {
            state.tableData.param.serialNumber = newVal.serialNumber;
            getTableData();
            getdictdata()
            thingsModel = newVal.cacheThingsModel;
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
</script>