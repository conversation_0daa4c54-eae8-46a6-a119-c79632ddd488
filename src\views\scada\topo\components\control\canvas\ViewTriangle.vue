<template>
    <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();

// Methods
const drapTriangle = (x1: number, y1: number, x2: number, y2: number, x3: number, y3: number, color: string, type: string) => {
  if (!elCanvas.value) return;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.beginPath();
  ctx.moveTo(x1, y1);
  ctx.lineTo(x2, y2);
  ctx.lineTo(x3, y3);
  (ctx as any)[type + 'Style'] = color;
  ctx.closePath();
  (ctx as any)[type]();
};

const onResize = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  const x1 = w / 2;
  const y1 = 0;
  const x2 = 0;
  const y2 = h;
  const x3 = w;
  const y3 = h;
  const color = getForeColor();
  drapTriangle(x1, y1, x2, y2, x3, y3, color, 'fill');
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};

// Lifecycle
onMounted(() => {
  onResize();
});
</script>
