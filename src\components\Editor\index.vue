<template>
    <div>
        <el-upload :action="uploadUrl" :before-upload="handleBeforeUpload" :on-success="handleUploadSuccess"
            :on-error="handleUploadError" name="file" :show-file-list="true" :headers="headers" style="display: none"
            ref="uploadRef" v-if="props.type == 'url'">
        </el-upload>
        <div ref="editor" class="rich-text-editor" :style="styles"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue';
import Quill from 'quill';
import 'quill/dist/quill.snow.css'; // 引入 Quill 的样式
import { ElMessage, ElUpload } from 'element-plus';
import { Session } from '/@/utils/storage';

// 定义 props
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    minHeight: {
        type: Number,
        default: 0
    },
    height: {
        type: Number,
        default: 0
    },
    // 上传文件大小限制(MB)
    fileSize: {
        type: Number,
        default: 5,
    },
    url_type: {
        default: 0,
    },
    /* 类型（base64格式、url格式） */
    type: {
        type: String,
        default: "url",
    },
});
// 用于切换上传类型
const uploadType = ref<string>('')
// 引用 uploadRef 和编辑器实例
const uploadRef = ref<InstanceType<typeof ElUpload> | null>(null); // el-upload 组件的引用
// 定义 emits
const emit = defineEmits(['update:modelValue']);
const uploadUrl = ref(import.meta.env.VITE_API_URL + "common/upload",) // 上传的图片服务器地址
const headers = ref({
    Authorization: "Bearer " + Session.get('token')
},)

// 使用 ref 来引用 DOM 元素
const editor = ref<HTMLElement | null>(null);
let quillInstance: Quill;

// 计算样式
const styles = computed(() => {
    let style: { minHeight?: string; height?: string } = {};
    if (props.minHeight) {
        style.minHeight = `${props.minHeight}px`;
    }
    if (props.height) {
        style.height = `${props.height}px`;
    }
    return style;
});
// 上传前校检格式和大小
const handleBeforeUpload = (file: { size: number; }) => {
    // 校检文件大小
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    return true;
}
const handleUploadSuccess = (res: {
    url: { code: number; fileName: string; }; code: number; fileName: string;
}, file: any) => {
    if (res.code == 200) {
        ElMessage.success("图片上传成功");
        // 获取光标所在位置
        let length = quillInstance.getSelection() as any
        if (props.url_type == 0) {
            // quill.insertEmbed(length, "image", process.env.VUE_APP_BASE_API + res.fileName);
            // 插入图片  res.url为服务器返回的图片地址
            quillInstance.insertEmbed(length.index, "image", import.meta.env.VITE_APP_BASE_API + res.fileName);
        } else {
            const baseUrl = 'http://' + window.location.host + import.meta.env.VITE_APP_BASE_API;
            quillInstance.insertEmbed(length.index, "image",baseUrl + res.fileName);
        }
        // 调整光标到最后
        quillInstance.setSelection(length.index + 1);
    } else {
        ElMessage.error("图片插入失败");
    }
}
// 插入图片到 Quill 编辑器中
const insertImageIntoEditor = (url: string) => {
    // 获取当前光标位置
    const range = quillInstance.getSelection();
    if (range) {
        // 插入图片到当前位置
        quillInstance.insertEmbed(range.index, 'image', url);
    }
};
const handleUploadError = () => {
    ElMessage.error("图片插入失败");
}
// 初始化 Quill 编辑器
onMounted(() => {
    if (editor.value) {
        quillInstance = new Quill(editor.value, {
            theme: 'snow',
            placeholder: "请输入内容",
            modules: {
                toolbar: [
                    ["bold", "italic", "underline", "strike"],       // 加粗 斜体 下划线 删除线
                    ["blockquote", "code-block"],                    // 引用  代码块
                    [{ list: "ordered" }, { list: "bullet" }],       // 有序、无序列表
                    [{ indent: "-1" }, { indent: "+1" }],            // 缩进
                    [{ size: ["small", false, "large", "huge"] }],   // 字体大小
                    [{ header: [1, 2, 3, 4, 5, 6, false] }],         // 标题
                    [{ color: [] }, { background: [] }],             // 字体颜色、字体背景颜色
                    [{ align: [] }],                                 // 对齐方式
                    ["clean"],                                       // 清除文本格式
                    ["link", "image", "video"]                       // 链接、图片、视频
                ],
                // 强制清除任何全局 CSS 或 Quill 默认的字体样式
                // formats: ["bold", "italic", "underline", "strike", "blockquote", "code-block", "list", "bullet", "header", "link", "image", "video"],

            }
        });
        // 通过 Quill 的 toolbar 自定义图片按钮的行为
        const toolbar = quillInstance.getModule("toolbar") as any;
        toolbar.addHandler("image", () => {
            // 触发上传框的点击事件
            uploadType.value = "image";  // 这里设置为 image 类型
            // 使用 querySelector 获取 input 元素
            const input: HTMLInputElement | null = uploadRef.value?.$el.querySelector('input[type="file"]');
            if (input) {
                input.click();  // 打开文件选择框
            }
        });
        // 确保初始化时没有任何格式（包括加粗）
        quillInstance.format('bold', false);
        // 初始化内容
        quillInstance.root.innerHTML = props.modelValue;

        // 监听 Quill 编辑器内容变化
        quillInstance.on('text-change', () => {
            const content = quillInstance.root.innerHTML;
            emit('update:modelValue', content);
        });
        // // 如果 type 为 'url'，则添加自定义图片上传处理
        // if (props.type === 'url') {
        //     const toolbar = quillInstance.getModule("toolbar");
        //     toolbar.addHandler("image", (value: any) => {
        //         uploadType.value = "image";
        //         if (value) {
        //             const input = uploadRef.value?.$refs.input;
        //             if (input) input.click();  // 激活上传文件选择框
        //         } else {
        //             quillInstance.format("image", false);  // 如果取消，删除图片
        //         }
        //     });
        // }

    }

});
// 监听 modelValue 变化，更新编辑器内容
watch(() => props.modelValue, (newValue) => {
    if (quillInstance && quillInstance.root.innerHTML !== newValue) {
        quillInstance.root.innerHTML = newValue;
    }

});
</script>

<style>
/* 确保没有加粗样式 */
.ql-editor {
    font-weight: normal !important;
    /* 取消加粗 */
}

.editor,
.ql-toolbar {
    white-space: pre-wrap !important;
    line-height: normal !important;
    font-weight: normal !important;
}


.quill-img {
    display: none;
}

.ql-snow .ql-tooltip[data-mode="link"]::before {
    content: "请输入链接地址:";
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
    border-right: 0px;
    content: "保存";
    padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
    content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
    content: "14px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="small"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="small"]::before {
    content: "10px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="large"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="large"]::before {
    content: "18px";
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value="huge"]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value="huge"]::before {
    content: "32px";
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
    content: "文本";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
    content: "标题1";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
    content: "标题2";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
    content: "标题3";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
    content: "标题4";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
    content: "标题5";
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
    content: "标题6";
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
    content: "标准字体";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
    content: "衬线字体";
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
    content: "等宽字体";
}
</style>