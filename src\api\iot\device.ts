import request from '/@/utils/request'; // 确保你有适当的类型声明

// 定义请求参数类型
interface QueryParams {
  [key: string]: any; // 或者根据你的具体请求参数来定义
}

interface Device {
  id: number;
  name: string;
  [key: string]: any;
}

// 查询未授权设备列表
// export function listUnAuthDevice(query: any) {
//   return request({
//     url: '/iot/device/unAuthlist',
//     method: 'get',
//     params: query,
//   });
// }

// 查询分组可添加设备分页列表
export function listDeviceByGroup(query: any) {
  return request<any>({
    url: '/iot/iotDevice/listByGroup',
    method: 'get',
    params: query,
  });
}

// 查询设备简短列表
export function listDeviceShort(query: any) {
  return request<any>({
    url: '/iot/iotDevice/shortList',
    method: 'get',
    params: query,
  });
}

// 查询设备详细
export function getDevice(deviceId: string | number) {
  return request<Device>({
    url: `/iot/iotDevice/${deviceId}`,
    method: 'get',
  });
}

// 根据设备编号查询设备详细
export function getDeviceBySerialNumber(serialNumber: string) {
  return request<Device>({
    url: `/iot/iotDevice/getDeviceBySerialNumber/${serialNumber}`,
    method: 'get',
  });
}


// 查询设备运行状态详细
export function getDeviceRunningStatus(params: QueryParams) {
  return request({
    url: '/iot/iotDevice/runningStatus',
    method: 'get',
    params,
  });
}

// 新增设备
export function addDevice(data: any) {
  return request({
    url: '/iot/iotDevice',
    method: 'post',
    data,
  });
}

// 修改设备
export function updateDevice(data: any) {
  return request({
    url: '/iot/iotDevice',
    method: 'put',
    data,
  });
}

// 删除设备
export function delDevice(deviceId: string | number) {
  return request({
    url: `/iot/iotDevice/${deviceId}`,
    method: 'delete',
  });
}

// 生成设备编号
export function generatorDeviceNum(params: QueryParams) {
  return request({
    url: '/iot/iotDevice/generator',
    method: 'get',
    params,
  });
}

// MQTT连接参数查看
export function getMqttConnect(params: QueryParams) {
  return request({
    url: '/iot/iotDevice/getMqttConnectData',
    method: 'get',
    params,
  });
}
// 查询分组可添加设备分页列表
export function ListThingsModel(deviceId: any) {
  return request<any>({
    url: `/iot/iotDevice/thingsModelList/${deviceId}`,
    method: 'get',
  });
}
// 查询井筒设备信息
export function listWellbore(deviceId: any) {
  return request<any>({
    url: `/iot/wellbore/${deviceId}`,
    method: 'get',
  });
}
