<template>
  <div class="view-chart-water" ref="chartView" :id="detail.identifier">
    <!-- 隐藏的依赖项，确保响应式更新 -->
    <div v-show="false">{{ chartsValue }}{{ width }}{{ height }}{{ detail.style.waterColor }}{{ typeChange }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useCounterStore } from '/@/stores/counterStore';
import * as echarts from 'echarts';
import 'echarts-liquidfill'; // 引入水球图的组件

// 定义组件名称
defineOptions({
  name: 'ViewChartWater'
});

// 定义 props
interface Props {
  editMode?: boolean;
  selected?: boolean;
  detail: any;
}

const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  selected: false,
  detail: () => ({
    identifier: 'water-chart-' + Date.now(),
    style: {
      position: { w: 200, h: 200 },
      waterShape: 'circle',
      waterColor: '#1cb5fc',
      waterFontSize: 14,
      waterBorderWidth: 2,
      waterBorderColor: '#1890ff',
      waterBackColor: 'transparent'
    },
    dataBind: {
      paramValue: 50
    }
  })
});

// 定义 emits
const emit = defineEmits(['refreshData', 'resize']);

// 使用 Pinia store
const counterStore = useCounterStore();
const { mqttData } = storeToRefs(counterStore);


// 监听 mqttData 的变化
watch(
  mqttData,
  (newData, oldData) => {
  },
  { deep: true, immediate: true }
);
// 响应式数据
const chartView = ref<HTMLElement>();
const paramValue = ref(0);
const myChart = ref<any>(null);
const refreshTimer = ref<NodeJS.Timeout | null>(null);

// 计算属性
const chartsValue = computed(() => {
  console.log(mqttData.value)
  // 将回调延迟到下次DOM更新循环之后执行。在修改数据之后立即使用它，然后等待DOM更新
  if (props.detail.dataBind.identifier && mqttData.value && mqttData.value.serialNumber == props.detail.dataBind.serialNumber) {
    const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataBind.identifier);
    if (message) {
      let paramVal = message.value;
      if (isNaN(paramVal)) {
        paramVal = 0;
      }
      paramValue.value = paramVal;
    }
  }
  return props.detail.dataBind.paramValue;
});

const typeChange = computed(() => {
  return props.detail.style.waterShape;
});

const width = computed(() => {
  return props.detail.style?.position?.w || 200;
});

const height = computed(() => {
  return props.detail.style?.position?.h || 200;
});

// 监听尺寸变化并调整图表大小
watch([width, height], ([newWidth, newHeight], [oldWidth, oldHeight]) => {
  if (myChart.value && (newWidth !== oldWidth || newHeight !== oldHeight)) {
    nextTick(() => {
      try {
        // 调用ECharts的resize方法，让它自动适应容器
        myChart.value.resize();
        console.log('📏 图表尺寸已调整:', `${newWidth}x${newHeight}`);
      } catch (error) {
        console.error('❌ 调整图表尺寸失败:', error);
      }
    });
  }
}, { immediate: false });
const option = computed(() => {
  let waterData = paramValue.value / 100;

  // 确保数据在合理范围内
  if (isNaN(waterData) || waterData < 0) {
    waterData = 0;
  } else if (waterData > 1) {
    waterData = 1;
  }

  console.log('水球图配置更新:', {
    paramValue: paramValue.value,
    waterData: waterData,
    waterColor: props.detail.style?.waterColor,
    waterShape: props.detail.style?.waterShape
  });

  let tempOption = {
    series: [
      {
        // 液位图
        type: 'liquidFill',
        //显示文字
        label: {
          formatter: function (param: any) {
            return Math.round(param.value * 100) + '%';
          },
          fontSize: props.detail.style?.waterFontSize || 14,
          position: ['50%', '50%'],
          color: '#000', // 确保文字颜色可见
        },
        //波浪颜色
        color: [props.detail.style?.waterColor || '#1890ff'],
        //鼠标放上显示
        tooltip: {
          show: true,
          formatter: function(param: any) {
            return `液位: ${Math.round(param.value * 100)}%`;
          }
        },
        //transparent
        backgroundStyle: {
          //边框宽度
          borderWidth: props.detail.style?.waterBorderWidth || 2,
          borderColor: props.detail.style?.waterBorderColor || '#1890ff',
          //背景色
          color: props.detail.style?.waterBackColor || 'transparent',
        },
        data: [waterData],
        // 液位图类型 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow',svg路径,container(充满容器)
        shape: props.detail.style?.waterShape || 'circle',
        name: '液位',
        outline: {
          //边框
          show: false,
        },
        //初始动画加载时间，毫秒
        animationDuration: 1000,
        //数据更改加载时间，毫秒
        animationDurationUpdate: 2000,
        //数据更改样式
        animationEasingUpdate: 'cubicOut',
        //波浪坡度
        amplitude: 5,
        //液体滚动速度
        period: function () {
          // 滚动毫秒
          return 2000;
        },
      },
    ],
  };

  return tempOption;
});
// 监听器
watch(
  option,
  (newVal, oldVal) => {
    // 数据自动刷新，必然需要一个监听机制告诉Echarts重新设置数据
    if (myChart.value) {
      if (newVal) {
        myChart.value.setOption(newVal);
      } else {
        myChart.value.setOption(oldVal);
      }
    } else {
      initEchart();
    }
  },
  { deep: true } // 对象内部属性的监听
);

// 方法
const initEchart = () => {
  try {
    initChartWithEcharts(echarts);
  } catch (error) {
    console.error('初始化 ECharts 失败:', error);
  }
};

const initChartWithEcharts = (echarts: any) => {

  if (!echarts) {
    console.error('ECharts 实例为空');
    return;
  }

  if (!props.detail.identifier) {
    console.error('组件标识符为空');
    return;
  }

  const dom = document.getElementById(props.detail.identifier);

  if (!dom) {
    console.error('找不到 DOM 元素:', props.detail.identifier);
    return;
  }

  // 确保容器样式正确，让它填充父容器
  dom.style.width = '100%';
  dom.style.height = '100%';
  dom.style.position = 'relative';

  // 如果已存在图表实例，先销毁
  if (myChart.value) {
    myChart.value.dispose();
  }

  try {
    myChart.value = echarts.init(dom);
    console.log(option.value)
    // 设置图表配置
    myChart.value.setOption(option.value);
    myChart.value.on('error', (error: any) => {
      console.error('水球图渲染错误:', error);
    });

  } catch (error) {
    console.error('设置图表配置失败:', error);

    // 如果 liquidfill 失败，尝试显示一个简单的饼图作为替代
    try {
      const fallbackOption = {
        title: {
          text: '液位: ' + Math.round(paramValue.value) + '%',
          left: 'center',
          top: 'center',
          textStyle: {
            color: props.detail.style?.waterColor || '#1890ff',
            fontSize: props.detail.style?.waterFontSize || 14
          }
        },
        series: [{
          type: 'pie',
          radius: ['60%', '80%'],
          center: ['50%', '50%'],
          data: [
            {
              value: paramValue.value,
              name: '液位',
              itemStyle: {
                color: props.detail.style?.waterColor || '#1890ff'
              }
            },
            {
              value: 100 - paramValue.value,
              name: '空余',
              itemStyle: {
                color: '#f0f0f0'
              }
            }
          ],
          label: {
            show: false
          }
        }]
      };
      myChart.value.setOption(fallbackOption);
    } catch (fallbackError) {
      console.error('设置替代图表也失败:', fallbackError);
    }
  }
};

// 启动定时刷新
const startAutoRefresh = () => {
  // 每1分钟自动刷新数据
  refreshTimer.value = setInterval(() => {
    if (myChart.value && option.value) {
      myChart.value.setOption(option.value, true); // true 表示不合并，直接替换
    }
  }, 60000); // 60秒 = 1分钟
};

// 停止定时刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value);
    refreshTimer.value = null;
  }
};

// 生命周期
onMounted(() => {
  // 使用 nextTick 确保 DOM 完全渲染
  nextTick(() => {
    setTimeout(() => {
      initEchart();

      // 启动自动刷新
      startAutoRefresh();
    }, 100); // 延迟100ms确保DOM完全准备好
  });
});

onUnmounted(() => {
  // 清理定时器
  stopAutoRefresh();
  // 销毁图表实例
  if (myChart.value) {
    myChart.value.dispose();
    myChart.value = null;
  }
});
</script>

<style lang="scss">
.view-chart-water {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // 确保容器有明确的尺寸
  &:empty {
    display: flex;
    justify-content: center;
    align-items: center;

    &::before {
      content: '水球图加载中...';
      color: #999;
      font-size: 14px;
    }
  }
}
</style>