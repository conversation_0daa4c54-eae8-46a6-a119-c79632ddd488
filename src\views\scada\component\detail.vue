<template>
    <div class="component-detail-wrap">
        <el-row :gutter="12">
            <el-col :span="12">
                <div class="card-wrap">
                    <el-tabs class="tabs-wrap" type="border-card">
                        <el-tab-pane label="Template">
                            <MonacoEditor ref="templateEditorRef"
                                v-model="form.componentTemplate"
                                height="75vh"
                                language="html"
                                @change="handleTemplateEditorChange"></MonacoEditor>
                        </el-tab-pane>
                        <el-tab-pane label="CSS">
                            <MonacoEditor ref="styleEditorRef"
                                v-model="form.componentStyle"
                                height="75vh"
                                language="css"
                                @change="handleStyleEditorChange"></MonacoEditor>
                        </el-tab-pane>
                        <el-tab-pane label="Script">
                            <MonacoEditor ref="scriptEditorRef"
                                v-model="form.componentScript"
                                language="javascript"
                                height="75vh"
                                @change="handleScriptEditorChange"></MonacoEditor>
                        </el-tab-pane>
                    </el-tabs>
                    <div class="tools-wrap">
                        <el-button class="item-btn" style="color: #e6a23c" type="primary" link @click="handleSubmitForm"
                            v-auths="['scada:component:edit']">
                            <svg-icon icon-class="save" />
                            保存
                        </el-button>
                        <el-button class="item-btn" type="primary" link :icon="Refresh" @click="handleRun">运行</el-button>
                    </div>
                </div>
            </el-col>
            <el-col :span="12">
                <el-card class="card-wrap">
                    <template #header>
                      <span>预览</span>
                    </template>
                    <div class="preview-content">
                        <div ref="componentResultRef" style="height: 70vh; width: 100%; padding: 10px;"></div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, createApp } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElLoading } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import html2canvas from 'html2canvas';
import { getComponent, updateComponent } from '/@/api/scada/component';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import MonacoEditor from '/@/components/MonacoEditor/index.vue';

// 定义组件名称
defineOptions({
    name: 'ComponentDetail'
});

// 路由
const route = useRoute();

// 响应式数据
const form = ref<any>({
    componentTemplate: '',
    componentStyle: '',
    
    componentScript: '',
    base64: ''
});

// 引用
const templateEditorRef = ref();
const styleEditorRef = ref();
const scriptEditorRef = ref();
const componentResultRef = ref();

// 获取详情
const getDetail = () => {
    getComponent(route.query.id as string).then((res) => {
      res=res.data
        if (res.code === 200) {
            form.value = res.data;
            // 由于使用了 v-model，数据会自动同步到编辑器
            loadData();
        }
    });
};

// 加载数据
const loadData = () => {
    let template = form.value.componentTemplate;
    if (!template) return;

    // 处理样式
    let styleCss = form.value.componentStyle;
    if (styleCss) {
        // 移除之前的样式
        const existingStyle = document.getElementById('component-custom-style');
        if (existingStyle) {
            existingStyle.remove();
        }

        let style = document.createElement('style');
        style.id = 'component-custom-style';
        style.innerHTML = styleCss;
        document.head.appendChild(style);
    }

    let script = form.value.componentScript;
    if (script) {
        script = script.replace(/export default/, 'return');
    }

    try {
        let obj = new Function(script)();
        obj.template = template;

        // 清空之前的内容
        if (componentResultRef.value?.innerHTML) {
            componentResultRef.value.innerHTML = '';
        }

        let newDiv = document.createElement('div');
        newDiv.setAttribute('id', 'component-result');
        componentResultRef.value?.appendChild(newDiv);

        // 使用 Vue 3 的 createApp 创建应用
        const app = createApp(obj);

        // 注册 Element Plus 组件库
        app.use(ElementPlus);

        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        // 注册 Element Plus 组件和消息服务
        app.config.globalProperties.$message = ElMessage;
        app.mount('#component-result');
    } catch (error: any) {
      // eslint-disable-next-line no-console
        console.error('组件加载失败:', error);
        ElMessage.error('组件加载失败，请检查代码: ' + (error?.message || '未知错误'));
    }
};

// 编辑器变化处理
const handleTemplateEditorChange = (data: string) => {
    form.value.componentTemplate = data;
};

const handleStyleEditorChange = (data: string) => {
    form.value.componentStyle = data;
};

const handleScriptEditorChange = (data: string) => {
    form.value.componentScript = data;
};

// 运行
const handleRun = () => {
    loadData();
};

// 提交按钮
const handleSubmitForm = () => {
    const loadingInstance = ElLoading.service({
        text: '保存中，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)'
    });

    let canvasBox = componentResultRef.value;
    html2canvas(canvasBox).then(function (canvas) {
        form.value.base64 = canvas.toDataURL('image/png');
        updateComponent(form.value).then((res) => {
          res=res.data
            if (res.code === 200) {
                ElMessage.success('修改成功');
            }
            loadingInstance.close();
        });
    });
};

// 生命周期
onMounted(() => {
    getDetail();
});
</script>
<style lang="scss" scoped>
.component-detail-wrap {
    padding: 20px;

    .card-wrap {
        position: relative;
        height: 86vh;

        .tabs-wrap {
            border-radius: 4px;
            border: 1px solid #e6ebf5;
            background-color: #ffffff;
            overflow: hidden;
            color: #303133;
        }

        .tools-wrap {
            position: absolute;
            right: 0;
            top: 0;
            padding: 0 10px;

            .item-btn {
                padding: 0 5px;
                height: 40px;
            }
        }

        ::v-deep .el-card__header {
            padding: 9px 15px 7px;
            min-height: 39px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #dfe4ed;
        }

        .preview-header {
            display: flex;
            flex-direction: column;
            gap: 5px;

            .preview-tips {
                font-size: 12px;
                color: #909399;
            }
        }

        .preview-content {
            position: relative;
            height: 100%;

            .preview-actions {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}
</style>
