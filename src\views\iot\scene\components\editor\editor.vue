<template>
    <div style="border: 0px solid #ebebeb; overflow: hidden; border-radius: 6px; background-color: #ebebeb; padding: 8px 5px 8px 0">
        <v-ace-editor v-model:value="content" :lang="props.lang" :theme="'github'" :codeStyle="props.codeStyle"  :readonly="props.readonly" :options="options" class="vue-ace-editor"/>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import { VAceEditor } from 'vue3-ace-editor';
import "./aceConfig.js";
import type { Ace } from "ace-builds";
// 定义 emits
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
    modelValue: {
        type: String,
        required: true,
        default: '',
    },
    width: {
        type: String,
        default: '100%',
    },
    height: {
        type: String,
        default: '500px',
    },
    lang: {
        type: String,
        default: 'groovy',
    },
    readonly: {
        type: Boolean,
        default: false,
    },
    codeStyle: {
        type: String,
        default: 'chrome',
    },
});
// 定义 modelValue 用于双向绑定
const content = ref(props.modelValue);
const options: Partial<Ace.EditorOptions> = reactive({
    useWorker: true, // 启用语法检查,必须为true
    enableBasicAutocompletion: true, // 自动补全
    enableLiveAutocompletion: true, // 智能补全
    autoScrollEditorIntoView: true,
    enableSnippets: true, // 启用代码段
    showPrintMargin: false, // 去掉灰色的线，printMarginColumn
    highlightActiveLine: true, // 高亮行
    highlightSelectedWord: true, // 高亮选中的字符
    tabSize: 4, // tab锁进字符
    fontSize: 13, // 设置字号
    wrap: false, // 是否换行
    // 是否可编辑
    // minLines: 10, // 最小行数，minLines和maxLines同时设置之后，可以不用给editor再设置高度
    // maxLines: 50, // 最大行数
});

watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        content.value = newValue;
    }
});
watch(() => content.value, (newValue) => {
    if (newValue) {
        emit('update:modelValue', newValue);
    }
});
</script>
<style scoped>
.vue-ace-editor {
    /* ace-editor默认没有高度，所以必须设置高度，或者同时设置最小行和最大行使编辑器的高度自动增高 */
    height: 450px;
    width: 100%;
    font-size: 16px;
    /* border: 1px solid; */
}
</style>
