<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="500">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="设备名称" prop="deviceName">
                        <el-input v-model="state.ruleForm.deviceName" placeholder="请输入设备名称" clearable></el-input>
                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="所属产品" prop="productName">
                        <el-input readonly v-model="state.ruleForm.productName" placeholder="请选择产品">
                            <template #append>
                                <div style="cursor:pointer" @click="selectProduct()">选择</div>
                            </template>
                        </el-input>

                    </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="设备编号" prop="serialNumber">
                        <!-- <el-input-number controls-position="right" v-model="state.ruleForm.serialNumber" type="number"
                            placeholder="请输入设备编号" /> -->
                        <el-input v-model="state.ruleForm.serialNumber" placeholder="请输入设备编号" maxlength="32">
                            <template #append v-if="state.ruleForm.deviceType !== 3" @click="generateNum"
                                :loading="genDisabled">
                                <div style="cursor:pointer" @click="generateNum">生成</div>
                            </template>
                            <template #append v-if="state.ruleForm.deviceType === 3">
                                <div style="cursor:pointer" @click="genSipID()">生成</div>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-col>

                <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                    <el-form-item label="备注">
                        <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
                            maxlength="150"></el-input>
                    </el-form-item>
                </el-col>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
            <!--添加从机对话框-->
            <el-dialog :title="productData.tableData.dialog.title" v-model="productData.tableData.dialog.isShowDialog"
                width="1000px" style="position: absolute; top: 100px;" append-to-body>
                <el-form ref="tempRef" :inline="true">
                    <el-form-item label="产品名称" size="default">
                        <el-input v-model="productData.tableData.param.productName" placeholder="模板名称">
                        </el-input>
                    </el-form-item>
                    <el-form-item size="small">
                        <el-button type="primary" size="small"
                            @click="queryTemp"><el-icon><ele-Search /></el-icon>搜索</el-button>
                        <el-button size="small" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
                    </el-form-item>
                </el-form>
                <el-table v-loading="productData.tableData.loading" :data="productData.tableData.data"
                    highlight-current-row ref="multipleTable" style="width: 100%" border size="small"
                    @row-click="rowClick" :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column label="选择" width="50" align="center">
                        <template #default="scope">
                            <input type="radio" :checked="scope.row.isSelect" name="product" />
                        </template>
                    </el-table-column>
                    <el-table-column label="产品名称" align="center" prop="productName" />
                    <el-table-column label="分类名称" align="center" prop="categoryName" />
                    <!-- <el-table-column label="租户名称" align="center" prop="tenantName" /> -->
                    <el-table-column label="备注" align="center" prop="remark" />
                    <el-table-column label="授权码" align="center" prop="status" width="70">
                        <template #default="scope">
                            <el-tag type="success" v-if="scope.row.isAuthorize == 1">启用</el-tag>
                            <el-tag type="info" v-if="scope.row.isAuthorize == 0">未启用</el-tag>
                        </template>
                    </el-table-column>
                <el-table-column label="产品状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="status" :value="scope.row.status" />
                    </template>
                </el-table-column>
                    <!-- <el-table-column label="认证方式" align="center" prop="status">
                        <template #default="scope">
                            <dictTag :options="vertificate_method_list" :value="scope.row.vertificateMethod" />
                        </template>
                    </el-table-column> -->
                    <!-- <el-table-column label="联网方式" align="center" prop="networkMethod">
                        <template #default="scope">
                            <dictTag :options="network_method_list" :value="scope.row.networkMethod" />
                        </template>
                    </el-table-column> -->
                    <el-table-column label="设备类型" align="center" prop="deviceType">
                        <template #default="scope">
                            <dict-tag :options="typelist" :value="scope.row.deviceType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination v-show="productData.tableData.total > 0" :total="productData.tableData.total"
                    class="mt15" style="justify-content: flex-end;" size="small" layout="total, prev, pager, next"
                    v-model:current-page="productData.tableData.param.pageNum"
                    v-model:page-size="productData.tableData.param.pageSize" @size-change="ontempHandleSizeChange"
                    background @current-change="ontempHandleCurrentChange" />
                <template #footer>
                    <span class="dialog-footer">
                        <el-button @click="cancel">取 消</el-button>
                        <el-button type="primary" @click="submitSelect">确 定</el-button>
                    </span>
                </template>
            </el-dialog>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { parseTime } from '/@/utils/next'
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listProduct } from '/@/api/iot/product';
import { generatorDeviceNum, addDevice } from '/@/api/iot/device';
import { getDeviceTemp } from '/@/api/iot/temp';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const defaultProps = reactive({
    children: "children",
    label: "deviceName",
    value: 'categoryId'
});
// 定义变量内容
const DialogFormRef = ref();
interface productOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface RowData {
    productId: any;
    isSelect: any;
    productName: any;
    deviceType: any;
    tenantId: any;
    tenantName: any;

    // 其他字段...
}
const initialState = {
    ruleForm: {
        deviceId: '' as any,
        deviceName: '',
        deviceType: 1 as any,
        productId: 0 as any,
        productName: '' as any,
        remark: '' as any,
        serialNumber: '' as any,
        status: 1 as any,
        tenantId: '' as any,
        tenantName: '' as any,
        // firmwareVersion: 1.0 as any,
        // isShadow: '' as any,
        // isSimulate: 0 as any,
        // latitude: '' as any,
        // locationWay: 1 as any,
        // longitude: '' as any,
        // networkAddress: '' as any,
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '新增设备管理',
        submitTxt: '新 增',
    },
}
const productData = reactive({
    tableData: {
        data: [] as RowData[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: 2,
            productName: ''
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
})
const loading = ref(false)
const network_method_list = ref<productOption[]>([]);//联网方式
const vertificate_method_list = ref<productOption[]>([]);//认证方式
const status = ref<productOption[]>([]);
const typelist = ref<productOption[]>([]);
const genDisabled = ref(false)// 生成设备编码是否禁用
// 打开设备配置对话框
const openSummary = ref(false)
const serverType = ref(1)
const openTip = ref(false)
const openServerTip = ref(false)
const deviceStatus = ref(0);
let product = reactive({
    productId: '',
    productName: '',
    deviceType: '',
    status: '',
    tenantId: '',
    tenantName: '',
    transport: '',
})
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    deviceName: [{
        required: true,
        message: '设备名称不能为空',
        trigger: 'blur',
    },
    {
        min: 2,
        max: 32,
        message: '设备名称长度在 2 到 32 个字符',
        trigger: 'blur',
    },],
    productName: [{
        required: true,
        message: "所属产品不能为空",
        trigger: "blur"
    },
        // {
        //     validator: (rule: any, value: number, callback: Function) => {
        //         if (value === 0) {
        //             callback(new Error("所属产品不能为0"));
        //         } else {
        //             callback();
        //         }
        //     },
        //     trigger: "blur"
        // }
    ],
    serialNumber: [{
        required: true,
        message: "设备编号不能为空",
        trigger: "blur"
    },

    ],

})
// 生成随机字母和数字
const generateNum = () => {
    if (!state.ruleForm.productId || state.ruleForm.productId == 0) {
        ElMessage.error('请先选择产品');
        return;
    }
    genDisabled.value = true;
    const params = { type: serverType.value };
    generatorDeviceNum(params).then((response) => {
        state.ruleForm.serialNumber = response.data.data;
        genDisabled.value = false;
    });
}
/*按照模板名查询*/
const queryTemp = () => {
    getProductList();
}
/** 搜索按钮操作 */
const handleQuery = () => {
    productData.tableData.param.pageNum = 1
    getProductList()
}
/** 重置按钮操作 */
const resetQuery = () => {
    productData.tableData.param.pageNum = 1
    productData.tableData.param.pageSize = 10
    productData.tableData.param.status = 2
    productData.tableData.param.productName = ''
    handleQuery()
}
// 打开弹窗
const openDialog = () => {
    getdictdata()
    resetState()
    state.dialog.isShowDialog = true;
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
/** 单选数据 */
const rowClick = (productdata: any) => {
    if (product != null) {
        setRadioSelected(productdata.productId);
        product = productdata as any;
    }
}
/** 设置单选按钮选中 */
const setRadioSelected = (productId: any) => {
    for (let i = 0; i < productData.tableData.data.length; i++) {
        if (productData.tableData.data[i].productId == productId) {
            productData.tableData.data[i].isSelect = true;
        } else {
            productData.tableData.data[i].isSelect = false;
        }
    }
}
/*选择模板*/
const selectProduct = () => {
    // this.reset();
    productData.tableData.dialog.isShowDialog = true;
    productData.tableData.dialog.title = "选择模板";
    getProductList();
}
/** 查询设备采集变量模板列表(弹框列表) */
const getProductList = async () => {
    try {
        productData.tableData.loading = true;
        const response = await listProduct(productData.tableData.param)
        //产品列表初始化isSelect值，用于单选
        for (let i = 0; i < response.data.rows.length; i++) {
            response.data.rows[i].isSelect = false;
        }
        productData.tableData.data = response.data.rows;
        productData.tableData.total = response.data.total;
        if (state.ruleForm.productId != 0) {
            setRadioSelected(state.ruleForm.productId);
        }
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            productData.tableData.loading = false;
            loading.value = false
        }, 500);
    }

}
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            // if (state.ruleForm.categoryId != '') {
            //     updateCategory(state.ruleForm).then(response => {
            //         //  刷新页面
            //         emit('refresh');
            //         closeDialog();
            //         ElMessage.success('修改成功');
            //     });
            // } else {
            //     addCategory(state.ruleForm).then(response => {
            //         //  刷新页面
            //         emit('refresh');
            //         closeDialog();
            //         ElMessage.success('新增成功');
            //     });
            // }
            addDevice(state.ruleForm).then(response => {
                //  刷新页面
                emit('refresh');
                closeDialog();
                ElMessage.success('新增成功');
            });

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        // device_type_list.value = await dictStore.fetchDict('iot_device_type')
        // transport_type_list.value = await dictStore.fetchDict('iot_transport_type')
        network_method_list.value = await dictStore.fetchDict('iot_network_method')
        vertificate_method_list.value = await dictStore.fetchDict('iot_vertificate_method')
        typelist.value = await dictStore.fetchDict('iot_device_type')
        status.value = await dictStore.fetchDict('iot_product_status')
        // location_way_list.value = await dictStore.fetchDict('iot_location_way')
        // collect_type_list.value = await dictStore.fetchDict('data_collect_type')
        // device_status_list.value = await dictStore.fetchDict('iot_device_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 取消按钮
const cancel = () => {
    productData.tableData.dialog.isShowDialog = false
}
/*确认选择模板*/
const submitSelect = () => {
    productData.tableData.dialog.isShowDialog = false
    state.ruleForm.productId = product.productId;
    state.ruleForm.productName = product.productName;
    state.ruleForm.deviceType = product.deviceType;
    getDeviceTempD();
    state.ruleForm.tenantId = product.tenantId;
    state.ruleForm.tenantName = product.tenantName;
    console.log(state.ruleForm, 'form.value', state.ruleForm.productName);

    if (product.transport === 'TCP') {
        openServerTip.value = true;
        serverType.value = 3;
    } else {
        openServerTip.value = false;
        serverType.value = 1;
    }
}
const getDeviceTempD = () => {
    getDeviceTemp(state.ruleForm).then((response) => {
        if (response.data.data && state.ruleForm.deviceType == 2) {
            openTip.value = true;
        } else {
            openTip.value = false;
        }
    });
}
// 分页改变
const ontempHandleSizeChange = (val: number) => {
    productData.tableData.param.pageSize = val;
    getProductList();
};
// 分页改变
const ontempHandleCurrentChange = (val: number) => {
    productData.tableData.param.pageNum = val;
    getProductList();
};
// 暴露变量
defineExpose({
    openDialog,
});
</script>
