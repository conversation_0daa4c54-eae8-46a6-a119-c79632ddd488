<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" width="80" v-show="showSearch">
                    <el-form-item label="第三方平台" prop="platform">
                        <el-select v-model="state.tableData.param.platform" placeholder="请选择平台" clearable
                            style="width: 240px">
                            <el-option v-for="plat in platformlist" :key="plat.dictValue" :label="plat.dictLabel"
                                :value="plat.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="请选择状态" clearable
                            style="width: 240px">
                            <el-option v-for="plat in platformstatuslist" :key="plat.dictValue" :label="plat.dictLabel"
                                :value="plat.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['iot:platform:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['iot:platform:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['iot:platform:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['iot:platform:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                    </div>
                    <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                        @queryTable="getTableData"></right-toolbar>
                </el-row>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                @selection-change="handleSelectionChange" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center"></el-table-column>
                <el-table-column label="平台名称" align="center" prop="platform">
                    <template #default="scope">
                        <DictTag :options="platformlist" :value="scope.row.platform"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="状态" align="center" prop="status" show-overflow-tooltip>
                    <template #default="scope">
                        <DictTag :options="platformstatuslist" :value="scope.row.status"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="平台申请ID" align="center" prop="clientId" :show-overflow-tooltip="true" />
                <el-table-column label="跳转地址" align="center" prop="redirectUri" />
                <el-table-column align="center" prop="bindUri">
                    <!-- 自定义表头内容 -->
                    <template #header>
                        <span>绑定登录uri</span>
                        <el-tooltip class="item" effect="dark" :content="columnTips.bindId" placement="top">
                            <el-icon color="#409eff" style="margin-left: 10px;"><ele-QuestionFilled /></el-icon>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="redirectLoginUri">
                    <!-- 自定义表头内容 -->
                    <template #header>
                        <span>跳转登录uri</span>
                        <el-tooltip class="item" effect="dark" :content="columnTips.redirectLogin" placement="top">
                            <el-icon color="#409eff" style="margin-left: 10px;"><ele-QuestionFilled /></el-icon>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="errorMsgUri">
                    <!-- 自定义表头内容 -->
                    <template #header>
                        <span>错误提示uri</span>
                        <el-tooltip class="item" effect="dark" :content="columnTips.errorId" placement="top">
                            <el-icon color="#409eff" style="margin-left: 10px;"><ele-QuestionFilled /></el-icon>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text type="primary" icon="el-icon-edit"
                            @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:platform:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
                        <el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
                            v-auths="['iot:platform:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <ConfigDialog ref="ConfigDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { download } from '/@/utils/request';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { delPlatform, listPlatform } from '/@/api/iot/platform';
import { parseTime } from '/@/utils/next'
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const ConfigDialog = defineAsyncComponent(() => import('/@/views/iot/platform/dialog.vue'));

// 定义变量内容
const ConfigDialogRef = ref();
const state = reactive<SysDicState>({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: '',
            platform: ''
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
interface platTypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface platstatusTypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const platformlist = ref<platTypeOption[]>([]); //平台列表
const platformstatuslist = ref<platstatusTypeOption[]>([]); //平台状态列表
const columnTips = reactive({
    bindId: "绑定登录uri, http://localhost/login?bindId=,域名换成对应域名即可，本地开发不需要更改",
    redirectLogin: "跳转登录uri,http://localhost/login?loginId=,域名换成对应域名即可，本地开发不需要更改",
    errorId: "错误提示获取uri,http://localhost/login?errorId=,域名换成对应域名即可，本地开发不需要更改"
})
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //socialPlatformId
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listPlatform(state.tableData.param);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        platform: ''
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        platformlist.value = await dictStore.fetchDict('iot_social_platform')
        platformstatuslist.value = await dictStore.fetchDict('iot_social_platform_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { socialPlatformId: string; }) => item.socialPlatformId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 打开新增参数弹窗
const onOpenAddDic = (type: string) => {
    ConfigDialogRef.value.openDialog(type);
};
// 打开修改参数弹窗
const onOpenEditDic = (type: string, row: RowPlatType | undefined) => {
    var socialPlatformId = ''
    if (!row) {
        socialPlatformId = ids.value
    } else {
        socialPlatformId = row.socialPlatformId
    }
    ConfigDialogRef.value.openDialog(type, row, socialPlatformId);
};
// 删除参数
const onRowDel = (row: RowPlatType) => {
    const socialPlatformIds = row.socialPlatformId || ids.value;    
    ElMessageBox.confirm(`此操作将永久第三方登录平台控制编号为“${socialPlatformIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delPlatform(socialPlatformIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('iot/platform/export', {
        ...state.tableData.param
    }, `platform_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
