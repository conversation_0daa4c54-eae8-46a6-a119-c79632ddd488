import request from '/@/utils/request'

// 定义查询参数类型
interface NewsQuery {
  // 根据你的需求补充具体的查询参数
  page?: number;
  pageSize?: number;
  keyword?: string;
}

// 定义新闻资讯项类型
interface NewsItem {
  id: number;
  title: string;
  content: string;
  // 其他字段...
}

// 查询新闻资讯列表
export function listNews(query: NewsQuery) {
  return request({
    url: '/iot/news/list',
    method: 'get',
    params: query
  })
}

// 查询新闻资讯详细
export function getNews(newsId: any) {
  return request({
    url: `/iot/news/${newsId}`,
    method: 'get'
  })
}

// 新增新闻资讯
export function addNews(data: any) {
  return request({
    url: '/iot/news',
    method: 'post',
    data
  })
}

// 修改新闻资讯
export function updateNews(data: any) {
  return request({
    url: '/iot/news',
    method: 'put',
    data
  })
}

// 删除新闻资讯
export function delNews(newsId: number) {
  return request({
    url: `/iot/news/${newsId}`,
    method: 'delete'
  })
}
