<template>
    <div id="mediaServerEdit" v-loading="isLoging">
        <el-dialog 
            title="流媒体服务器节点"
            :width="dialogWidth" 
            top="5vh" 
            :close-on-click-modal="false" 
            v-model="showDialog" 
            :destroy-on-close="true" 
            @close="close()"
        >
        <div id="formStep" style="padding: 0 20px">
                <!-- 简化版的第一步表单 -->
                <el-form v-if="currentStep == 1" ref="mediaServerForm" label-width="100px">
                    <el-form-item label="服务器IP" required>
                        <el-input v-model="ipAddress" placeholder="请输入有效的IP地址" clearable></el-input>
                        <div class="form-tip">请输入有效的IP地址</div>
                    </el-form-item>
                    <el-form-item label="Http端口" required>
                        <el-input v-model="httpPort" placeholder="请输入有效的端口号" clearable></el-input>
                        <div class="form-tip">请输入有效的端口号</div>
                    </el-form-item>
                    <el-form-item label="服务密钥" required>
                        <el-input v-model="secretKey" placeholder="请输入secret" clearable></el-input>
                        <div class="form-tip">请输入secret</div>
                    </el-form-item>
                    <div class="form-actions">
                        <el-button @click="close">取 消</el-button>
                        <el-button type="primary" @click="checkServer" :loading="btnLoading">测 试</el-button>
                    </div>
                </el-form>
                
                <!-- 第二步和第三步保留原来的复杂表单，但只在测试通过后显示 -->
                <el-row :gutter="24" v-if="currentStep === 2 || currentStep === 3">
                    <el-col :span="12">
                        <el-form v-if="(currentStep === 2 || currentStep === 3) && mediaServerFormData" ref="mediaServerForm1" :model="mediaServerFormData" label-width="140px" :disabled="!editFlag">
                            <el-form-item label="配置名称" prop="serverId">
                                <el-input v-model="mediaServerFormData.serverId" placeholder="请输入配置名称" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="服务器IP" prop="ip">
                                <el-input v-model="mediaServerFormData.ip" placeholder="请输入服务器IP" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="播放协议" prop="protocol">
                                <el-select v-model="mediaServerFormData.protocol" style="width: 100%">
                                    <el-option key="http" label="http" value="http"></el-option>
                                    <el-option key="https" label="https" value="https"></el-option>
                                    <el-option key="ws" label="ws" value="ws"></el-option>
                                    <el-option key="rtmp" label="rtmp" value="rtmp"></el-option>
                                    <el-option key="rtsp" label="rtsp" value="rtsp"></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="HookUrl" prop="hookurl">
                                <el-input v-model="mediaServerFormData.hookurl" placeholder="HookUrl" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="Http端口" prop="portHttp">
                                <el-input v-model="mediaServerFormData.portHttp" placeholder="请输入Http端口" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="Https端口" prop="portHttps">
                                <el-input v-model="mediaServerFormData.portHttps" placeholder="Https端口" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="Rtsp端口" prop="portRtsp">
                                <el-input v-model="mediaServerFormData.portRtsp" placeholder="请输入Rtsp端口" clearable></el-input>
                            </el-form-item>
                        </el-form>
                    </el-col>
                    <el-col :span="12">
                        <el-form v-if="(currentStep === 2 || currentStep === 3) && mediaServerFormData" ref="mediaServerForm2" :model="mediaServerFormData" label-width="180px" :disabled="!editFlag">
                            <el-form-item label="流媒体密钥" prop="secret">
                                <el-input v-model="mediaServerFormData.secret" placeholder="请输入流媒体密钥" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="服务器域名" prop="domain">
                                <el-input v-model="mediaServerFormData.domain" placeholder="请输入服务器域名" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="自动配置">
                                <el-switch v-model="mediaServerFormData.autoConfig"></el-switch>
                            </el-form-item>
                            <el-form-item label="收流模式">
                                <el-switch
                                    active-text="多端口"
                                    inactive-text="单端口"
                                    @change="portRangeChange"
                                    v-model="mediaServerFormData.rtpEnable"
                                ></el-switch>
                            </el-form-item>
                            <el-form-item v-if="!mediaServerFormData.rtpEnable" label="收流端口" prop="rtpProxyPort">
                                <el-input v-model.number="mediaServerFormData.rtpProxyPort" clearable></el-input>
                            </el-form-item>
                            <el-form-item v-if="mediaServerFormData.rtpEnable" label="Rtmp端口">
                                <el-input v-model="rtpPortRange1" placeholder="起始端口" @change="portRangeChange" clearable style="width: 100px" prop="rtpPortRange1"></el-input>
                                <el-input v-model="rtpPortRange2" placeholder="结束端口" @change="portRangeChange" clearable style="width: 100px" prop="rtpPortRange2"></el-input>
                            </el-form-item>
                            <el-form-item label="Rtmp端口" prop="portRtmp">
                                <el-input v-model="mediaServerFormData.portRtmp" placeholder="请输入Rtmp端口" clearable></el-input>
                            </el-form-item>
                            <el-form-item label="录像管理端口" prop="recordPort">
                                <el-input v-model.number="mediaServerFormData.recordPort" placeholder="请输入录像管理端口" clearable>
                                    <template #append>
                                        <el-button v-if="mediaServerFormData.recordPort > 0 && editFlag" class="el-icon-check" type="primary" @click="checkRecordServer"></el-button>
                                    </template>
                                </el-input>
                                <i v-if="recordServerCheck === 1" class="el-icon-success" style="color: #3caf36; position: absolute; top: 14px"></i>
                                <i v-if="recordServerCheck === 2" class="el-icon-loading" style="color: #3caf36; position: absolute; top: 14px"></i>
                                <i v-if="recordServerCheck === -1" class="el-icon-error" style="color: #c80000; position: absolute; top: 14px"></i>
                            </el-form-item>
                            <el-form-item>
                                <div style="float: right">
                                    <el-button type="primary" @click="onSubmit" v-if="editFlag">提交</el-button>
                                    <el-button @click="close" v-if="editFlag">取消</el-button>
                                </div>
                            </el-form-item>
                        </el-form>
                    </el-col>
                </el-row>
        </div>
    </el-dialog>
    </div>
</template>
  
<script setup>
import { ref, reactive, nextTick, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { addmediaServer, checkmediaServer, updatemediaServer } from '/@/api/iot/mediaServer';

const emit = defineEmits(['update:editFlag']);
const props = defineProps({
  editFlag: {
    type: Boolean,
    default: false
  }
});

// 监听props.editFlag的变化
watch(() => props.editFlag, (newVal) => {
  console.log('editFlag changed:', newVal);
});

// 简化输入字段，使用ref创建独立的响应式值
const ipAddress = ref('');
const httpPort = ref('');
const secretKey = ref('');

// 确保在组件挂载后初始化一次
onMounted(() => {
  console.log('组件已挂载，确保表单已初始化');
  resetForm();
});

// 响应式状态
const isLoging = ref(false);
const dialogWidth = ref('550px'); // 缩小初始对话框宽度
const showDialog = ref(false);
const dialogLoading = ref(false);
const currentStep = ref(1);
const serverCheck = ref(0);
const recordServerCheck = ref(0);
const btnLoading = ref(false);
const tempTenantId = ref('');
const tempTenantName = ref('');
const rtpPortRange1 = ref(30000);
const rtpPortRange2 = ref(30100);

// 表单 ref（用于调用 validate）
const mediaServerForm = ref(null);

// 表单数据
const mediaServerFormData = reactive({
  id: undefined,
  serverId: '',
  ip: '',
  portHttp: '',
  portHttps: '',
  portRtmp: '',
  portRtsp: '',
  protocol: 'http',
  secret: '',
  domain: '',
  hookurl: '',
  autoConfig: false,
  rtpEnable: false,
  rtpProxyPort: '',
  recordPort: ''
});

const resetForm = () => {
  // 重置简化字段
  ipAddress.value = '';
  httpPort.value = '';
  secretKey.value = '';
  
  // 重置完整表单对象
  Object.assign(mediaServerFormData, {
    id: undefined,
    serverId: '',
    ip: '',
    portHttp: '',
    portHttps: '',
    portRtmp: '',
    portRtsp: '',
    protocol: 'http',
    secret: '',
    domain: '',
    hookurl: '',
    autoConfig: false,
    rtpEnable: false,
    rtpProxyPort: '',
    recordPort: ''
  });
  
  currentStep.value = 1;
  serverCheck.value = 0;
  recordServerCheck.value = 0;
  rtpPortRange1.value = 30000;
  rtpPortRange2.value = 30100;
};

const close = () => {
  showDialog.value = false;
  resetForm();
  emit('update:editFlag', false);
};

// 检查服务器
const checkServer = async () => {
  console.log('测试连接按钮被点击');
  
  // 检查必填字段
  if (!ipAddress.value) {
    ElMessage.error('请输入服务器IP');
    return;
  }
  if (!httpPort.value) {
    ElMessage.error('请输入Http端口');
    return;
  }
  if (!secretKey.value) {
    ElMessage.error('请输入服务密钥');
    return;
  }
  
  // 验证IP格式
  const ipRegex = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
  if (!ipRegex.test(ipAddress.value)) {
    ElMessage.error('IP地址格式不正确');
    return;
  }
  
  // 验证端口格式
  const portRegex = /^(([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]))$/;
  if (!portRegex.test(httpPort.value)) {
    ElMessage.error('端口号格式不正确');
    return;
  }
  
  btnLoading.value = true;
  serverCheck.value = 0;

  try {
    const query = {
      ip: ipAddress.value,
      port: httpPort.value,
      secret: secretKey.value,
    };
    
    console.log('发送请求参数:', query);
    
    const response = await checkmediaServer(query);
    console.log('服务器响应:', response);
    btnLoading.value = false;
    
    // 检查响应中是否包含实际的连接状态信息
    // 注意：后端可能返回HTTP 200，但实际连接可能失败
    if (response && response.data) {
      // 检查响应中是否有详细的连接结果
      if (response.data.success === true || 
          (response.data.code === 200 && response.data.data && 
           Object.keys(response.data.data).length > 0)) {
        // 真正连接成功的情况
        
        // 将接口返回的数据填充到表单中
        if (response.data.data) {
          Object.assign(mediaServerFormData, response.data.data);
        }
        
        // 设置默认值
        mediaServerFormData.ip = ipAddress.value;
        mediaServerFormData.portHttp = httpPort.value;
        mediaServerFormData.secret = secretKey.value;
        mediaServerFormData.autoConfig = true;
        mediaServerFormData.rtpEnable = true;
        mediaServerFormData.protocol = 'http';
        mediaServerFormData.domain = 'fastbee.com';
        mediaServerFormData.enabled = 1;
        mediaServerFormData.tenantId = tempTenantId.value;
        mediaServerFormData.tenantName = tempTenantName.value;
        mediaServerFormData.serverId = 'fastbee';
        mediaServerFormData.hookurl = 'java:8080';
        mediaServerFormData.portHttps = 8443;
        mediaServerFormData.recordPort = 18081;
        mediaServerFormData.portRtmp = 1935;
        mediaServerFormData.portRtsp = 554;
        mediaServerFormData.rtpProxyPort = '';
        rtpPortRange1.value = 30000;
        rtpPortRange2.value = 30100;
        serverCheck.value = 1;
        
        ElMessage.success('服务器连接成功');
        
        // 自动进入下一步
        next();
      } else if (response.data.msg === 'success!' && !response.data.data) {
        // 这种情况是后端API调用成功，但实际连接流媒体服务器失败
        serverCheck.value = -1;
        ElMessage.error('流媒体服务器连接失败：无法连接到指定的流媒体服务器');
      } else {
        // 其他失败情况
        serverCheck.value = -1;
        ElMessage.error(response.data.msg || '服务器连接失败');
      }
    } else {
      serverCheck.value = -1;
      ElMessage.error('服务器返回数据格式不正确');
    }
  } catch (error) {
    console.error('测试连接错误:', error);
    btnLoading.value = false;
    serverCheck.value = -1;
    
    // 根据错误类型显示不同的错误信息
    if (error.message && error.message.includes('timeout')) {
      ElMessage.error('连接超时：请检查服务器IP和端口是否正确，或者服务器是否在线');
    } else if (error.message && error.message.includes('Network Error')) {
      ElMessage.error('网络错误：无法连接到服务器，请检查网络连接');
    } else {
      ElMessage.error(error.message || '连接服务器失败，请稍后重试');
    }
  }
};

// 下一步
const next = () => {
  if (currentStep.value < 3) {
    currentStep.value++;
    // 第二步和第三步需要更宽的对话框
    dialogWidth.value = '1000px';
  }
};

// 端口范围变化
const portRangeChange = () => {
  if (mediaServerFormData.rtpEnable) {
    mediaServerFormData.rtpPortRange = rtpPortRange1.value + ',' + rtpPortRange2.value;
  }
};

// 检查录制服务器
const checkRecordServer = () => {
  recordServerCheck.value = 2;
  // 这里应该调用检查录制服务器的API
  setTimeout(() => {
    recordServerCheck.value = 1;
    ElMessage.success('录制服务器连接成功');
  }, 1000);
};

// 提交表单
const onSubmit = async () => {
  try {
    if (mediaServerFormData.id) {
      await updatemediaServer(mediaServerFormData);
      ElMessage.success('修改成功');
    } else {
      await addmediaServer(mediaServerFormData);
      ElMessage.success('添加成功');
    }
    close();
  } catch (error) {
    console.error('表单提交错误:', error);
    ElMessage.error(error.message || '操作失败');
  }
};

// 打开弹窗的方法
const openDialog = (row, callback, editFlag = false) => {
  // 先重置表单
  resetForm();
  
  // 重置对话框宽度
  dialogWidth.value = '550px';
  
  // 如果传入了row，复制它的值
  if (row && typeof row === 'object') {
    // 我们确认row已经是安全的对象，可以直接使用
    Object.assign(mediaServerFormData, row);
    
    // 同时更新简化字段
    ipAddress.value = row.ip || '';
    httpPort.value = row.portHttp || '';
    secretKey.value = row.secret || '';
    
    // 如果有ID，判断模式
    if (row.id) {
      // 编辑/查看现有节点时，直接跳到第二步
      currentStep.value = 2;
      dialogWidth.value = '1000px';
      
      // 如果是查看模式，设置服务器检查状态为已通过
      if (!editFlag) {
        serverCheck.value = 1;
        if (mediaServerFormData.recordPort) {
          recordServerCheck.value = 1;
        }
      }
    } else {
      // 新增节点时，从第一步开始
      currentStep.value = 1;
    }
  } else {
    // 如果没有传入row，保持重置后的状态，从第一步开始
    currentStep.value = 1;
  }
  
  // 确保对话框可以显示
  showDialog.value = true;
  
  // 回调处理
  if (typeof callback === 'function') {
    callback();
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>
  
<style scoped>
.dialog-footer {
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #f56c6c;
  padding-top: 4px;
  display: none;
}

.form-actions {
  margin-top: 30px;
  text-align: center;
}

.form-actions .el-button {
  width: 90px;
  margin: 0 10px;
}

/* 必填字段的星号样式 */
:deep(.el-form-item.is-required .el-form-item__label::before) {
  color: #F56C6C;
}

/* 调整表单项间距 */
:deep(.el-form-item) {
  margin-bottom: 24px;
}

/* 调整对话框样式 */
:deep(.el-dialog__header) {
  border-bottom: 1px solid #ebeef5;
  padding: 15px 20px;
}

:deep(.el-dialog__body) {
  padding: 20px 0;
}
</style>