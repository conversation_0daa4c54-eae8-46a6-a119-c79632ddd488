// src/api/nettyMqtt.ts
import request from '/@/utils/request'

// 定义查询参数类型（根据你的实际需求，修改类型）
interface QueryParams {
  [key: string]: any;
}

// 集群下所有客户端列表
export function listNettyMqttClient(query: QueryParams): Promise<any> {
  return request({
    url: '/iot/mqtt/clients',
    method: 'get',
    params: query,
  });
}

// 客户端退出
export function clientOut(query: QueryParams): Promise<any> {
  return request({
    url: '/iot/mqtt/client/out',
    method: 'get',
    params: query,
  });
}

// 获取 MQTT 状态统计
export function getNettyMqttStats(): Promise<any> {
  return request({
    url: '/bashBoard/stats',
    method: 'get',
  });
}

// 获取 MQTT 统计数据
export function statisticNettyMqtt(query?: QueryParams): Promise<any> {
  return request({
    url: '/bashBoard/metrics',
    method: 'get',
    params: query,
  });
}
