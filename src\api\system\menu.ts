import request from '/@/utils/request';

// 定义查询菜单列表的参数类型
interface QueryParams {
  // 这里添加你的 query 参数的具体类型，示例
  name?: string;
  status?: string;
  // 其他可能的参数...
}

// 定义菜单项的数据结构（根据你的 API 返回的实际数据来定义）
interface Menu {
  menuId: number;
  menuName: string;
  parentId: number;
  orderNum: any;
  // 其他菜单字段...
}

// 定义返回数据的类型
interface ApiResponse<T> {
  [x: string]: any;
  code: number;
  msg: string;
  data: T;
}

// 查询菜单列表
export function listMenu(query: QueryParams) {
  return request<ApiResponse<Menu[]>>({
    url: '/system/menu/list',
    method: 'get',
    params: query
  });
}

// 查询菜单详细信息
export function getMenu(menuId: number) {
  return request<ApiResponse<Menu>>({
    url: `/system/menu/${menuId}`,
    method: 'get'
  });
}

// 查询菜单下拉树结构
export function treeselect() {
  return request<ApiResponse<Menu[]>>({
    url: '/system/menu/treeselect',
    method: 'get'
  });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId: string) {
  return request<ApiResponse<Menu[]>>({
    url: `/system/menu/roleMenuTreeselect/${roleId}`,
    method: 'get'
  });
}

// 新增菜单
export function addMenu(data: Menu) {
  return request<ApiResponse<null>>({
    url: '/system/menu',
    method: 'post',
    data: data
  });
}

// 修改菜单
export function updateMenu(data: Menu) {
  return request<ApiResponse<null>>({
    url: '/system/menu',
    method: 'put',
    data: data
  });
}

// 删除菜单
export function delMenu(menuId: number) {
  return request<ApiResponse<null>>({
    url: `/system/menu/${menuId}`,
    method: 'delete'
  });
}
