import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Session } from '/@/utils/storage';
import { ElNotification } from 'element-plus';
import errorCode from './errorCode'
import qs from 'qs';
import { ElMessage as Message, ElLoading } from 'element-plus';
import { tansParams, blobValidate } from '/@/utils/next';
import { saveAs } from 'file-saver'
// 是否显示重新登录
export let isRelogin = { show: false };

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	// import.meta.env.VITE_API_URL111
	baseURL: import.meta.env.VITE_API_URL,
	timeout: 50000,
	headers: { 'Content-Type': 'application/json' },
	paramsSerializer: {
		serialize(params) {
			return qs.stringify(params, { allowDots: true });
		},
	},
});

// 添加请求拦截器
service.interceptors.request.use(
	(config) => {
		// 在发送请求之前做些什么 token
		if (Session.get('token')) {
			config.headers!['Authorization'] = `${Session.get('token')}`;
		}
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		// 未设置状态码则默认成功状态
		const code = response.data.code || 200;
		// 获取错误信息
		const msg = errorCode[code] || response.data.msg || errorCode['default']
		// 二进制数据则直接返回
		if (response.request.responseType === 'blob' || response.request.responseType === 'arraybuffer') {
			return response.data
		}
		if (code === 401 || code === 4001) {
			Session.clear(); // 清除浏览器全部临时缓存
			window.location.href = '/'; // 去登录页
			ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
				.then(() => { })
				.catch(() => { });
		} else if (code === 500) {
			ElMessage({ message: msg, type: 'error' })
			return Promise.reject(new Error(msg))
		} else if (code === 601) {
			ElMessage({ message: msg, type: 'warning' })
			return Promise.reject('error')
		} else if (code !== 200) {
			ElNotification.error({ title: msg })
			return Promise.reject('error')
		} else {
			return response
		}
		// 对响应数据做点什么
		// const res = response.data;
		// if (res.code && res.code !== 0) {
		// 	// `token` 过期或者账号已在别处登录
		// 	if (res.code === 401 || res.code === 4001) {
		// 		Session.clear(); // 清除浏览器全部临时缓存
		// 		window.location.href = '/'; // 去登录页
		// 		ElMessageBox.alert('你已被登出，请重新登录', '提示', {})
		// 			.then(() => { })
		// 			.catch(() => { });
		// 	}
		// 	return res
		// } else {
		// 	return res;
		// }
	},
	(error) => {
		// 对响应错误做点什么
		if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('网络超时');
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误');
		} else {
			if (error.response.data) ElMessage.error(error.response.statusText);
			else ElMessage.error('接口路径找不到');
		}
		return Promise.reject(error);
	}
);
// 通用下载方法
export function download(
	url: string,
	params: Record<string, any>, // 参数为一个对象
	filename: string,
	config?: Record<string, any> // 可选的额外配置
): Promise<void> {
	// 显示加载中的提示
	const downloadLoadingInstance = ElLoading.service({
		text: "正在下载数据，请稍候",
		spinner: "el-icon-loading",
		background: "rgba(0, 0, 0, 0.7)",
	});

	// 发起 POST 请求
	return service.post(url, params, {
		transformRequest: [(params) => tansParams(params)], // 假设 tansParams 是处理请求参数的方法
		headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
		responseType: 'blob', // 以 blob 格式接收响应
		...config, // 合并额外的配置
	}).then(async (response) => {
		console.log(response, 'response');
		const isLogin = await blobValidate(response as any);
		console.log(isLogin, 'isLogin');
		if (isLogin) {
			const blob = new Blob([response as any]);
			console.log(blob, 'blob');

			saveAs(blob, filename); // 使用 file-saver 保存文件
		} else {
			const resText = await new response.text();
			const rspObj = JSON.parse(resText);
			const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
			Message.error(errMsg); // 显示错误消息
		}
		// 关闭加载提示
		downloadLoadingInstance.close();
	}).catch((error: Error) => {
		console.error(error);
		Message.error('下载文件出现错误，请联系管理员！');
		// 关闭加载提示
		downloadLoadingInstance.close();
	});
}
// 导出 axios 实例
export default service;


