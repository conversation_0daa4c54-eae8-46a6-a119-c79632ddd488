import request from '/@/utils/request';

// 定义类型
interface AuthorizeQuery {
  [key: string]: any;
}

interface AuthorizeData {
  // 这里根据实际情况定义授权码数据的类型
  id?: number;
  name?: string;
  // 其他字段
}

// 查询产品授权码列表
export function listAuthorize(query: any) {
  return request({
    url: '/iot/authorize/list',
    method: 'get',
    params: query
  });
}

// 查询产品授权码详细
export function getAuthorize(authorizeId: number) {
  return request({
    url: `/iot/authorize/${authorizeId}`,
    method: 'get'
  });
}

// 新增产品授权码
export function addAuthorize(data: any) {
  return request({
    url: '/iot/authorize',
    method: 'post',
    data: data
  });
}

// 根据数量批量新增产品授权码
export function addProductAuthorizeByNum(data: any) {
  return request({
    url: '/iot/authorize/addProductAuthorizeByNum',
    method: 'post',
    data: data
  });
}

// 修改产品授权码
export function updateAuthorize(data: any) {
  return request({
    url: '/iot/authorize',
    method: 'put',
    data: data
  });
}

// 删除产品授权码
export function delAuthorize(authorizeId: number) {
  return request({
    url: `/iot/authorize/${authorizeId}`,
    method: 'delete'
  });
}
