import request from '/@/utils/request'

// 查询产品列表
export function listProduct(query: any) {
  return request({
    url: '/iot/iotproduct/list',
    method: 'get',
    params: query
  })
}

// 查询产品详细信息
export function getProduct(productId: any) {
  return request({
    url: `/iot/iotproduct/${productId}`,
    method: 'get'
  })
}

// 新增产品
export function addProduct(data: any) {
  return request({
    url: '/iot/iotproduct',
    method: 'post',
    data: data
  })
}

// 修改产品
export function updateProduct(data: any) {
  return request({
    url: '/iot/iotproduct',
    method: 'put',
    data: data
  })
}

// 获取产品下设备的数量
export function deviceCount(productId: number) {
  return request({
    url: `/iot/iotproduct/deviceCount/${productId}`,
    method: 'get'
  })
}

// 更新产品状态
export function changeProductStatus(data: any) {
  return request({
    url: '/iot/iotproduct/status/',
    method: 'put',
    data: data
  })
}

// 删除产品
export function delProduct(productId: number) {
  return request({
    url: `/iot/iotproduct/${productId}`,
    method: 'delete'
  })
}

