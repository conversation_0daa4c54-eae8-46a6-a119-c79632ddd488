import request from '/@/utils/request'

// 定义请求和响应的类型

// SIP配置的数据类型
interface SipConfig {
  id: number;
  productId: number;
  configName: string;
  isDefault: boolean;
  // 根据实际 SIP 配置字段继续扩展
}

// 查询条件类型
interface SipConfigQuery {
  page?: number;
  size?: number;
  [key: string]: any; // 可扩展的查询字段
}

// 默认 SIP 配置的类型
interface SetDefaultSipConfig {
  productId: number;
  configId: number;
}

// 查询 SIP 系统配置列表
export function listSipconfig(query: SipConfigQuery) {
  return request({
    url: '/sip/sipconfig/list',
    method: 'get',
    params: query
  })
}

// 查询 SIP 系统配置详细信息
export function getSipconfig(productId: number, isDefault: boolean) {
  return request({
    url: `/sip/sipconfig/${productId}/${isDefault}`,
    method: 'get'
  })
}

// 新增 SIP 系统配置
export function addSipconfig(data: SipConfig) {
  return request({
    url: '/sip/sipconfig',
    method: 'post',
    data: data
  })
}

// 修改 SIP 系统配置
export function updateSipconfig(data: SipConfig) {
  return request({
    url: '/sip/sipconfig',
    method: 'put',
    data: data
  })
}

// 删除 SIP 系统配置
export function delSipconfig(id: number) {
  return request({
    url: `/sip/sipconfig/${id}`,
    method: 'delete'
  })
}

// 根据产品 ID 删除 SIP 系统配置
export function delSipconfigByProductId(productId: number) {
  return request({
    url: `/sip/sipconfig/product/${productId}`,
    method: 'delete'
  })
}

// 设置默认 SIP 系统配置
export function setDefaultSipconfig(data: SetDefaultSipConfig) {
  return request({
    url: '/sip/sipconfig/defaultconfig',
    method: 'post',
    data: data
  })
}
