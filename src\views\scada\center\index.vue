<template>
    <div class="layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" v-show="showSearch">
                    <el-form-item label="名称" prop="pageName">
                        <el-input v-model="state.tableData.param.pageName" clearable size="default"
                            placeholder="请输入页面名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
                <el-row :gutter="10" class="mb8" :justify="'space-between'">
                    <div>
                        <el-button v-auths="['scada:center:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button v-auths="['scada:center:edit']" size="default" type="success" class="ml10"
                            :disabled="single" @click="onOpenEditDic('edit', undefined)">
                            <el-icon><ele-EditPen /></el-icon>
                            修改
                        </el-button>
                        <el-button v-auths="['scada:center:remove']" size="default" type="danger" class="ml10"
                            :disabled="multiple" @click="onRowDel">
                            <el-icon><ele-DeleteFilled /></el-icon>
                            删除
                        </el-button>
                        <el-button v-auths="['scada:center:export']" size="default" type="warning" class="ml10"
                            @click="handleExport">
                            <el-icon><ele-Download /></el-icon>
                            导出
                        </el-button>
                    </div>
                    <div class="flex">
                        <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                            @queryTable="getTableData"></right-toolbar>
                        <div class="ml5">
                            <el-tooltip effect="dark" content="切换卡片/列表" placement="top">
                                <el-button size="default" circle @click="handleChangeShowType">
                                    <el-icon><ele-Grid /></el-icon>
                                </el-button>
                            </el-tooltip>
                        </div>
                    </div>
                </el-row>
            </div>
            <div v-if="showType == 'card'">
                <el-row :gutter="15" v-loading="state.tableData.loading">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="6" v-for="item in state.tableData.data"
                        :key="item.id" style="margin-top: 7.5px; margin-bottom: 7.5px">
                        <el-card class="card-wrap" :body-style="{ padding: '10px' }">
                            <div class="img-wrap">
                                <el-image style="width: 100%; height: 100%; border-radius: 5px" lazy
                                    :src="baseUrl + item.pageImage" fit="cover" v-auths="['scada:center:query']"
                                    @click="goToDetail(item)"></el-image>
                            </div>
                            <div class="tag-wrap">
                                <span>{{ item.pageResolution ? item.pageResolution : '未知' }}</span>
                            </div>
                            <div class="name-wrap">
                                <span>{{ item.pageName }}</span>
                            </div>
                            <div class="tools-wrap"
                                style="display: flex; align-items: center;justify-content: space-between;">
                                <el-checkbox-group v-model="ids" @change="checkboxChange">
                                    <el-checkbox class="checkbox" :value="item.id" :key="item.id">
                                        <span></span>
                                    </el-checkbox>
                                </el-checkbox-group>
                                <div class="right-wrap">
                                    <el-dropdown style="margin-left: 8px">
                                        <span class="el-dropdown-link" style="cursor: pointer;"
                                            v-auths="['scada:center:preview', 'scada:center:remove', 'scada:center:edit']">
                                            <SvgIcon :name="'more-vertical'" :type="'menu'" :color="'#409eff'">
                                            </SvgIcon>
                                        </span>
                                        <template #dropdown>
                                            <el-dropdown-menu>
                                                <el-dropdown-item command="edit">
                                                    <el-button size="small" text type="primary" 
                                                        @click="goToDetail(item)"><el-icon><ele-View /></el-icon>详情</el-button>
                                                </el-dropdown-item>
                                                <el-dropdown-item command="preview">
                                                    <el-button style="color: #e6a23c" text size="small"
                                                        @click="handlePreview(item)"><el-icon><ele-View /></el-icon>预览</el-button>
                                                </el-dropdown-item>
                                                <el-dropdown-item command="remove">
                                                    <el-button style="color: #f56c6c" size="small" text
                                                        @click="onRowDel(item)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                                                </el-dropdown-item>
                                            </el-dropdown-menu>
                                        </template>
                                    </el-dropdown>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
                <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                    style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>
            </div>
            <div v-if="showType == 'list'">
                <el-table :data="state.tableData.data" v-loading="state.tableData.loading"
                    @selection-change="handleSelectionChange" border style="width: 100%"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column type="selection" width="55" align="center"></el-table-column>
                    <el-table-column label="id" align="center" prop="id" width="100" />
                    <el-table-column label="名称" align="center" prop="pageName" />
                    <el-table-column label="分辨率" align="center" prop="pageResolution" width="120">
                        <template #default="scope">
                            <span>{{ scope.row.pageResolution ? scope.row.pageResolution : '未知' }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="封面" align="center" prop="pageImage" width="120">
                        <template #default="scope">
                            <el-image style="box-shadow: 0 0 5px 1px #ccc;" :width="50" :height="50" lazy
                                preview-teleported="true" :preview-src-list="[baseUrl + scope.row.pageImage]"
                                :src="baseUrl + scope.row.pageImage" fit="cover" />
                        </template>
                    </el-table-column>
                    <el-table-column label="更新时间" align="center" prop="updateTime" width="180" />
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-button size="default" text type="primary" @click="onOpenEditDic('edit', scope.row)"
                                v-auths="['scada:center:query']"><el-icon><ele-View /></el-icon>详情</el-button>
                            <el-button style="color: #e6a23c" size="default" text type="primary"
                                @click="handlePreview(scope.row)"
                                v-auths="['scada:center:preview']"><el-icon><ele-View /></el-icon>预览</el-button>
                            <el-button style="color: #f56c6c" size="default" text type="primary"
                                @click="onRowDel(scope.row)"
                                v-auths="['scada:center:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                    style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                    v-model:current-page="state.tableData.param.pageNum" background
                    v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                    :total="state.tableData.total">
                </el-pagination>
            </div>
        </el-card>
        <ScadaDialog ref="ScadaDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { download } from '/@/utils/request';
import { delCenter, listCenter } from '/@/api/scada/center';
import router from '/@/router';

// 引入组件
const ScadaDialog = defineAsyncComponent(() => import('/@/views/scada/center/dialog.vue'));
interface TableDataItem {
    id: string; // 假设 id 是 string 类型，如果是其他类型，请调整
    pageResolution?: string; // 可选字段，表示分辨率
    pageName: string;
    pageImage: string;
}

// 定义变量内容
const ScadaDialogRef = ref();
const state = reactive({
    tableData: {
        data: [] as TableDataItem[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            pageName: undefined //分类名称
        },
    },
});
const showType = ref('card') // 展示方式
const showSearch = ref(true)    // 显示搜索条件
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //id
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API)
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listCenter(state.tableData.param);
        if (response.data.code == 200) {
            state.tableData.data = response.data.rows;
            state.tableData.total = response.data.total;
        }
    } catch (error) {
      // eslint-disable-next-line no-console
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        pageName: undefined,
    }
}
// 切换显示方式
const handleChangeShowType = () => {
    ids.value = [];
    showType.value = showType.value == 'card' ? 'list' : 'card';
}
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { id: string; }) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 卡片选择
const checkboxChange = (selection: any) => {
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
// 跳转组态详情
const goToDetail = (row: any) => {
  router.push({
        path: '/scada/topo/editor',
        query: {
            id: row.id,
            guid: row.guid,
        },
    });
}
// 跳转预览详情
const handlePreview = (row: any) => {
    const routeUrl = router.resolve({
        path: '/scada/topo/fullscreen',
        query: {
            id: row.id,
            guid: row.guid,
        },
    });
    window.open(routeUrl.href, '_blank');
}
// 打开新增组态信息弹窗
const onOpenAddDic = (type: string) => {
    ScadaDialogRef.value.openDialog(type);
};
// 打开修改组态信息弹窗
const onOpenEditDic = (type: string, row: any | undefined) => {
    var id = ''
    if (!row) {
        id = ids.value
    } else {
        id = row.id
    }
    ScadaDialogRef.value.openDialog(type, row, id);
};
// 删除组态信息
const onRowDel = (row: any) => {
    const idss = row.id || ids.value;
    ElMessageBox.confirm(`是否确认删除组态编号为${idss}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delCenter(idss).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
    download('scada/center/export', {
        ...state.tableData.param
    }, `组态${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
});
</script>
<style lang="scss" scoped>
.card-wrap {
    position: relative;
    border-radius: 5px;

    .img-wrap {
        height: 200px;
        width: 100%;
        // transition: transform 0.3s ease;

        // &:hover {
        //     cursor: pointer;
        //     transform: scale(1.1);
        // }
    }

    .tag-wrap {
        position: absolute;
        top: 0;
        left: 0;
        background-color: #1890ff;
        border-top-left-radius: 5px;
        border-bottom-right-radius: 5px;
        padding: 5px 15px;
        font-size: 12px;
        color: #fff;
    }

    .name-wrap {
        height: 20px;
        line-height: 20px;
        margin-top: 10px;
        font-size: 14px;
    }

    .tools-wrap {
        margin-top: 10px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .right-wrap {
            display: flex;
            flex-direction: row;
            align-items: center;
            font-size: 13px;
            cursor: pointer;
        }
    }
}


:deep(.disable .el-upload--picture-card) {
    display: none !important;
}
</style>