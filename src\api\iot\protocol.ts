// api/protocol.ts

import request from '/@/utils/request'

// 定义协议的类型
interface Protocol {
  id: string | number;
  name: string;
  description: string;
  // 根据实际情况补充协议的属性
}

// 定义返回的数据类型
interface ProtocolResponse {
  data: Protocol[];
  total: number;
}

// 查询协议列表
export function listProtocol(query: Record<string, any>): Promise<any> {
  return request({
    url: '/iot/protocol/list',
    method: 'get',
    params: query
  })
}

// 查询协议详细
export function getProtocol(id: string | number): Promise<Protocol> {
  return request({
    url: '/iot/protocol/' + id,
    method: 'get'
  })
}

// 新增协议
export function addProtocol(data: Protocol): Promise<void> {
  return request({
    url: '/iot/protocol',
    method: 'post',
    data
  })
}

// 修改协议
export function updateProtocol(data: Protocol): Promise<void> {
  return request({
    url: '/iot/protocol',
    method: 'put',
    data
  })
}

// 删除协议
export function delProtocol(id: string | number): Promise<void> {
  return request({
    url: '/iot/protocol/' + id,
    method: 'delete'
  })
}
