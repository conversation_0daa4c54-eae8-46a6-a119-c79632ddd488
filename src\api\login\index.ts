import request from '/@/utils/request';

/**
 * （不建议写成 request.post(xxx)，因为这样 post 时，无法 params 与 data 同时传参）
 *
 * 登录api接口集合
 * @method signIn 用户登录
 * @method signOut 用户退出登录
 */
export function useLoginApi() {
	return {
		signIn: (data: object) => {
			return request({
				url: '/user/signIn',
				method: 'post',
				data,
			});
		},
		signOut: (data: object) => {
			return request({
				url: '/user/signOut',
				method: 'post',
				data,
			});
		},
	};
}

// 获取登录验证码图片
export function getCodeImg(data: any) {
	return request({
		url: '/captchaImage',
		data: data,
	});
}
// 登录方法
export function loginApi(data: any) {	
	return request({
		url: '/login',
		headers: {
			isToken: false,
		},
		method: 'post',
		data: data,
	});
}
// 退出方法
export function logout() {
    return request({
        url: '/logout',
        method: 'post',
    });
}
//短信登录获取验证码
export function getSmsLoginCaptcha(phoneNumber:string) {
    return request({
        url: '/notify/smsLoginCaptcha?phoneNumber=' + phoneNumber,
        method: 'get',
    });
}

//短信登录
export function smsLogin(data:object) {
    return request({
        url: '/auth/sms/login',
        method: 'post',
        data: data,
    });
}
// 获取用户详细信息
export function getInfo() {
    return request({
        url: '/getInfo',
        method: 'get',
    });
}
