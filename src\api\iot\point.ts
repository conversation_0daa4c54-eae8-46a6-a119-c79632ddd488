import request from '/@/utils/request'

// 定义 `query` 和 `data` 的接口类型（根据实际情况修改这些类型）
interface QueryParams {
  [key: string]: any;  // 可以根据实际需要更改类型
}

interface PointData {
  id?: number;  // `id` 是可选的，因为新增时没有
  [key: string]: any;  // 可以根据实际需要更改类型
}

// 查询变量模板从机采集点列表
export function listPoint(query: QueryParams) {
  return request({
    url: '/iot/point/list',
    method: 'get',
    params: query
  })
}

// 查询变量模板从机采集点详细
export function getPoint(id: number) {
  return request({
    url: '/iot/point/' + id,
    method: 'get'
  })
}

// 新增变量模板从机采集点
export function addPoint(data: PointData) {
  return request({
    url: '/iot/point',
    method: 'post',
    data: data
  })
}

// 修改变量模板从机采集点
export function updatePoint(data: PointData) {
  return request({
    url: '/iot/point',
    method: 'put',
    data: data
  })
}

// 删除变量模板从机采集点
export function delPoint(id: number) {
  return request({
    url: '/iot/point/' + id,
    method: 'delete'
  })
}

// 根据从机id删除采集点数据
export function delBySlaveId(data: PointData) {
  return request({
    url: '/iot/point/delBySlaveId',
    method: 'delete',
    data: data
  })
}

// 根据模板查询采集点
export function selectByTemp(query: QueryParams) {
  return request({
    url: '/iot/point/getPoints',
    method: 'get',
    params: query
  })
}
