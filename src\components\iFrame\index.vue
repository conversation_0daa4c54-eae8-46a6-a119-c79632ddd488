<template>
    <div v-loading="loading" :style="{ height }">
      <iframe
        :src="src"
        frameborder="no"
        style="width: 100%; height: 100%"
        scrolling="auto"
      />
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  
  const props = defineProps({
    src: {
      type: String,
      required: true,
    },
  });
  
  const height = ref<string>(`${document.documentElement.clientHeight - 94.5}px`);
  const loading = ref<boolean>(true);
  
  // Adjust height on window resize
  const updateHeight = () => {
    height.value = `${document.documentElement.clientHeight - 94.5}px`;
  };
  
  onMounted(() => {
    // Set loading state to false after 300ms
    setTimeout(() => {
      loading.value = false;
    }, 300);
  
    // Add event listener for resize
    window.addEventListener('resize', updateHeight);
  });
  
  // Cleanup on unmount
  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateHeight);
  });
  
  </script>
  