<template>
    <div class="layout-padding">
        <el-row>
            <div class="card-box deep" style="display: flex;justify-content: space-between;width: 100%;">
                <el-card style="height: 280px; width:calc(49% + 10px);">
                    <div class="title">
                        <span>CPU</span>
                    </div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <table cellspacing="0" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">属性</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">值</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">核心数</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">{{ server.cpu.cpuNum }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">用户使用率</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">{{ server.cpu.used }}%</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">系统使用率</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">{{ server.cpu.sys }}%</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">当前空闲率</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.cpu">{{ server.cpu.free }}%</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
                <el-card style="height: 280px; width:calc(49% + 10px);;">
                    <div class="title"><span>内存</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <table cellspacing="0" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">属性</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">内存</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">JVM</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">总内存</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.total }}G</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.total }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">已用内存</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.used }}G</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.used }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">剩余内存</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem">{{ server.mem.free }}G</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.free }}M</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">使用率</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.mem"
                                            :class="{ 'text-danger': server.mem.usage > 80 }">{{ server.mem.usage
                                            }}%
                                        </div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm"
                                            :class="{ 'text-danger': server.jvm.usage > 80 }">{{ server.jvm.usage
                                            }}%
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </div>

            <el-col :span="24" class="card-box">
                <el-card>
                    <div class="title"><span>服务器信息</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <table cellspacing="0" style="width: 100%;">
                            <tbody>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">服务器名称</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.computerName }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">操作系统</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.osName }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">服务器IP</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.computerIp }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">系统架构</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.osArch }}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="24" class="card-box">
                <el-card>
                    <div class="title"><span>Java虚拟机信息</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <table cellspacing="0" style="width: 100%; table-layout: fixed;">
                            <tbody>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">Java名称</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.name }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">Java版本</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.version }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">启动时间</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.startTime }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">运行时长</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.runTime }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="el-table__cell is-leaf">
                                        <div class="cell">安装路径</div>
                                    </td>
                                    <td colspan="3" class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.home }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="el-table__cell is-leaf">
                                        <div class="cell">项目路径</div>
                                    </td>
                                    <td colspan="3" class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.sys">{{ server.sys.userDir }}</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="1" class="el-table__cell is-leaf">
                                        <div class="cell">运行参数</div>
                                    </td>
                                    <td colspan="3" class="el-table__cell is-leaf">
                                        <div class="cell" v-if="server.jvm">{{ server.jvm.inputArgs }}</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </el-col>

            <el-col :span="24" class="card-box">
                <el-card>
                    <div class="title"><span>磁盘状态</span></div>
                    <div class="el-table el-table--enable-row-hover el-table--medium padd">
                        <table cellspacing="0" style="width: 100%;">
                            <thead>
                                <tr>
                                    <th class="el-table__cell el-table__cell is-leaf">
                                        <div class="cell">盘符路径</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">文件系统</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">盘符类型</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">总大小</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">可用大小</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">已用大小</div>
                                    </th>
                                    <th class="el-table__cell is-leaf">
                                        <div class="cell">已用百分比</div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody v-if="server.sysFiles">
                                <tr v-for="(sysFile, index) in server.sysFiles" :key="index">
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.dirName }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.sysTypeName }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.typeName }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.total }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.free }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell">{{ sysFile.used }}</div>
                                    </td>
                                    <td class="el-table__cell is-leaf">
                                        <div class="cell" :class="{ 'text-danger': sysFile.usage > 80 }">{{
                                            sysFile.usage
                                            }}%</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { getServer } from '/@/api/monitor/server';
import { ElLoading } from 'element-plus';

interface Cpu {
    cpuNum: number;
    used: number;
    sys: number;
    free: number;
}

interface Mem {
    total: number;
    used: number;
    free: number;
    usage: number;
}

interface Jvm {
    total: number;
    used: number;
    free: number;
    usage: number;
    name: string;
    version: string;
    startTime: string;
    runTime: string;
    home: string;
    inputArgs: string;
}

interface Sys {
    computerName: string;
    osName: string;
    computerIp: string;
    osArch: string;
    userDir: string;
}

interface Disk {
    dirName: any;
    sysTypeName: any;
    typeName: any;
    total: any;
    free: any;
    used: any;
    usage: any;
}

interface Server {
    cpu: Cpu;
    mem: Mem;
    jvm: Jvm;
    sys: Sys;
    sysFiles: Disk[];
}
const loadingInstance = ref<any | null>(null);
const server = ref<Server>({
    cpu: { cpuNum: 0, used: 0, sys: 0, free: 0 },
    mem: { total: 0, used: 0, free: 0, usage: 0 },
    jvm: { total: 0, used: 0, free: 0, usage: 0, name: ' ', version: '', startTime: '', runTime: '', home: '', inputArgs: '' },
    sys: { computerName: '', osName: '', computerIp: '', osArch: '', userDir: '' },
    sysFiles: [
        {
            dirName: undefined, total: 0, used: 0, free: 0, usage: undefined,
            sysTypeName: undefined,
            typeName: undefined
        },
        {
            dirName: undefined, total: 0, used: 0, free: 0, usage: undefined,
            sysTypeName: undefined,
            typeName: undefined
        },
    ],
});
// 初始化表格数据
const getTableData = async () => {
    await getServer().then(response => {
        server.value = response.data.data;
        loadingInstance.value.close()
    });
};
const openLoading = () => {
    loadingInstance.value = ElLoading.service({
        text: '正在加载服务监控数据，请稍候！',
        background: 'rgba(0, 0, 0, 0.7)', // 自定义背景色
    });
};
// 页面加载时
onMounted(() => {
    getTableData();
    openLoading()
});
</script>

<style scoped>
.card-box {
    margin-bottom: 20px;
}

:deep(.el-card__body) {
    padding: 15px 0 20px 0 !important;
}

.padd {
    padding: 0 20px;
}

.title {
    height: 30px;
    padding-left: 20px;
    border-bottom: 1px solid #e6ebf5;
    margin-bottom: 10px;
}
</style>