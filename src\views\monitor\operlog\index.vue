<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <el-form :model="state.tableData.param" ref="queryForm" size="default" :inline="true" v-show="showSearch"
                label-width="68px">
                <el-form-item label="系统模块" prop="title">
                    <el-input v-model="state.tableData.param.title" placeholder="请输入系统模块" clearable
                        style="width: 240px;" />
                </el-form-item>
                <el-form-item label="操作人员" prop="operName">
                    <el-input v-model="state.tableData.param.operName" placeholder="请输入操作人员" clearable
                        style="width: 240px;" />
                </el-form-item>
                <el-form-item label="类型" prop="businessType">
                    <el-select v-model="state.tableData.param.businessType" placeholder="操作类型" clearable
                        style="width: 240px">
                        <el-option v-for="dict in typelist" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-select v-model="state.tableData.param.status" placeholder="操作状态" clearable style="width: 240px">
                        <el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
                            :value="dict.dictValue" />
                    </el-select>
                </el-form-item>
                <el-form-item label="操作时间">
                    <el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
                        end-placeholder="结束日期"></el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button size="default" type="primary" class="ml10" @click="getTableData">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        查询
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>
                        重置
                    </el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8" :justify="'space-between'">
                <div>
                    <el-button type="danger" size="default" :disabled="multiple" @click="handleDelete"
                        v-auths="['monitor:operlog:remove']">
                        <el-icon><ele-DeleteFilled /></el-icon>删除
                    </el-button>
                    <el-button type="danger" size="default" @click="handleClean" v-auths="['monitor:operlog:remove']">
                        <el-icon><ele-DeleteFilled /></el-icon>清空
                    </el-button>
                    <el-button type="warning" size="default" @click="handleExport" v-auths="['monitor:operlog:export']">
                        <el-icon><ele-Download /></el-icon>导出
                    </el-button>
                </div>
                <right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
                    @queryTable="getTableData"></right-toolbar>
            </el-row>
            <el-table v-loading="state.tableData.loading" :data="state.tableData.data"
                @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange"
                border style="width: 100%" :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="日志编号" align="center" prop="operId" />
                <el-table-column label="系统模块" align="center" prop="title" />
                <el-table-column label="操作类型" width="100"  align="center" prop="businessType">
                    <template #default="scope">
                        <!-- <div>{{ scope.row }}</div> -->
                        <DictTag :options="typelist" :value="scope.row.businessType"></DictTag>
                    </template>
                </el-table-column>
                <el-table-column label="请求方式" align="center" prop="requestMethod" />
                <el-table-column label="操作人员" align="center" prop="operName" width="130" :show-overflow-tooltip="true"
                    sortable="custom" :sort-orders="['descending', 'ascending']" />
                <el-table-column label="操作地址" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
                <el-table-column label="操作地点" align="center" prop="operLocation" :show-overflow-tooltip="true" />
                <el-table-column label="操作状态" align="center" prop="status">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.status"></DictTag>
                        <!-- <el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">{{ scope.row.status == 0 ? '成功' :
                            '失败'
                            }}</el-tag> -->
                    </template>
                </el-table-column>
                <el-table-column label="操作日期" align="center" prop="operTime" sortable="custom"
                    :sort-orders="['descending', 'ascending']" width="180">
                    <template #default="scope">
                        <span>{{ scope.row.operTime }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="default-padding fixed-width">
                    <template #default="scope">
                        <el-button size="default" text  type="primary" @click="handleView(scope.row)"
                            v-auths="['monitor:operlog:query']">
                            <el-icon><ele-View /></el-icon>详细
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>

            <!-- 操作日志详细 -->
            <el-dialog style="position: absolute; top: 100px;" title="操作日志详细" v-model="open" width="700px" append-to-body>
                <el-form :model="form" label-width="100px" size="default">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="操作模块：">{{ form.title }} / {{ typeFormat(form) }}</el-form-item>
                            <el-form-item label="登录信息：">{{ form.operName }} / {{ form.operIp }} / {{
                                form.operLocation }}</el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="请求地址：">{{ form.operUrl }}</el-form-item>
                            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="操作方法：">{{ form.method }}</el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="请求参数：">{{ form.operParam }}</el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="返回参数：">{{ form.jsonResult }}</el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="操作状态：">
                                <div v-if="form.status === 0">正常</div>
                                <div v-else-if="form.status === 1">失败</div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="操作时间：">{{ form.operTime }}</el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="异常信息：" v-if="form.status === 1">{{ form.errorMsg }}</el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer" style="float: right; margin-top: 20px">
                    <el-button @click="open = false">关 闭</el-button>
                </div>
            </el-dialog>
        </el-card>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, Ref } from 'vue';
import { list, delOperlog, cleanOperlog } from "/@/api/monitor/operlog";
import { ElMessageBox, ElMessage } from 'element-plus';
import { addDateRange, selectDictLabel } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { download } from '/@/utils/request';
import DictTag from '/@/components/DictTag/index.vue'

const dictStore = useDictStore();  // 使用 Pinia store
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            title: undefined,
            operName: undefined,
            businessType: undefined,
            status: '',
            orderByColumn: undefined,
            isAsc: ''
        }
    },
});
let form = reactive({
    title: '',
    operName: '',
    operIp: '',
    operLocation: '',
    operUrl: '',
    requestMethod: '',
    method: '',
    operParam: '',
    jsonResult: '',
    operTime: '',
    errorMsg: '',
    status: 0,

});
const defaultSort = reactive({
    prop: 'operTime',
    order: 'descending',
});
// 显示搜索条件
const showSearch = ref(true);
interface statusOption {
    dictValue: string;
    dictLabel: string;
    listClass:string;
    cssClass:string;
}
interface typeOption {
    dictValue: string;
    dictLabel: string;
    listClass:string;
    cssClass:string;
}
const statuslist = ref<statusOption[]>([]); //状态列表
const typelist = ref<typeOption[]>([]); //类型列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref()
const dateRange = ref<[string, string]>(['', '']); //时间范围

const open = ref(false)

// 初始化表格数据 查询登录日志列表
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const data = addDateRange(state.tableData.param, dateRange.value)
        const response = await list(data);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        operName: undefined,
        businessType: undefined,
        orderByColumn: undefined,
        isAsc: '',
        status: ''
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_common_status')
        typelist.value = await dictStore.fetchDict('sys_oper_type')        
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
    ids.value = selection.map((item: { operId: string; }) => item.operId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
    console.log(ids.value);

}
// 排序触发事件
const handleSortChange = (column: { prop: undefined; order: string; }) => {
    state.tableData.param.orderByColumn = column.prop;  // 设置排序字段
    state.tableData.param.isAsc = column.order;  // 判断升序或降序
    getTableData();  // 触发数据获取
};
/** 详细按钮操作 */
const handleView = (row: any) => {
    open.value = true; 
    form = row;
}
// 操作日志类型字典翻译
const typeFormat = (row: any) => {
    return selectDictLabel(typelist, row.businessType);
}
// 删除
const handleDelete = (row: { operId: Ref<any, any>; }) => {
    const operIds = row.operId || ids.value;
    ElMessageBox.confirm('是否确认删除日志编号为"' + operIds + '"的数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delOperlog(operIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
}
// 清空
const handleClean = () => {
    ElMessageBox.confirm('是否确认清空所有操作日志数据项？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            cleanOperlog().then(() => {
                getTableData();
                ElMessage.success('清空成功');
            })
        })
        .catch(() => { });
}
// 导出
const handleExport = () => {
    download('monitor/operlog/export', {
        ...state.tableData.param
    }, `operlog_${new Date().getTime()}.xlsx`)
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>