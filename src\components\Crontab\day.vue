<template>
  <el-form size="small">
    <!-- 日，允许的通配符 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="1">日，允许的通配符[, - * ? / L W]</el-radio>
    </el-form-item>

    <!-- 不指定 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="2">不指定</el-radio>
    </el-form-item>

    <!-- 周期从 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="3">
        周期从
        <el-input-number v-model="cycle01" :min="1" :max="30" /> -
        <el-input-number v-model="cycle02" :min="cycle01 + 1" :max="31" />
        日
      </el-radio>
    </el-form-item>

    <!-- 从开始，每N天执行一次 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="4">
        从
        <el-input-number v-model="average01" :min="1" :max="30" /> 号开始，每
        <el-input-number v-model="average02" :min="1" :max="31 - average01 || 1" /> 日执行一次
      </el-radio>
    </el-form-item>

    <!-- 每月最近的工作日 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="5">
        每月
        <el-input-number v-model="workday" :min="1" :max="31" /> 号最近的那个工作日
      </el-radio>
    </el-form-item>

    <!-- 本月最后一天 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="6">本月最后一天</el-radio>
    </el-form-item>

    <!-- 指定 -->
    <el-form-item>
      <el-radio v-model="radioValue" :label="7">
        指定
        <el-select clearable v-model="checkboxList" placeholder="可多选" multiple style="width:100%">
          <el-option v-for="item in 31" :key="item" :value="item">{{ item }}</el-option>
        </el-select>
      </el-radio>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Define props
const props = defineProps({
  check: Function as any,
  cron: Object as () => {
    week: string;
  }
});

// Define emit
const emit = defineEmits<{
  (event: 'update', field: string, value: string, from: string): void;
}>();

// Reactive data
const radioValue = ref<any>(1);
const workday = ref<any>(1);
const cycle01 = ref<any>(1);
const cycle02 = ref<any>(2);
const average01 = ref<any>(1);
const average02 = ref<any>(1);
const checkboxList = ref<any[]>([]);

// Computed properties for cycle and average
const cycleTotal = computed(() => {
  const cycle01Value = props.check(cycle01.value, 1, 30);
  const cycle02Value = props.check(cycle02.value, cycle01Value ? cycle01Value + 1 : 2, 31);
  return `${cycle01Value}-${cycle02Value}`;
});

const averageTotal = computed(() => {
  const average01Value = props.check(average01.value, 1, 30);
  const average02Value = props.check(average02.value, 1, 31 - average01.value || 0);
  return `${average01Value}/${average02Value}`;
});

const workdayCheck = computed(() => {
  return props.check(workday.value, 1, 31);
});

const checkboxString = computed(() => {
  return checkboxList.value.length > 0 ? checkboxList.value.join() : '*';
});

// Common emit function
const updateDay = (value: string) => {
  emit('update', 'day', value, 'day');
};

// Watchers
watch(radioValue, () => {
  switch (radioValue.value) {
    case 1: updateDay('*'); break;
    case 2: updateDay('?'); break;
    case 3: updateDay(cycleTotal.value); break;
    case 4: updateDay(averageTotal.value); break;
    case 5: updateDay(`${workdayCheck.value}W`); break;
    case 6: updateDay('L'); break;
    case 7: updateDay(checkboxString.value); break;
  }
});

watch(cycleTotal, () => {
  if (radioValue.value === 3) updateDay(cycleTotal.value);
});

watch(averageTotal, () => {
  if (radioValue.value === 4) updateDay(averageTotal.value);
});

watch(workdayCheck, () => {
  if (radioValue.value === 5) updateDay(`${workdayCheck.value}W`);
});

watch(checkboxString, () => {
  if (radioValue.value === 7) updateDay(checkboxString.value);
});
defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList,
  workday
});
</script>