import request from '/@/utils/request'

// 定义接口参数和返回类型
interface Template {
  // 根据实际返回的数据结构，调整字段类型
  id: string;
  name: string;
  description: string;
  // 更多字段...
}

// 返回的数据类型（你可以根据实际返回数据修改类型）
interface ApiResponse<T = any> {
  msg: string;
  code: number;
  data: T;
  message: string;
}

// 物模型数据类型
interface ModelData {
  modelId?: number;  // 如果有 id，则是可选的
  [key: string]: any;  // 根据实际情况进行修改
}

// 查询通用物模型列表
export function listTemplate(query: any) {
  return request({
    url: '/iot/thingsModel/list',
    method: 'get',
    params: query
  })
}

// 查询通用物模型详细
export function getTemplate(templateId: string) {
  return request({
    url: `/iot/thingsModel/${templateId}`,
    method: 'get'
  })
}

// 新增通用物模型
export function addTemplate(data: Template) {
  return request({
    url: '/iot/thingsModel',
    method: 'post',
    data: data
  })
}

// 修改通用物模型
export function updateTemplate(data: Template) {
  return request({
    url: '/iot/thingsModel',
    method: 'put',
    data: data
  })
}

// 删除通用物模型
export function delTemplate(templateId: string) {
  return request({
    url: `/iot/thingsModel/${templateId}`,
    method: 'delete'
  })
}

// 查询通用物模型详细的所有点
// export function getAllPoints(params: any) {
//   return request({
//     url: '/iot/template/getPoints',
//     method: 'get',
//     params: params
//   })
// }

// 根据产品ID获取缓存的物模型
export function cacheJsonThingsModel(productId: number) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/thingsModel/cache/' + productId,
    method: 'get'
  });
}

// 导入通用物模型
export function importModel(data: ModelData) {
  return request<ApiResponse>({  // 使用泛型声明返回类型
    url: '/iot/thingsModel/import',
    method: 'post',
    data: data
  });
}
