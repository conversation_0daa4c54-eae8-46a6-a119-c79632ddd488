declare global {
    interface Window {
      BMap: any;
      onBMapCallback: () => void;
    }
  }
  
  export function loadBMap(): Promise<typeof window.BMap> {
    return new Promise((resolve, reject) => {
      // 如果BMap已经加载，直接返回
      if (typeof window.BMap !== 'undefined') {
        resolve(window.BMap);
        return;
      }
  
      // 定义回调函数
      window.onBMapCallback = () => {
        resolve(window.BMap);
      };
  
      // 使用https协议时添加meta标签
      const protocolStr = document.location.protocol;
      if (protocolStr === 'https:') {
        const meta = document.createElement('meta');
        meta.httpEquiv = 'Content-Security-Policy';
        meta.content = 'upgrade-insecure-requests';
        meta.onerror = reject;
        document.head.appendChild(meta);
      }
  
      // 引入百度地图
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = `https://api.map.baidu.com/api?v=3.0&ak=${'nAtaBg9FYzav6c8P9rF9qzsWZfT8O0PD'}&s=1&callback=onBMapCallback`;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }
  