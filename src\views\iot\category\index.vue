<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="分类名称" prop="categoryName">
                        <el-input v-model="state.tableData.param.categoryName" clearable size="default"
                            placeholder="请输入产品分类名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:category:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button size="default" type="info" class="ml10" @click="toggleExpandAll">
							<el-icon><ele-Sort /></el-icon>
							展开/折叠
						</el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table v-if="refreshTable" :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" :default-expand-all="isExpandAll" 
            row-key="categoryId" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                <el-table-column label="产品分类名称" align="center" prop="categoryName" width="300" />
                <el-table-column label="备注" align="left" header-align="center" prop="remark" width="150" />
                <!-- <el-table-column label="系统定义" align="center" prop="isSys" width="100">
                    <template #default="scope">
                        <DictTag :options="statuslist" :value="scope.row.isSys" />
                    </template>
                </el-table-column> -->
                <el-table-column label="显示顺序" align="center" prop="orderNum" width="150"/>
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default"  type="primary" @click="onOpenAdd('add', scope.row)"
                            v-auths="['iot:category:add']"><el-icon size="small"><ele-Plus /></el-icon>新增</el-button>
                        <el-button size="default"  type="primary" @click="onOpenEditDic('edit', scope.row)"
                            v-auths="['iot:category:query']"><el-icon size="small"><ele-View /></el-icon>查看</el-button>
                        <el-button size="default"  type="danger" @click="onRowDel(scope.row)"
                            v-auths="['iot:category:remove']"><el-icon
                                size="large"><ele-DeleteFilled /></el-icon>删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination> -->
        </el-card>
        <CategoryDialog ref="CategoryDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { handleTree, parseTime } from '/@/utils/next'
import DictTag from '/@/components/DictTag/index.vue'
import { delCategory, listCategory } from '/@/api/iot/category';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const CategoryDialog = defineAsyncComponent(() => import('/@/views/iot/category/dialog.vue'));

// 定义变量内容
const CategoryDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        // total: 0,
        loading: false,
        param: {
            // pageNum: 1,
            // pageSize: 20,
            categoryName: undefined,//分类名称
        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //categoryId
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<TypeOption[]>([]);
// 是否展开，默认全部展开
const isExpandAll = ref(true)
// 重新渲染表格状态
const refreshTable = ref(true)
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listCategory(state.tableData.param); 
        state.tableData.data = handleTree(response.data.data, "categoryId") as any;
        console.log(state.tableData.data);
        // state.tableData.total = response.data.total;
        // console.log(state.tableData.data);
        
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        // pageNum: 1,
        // pageSize: 20,
        categoryName: undefined,
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('iot_yes_no')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/** 展开/折叠操作 */
const toggleExpandAll = () => {
	refreshTable.value = false;
	isExpandAll.value = !isExpandAll.value;
	nextTick(() => {
		refreshTable.value = true;
	});
}
// 打开新增产品分类弹窗
const onOpenAddDic = (type: string) => {
    CategoryDialogRef.value.openDialog(type);
};
// 打开修改产品分类弹窗
const onOpenAdd = (type: string, row: RowNewsCategoryType | undefined) => {
    var categoryId = ''
    if (!row) {
        categoryId = ids.value
    } else {
        categoryId = row.categoryId
    }
    CategoryDialogRef.value.openDialog(type, row, categoryId);
};
// 打开修改产品分类弹窗
const onOpenEditDic = (type: string, row: RowNewsCategoryType | undefined) => {
    var categoryId = ''
    if (!row) {
        categoryId = ids.value
    } else {
        categoryId = row.categoryId
    }
    CategoryDialogRef.value.openDialog(type, row, categoryId);
};
// 删除产品分类
const onRowDel = (row: RowNewsCategoryType) => {
    const categoryIds = row.categoryId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除产品分类编号为：“${categoryIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delCategory(categoryIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 分页改变
// const onHandleSizeChange = (val: number) => {
//     state.tableData.param.pageSize = val;
//     getTableData();
// };
// 分页改变
// const onHandleCurrentChange = (val: number) => {
//     state.tableData.param.pageNum = val;
//     getTableData();
// };
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
