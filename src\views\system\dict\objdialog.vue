<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="500px">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="字典类型" prop="dictType" >
                            <el-input :disabled="true" v-model="state.ruleForm.dictType" placeholder="请输入字典类型" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="数据标签" prop="dictLabel">
                            <el-input v-model="state.ruleForm.dictLabel" placeholder="请输入数据标签" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="数据键值" prop="dictValue">
                            <el-input v-model="state.ruleForm.dictValue" placeholder="请输入数据键值" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="样式属性" prop="cssClass">
                            <el-input v-model="state.ruleForm.cssClass" placeholder="请输入样式属性" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="显示排序" prop="dictSort">
                            <el-input-number v-model="state.ruleForm.dictSort" controls-position="right" :min="0" />
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="回显样式" prop="listClass">
                            <el-select v-model="state.ruleForm.listClass">
                                <el-option v-for="item in listClassOptions" :key="item.value"
                                    :label="item.label + '(' + item.value + ')'" :value="item.value"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="状态">
                            <el-radio-group v-model="state.ruleForm.status">
                                <el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue"
                                    :value="item.dictValue">{{
                                        item.dictLabel
                                    }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="备注">
                            <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
                                maxlength="150"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{ state.dialog.submitTxt
                        }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="systemDicDialog">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addData, getData, updateData } from '/@/api/system/dict/data';

const dictStore = useDictStore();  // 使用 Pinia store

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        dictValue: '',
        dictType: '',
        dictLabel: '',
        status: '0',
        remark: '',
        cssClass: '',
        listClass:'default',
        dictSort: 0
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}
// const dictId  = ref()
const listClassOptions = reactive([
    {
        value: "default",
        label: "默认"
    },
    {
        value: "primary",
        label: "主要"
    },
    {
        value: "success",
        label: "成功"
    },
    {
        value: "info",
        label: "信息"
    },
    {
        value: "warning",
        label: "警告"
    },
    {
        value: "danger",
        label: "危险"
    }
])    // 数据标签回显样式

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    // deptData: [...initialState.deptData],
    dialog: { ...initialState.dialog },
});
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<statusOption[]>([]); //状态
// 校验规则
const rules = reactive({
    dictLabel: [
        { required: true, message: "数据标签不能为空", trigger: "blur" }
    ],
    dictValue: [
        { required: true, message: "数据键值不能为空", trigger: "blur" }
    ],
    dictSort: [
        { required: true, message: "数据顺序不能为空", trigger: "blur" }
    ]

})
// 打开弹窗
const openDialog = (type: string, row: any, dictId: string) => {
    if (type === 'edit') {
        if (row != undefined) {
            getData(row.dictCode).then(response => {
                state.ruleForm = response.data.data
            });
        } else {
            getData(dictId).then(response => {
                state.ruleForm = response.data.data
            });
        }
        state.dialog.title = '修改字典类型';
        state.dialog.submitTxt = '修 改';
        state.dialog.type = 'updata'
    } else {
        resetState();
        state.dialog.title = '新增字典类型';
        state.dialog.submitTxt = '新 增';
        state.ruleForm.dictType = row
    }
    state.dialog.isShowDialog = true;
    getdictdata()
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.dialog.type != '') {
                updateData(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addData(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_normal_disable')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
