<template>
	<div class="system-dept-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<div class="system-dept-search mb15">
				<el-form :model="queryParams" ref="queryForm" size="default" :inline="true" v-show="showSearch">
					<el-form-item label="菜单名称" prop="deptName">
						<el-input v-model="queryParams.deptName" placeholder="请输入菜单名称" clearable />
					</el-form-item>
					<el-form-item label="状态">
						<el-select v-model="queryParams.status" placeholder="用户状态" clearable style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item>
						<el-button size="default" type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:dept:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddDept('add')">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button size="default" type="info" class="ml10" @click="toggleExpandAll">
							<el-icon><ele-Sort /></el-icon>
							展开/折叠
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
				<!-- <el-input size="default" placeholder="请输入部门名称" style="max-width: 180px"> </el-input> -->
				<!-- <el-button size="default" type="primary" class="ml10">
					<el-icon>
						<ele-Search />
					</el-icon>
					查询
				</el-button>
				<el-button size="default" type="success" class="ml10" @click="onOpenAddDept('add')">
					<el-icon>
						<ele-FolderAdd />
					</el-icon>
					新增部门
				</el-button> -->
			</div>
			<el-table ref="table" v-if="refreshTable" :data="state.tableData.data" v-loading="state.tableData.loading"
				style="width: 100%" row-key="deptId" :default-expand-all="isExpandAll" border
				:tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column prop="deptName" label="部门名称" show-overflow-tooltip width="300"> </el-table-column>
				<el-table-column prop="orderNum" label="排序" show-overflow-tooltip width="80"
					align="center"></el-table-column>
				<el-table-column prop="status" label="部门状态" show-overflow-tooltip align="center" width="100">
					<template #default="scope">
						<DictTag :options="statuslist" :value="scope.row.status"></DictTag>
						<!-- <el-tag :type="scope.row.status == 0 ? '' : 'danger'">{{ scope.row.status == 0 ? '正常' :
							'停用'
							}}</el-tag> -->
					</template>
				</el-table-column>
				<el-table-column prop="createTime" label="创建时间" show-overflow-tooltip align="center"
					width="400"></el-table-column>
				<el-table-column label="操作" show-overflow-tooltip align="center" class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button v-auths="['system:dept:add']" size="default" text type="primary"
							@click="onOpenAddDept('add', scope.row)"><el-icon><ele-Plus /></el-icon>新增</el-button>
						<el-button v-auths="['system:dept:edit']" class="ml15" size="default" text type="primary"
							@click="onOpenEditDept('edit', scope.row)"><el-icon><ele-EditPen /></el-icon>修改</el-button>
						<el-button v-if="scope.row.parentId != 0" v-auths="['system:dept:remove']" class="ml15"
							size="default" text type="primary"
							@click="onTabelRowDel(scope.row)"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
					</template>
				</el-table-column>
			</el-table>
		</el-card>
		<DeptDialog ref="deptDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemDept">
import { defineAsyncComponent, ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from "/@/api/system/dept";
import { handleTree } from '/@/utils/next';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const DeptDialog = defineAsyncComponent(() => import('/@/views/system/dept/dialog.vue'));

// 定义变量内容
const deptDialogRef = ref();
const state = reactive<SysDeptState>({
	tableData: {
		data: [] as DeptTreeType[],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
		},
	},
});
// 搜索内容
let queryParams = reactive({
	deptName: undefined,
	status: ''
});
interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}
// 状态列表
const statuslist = ref<statusOption[]>([]);
// 是否展开，默认全部展开
const isExpandAll = ref(true)
// 重新渲染表格状态
const refreshTable = ref(true)
// 显示搜索条件
const showSearch = ref(true)
// 初始化表格数据
const getTableData = () => {
	state.tableData.loading = true;
	state.tableData.data = [];
	listDept(queryParams).then(response => {
		state.tableData.data = handleTree(response.data.data, "deptId");
		state.tableData.loading = false;
		console.log(state.tableData.data);
		
	});
	setTimeout(() => {
		state.tableData.loading = false;
	}, 500);
};
/** 重置按钮操作 */
const resetQuery = () => {
	queryParams.deptName = undefined
	queryParams.status = ''
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
/** 展开/折叠操作 */
const toggleExpandAll = () => {
	refreshTable.value = false;
	isExpandAll.value = !isExpandAll.value;
	nextTick(() => {
		refreshTable.value = true;
	});
}
// 打开新增菜单弹窗
const onOpenAddDept = (type: string, row?: DeptTreeType) => {
	deptDialogRef.value.openDialog(type, row);
};
// 打开编辑菜单弹窗
const onOpenEditDept = (type: string, row: DeptTreeType) => {
	deptDialogRef.value.openDialog(type, row);
};
// 删除当前行
const onTabelRowDel = (row: DeptTreeType) => {
	ElMessageBox.confirm(`此操作将永久删除部门：${row.deptName}, 是否继续?`, '提示', {
		confirmButtonText: '删除',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		// 删除角色 API 调用
		try {
			await delDept(row.deptId);
			await getTableData();
			ElMessage.success('删除成功');
		} catch (error) {
			ElMessage.error('删除失败');
		}
	}).catch(() => {
		// 取消删除，不做任何操作
	});

};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>
