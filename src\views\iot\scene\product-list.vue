<template>
    <el-dialog style="position: absolute; top: 100px;" v-model="open" width="900px">
        <div style="padding: 0;">
            <el-form :model="state.tableData.param" ref="queryForm" :inline="true" label-width="68px">
                <el-form-item label="产品名称" prop="productName">
                    <el-input v-model="state.tableData.param.productName" placeholder="请输入产品名称" clearable size="default"
                        @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="default" @click="handleQuery">
                        <el-icon><ele-Search /></el-icon>搜索
                    </el-button>
                    <el-button size="default" @click="resetQuery">
                        <el-icon><ele-Refresh /></el-icon>重置
                    </el-button>
                </el-form-item>
            </el-form>

            <el-table v-loading="state.tableData.loading" ref="singleTable" :data="state.tableData.productList"
                @row-click="rowClick" highlight-current-row size="default" border>
                <el-table-column label="选择" width="60" align="center">
                    <template #default="scope">
                        <input type="radio" :checked="scope.row.isSelect" name="product" />
                    </template>
                </el-table-column>
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="分类名称" align="center" prop="categoryName" />
                <el-table-column label="授权码" align="center" prop="status" width="70">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.isAuthorize == 1">启用</el-tag>
                        <el-tag type="info" v-if="scope.row.isAuthorize == 0">未启用</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="设备类型" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="device_type_list" :value="scope.row.deviceType" />
                    </template>
                </el-table-column>
                <el-table-column label="产品状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="product_status_list" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="100">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange"
                class="mt15" style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="closeDialog" type="info">关 闭</el-button>
                <el-button @click="confirmSelectProduct" type="primary">确定</el-button>
            </span>
        </template>
    </el-dialog>
</template>
<script setup lang="ts" name="">
import { reactive, ref   } from 'vue';
import { listProduct } from '/@/api/iot/product';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
const dictStore = useDictStore();  // 使用 Pinia store
import { parseTime } from '/@/utils/next'
const emit = defineEmits(); // 定义 emit
const state = reactive({
    tableData: {
        productList: [] as any[],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            productName: '',
            scriptId: '' as any,
            scriptName: '' as any

        },
    },
});
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const vertificate_method_list = ref<TypeOption[]>([]);
const network_method_list = ref<TypeOption[]>([]);
const device_type_list = ref<TypeOption[]>([]);
const product_status_list = ref<TypeOption[]>([]);
const open = ref(false);
const products = ref({
    productId: '',
    productName: '',
    categoryName: '',
    tenantName: '',
    isAuthorize: '',
    vertificateMethod: '',
    networkMethod: '',
    deviceType: '',
    status: '',
    createTime: ''
});//选中的产品
// 选中的产品编号
const selectProductId = ref(0)
/** 查询产品列表 */
const getList = () => {
    state.tableData.loading = true;
    try {
        listProduct(state.tableData.param).then((response) => {
            //产品列表初始化isSelect值，用于单选
            for (let i = 0; i < response.data.rows.length; i++) {
                response.data.rows[i].isSelect = false;
            }
            state.tableData.productList = response.data.rows;
            state.tableData.total = response.data.total;
            // 设置产品选中
            setRadioSelected(selectProductId.value);
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.param.pageNum = 1;
    getList();
}
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        scriptId: '' as any,
        scriptName: '' as any
    }
    handleQuery();
}
/** 单选数据 */
const rowClick = (product: any) => {
    if (product != null) {      
        products.value = product;
        setRadioSelected(product.productId);        
    }
}
/** 设置单选按钮选中 */
const setRadioSelected = (productId: any) => {
    for (let i = 0; i < state.tableData.productList.length; i++) {
        if (state.tableData.productList[i].productId == productId) {
            state.tableData.productList[i].isSelect = true;
        } else {
            state.tableData.productList[i].isSelect = false;
        }
    }
}
/**确定选择产品，产品传递给父组件 */
const confirmSelectProduct = () => {
    emit('productEvent', products.value);
    open.value = false;
}
/**关闭对话框 */
const closeDialog = () => {
    open.value = false;
}
// 打开弹窗
const openProductList = () => {
    open.value = true
    getList();
    getdictdata();
};
// 获取状态数据
const getdictdata = async () => {
    try {
        vertificate_method_list.value = await dictStore.fetchDict('iot_vertificate_method ')
        network_method_list.value = await dictStore.fetchDict('iot_network_method')
        device_type_list.value = await dictStore.fetchDict('iot_device_type')
        product_status_list.value = await dictStore.fetchDict('iot_product_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getList();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getList();
};
// 暴露变量
defineExpose({
    openProductList,
});
</script>