// Vue 3 + TypeScript 图表选项类型定义
import type { ECharts } from 'echarts';

/**
 * 图表数据项接口
 */
export interface ChartDataItem {
  name: string;
  value: string | number;
  [key: string]: any;
}

/**
 * 地图数据接口
 */
export interface MapDataItem extends ChartDataItem {
  name: string;
  value: string | number;
}

/**
 * 图表选项生成器函数类型
 */
export type ChartOptionGenerator = (echarts: typeof import('echarts'), data: any) => any;

/**
 * 图表选项配置接口
 */
export interface ChartOptionConfig {
  /**
   * 获取函数字符串
   * @param optionStr 选项字符串
   * @returns 函数字符串
   */
  getFun: (optionStr: string) => string;

  /**
   * 获取地图图表选项字符串
   * @returns 地图选项字符串
   */
  getOptionMap: () => string;

  /**
   * 获取3D柱状图选项字符串
   * @returns 3D柱状图选项字符串
   */
  getOption: () => string;

  /**
   * 获取饼图选项字符串
   * @returns 饼图选项字符串
   */
  getOptionPie?: () => string;

  /**
   * 获取折线图选项字符串
   * @returns 折线图选项字符串
   */
  getOptionLine?: () => string;

  /**
   * 获取柱状图选项字符串
   * @returns 柱状图选项字符串
   */
  getOptionBar?: () => string;
}

/**
 * Vue 3 图表选项配置实现
 */
const chartOption: ChartOptionConfig = {
  /**
   * 解析函数字符串
   */
  getFun(optionStr: string): string {
    const funStr = `function (echarts, echartData) {
${optionStr}
    return option;
}`;
    return funStr;
  },

  /**
   * 获取地图图表选项
   */
  getOptionMap(): string {
    const optionStr = `let option = {
    tooltip: {
        trigger: "item",
        formatter: function(params) {
            return params.name + ': ' + (params.value || 0);
        }
    },
    visualMap: {
        min: 30,
        max: 700,
        splitNumber: 0,
        text: ["高", "低"],
        realtime: false,
        calculable: false,
        selectedMode: false,
        itemWidth: 10,
        itemHeight: 60,
        inRange: {
            color: ["lightskyblue", "yellow", "orangered"]
        }
    },
    series: [{
        type: "map",
        map: "mapJson",
        scaleLimit: {
            min: 0.8,
            max: 1.9
        },
        mapLocation: {
            y: 60
        },
        emphasis: {
            label: {
                show: true
            },
            itemStyle: {
                areaColor: '#389BB7',
                borderWidth: 0
            }
        },
        label: {
            show: true,
            color: '#000'
        },
        data: [
            { name: "郑州市", value: "585" },
            { name: "洛阳市", value: "450" },
            { name: "许昌市", value: "256" },
            { name: "开封市", value: "398" },
            { name: "平顶山市", value: "444" },
            { name: "安阳市", value: "74" },
            { name: "鹤壁市", value: "127" },
            { name: "新乡市", value: "269" },
            { name: "焦作市", value: "36" },
            { name: "濮阳市", value: "187" },
            { name: "漯河市", value: "33" },
            { name: "三门峡市", value: "98" },
            { name: "商丘市", value: "254" },
            { name: "周口市", value: "87" },
            { name: "驻马店市", value: "76" },
            { name: "南阳市", value: "325" },
            { name: "信阳市", value: "333" },
            { name: "济源市", value: "15" }
        ]
    }]
};`;
    return optionStr;
  },

  /**
   * 获取3D柱状图选项
   */
  getOption(): string {
    const optionStr = `let xaxisData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
let yaxisData = [1600, 1880, 1100, 2200, 2400, 1350, 1180, 2500, 1800, 1400, 1950, 1580];
const offsetX = 10;
const offsetY = 5;

// 绘制左侧面
const CubeLeft = echarts.graphic.extendShape({
   shape: {
      x: 0,
      y: 0,
   },
   buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c0 = [shape.x, shape.y];
      const c1 = [shape.x - offsetX, shape.y - offsetY];
      const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY];
      const c3 = [xAxisPoint[0], xAxisPoint[1]];
      ctx
         .moveTo(c0[0], c0[1])
         .lineTo(c1[0], c1[1])
         .lineTo(c2[0], c2[1])
         .lineTo(c3[0], c3[1])
         .closePath();
   },
});

// 绘制右侧面
const CubeRight = echarts.graphic.extendShape({
   shape: {
      x: 0,
      y: 0,
   },
   buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint;
      const c1 = [shape.x, shape.y];
      const c2 = [xAxisPoint[0], xAxisPoint[1]];
      const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY];
      const c4 = [shape.x + offsetX, shape.y - offsetY];
      ctx
         .moveTo(c1[0], c1[1])
         .lineTo(c2[0], c2[1])
         .lineTo(c3[0], c3[1])
         .lineTo(c4[0], c4[1])
         .closePath();
   },
});

// 绘制顶面
const CubeTop = echarts.graphic.extendShape({
   shape: {
      x: 0,
      y: 0,
   },
   buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y];
      const c2 = [shape.x + offsetX, shape.y - offsetY];
      const c3 = [shape.x, shape.y - offsetX];
      const c4 = [shape.x - offsetX, shape.y - offsetY];
      ctx
         .moveTo(c1[0], c1[1])
         .lineTo(c2[0], c2[1])
         .lineTo(c3[0], c3[1])
         .lineTo(c4[0], c4[1])
         .closePath();
   },
});

// 注册三个面图形
echarts.graphic.registerShape("CubeLeft", CubeLeft);
echarts.graphic.registerShape("CubeRight", CubeRight);
echarts.graphic.registerShape("CubeTop", CubeTop);

let option = {
   backgroundColor: 'black',
   tooltip: {
      trigger: "axis",
      axisPointer: {
         type: "shadow",
      },
      backgroundColor: "rgba(255,255,255,0.75)",
      extraCssText: "box-shadow: 2px 2px 4px 0px rgba(0,0,0,0.3);",
      textStyle: {
         fontSize: 14,
         color: "#000",
      },
      formatter: (params, ticket, callback) => {
         const item = params[1];
         return item.name + " : " + item.value + " 个";
      },
   },
   grid: {
      left: "1%",
      right: "0%",
      top: "16%",
      bottom: "5%",
      containLabel: true,
   },
   xAxis: {
      type: 'category',
      data: xaxisData,
      axisLine: {
         show: true,
         lineStyle: {
            width: 1,
            color: '#008de7'
         }
      },
      axisTick: {
         show: false
      },
      axisLabel: {
         fontSize: 14,
         color: '#FFFFFF'
      }
   },
   yAxis: {
      name: "万千瓦时",
      type: 'value',
      nameTextStyle: {
         color: '#fff',
         fontWeight: 400,
         fontSize: 14
      },
      axisLine: {
         show: true,
         lineStyle: {
            width: 1,
            color: '#008de7'
         }
      },
      splitLine: {
         show: true,
         lineStyle: {
            color: '#008de7',
            type: 'dashed'
         }
      },
      axisTick: {
         show: false
      },
      axisLabel: {
         fontSize: 14,
         color: '#FFFFFF'
      }
   },
   series: [
      {
         type: "custom",
         renderItem: (params, api) => {
            const location = api.coord([api.value(0), api.value(1)]);
            return {
               type: "group",
               children: [
                  {
                     type: "CubeLeft",
                     shape: {
                        api,
                        xValue: api.value(0),
                        yValue: api.value(1),
                        x: location[0],
                        y: location[1],
                        xAxisPoint: api.coord([api.value(0), 0]),
                     },
                     style: {
                        fill: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                           {
                              offset: 0,
                              color: '#09f7f9',
                           },
                           {
                              offset: 1,
                              color: '#09f7f920'
                           }
                        ]),
                     },
                  },
                  {
                     type: "CubeRight",
                     shape: {
                        api,
                        xValue: api.value(0),
                        yValue: api.value(1),
                        x: location[0],
                        y: location[1],
                        xAxisPoint: api.coord([api.value(0), 0]),
                     },
                     style: {
                        fill: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                           {
                              offset: 0,
                              color: '#00a6a7',
                           },
                           {
                              offset: 1,
                              color: '#00a6a720'
                           },
                        ]),
                     },
                  },
                  {
                     type: "CubeTop",
                     shape: {
                        api,
                        xValue: api.value(0),
                        yValue: api.value(1),
                        x: location[0],
                        y: location[1],
                        xAxisPoint: api.coord([api.value(0), 0]),
                     },
                     style: {
                        fill: '#09f7f8'
                     },
                  },
               ],
            };
         },
         data: yaxisData,
      },
      {
         type: "bar",
         itemStyle: {
            color: "transparent",
         },
         data: yaxisData,
      },
   ],
};`;
    return optionStr;
  }
};

export default chartOption;
