<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="700">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default"
                label-width="100px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="任务名称" prop="jobName">
                            <el-input v-model="state.ruleForm.jobName" placeholder="请输入任务名称" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="任务分组" prop="jobGroup">
                            <el-select v-model="state.ruleForm.jobGroup" placeholder="请选择任务分组">
                                <el-option v-for="dict in job_group_list" :key="dict.dictValue" :label="dict.dictLabel"
                                    :value="dict.dictValue"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item prop="invokeTarget">
                            <template #label>
                                <div style="display:flex;align-items: center">
                                    <span style="width: 56px; margin-right: 5px">调用方法</span>
                                    <el-tooltip placement="top">
                                        <template #content>Bean调用示例：ryTask.ryParams('ry')
                                            <br />Class类调用示例：com.ruoyi.quartz.task.RyTask.ryParams('ry')
                                            <br />参数说明：支持字符串，布尔类型，长整型，浮点型，整型</template>
                                        <el-icon><ele-QuestionFilled /></el-icon>
                                    </el-tooltip>
                                </div>
                            </template>
                            <el-input v-model="state.ruleForm.invokeTarget" placeholder="请输入调用目标字符串"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="cron表达式" prop="cronExpression">
                            <el-input v-model="state.ruleForm.cronExpression" placeholder="请输入cron执行表达式">
                                <template #append>
                                    <el-button type="primary" @click="handleShowCron">
                                        <div style="margin-right: 10px;">生成表达式</div>
                                        <el-icon :size="30"><ele-Clock /></el-icon>
                                    </el-button>
                                </template>
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
                        <el-form-item label="执行策略" prop="misfirePolicy">
                            <el-radio-group v-model="state.ruleForm.misfirePolicy" size="default">
                                <el-radio-button value="1">立即执行</el-radio-button>
                                <el-radio-button value="2">执行一次</el-radio-button>
                                <el-radio-button value="3">放弃执行</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" style="margin-top: 20px;">
                        <el-form-item label="是否并发" prop="concurrent">
                            <el-radio-group v-model="state.ruleForm.concurrent" size="default">
                                <el-radio-button value="0">允许</el-radio-button>
                                <el-radio-button value="1">禁止</el-radio-button>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
                        <el-form-item label="状态">
                            <el-radio-group v-model="state.ruleForm.status">
                                <el-radio v-for="dict in job_status_list" :key="dict.dictValue"
                                    :label="dict.dictValue" :value="dict.dictValue">{{
                                        dict.dictLabel
                                    }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
            <el-dialog  style="position: absolute; top: 100px;" title="Cron表达式生成器" v-model="openCron" append-to-body destroy-on-close class="scrollbar">
                <crontab @hide="openCron = false" @fill="crontabFill" :expression="expression"></crontab>
            </el-dialog>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { defineAsyncComponent, reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addJob, getJob, updateJob } from '/@/api/monitor/job';
// import crontab from '/@/components/Crontab/index.vue';  // 确保路径正确
const crontab = defineAsyncComponent(() => import('/@/components/Crontab/index.vue'));
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        jobName: '', 
        jobId: '',
        jobGroup: '',
        cronExpression:'' as any,
        invokeTarget:'',
        concurrent:'1',
        status:"0",
        misfirePolicy:'1',
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}
const expression = ref('')  // 传入的表达式
const openCron = ref(false)  // 是否显示Cron表达式弹出层
interface statusOption {
    dictValue: string;
    dictLabel: string;
}
const job_group_list = ref<statusOption[]>([]);
const job_status_list = ref<statusOption[]>([]);
// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});

// 校验规则
const rules = reactive({
    jobName: [
        { required: true, message: "任务名称不能为空", trigger: "blur" }
    ],
    invokeTarget: [
        { required: true, message: "调用目标字符串不能为空", trigger: "blur" }
    ],
    cronExpression: [
        { required: true, message: "cron执行表达式不能为空", trigger: "blur" }
    ]

})
// 获取状态数据
const getdictdata = async () => {
    try {
        job_group_list.value = await dictStore.fetchDict('sys_job_group')
        job_status_list.value = await dictStore.fetchDict('sys_job_status')
        // 处理字典数据
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
}
// 打开弹窗
const openDialog = (type: string, row: JobType, jobId: string) => {
    if (type === 'edit') {
        if (row != undefined) {
            getJob(row.jobId).then(response => {
                state.ruleForm = response.data.data

            });
        } else {
            getJob(jobId).then(response => {
                state.ruleForm = response.data.data
            });
        }
        console.log( state.ruleForm);
        
        state.dialog.title = '修改任务';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '添加任务';
        state.dialog.submitTxt = '新 增';
    }
    state.dialog.isShowDialog = true;
    getdictdata()
};
/** cron表达式按钮操作 */
const handleShowCron = () => {
    expression.value = state.ruleForm.cronExpression;
    openCron.value = true;
}
/** 确定后回传值 */
const crontabFill = (value: any) => {
    state.ruleForm.cronExpression = value;
}
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.jobId != '') {
                updateJob(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addJob(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
