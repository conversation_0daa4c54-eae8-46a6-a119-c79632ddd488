<template>
  <div class="view-image-switch" :style="animationClass">
    <img
      :style="filterClass"
      :src="imageURL"
      @dragstart.prevent
      @dragover.prevent
      @drop.prevent
      @load="onImageLoad"
      @error="onImageError"
    />
    <svg id="svg">
      <defs>
        <filter :id="detail.identifier + '_svg'">
          <feColorMatrix
            color-interpolation-filters="sRGB"
            type="matrix"
            :values="hexTofeColorMatrix(detail.style?.foreColor)"
          />
        </filter>
      </defs>
    </svg>
    <!-- 隐藏的依赖项，确保响应式更新 -->
    <div v-show="false">{{ dataInit }}{{ colorChange }}{{ imageUrlChange }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoute } from 'vue-router';
import { useCounterStore } from '/@/stores/counterStore';
import switchImage from '/@/assets/topo-img/images/switch_128.png';
import topoUtil from '/@/utils/topo/topo-util';
import { listDeviceBind } from '/@/api/scada/topo';

// 定义组件名称
defineOptions({
  name: 'ViewImageSwitch'
});

// Props
interface Props {
  editMode?: boolean;
  selected?: boolean;
  detail: any;
}
const props = withDefaults(defineProps<Props>(), {
  editMode: false,
  selected: false
});

// Composables
const route = useRoute();
const counterStore = useCounterStore();
const { mqttData } = storeToRefs(counterStore);

// Refs
const imageURL = ref(switchImage);
const deviceTimer = ref<NodeJS.Timeout | null>(null);
const filterClass = ref<any>({
  width: '100%',
  height: '100%',
  filter: '',
  position: 'absolute',
  animation: props.detail.hdClassName + ' 5s infinite',
});
const animationClass = ref<any>({});

// Computed
const colorChange = computed(() => {
  console.log('🎨 colorChange 计算属性执行');

  if (props.detail.style?.foreColor && props.detail.style?.isFilter) {
    filterClass.value.marginLeft = '';
    filterClass.value.filter = 'url(#' + props.detail.identifier + '_svg)';
    animationClass.value = {};
  } else if (props.detail.style?.foreColor && props.detail.style?.isFilter == false) {
    filterClass.value.marginLeft = '-10000px';
    filterClass.value.filter = 'drop-shadow(5000px 0px ' + props.detail.style.foreColor + ')';
    animationClass.value = {
      overflow: 'hidden',
      position: 'relative',
    };
  } else {
    animationClass.value = {};
    filterClass.value.marginLeft = '';
    filterClass.value.filter = '';
  }

  console.log('🎨 最终颜色:', props.detail.style?.foreColor);
  return props.detail.style?.foreColor;
});

const imageUrlChange = computed(() => {
  console.log('🖼️ imageUrlChange 计算属性执行');
  console.log('  props.detail.style.url:', props.detail.style?.url);

  if (props.detail.style?.url && props.detail.style.url.trim() !== '') {
    console.log('  ✅ 使用自定义图片:', props.detail.style.url);
    imageURL.value = props.detail.style.url;
  } else {
    console.log('  🔧 使用默认图片:', switchImage);
    imageURL.value = switchImage;
  }

  console.log('  📊 最终图片URL:', imageURL.value);
  return props.detail.style?.url;
});

const dataInit = computed(() => {
  console.log('🔄 dataInit 计算属性执行');
  console.log('  MQTT数据:', mqttData.value);
  console.log('  数据绑定配置:', props.detail.dataBind);

  if (props.detail.dataBind?.identifier && mqttData.value && mqttData.value.serialNumber == props.detail.dataBind.serialNumber && props.detail.dataBind.activeName == '变量状态') {
    console.log('✅ 数据绑定条件满足');

    let val: any = '';
    const message = mqttData.value.message.find((item: any) => item.id === props.detail.dataBind.identifier);
    if (message) {
      console.log('✅ 找到匹配的消息:', message);

      val = message.value;
      if (val == null) {
        val = 0;
      }

      console.log('📊 处理数值:', val);
      console.log('🔧 状态列表:', props.detail.dataBind.stateList);

      props.detail.dataBind.stateList?.forEach((element: any) => {
        let isSure = topoUtil.judgeSize(element.paramCondition, val, element.paramData);
        console.log(`🔍 条件判断: ${val} ${element.paramCondition} ${element.paramData} = ${isSure}`);

        // 更新开关状态
        if (props.detail.dataBind.controValue == '0开1关') {
          if (val == 1) {
            props.detail.dataAction.actualValue = '关';
          } else {
            props.detail.dataAction.actualValue = '开';
          }
        } else if (props.detail.dataBind.controValue == '0关1开') {
          if (val == 0) {
            props.detail.dataAction.actualValue = '关';
          } else {
            props.detail.dataAction.actualValue = '开';
          }
        }

        if (isSure) {
          console.log('✅ 条件满足，更新图片和颜色');
          console.log('  新颜色:', element.foreColor);
          console.log('  新图片:', element.imageUrl);

          props.detail.style.foreColor = element.foreColor;
          if (element.imageUrl && element.imageUrl.trim() !== '') {
            imageURL.value = element.imageUrl;
          }
        }
      });
    } else {
      console.log('❌ 未找到匹配的消息');
    }
  } else {
    console.log('❌ 数据绑定条件不满足');
  }

  return props.detail.dataBind;
});

// Methods
const getDeviceRealStatus = (serialNumber: string) => {
  const params = {
    pageNum: 1,
    pageSize: 9999,
    serialNumber: serialNumber,
    scadaGuid: route.query.guid,
  };

  listDeviceBind(params)
    .then((res) => {
      if (res.code == 200) {
        if (res.rows.length > 0) {
          let status = res.rows[0].status;
          if (props.detail.dataBind?.openImageUrl && props.detail.dataBind?.shutImageUrl) {
            if (status == 3) {
              imageURL.value = props.detail.dataBind.openImageUrl;
            } else if (status == 4) {
              imageURL.value = props.detail.dataBind.warnImageUrl;
            } else if (status == 2) {
              imageURL.value = props.detail.dataBind.shutImageUrl;
            } else {
              // 保持当前图片
            }
          }
        }
      }
    })
    .catch((err) => {
      console.error('获取设备状态失败:', err);
    });
};

const hexTofeColorMatrix = (hex?: string) => {
  console.log('🎨 hexTofeColorMatrix 执行，hex:', hex);

  if (!hex) {
    hex = '0000';
  }
  hex = hex.replace('#', '');
  let RGB = [];
  let numberList = [1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1, 0];

  for (let i = 0; i < hex.length; i += 2) {
    const firstDigit = parseInt(hex[i], 16);
    const firstDigitPartial = firstDigit * 16;
    let RGBValue = parseInt(hex[i + 1], 16) + firstDigitPartial;
    RGBValue = RGBValue / 255;
    RGBValue = Number(RGBValue.toFixed(2));
    RGB.push(RGBValue);
  }

  const red = RGB[0];
  const green = RGB[1];
  const blue = RGB[2];

  numberList[0] = red;
  numberList[6] = green;
  numberList[12] = blue;

  return numberList.join(' ');
};

// 图片加载事件处理
const onImageLoad = (event: Event) => {
  console.log('✅ 图片加载成功:', imageURL.value);
  const img = event.target as HTMLImageElement;
  console.log('📐 图片尺寸:', {
    naturalWidth: img.naturalWidth,
    naturalHeight: img.naturalHeight,
    displayWidth: img.width,
    displayHeight: img.height
  });
};

const onImageError = (event: Event) => {
  console.error('❌ 图片加载失败:', imageURL.value);
  console.error('错误事件:', event);

  // 如果当前图片加载失败，尝试使用默认图片
  if (imageURL.value !== switchImage) {
    console.log('🔄 尝试使用默认图片:', switchImage);
    imageURL.value = switchImage;
  } else {
    console.error('❌ 连默认图片也加载失败');
  }
};

// Lifecycle
onMounted(() => {
  console.log('🖼️ ViewImageSwitch 组件挂载');
  console.log('📊 组件配置:', {
    detail: props.detail,
    switchImage: switchImage,
    dataBind: props.detail.dataBind,
    style: props.detail.style
  });

  // 触发计算属性执行
  console.log('🔄 触发 imageUrlChange 计算属性');
  imageUrlChange.value;

  // 验证最终的图片URL
  console.log('🖼️ 最终设置的图片 URL:', imageURL.value);

  // 设备状态监听
  if (props.detail.dataBind?.activeName == '设备状态') {
    console.log('🔌 启动设备状态监听');
    getDeviceRealStatus(props.detail.dataBind.serialNumber);
    deviceTimer.value = setInterval(() => {
      getDeviceRealStatus(props.detail.dataBind.serialNumber);
    }, 60000);
  }

  // 延迟3秒后进行测试
  setTimeout(() => {
    console.log('🧪 开始图片显示测试...');

    // 测试图片切换
    if (props.detail.dataBind?.stateList && props.detail.dataBind.stateList.length > 0) {
      const testState = props.detail.dataBind.stateList[0];
      if (testState.imageUrl) {
        console.log('🧪 测试图片切换:', testState.imageUrl);
        imageURL.value = testState.imageUrl;
      }
    }
  }, 3000);
});

onBeforeUnmount(() => {
  if (deviceTimer.value) {
    clearInterval(deviceTimer.value);
    deviceTimer.value = null;
  }
});

// 测试方法
const setImage = (url: string) => {
  console.log('🧪 手动设置图片:', url);
  imageURL.value = url;
};

const resetToDefault = () => {
  console.log('🔄 重置为默认图片');
  imageURL.value = switchImage;
};

// 暴露测试方法
defineExpose({
  setImage,
  resetToDefault
});
</script>

<style lang="scss">
.view-image-switch {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;

  // 确保容器有明确的尺寸
  min-width: 20px;
  min-height: 20px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
    position: relative;
    z-index: 1;

    // 确保图片可见
    opacity: 1;
    visibility: visible;
  }

  svg {
    position: absolute;
    width: 0;
    height: 0;
    pointer-events: none;
    z-index: 0;
  }

  // 调试样式：添加边框以便查看容器
  &:empty {
    border: 1px dashed #ccc;

    &::before {
      content: '图片加载中...';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #999;
      font-size: 12px;
    }
  }
}
</style>
