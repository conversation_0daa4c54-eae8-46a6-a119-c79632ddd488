<template>
    <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();

// Methods
const drawRect = (x: number, y: number, width: number, height: number, radius: number, color: string, type: string) => {
  if (!elCanvas.value) return;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.beginPath();
  ctx.moveTo(x, y + radius);
  ctx.lineTo(x, y + height - radius);
  ctx.quadraticCurveTo(x, y + height, x + radius, y + height);
  ctx.lineTo(x + width - radius, y + height);
  ctx.quadraticCurveTo(x + width, y + height, x + width, y + height - radius);
  ctx.lineTo(x + width, y + radius);
  ctx.quadraticCurveTo(x + width, y, x + width - radius, y);
  ctx.lineTo(x + radius, y);
  ctx.quadraticCurveTo(x, y, x, y + radius);
  (ctx as any)[type + 'Style'] = color;
  ctx.closePath();
  (ctx as any)[type]();
};

const onResize = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  const radius = props.detail.style.radius ? props.detail.style.radius : 0;
  const color = getForeColor();
  drawRect(4, 4, w - 8, h - 8, radius, color, 'fill');
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};

// Lifecycle
onMounted(() => {
  onResize();
});
</script>
