<template>
    <div ref="xwin" style="height: 100%; width: 100%"></div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import axios from 'axios';
import henanJson from '/@/assets/echarts-map-json/province/henan.json'; //湖南
import hunanJson from '/@/assets/echarts-map-json/province/hunan.json'; //河南
import anhuiJson from '/@/assets/echarts-map-json/province/anhui.json'; //安徽
import aomenJson from '/@/assets/echarts-map-json/province/aomen.json'; //澳门
import beijingJson from '/@/assets/echarts-map-json/province/beijing.json'; //北京
import chongqingJson from '/@/assets/echarts-map-json/province/chongqing.json'; //重庆
import fujianJson from '/@/assets/echarts-map-json/province/fujian.json'; //福建
import gansuJson from '/@/assets/echarts-map-json/province/gansu.json'; //甘肃
import guangdongJson from '/@/assets/echarts-map-json/province/guangdong.json'; //广州
import guangxiJson from '/@/assets/echarts-map-json/province/guangxi.json'; //广西
import guizhouJson from '/@/assets/echarts-map-json/province/guizhou.json'; //贵州
import hainanJson from '/@/assets/echarts-map-json/province/hainan.json'; //海南
import hebeiJson from '/@/assets/echarts-map-json/province/hebei.json'; //河北
import heilongjiangJson from '/@/assets/echarts-map-json/province/heilongjiang.json'; //黑龙江
import hubeiJson from '/@/assets/echarts-map-json/province/hubei.json'; //湖北

import jiangsuJson from '/@/assets/echarts-map-json/province/jiangsu.json'; //江苏
import jiangxiJson from '/@/assets/echarts-map-json/province/jiangxi.json'; //江西
import jilinJson from '/@/assets/echarts-map-json/province/jilin.json'; //吉林
import liaoningJson from '/@/assets/echarts-map-json/province/liaoning.json'; //辽宁
import neimengguJson from '/@/assets/echarts-map-json/province/neimenggu.json'; //内蒙古

import ningxiaJson from '/@/assets/echarts-map-json/province/ningxia.json'; //宁夏
import qinghaiJson from '/@/assets/echarts-map-json/province/qinghai.json'; //青海
import shandongJson from '/@/assets/echarts-map-json/province/shandong.json'; //山东
import shanghaiJson from '/@/assets/echarts-map-json/province/shanghai.json'; //上海
import shanxiJson from '/@/assets/echarts-map-json/province/shanxi1.json'; //山西
import sichuanJson from '/@/assets/echarts-map-json/province/sichuan.json'; //四川
import taiwanJson from '/@/assets/echarts-map-json/province/taiwan.json'; //台湾
import tianjinJson from '/@/assets/echarts-map-json/province/tianjin.json'; //天津
import xianggangJson from '/@/assets/echarts-map-json/province/xianggang.json'; //香港
import xinjiangJson from '/@/assets/echarts-map-json/province/xinjiang.json'; //新疆
import xizangJson from '/@/assets/echarts-map-json/province/xizang.json'; //西藏
import yunnanJson from '/@/assets/echarts-map-json/province/yunnan.json'; //云南
import zhejiangJson from '/@/assets/echarts-map-json/province/zhejiang.json'; //浙江
import chartOption from '/@/assets/topo-data/chart-option.js';

// Props
interface Props {
  detail: any;
  editMode?: boolean;
}
const props = defineProps<Props>();

// Refs
const xwin = ref<HTMLElement>();
const echart = ref<any>(null);
const mapJsons = ref<any>({});
const timer = ref<any>(null);
// Computed
const echartRun = computed(() => {
  nextTick(() => {
    if (props.detail.dataBind.echartOption && props.detail.dataBind.echartRun > new Date().getTime() - 10000) {
      try {
        let flag = false;
        if (props.detail.dataBind.mapAddress == '自定义') {
          flag = getMapJson(props.detail.dataBind.mapUrl);
        } else {
          flag = initEcharts();
        }
        flag && ElMessage({ message: '运行成功', type: 'success' });
      } catch (error) {
        console.log(error);
        ElMessage({
          message: '图表初始化失败，请检查代码视图！',
          type: 'warning',
        });
      }
    }
  });
  return props.detail.dataBind.echartOption + props.detail.dataBind.echartRun;
});

const mapChange = computed(() => {
  nextTick(() => {
    try {
      if (props.detail.dataBind.mapAddress == '自定义' && props.detail.dataBind.mapUrl) {
        getMapJson(props.detail.dataBind.mapUrl);
      } else {
        initEcharts();
      }
    } catch (error) {
      console.log(error);
      ElMessage({
        message: '图表初始化失败，请检查代码视图！',
        type: 'warning',
      });
    }
  });
  return props.detail.dataBind.mapAddress;
});
// Methods
const loadData = (option: any, mapJson: any) => {
  if (echart.value) {
    echart.value.dispose();
  }
  switch (props.detail.dataBind.mapAddress) {
    case '安徽':
      mapJson = anhuiJson;
      break;
    case '澳门':
      mapJson = aomenJson;
      break;
    case '北京':
      mapJson = beijingJson;
      break;
    case '重庆':
      mapJson = chongqingJson;
      break;
    case '福建':
      mapJson = fujianJson;
      break;
    case '甘肃':
      mapJson = gansuJson;
      break;
    case '广东':
      mapJson = guangdongJson;
      break;
    case '广西':
      mapJson = guangxiJson;
      break;
    case '贵州':
      mapJson = guizhouJson;
      break;
    case '海南':
      mapJson = hainanJson;
      break;
    case '河北':
      mapJson = hebeiJson;
      break;
    case '黑龙江':
      mapJson = heilongjiangJson;
      break;
    case '河南':
      mapJson = henanJson;
      break;
    case '湖北':
      mapJson = hubeiJson;
      break;
    case '湖南':
      mapJson = hunanJson;
      break;
    case '江苏':
      mapJson = jiangsuJson;
      break;
    case '江西':
      mapJson = jiangxiJson;
      break;
    case '吉林':
      mapJson = jilinJson;
      break;
    case '辽宁':
      mapJson = liaoningJson;
      break;
    case '内蒙古':
      mapJson = neimengguJson;
      break;
    case '宁夏':
      mapJson = ningxiaJson;
      break;
    case '青海':
      mapJson = qinghaiJson;
      break;
    case '山东':
      mapJson = shandongJson;
      break;
    case '上海':
      mapJson = shanghaiJson;
      break;
    case '山西':
      mapJson = shanxiJson;
      break;
    case '四川':
      mapJson = sichuanJson;
      break;
    case '台湾':
      mapJson = taiwanJson;
      break;
    case '天津':
      mapJson = tianjinJson;
      break;
    case '香港':
      mapJson = xianggangJson;
      break;
    case '新疆':
      mapJson = xinjiangJson;
      break;
    case '西藏':
      mapJson = xizangJson;
      break;
    case '云南':
      mapJson = yunnanJson;
      break;
    case '浙江':
      mapJson = zhejiangJson;
      break;
    case '自定义':
      mapJson = mapJsons.value;
      break;
    default:
      mapJson = henanJson;
      break;
  }
  echarts.registerMap('mapJson', mapJson);
  let view = xwin.value;
  echart.value = echarts.init(view);
  echart.value.setOption(option);
};
const onResize = () => {
  if (echart.value) {
    echart.value.resize();
  }
};

// https://www.isqqw.com/asset/get/areas_v3/country/china.json
const getMapJson = async (mapUrl: string) => {
  try {
    const res = await axios.get(mapUrl);
    mapJsons.value = res.data;
    return initEcharts();
  } catch (err) {
    ElMessage({
      message: '请输入正确的url!',
      type: 'warning',
    });
    return false;
  }
};

const initEcharts = () => {
  if (!props.detail.dataBind.echartOption) {
    props.detail.dataBind.echartOption = chartOption.getOptionMap();
  }
  let funStr = chartOption.getFun(props.detail.dataBind.echartOption);
  let fun = eval('(' + funStr + ')');
  let echartData = {};
  if (props.detail.dataBind.echartData) {
    try {
      echartData = JSON.parse(props.detail.dataBind.echartData);
    } catch (error) {
      ElMessage({ message: '请输入正确的json数据', type: 'warning' });
      return false;
    }
  }
  let option = fun(echarts, echartData);
  loadData(option, mapJsons.value);
  onResize();
  return true;
};
const getEchartData = async (dataUrl: string) => {
  try {
    const res = await axios({
      url: dataUrl,
      method: 'get',
    });
    props.detail.dataBind.echartData = JSON.stringify(res.data);
    if (props.detail.dataBind.mapAddress == '自定义') {
      getMapJson(props.detail.dataBind.mapUrl);
    } else {
      initEcharts();
    }
  } catch (error) {
    console.error('获取图表数据失败:', error);
  }
};

// Lifecycle
onMounted(() => {
  if (props.editMode && props.detail.dataBind.echartUrl) {
    let echartSecond = props.detail.dataBind.echartSecond;
    if (!echartSecond) {
      echartSecond = 60 * 1000;
    } else {
      echartSecond = echartSecond * 1000;
    }
    getEchartData(props.detail.dataBind.echartUrl);
    timer.value = setInterval(() => {
      getEchartData(props.detail.dataBind.echartUrl);
    }, echartSecond);
  } else {
    if (props.detail.dataBind.mapAddress == '自定义') {
      getMapJson(props.detail.dataBind.mapUrl);
    } else {
      initEcharts();
    }
  }
});

onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  if (echart.value) {
    echart.value.dispose();
  }
});
</script>
