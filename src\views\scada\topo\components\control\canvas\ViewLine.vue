<template>
    <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();

// Methods
const drawLine = (x1: number, y1: number, x2: number, y2: number, lineWidth: number, color: string) => {
  if (!elCanvas.value) return;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.beginPath();
  ctx.moveTo(x1, y1); //设置起点状态
  ctx.lineTo(x2, y2); //设置末端状态
  ctx.lineWidth = lineWidth; //设置线宽状态
  ctx.strokeStyle = color; //设置线的颜色状态
  ctx.stroke(); //进行绘制
  ctx.closePath();
};

const onResize = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  let x1 = 0;
  let y1 = h / 2;
  let x2 = w;
  let y2 = h / 2;
  const color = getForeColor();
  let lineWidth = props.detail.style.lineWidth;
  if (lineWidth == undefined || typeof lineWidth != 'number') {
    lineWidth = 2;
  }
  if (props.detail.direction && props.detail.direction == 'vertical') {
    //竖线
    x1 = w / 2;
    x2 = w / 2;
    y1 = 0;
    y2 = h;
  }
  drawLine(x1, y1, x2, y2, lineWidth, color);
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};

// Lifecycle
onMounted(() => {
  onResize();
});
</script>
