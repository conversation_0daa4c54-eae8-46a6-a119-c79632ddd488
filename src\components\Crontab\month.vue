<template>
	<el-form size="small">
		<el-form-item>
			<el-radio v-model="radioValue" :label="1">
				月，允许的通配符[, - * /]
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="2">
				周期从
				<el-input-number v-model="cycle01" :min="1" :max="11" /> -
				<el-input-number v-model="cycle02" :min="cycle01 ? cycle01 + 1 : 2" :max="12" /> 月
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="3">
				从
				<el-input-number v-model="average01" :min="1" :max="11" /> 月开始，每
				<el-input-number v-model="average02" :min="1" :max="12 - average01 || 0" /> 月执行一次
			</el-radio>
		</el-form-item>

		<el-form-item>
			<el-radio v-model="radioValue" :label="4">
				指定
				<el-select clearable v-model="checkboxList" placeholder="可多选" multiple style="width:100%">
					<el-option v-for="item in 12" :key="item" :value="item">{{ item }}</el-option>
				</el-select>
			</el-radio>
		</el-form-item>
	</el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// 定义props
const props = defineProps<{
  check: (value: any, min: any, max: any) => any;
  cron: { month: string };
}>();

// 定义响应式数据
const radioValue = ref<any>(1);
const cycle01 = ref<any>(1);
const cycle02 = ref<any>(2);
const average01 = ref<any>(1);
const average02 = ref<any>(1);
const checkboxList = ref<any[]>([]);

// 定义emit事件
const emit = defineEmits<{
  (event: 'update', field: string, value: string): void;
}>();

// 计算属性
const cycleTotal = computed(() => {
  const cycle01Value = props.check(cycle01.value, 1, 11);
  const cycle02Value = props.check(cycle02.value, cycle01Value ? cycle01Value + 1 : 2, 12);
  return `${cycle01Value}-${cycle02Value}`;
});

const averageTotal = computed(() => {
  const average01Value = props.check(average01.value, 1, 11);
  const average02Value = props.check(average02.value, 1, 12 - average01.value || 0);
  return `${average01Value}/${average02Value}`;
});

const checkboxString = computed(() => {
  const str = checkboxList.value.join();
  return str === '' ? '*' : str;
});

// 方法
const radioChange = () => {
  switch (radioValue.value) {
    case 1:
      emit('update', 'month', '*');
      break;
    case 2:
      emit('update', 'month', cycleTotal.value);
      break;
    case 3:
      emit('update', 'month', averageTotal.value);
      break;
    case 4:
      emit('update', 'month', checkboxString.value);
      break;
  }
};

const cycleChange = () => {
  if (radioValue.value === 2) {
    emit('update', 'month', cycleTotal.value);
  }
};

const averageChange = () => {
  if (radioValue.value === 3) {
    emit('update', 'month', averageTotal.value);
  }
};

const checkboxChange = () => {
  if (radioValue.value === 4) {
    emit('update', 'month', checkboxString.value);
  }
};

// 观察响应式数据
watch(radioValue, radioChange);
watch(cycleTotal, cycleChange);
watch(averageTotal, averageChange);
watch(checkboxString, checkboxChange);
defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList
});
</script>
