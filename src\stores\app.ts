import { defineStore } from 'pinia'
import Cookies from 'js-cookie'

// 定义 Store 的状态类型
interface SidebarState {
  opened: boolean;
  withoutAnimation: boolean;
  hide: boolean;
}

interface AppState {
  sidebar: SidebarState;
  device: string;
  size: string;
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    sidebar: {
      opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
      withoutAnimation: false,
      hide: false,
    },
    device: 'desktop',
    size: Cookies.get('size') || 'medium',
  }),

  actions: {
    toggleSideBar() {
      if (this.sidebar.hide) {
        return false;
      }
      this.sidebar.opened = !this.sidebar.opened
      this.sidebar.withoutAnimation = false
      if (this.sidebar.opened) {
        Cookies.set('sidebarStatus', '1')
      } else {
        Cookies.set('sidebarStatus', '0')
      }
    },

    closeSideBar(withoutAnimation: boolean) {
      Cookies.set('sidebarStatus', '0')
      this.sidebar.opened = false
      this.sidebar.withoutAnimation = withoutAnimation
    },

    toggleDevice(device: string) {
      this.device = device
    },

    setSize(size: string) {
      this.size = size
      Cookies.set('size', size)
    },

    toggleSideBarHide(status: boolean) {
      this.sidebar.hide = status
    }
  }
})
