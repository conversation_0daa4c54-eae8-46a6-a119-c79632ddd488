<template>
    <div
        :style="{
            fontSize: detail.style.fontSize + 'px',
            fontFamily: detail.style.fontFamily,
            color: detail.style.foreColor,
            textAlign: detail.style.textAlign,
            border: detail.style.waterBorderWidth + 'px solid !important',
            borderRadius: detail.style.borderRadius + 'px !important',
            borderColor: detail.style.waterBorderColor,
            width: '100%',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
        }"
    >
        <iframe :style="{ width: detail.style.position.w - 20 + 'px', height: detail.style.position.h - 20 + 'px', border: 'transparent' }" :src="childUrl"></iframe>
        <div v-show="false">{{ dataInit }}</div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';

// Props
interface Props {
  detail: any;
}
const props = defineProps<Props>();

// Refs
const childUrl = ref('');

// Computed
const dataInit = computed(() => {
  if (props.detail.dataBind.ztPageData) {
    let split = props.detail.dataBind.ztPageData.split('&');
    // 修复 URL 构建 - 使用正确的 Vue Router hash 模式路径
    childUrl.value = window.location.origin + '/?#/scada/topo/fullscreen?id=' + split[0] + '&guid=' + split[1] + '&t=' + new Date().getTime();
    console.log('修复后的 URL:', childUrl.value);
  }
  return props.detail.dataBind.ztPageData;
});
</script>

<style lang="scss" scoped></style>
