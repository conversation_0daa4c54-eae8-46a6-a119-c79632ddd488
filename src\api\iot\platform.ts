import request from '/@/utils/request';

// 类型定义
interface QueryParams {
  [key: string]: any;
}

interface Platform {
  id: number;
  name: string;
  [key: string]: any;
}

interface BindResponse {
  success: boolean;
  message: string;
  [key: string]: any;
}

interface UnbindResponse {
  success: boolean;
  message: string;
  [key: string]: any;
}

// 查询第三方登录平台控制列表
export function listPlatform(query: QueryParams) {
  return request({
    url: '/iot/platform/list',
    method: 'get',
    params: query,
  });
}

// 查询第三方登录平台控制详细
export function getPlatform(socialPlatformId: number | string) {
  return request({
    url: `/iot/platform/${socialPlatformId}`,
    method: 'get',
  });
}

// 新增第三方登录平台控制
export function addPlatform(data: any) {
  return request({
    url: '/iot/platform',
    method: 'post',
    data: data,
  });
}

// 修改第三方登录平台控制
export function updatePlatform(data: any) {
  return request({
    url: '/iot/platform',
    method: 'put',
    data: data,
  });
}

// 删除第三方登录平台控制
export function delPlatform(socialPlatformId: number | string) {
  return request({
    url: `/iot/platform/${socialPlatformId}`,
    method: 'delete',
  });
}

// 解除绑定
export function unbind(socialUserId: number | string) {
  return request<UnbindResponse>({
    url: `/iot/social/unbind/${socialUserId}`,
    method: 'get',
  });
}

// 绑定跳转
export function bind(platform: string) {
  return request<BindResponse>({
    url: `/iot/social/bind/${platform}`,
    method: 'get',
  });
}

// 绑定
export function bindUser(bindId: string) {
  return request<BindResponse>({
    url: `/iot/social/bindId/${bindId}`,
    method: 'get',
  });
}
