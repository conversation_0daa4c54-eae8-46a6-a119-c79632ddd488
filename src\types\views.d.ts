/**
 * views personal
 */
type NewInfo = {
	title: string;
	date: string;
	link: string;
};
type Recommend = {
	title: string;
	msg: string;
	icon: string;
	bg: string;
	iconColor: string;
};
declare type PersonalState = {
	newsInfoList: NewInfo[];
	recommendList: Recommend[];
	personalForm: {
		name: string;
		email: string;
		autograph: string;
		occupation: string;
		phone: string;
		sex: string;
	};
};

/**
 * views visualizing
 */
declare type Demo2State<T = any> = {
	time: {
		txt: string;
		fun: number;
	};
	dropdownList: T[];
	dropdownActive: string;
	skyList: T[];
	dBtnList: T[];
	chartData4Index: number;
	dBtnActive: number;
	earth3DBtnList: T[];
	chartData4List: T[];
	myCharts: T[];
};

/**
 * views params
 */
declare type ParamsState = {
	value: string;
	tagsViewName: string;
	tagsViewNameIsI18n: boolean;
};

/**
 * views system
 */
// role
declare interface RowRoleType {
	roleId:string;
	roleName: string;
	roleKey: string;
	remark: string;
	roleSort: number;
	status: string;
	createTime: string;
	deptCheckStrictly:boolean;
	deptIds:Array;
	menuCheckStrictly:boolean;
	menuIds:Array;
	dataScope:string
}

interface SysRoleTableType extends TableType {
	data: RowRoleType[];
	ruleForm:RowRoleType
}

declare interface SysRoleState {
	tableData: SysRoleTableType;
}

declare type TreeType = {
	id: number;
	label: string;
	children?: TreeType[];
};

// user
declare type RowUserType<T = any> = {
	userId:any;
	userName: string;
	nickName: string;
	roleSign: string;
	department: string[];
	phonenumber: string;
	deptId:string;
	roleIds:Array;
	postIds:Array;
	email: string;
	sex: string;
	password: string;
	overdueTime: T;
	status: string;
	describe: string;
	createTime: T;
	remark:string;
};
interface SysUserTableType extends TableType {
	data: RowUserType[];
}

declare interface SysUserState {
	tableData: SysUserTableType;
}

declare type DeptTreeType = {
	deptId: number;
	deptName: string;
	createTime: string;
	status: string;
	orderNum: any;
	describe: string;
	id: number | string;
	children?: DeptTreeType[];
};

// dept
declare interface RowDeptType extends DeptTreeType {
	deptId:any;
	deptLevel: string[];
	leader: string;
	phone: string;
	email: string;
	parentId:string
}

interface SysDeptTableType extends TableType {
	data: DeptTreeType[];
}

declare interface SysDeptState {
	tableData: SysDeptTableType;
}
//RowNewsType
declare interface RowPlatType {
	socialPlatformId:string;
	platform: string;
	clientId: string;
    secretKey: string;
	redirectUri:string;
	createBy:string;
	status:string;
	createTime:string;
	updateTime:string;
	bindUri:string;
	remark:string;
	redirectLoginUri:string;
	errorMsgUri: string
}
//RowNewsType
declare interface RowNewsType {
	categoryName:string;
	newsId: string;
	title: string;
    remark: string;
	isTop:string;
	isBanner:string;
	status:string;
	author:string;
	categoryId:string;
	content:string;
	imgUrl:string;
	newsId:string;
}
// JobType
declare interface JobType {
	groupName:string;
	jobId: string;
	identifier: string;
    remark: string;
}
//groupType
declare interface groupType {
	groupName:string;
	groupId: string;
	identifier: string;
    remark: string;
}
//templateType
declare interface templateType {
	templateName:string;
	templateId: string;
	identifier: string;
    remark: string;
}
//newsCategory
declare interface RowNewsCategoryType {
	categoryName:string;
	categoryId: string;
	orderNum: string;
    remark: string;
}
// notice


declare interface RowNoticeType {
	noticeTitle:string;
	noticeId: string;
	createBy: string;
	noticeType: string;
	createTime: string;
	remark:string;
	status:string;
	noticeContent:string;
}

interface SysNoticeTableType extends TableType {
	data: RowNoticeType[];
}

declare interface SysNoticeState {
	tableData: SysNoticeTableType;
}
// config
type ListType = {
	id: number;
	label: string;
	value: string;
};

declare interface RowConfigType {
	configId: string;
	configName: string;
	configKey: string;
	configValue: string;
	configType: string;
	createTime: string;
	remark:string;
	status:string;
}

interface SysConfigTableType extends TableType {
	data: RowConfigType[];
}

declare interface SysConfigState {
	tableData: SysConfigTableType;
}
// dic
type ListType = {
	id: number;
	label: string;
	value: string;
};

declare interface RowDicType {
	dictId: string;
	dictName: string;
	dictType: string;
	remark: string;
	status: string;
	createTime: string;
	list: ListType[];
}

interface SysDicTableType extends TableType {
	data: RowDicType[];
}

declare interface SysDicState {
	tableData: SysDicTableType;
}

/**
 * views pages
 */
//  filtering
declare type FilteringChilType = {
	id: number | string;
	label: string;
	active: boolean;
};

declare type FilterListType = {
	img: string;
	title: string;
	evaluate: string;
	collection: string;
	price: string;
	monSales: string;
	id: number | string;
	loading?: boolean;
};

declare type FilteringRowType = {
	title: string;
	isMore: boolean;
	isShowMore: boolean;
	id: number | string;
	children: FilteringChilType[];
};

// tableRules
declare type TableRulesHeaderType = {
	prop: string;
	width: string | number;
	label: string;
	isRequired?: boolean;
	isTooltip?: boolean;
	type: string;
};

declare type TableRulesState = {
	tableData: {
		data: EmptyObjectType[];
		header: TableRulesHeaderType[];
		option: SelectOptionType[];
	};
};

declare type TableRulesOneProps = {
	name: string;
	email: string;
	autograph: string;
	occupation: string;
};

// tree
declare type RowTreeType = {
	id: number;
	label: string;
	label1: string;
	label2: string;
	isShow: boolean;
	children?: RowTreeType[];
};

// workflow index
declare type NodeListState = {
	id: string | number;
	nodeId: string | undefined;
	class: HTMLElement | string;
	left: number | string;
	top: number | string;
	icon: string;
	name: string;
};

declare type LineListState = {
	sourceId: string;
	targetId: string;
	label: string;
};

declare type XyState = {
	x: string | number;
	y: string | number;
};

declare type WorkflowState<T = any> = {
	leftNavList: T[];
	dropdownNode: XyState;
	dropdownLine: XyState;
	isShow: boolean;
	jsPlumb: T;
	jsPlumbNodeIndex: null | number;
	jsplumbDefaults: T;
	jsplumbMakeSource: T;
	jsplumbMakeTarget: T;
	jsplumbConnect: T;
	jsplumbData: {
		nodeList: NodeListState[];
		lineList: LineListState[];
	};
};

// workflow drawer
declare type WorkflowDrawerNodeState<T = any> = {
	node: { [key: string]: T };
	nodeRules: T;
	form: T;
	tabsActive: string;
	loading: {
		extend: boolean;
	};
};

declare type WorkflowDrawerLabelType = {
	type: string;
	label: string;
};

declare type WorkflowDrawerState<T = any> = {
	isOpen: boolean;
	nodeData: {
		type: string;
	};
	jsplumbConn: T;
};

/**
 * views make
 */
// tableDemo
declare type TableDemoPageType = {
	pageNum: number;
	pageSize: number;
};

declare type TableHeaderType = {
	key: string;
	width: string;
	title: string;
	type: string | number;
	colWidth: string;
	width?: string | number;
	height?: string | number;
	isCheck: boolean;
};

declare type TableSearchType = {
	label: string;
	prop: string;
	placeholder: string;
	required: boolean;
	type: string;
	options?: SelectOptionType[];
};

declare type TableDemoState = {
	tableData: {
		data: EmptyObjectType[];
		header: TableHeaderType[];
		config: {
			total: number;
			loading: boolean;
			isBorder: boolean;
			isSelection: boolean;
			isSerialNo: boolean;
			isOperate: boolean;
		};
		search: TableSearchType[];
		param: EmptyObjectType;
		printName: string;
	};
};
