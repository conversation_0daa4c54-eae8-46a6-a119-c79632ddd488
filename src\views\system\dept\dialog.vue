<template>
	<div class="system-dept-dialog-container">
		<el-dialog style="position: absolute; top: 100px; padding: 20px 30px" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="DialogFormRef" :model="state.ruleForm" :rules="rules" size="default" label-width="100px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上级部门" prop="parentId"  v-if="state.ruleForm.parentId != '0'">
							<el-tree-select v-model="state.ruleForm.parentId" :props="defaultProps"
								:data="state.deptData" :render-after-expand="true" style="width: 600px"
								:show-count="true" @node-click="handleNodeClick" check-strictly>
								<template #default="{ node, data }">
									<span>{{ data.deptName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-tree-select>
							<!-- <el-cascader
								:options="state.deptData"
								:props="{ checkStrictly: true, value: 'deptName', label: 'deptName' }"
								placeholder="请选择部门"
								clearable
								class="w100"
								v-model="state.ruleForm.deptLevel"
							>
								<template #default="{ node, data }">
									<span>{{ data.deptName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-cascader> -->
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="部门名称" prop="deptName">
							<el-input v-model="state.ruleForm.deptName" placeholder="请输入部门名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="显示排序" prop="orderNum">
							<el-input-number v-model="state.ruleForm.orderNum" :min="0" :max="999"
								controls-position="right"  class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="负责人" prop="leader">
							<el-input v-model="state.ruleForm.leader" placeholder="请输入负责人" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="联系电话" prop="phone">
							<el-input v-model="state.ruleForm.phone" placeholder="请输入手机号" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="邮箱" prop="email">
							<el-input v-model="state.ruleForm.email" placeholder="请输入" clearable></el-input>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item label="部门状态">
							<el-radio-group v-model="state.ruleForm.status">
								<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue">{{
									item.dictLabel
								}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="部门描述">
							<el-input v-model="state.ruleForm.describe" type="textarea" placeholder="请输入部门描述" maxlength="150"></el-input>
						</el-form-item>
					</el-col> -->
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
						state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemDeptDialog">
import { reactive, ref } from 'vue';
import { addDept, getDept, listDept, listDeptExcludeChild, updateDept } from '/@/api/system/dept';
import { addDateRange, handleTree } from '/@/utils/next';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store


// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
// 原始数据存储
const initialState = {
	ruleForm: {
		// deptLevel: [] as string[], // 上级部门
		deptName: '', // 部门名称
		leader: '', // 负责人
		phone: '', // 手机号
		email: '', // 邮箱
		orderNum: undefined, // 排序
		status: '0', // 部门状态
		parentId: '',
		deptId: ''
	},
	deptData: [] as DeptTreeType[], // 部门数据
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
}
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	deptData: [...initialState.deptData],
	dialog: { ...initialState.dialog },
});
interface statusOption {
	dictValue: string;
	dictLabel: string;
}
const statuslist = ref<statusOption[]>([]); //状态
// 校验规则
const rules = reactive({
	parentId: [
		{ required: true, message: "上级部门不能为空", trigger: "blur" }
	],
	deptName: [
		{ required: true, message: "部门名称不能为空", trigger: "blur" }
	],
	orderNum: [
		{ required: true, message: "显示排序不能为空", trigger: "blur" }
	],
	email: [
		{
			type: "email",
			message: "请输入正确的邮箱地址",
			trigger: ["blur", "change"]
		}
	],
	phone: [
		{
			pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
			message: "请输入正确的手机号码",
			trigger: "blur"
		}
	]

})
const defaultProps = reactive({
	children: "children",
	label: "deptName",
	value: 'deptId'
});
// 搜索内容
let queryParams = reactive({
	deptName: undefined,
	status: ''
});
// 打开弹窗
const openDialog = (type: string, row: RowDeptType) => {
	if (type === 'edit') {
		console.log(row, 'row');
		// getDept(row.deptId).then(response => {
		// 	// console.log(response,'response');
		// 	state.ruleForm = response.data.data
		// })
		getDept(row.deptId).then(response => {
			state.ruleForm = response.data.data
			if (row) {
				state.ruleForm.deptId = row.deptId

			}
		})


		// state.ruleForm = row;
		state.dialog.title = '修改部门';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();
		// console.log(row);

		if (row) {
			state.ruleForm.parentId = row.deptId
		}
		state.dialog.title = '新增部门';
		state.dialog.submitTxt = '新 增';
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	DialogFormRef.value.resetFields();
		// });
	}
	state.dialog.isShowDialog = true;
	getDeptData();
	getdictdata()
};
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm }
	state.deptData = [...initialState.deptData]
	state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.deptId != '') {
				
				updateDept(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				// const data = addDateRange(state.ruleForm)
				// console.log(data,'data');
				// return
				addDept(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
	// if (state.dialog.type === 'add') { }
};
// 点击节点时更新父节点
const handleNodeClick = (node: any, data: any) => {
	// 判断是否是父节点
	// if (!node.isLeaf) {
	// 如果是父节点，更新模型值
	state.ruleForm.parentId = data.data.deptId;
	console.log(state.ruleForm.parentId);

	// }
};
// 初始化部门数据
const getDeptData = () => {
	listDept(queryParams).then(response => {
		state.deptData = handleTree(response.data.data, "deptId");
		console.log(state.deptData);

	});
};
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value =  await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
