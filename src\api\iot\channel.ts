import request from '/@/utils/request'


interface QueryParams {
  [key: string]: string | number | boolean;
}
// 查询监控设备通道信息列表
export function listChannel(query: any) {
    return request({
        url: '/iotsip/channel/list',
        method: 'get',
        params: query,
    });
}

// 开始播放
  export function startPlay(deviceId: string, channelId: string): Promise<any> {
    return request({
      url: '/iotsip/player/play/' + deviceId + channelId,
      method: 'get',
    });
  };

// 查询监控设备通道信息详细
export function getChannel(channelId: any) {
    return request({
        url: '/iotsip/channel/' + channelId,
        method: 'get',
    });
}

// 新增监控设备通道信息
export function addChannel(createNum: any, data :any) {
    return request({
        url: '/iotsip/channel/' + createNum,
        method: 'post',
        data: data,
    });
}

// 修改监控设备通道信息
export function updateChannel(data: any) {
    return request({
        url: '/iotsip/channel',
        method: 'put',
        data: data,
    });
}

// 删除监控设备通道信息
export function delChannel(channelId : number) {
    return request({
        url: '/iotsip/channel/' + channelId,
        method: 'delete',
    });
}
