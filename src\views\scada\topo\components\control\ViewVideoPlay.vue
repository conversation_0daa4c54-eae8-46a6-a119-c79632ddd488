<template>
  <div>
    <div
        class="video-placeholder"
        :style="{
                width: detail.style?.position?.w ? detail.style.position.w + 'px' : '640px',
                height: detail.style?.position?.h ? detail.style.position.h + 'px' : '360px',
            }"
    >
      <div class="video-placeholder-content">
        <div class="video-icon">
          <el-icon size="48"><VideoPlay /></el-icon>
        </div>
        <h3>Video.js 播放器</h3>
        <p>Video.js 播放器组件需要安装相关依赖包</p>
        <p>请运行: npm install vue-video-player video.js</p>
        <div class="video-info" v-if="detail.videoUrl">
          <p><strong>视频地址:</strong> {{ detail.videoUrl }}</p>
          <p><strong>播放器类型:</strong> Video.js</p>
          <p><strong>支持格式:</strong> MP4, WebM, OGG</p>
        </div>
        <div class="video-controls">
          <el-button type="primary" size="small" disabled>
            <el-icon><VideoPlay /></el-icon>
            播放
          </el-button>
          <el-button size="small" disabled>
            <el-icon><VideoPause /></el-icon>
            暂停
          </el-button>
          <el-button size="small" disabled>
            <el-icon><FullScreen /></el-icon>
            全屏
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { VideoPlay, VideoPause, FullScreen } from '@element-plus/icons-vue'

// Props
interface Props {
  detail?: any
}

const props = withDefaults(defineProps<Props>(), {
  detail: () => ({
    videoUrl: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4',
    imageUrl: '',
    style: {
      position: {
        w: 640,
        h: 360
      }
    }
  })
})

// 使用默认配置
const detail = computed(() => props.detail || {
  videoUrl: 'http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4',
  imageUrl: '',
  style: {
    position: {
      w: 640,
      h: 360
    }
  }
})
</script>

<style lang="scss" scoped>
.video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
}

.video-placeholder-content {
  padding: 20px;
  z-index: 2;
}

.video-icon {
  margin-bottom: 15px;
  opacity: 0.9;
}

.video-placeholder-content h3 {
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 600;
}

.video-placeholder-content p {
  margin: 8px 0;
  font-size: 14px;
  opacity: 0.9;
}

.video-info {
  margin: 20px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.video-info p {
  margin: 5px 0;
  font-size: 12px;
  text-align: left;
}

.video-controls {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.video-controls .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.video-controls .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
