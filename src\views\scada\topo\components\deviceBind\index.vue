<template>
  <div>
    <el-form @submit.native.prevent :model="queryParams"
        ref="queryFormRef" :inline="true" label-width="68px" >
      <el-form-item label="设备编号" prop="serialNumber">
        <el-input
            v-model="queryParams.serialNumber"
            placeholder="请输入设备编号"
            clearable
            size="default"
            @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="default" @click="handleQuery"><el-icon><ele-Search /></el-icon>搜索</el-button>
        <el-button size="default" @click="resetQuery"><el-icon><ele-Refresh /></el-icon>重置</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="primary" size="default" @click="handleAdd"><el-icon><ele-Plus /></el-icon>新增</el-button>
      </el-form-item>
    </el-form>

    <el-table v-loading="loading" :data="deviceBindList" size="default">
      <el-table-column label="id" align="center" prop="id" width="80" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备编号" align="center" prop="serialNumber" />
      <el-table-column label="操作" align="center" width="80">
        <template #default="{ row }">
          <el-button style="color: #f56c6c" size="default" link icon="Delete" @click="handleDelete(row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination v-show="total > 0" size="small" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
                   layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 30]"
                   :pager-count="5" background class="mt15" style="justify-content: flex-end;"
                   @size-change="handleSizeChange" @current-change="handleCurrentChange" />

    <!-- 设备选择弹窗 -->
    <el-dialog
        title="设备管理"
        v-model="isDeviceDialog"
        width="800px"
        append-to-body
        :close-on-click-modal="false"
    >
      <div class="el-divider el-divider--horizontal" style="margin-top: -25px"></div>
      <device ref="deviceRef" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isDeviceDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleSelectDevice">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Device from './device.vue'
import { ElMessageBox, ElMessage } from 'element-plus';
import {useRoute} from 'vue-router';
import {
  listDeviceBind as getListApi,
  saveDeviceBind as saveBindApi,
  removeDeviceBind as deleteBindApi
} from '/@/api/scada/topo'

// 类型定义
interface DeviceBindItem {
  id: number
  deviceName: string
  serialNumber: string
}

interface QueryParams {
  pageNum: number
  pageSize: number
  serialNumber: string
  scadaGuid: string
}

// 响应式状态
const loading = ref<boolean>(true)
const total = ref<number>(0)
const deviceBindList = ref<DeviceBindItem[]>([])
const queryParams = ref<QueryParams>({
  pageNum: 1,
  pageSize: 10,
  serialNumber: '',
  scadaGuid: ''
})
const isDeviceDialog = ref<boolean>(false)
const serialNumbers = ref<string>('')
const queryFormRef = ref()
const deviceRef = ref()

// 获取路由参数
const route = useRoute()
queryParams.value.scadaGuid = route.query.guid as string

// 初始化获取列表
onMounted(() => {
  getList()
})

// 查询云组态组件关联设备列表
function getList() {
  loading.value = true
  getListApi(queryParams.value)
      .then(res => {
        res = res.data
        if (res.code === 200) {
          deviceBindList.value = res.rows
          total.value = res.total
        }
        loading.value = false
      })
      .catch(err => {
        // eslint-disable-next-line no-console
        console.error(err)
        loading.value = false
      })
}

// 搜索按钮操作
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

// 处理每页数量变化
function handleSizeChange(size: number) {
  queryParams.value.pageSize = size
  getList()
}

// 处理当前页码变化
function handleCurrentChange(page: number) {
  queryParams.value.pageNum = page
  getList()
}

// 重置按钮操作
function resetQuery() {
  // 注意：resetForm 是 Element Plus 提供的方法，需要确保你的项目中有对应封装
  // 否则可以手动重置表单字段
  if (queryFormRef.value?.resetFields) {
    queryFormRef.value.resetFields()
  }
  handleQuery()
}

// 新增按钮操作
function handleAdd() {
  isDeviceDialog.value = true
  deviceRef.value?.clearSelection?.()
}

// 选择设备
function handleSelectDevice() {
  isDeviceDialog.value = false
  const list = deviceRef.value?.selectRowDataClick?.()
  serialNumbers.value = list.join(',')
  saveBind()
}

// 保存绑定
function saveBind() {
  const params = {
    scadaGuid: route.query.guid,
    serialNumbers: serialNumbers.value
  }
  saveBindApi(params)
      .then(() => {
        getList()
      })
      .catch(err => {
        // eslint-disable-next-line no-console
        console.error(err)
      })
}

// 删除按钮操作
function handleDelete(row: DeviceBindItem) {
  ElMessageBox.confirm('您是否确认移除此关联设备？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteBindApi(row.id)
        .then(res => {
          res = res.data
          if (res.code === 200) {
            ElMessage.success('移除成功')
            getList()
          }
        })
        .catch(err => {
          ElMessage.error(err || '删除失败')
        })
  })
}
</script>

<style scoped></style>