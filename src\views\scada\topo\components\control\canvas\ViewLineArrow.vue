<template>
    <div class="view-line-arrow" @mousemove="onMousemove($event)" @mouseup="onMouseUp($event)">
        <canvas ref="elCanvas" :width="detail.style.position.w" :height="detail.style.position.h">Your browser does not support the HTML5 canvas tag.</canvas>

        <template v-if="editMode && selected">
            <template v-for="(pass, index) in points" :key="index">
                <div
                    class="passby"
                    @mousedown.stop="aroowPassDown(pass, $event, index)"
                    :style="{
                        left: pass.x - 5 + 'px',
                        top: pass.y - 5 + 'px',
                    }"
                ></div>
            </template>
        </template>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';

// Props
interface Props {
  detail: any;
  editMode?: boolean;
  selected?: boolean;
}
const props = defineProps<Props>();

// Refs
const elCanvas = ref<HTMLCanvasElement>();
const lineWidth = ref(2);
const flag = ref(false);
const passItem = ref<any>({});
const points = ref<any[]>([]); // 控制点（包含起始和终点）
const FACTOR_H = 5; // 箭头 水平高度倍数
const FACTOR_V = 4; // 箭头 垂直长度倍数

// Methods
const drawArrow = (ctx: CanvasRenderingContext2D, x2: number, y2: number, lineWidth: number, color: string) => {
  // (x1, y1)是线段起点  (x2, y2)是线段终点
  ctx.beginPath(); // 坐标原点 => (x2, y2)
  ctx.moveTo(x2, y2);
  ctx.lineTo(x2 - lineWidth * FACTOR_H, y2 - lineWidth * FACTOR_V);
  ctx.lineTo(x2 - lineWidth * FACTOR_H, y2 + lineWidth * FACTOR_V);
  ctx.closePath();
  ctx.fillStyle = color; // 设置线的颜色状态
  ctx.fill();
};

const drawLine = (ctx: CanvasRenderingContext2D) => {
  const color = getForeColor();
  ctx.beginPath();
  for (let index = 0; index < points.value.length; index++) {
    const begin = points.value[index];
    const end = points.value[index + 1];
    ctx.moveTo(begin.x, begin.y);
    ctx.lineTo(end.x, end.y);
    if (index == points.value.length - 2) break;
  }
  ctx.lineWidth = lineWidth.value; // 设置线宽状态
  ctx.strokeStyle = color; // 设置线的颜色状态
  ctx.stroke(); // 进行绘制
  ctx.closePath();
};

const reDraw = () => {
  if (!elCanvas.value) return;

  const w = props.detail.style.position.w;
  const h = props.detail.style.position.h;
  const ctx = elCanvas.value.getContext('2d');
  if (!ctx) return;

  ctx.clearRect(0, 0, w, h);
  drawLine(ctx);
  drawArrow(ctx, points.value[points.value.length - 1].x, points.value[points.value.length - 1].y, lineWidth.value, getForeColor());
};

const onResize = () => {
  let width = props.detail.style.lineWidth;
  if (width == undefined || typeof width != 'number') {
    width = 2;
  }
  lineWidth.value = width;
  reDraw();
};

const aroowPassDown = (pass: any, event: MouseEvent, index: number) => {
  flag.value = true;
  pass.startX = event.pageX;
  pass.startY = event.pageY;
  pass.temp = {};
  pass.temp.x = pass.x;
  pass.temp.y = pass.y;
  passItem.value = pass;
};

const onMousemove = (event: MouseEvent) => {
  if (!flag.value) return;
  event.cancelBubble = true;
  const dx = event.pageX - passItem.value.startX;
  const dy = event.pageY - passItem.value.startY;
  passItem.value.x = passItem.value.temp.x + dx;
  passItem.value.y = passItem.value.temp.y + dy;
  reDraw();
};

const onMouseUp = (event: MouseEvent) => {
  flag.value = false;
};

const getForeColor = () => {
  return props.detail.style.foreColor;
};

// Lifecycle
onMounted(() => {
  let width = props.detail.style.lineWidth;
  if (width == undefined) {
    width = 20;
  } else if (typeof width == 'string') {
    width = parseInt(width);
  }
  points.value = props.detail.style.points;
  // 增加2个中间节点，应该可以动态控制，这里暂时写死
  onResize();
});
</script>

<style lang="scss" scoped>
.view-line-arrow {
    height: 100%;
    width: 100%;
    position: relative;

    .passby {
        position: absolute;
        height: 15px;
        width: 15px;
        border-radius: 50%;
        background-color: white;
        border: 1px solid rgb(34, 14, 223);
        cursor: move;
    }
}
</style>
