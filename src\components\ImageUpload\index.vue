<template>
    <div class="component-upload-image">
        <el-upload :disabled="props.isDisabled" multiple :action="uploadImgUrl" list-type="picture-card" :on-success="handleUploadSuccess"
            :before-upload="handleBeforeUpload" :limit="props.limit" :on-error="handleUploadError"
            :on-exceed="handleExceed" ref="imageUpload" :on-remove="handleDelete" :show-file-list="true"
            :headers="headers" :file-list="fileList" :on-preview="handlePictureCardPreview"
            :class="{ hide: fileList.length >= props.limit }">
            <i><el-icon><ele-Plus /></el-icon></i>
        </el-upload>

        <!-- 上传提示 -->
        <div class="el-upload__tip" v-if="showTip">
            请上传
            <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
            <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
            的文件
        </div>

        <el-dialog style="position: absolute; top: 150px;" v-model="dialogVisible" title="预览" width="800"
            append-to-body>
            <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { Session } from '/@/utils/storage';
import { ElLoading, ElMessage } from 'element-plus';

// 定义 props
const props = defineProps({
    modelValue: [String, Object, Array],
    limit: {
        type: Number,
        default: 5,
    },
    fileSize: {
        type: Number,
        default: 5,
    },
    fileType: {
        type: Array,
        default: () => ['png', 'jpg', 'jpeg'],
    },
    isShowTip: {
        type: Boolean,
        default: true,
    },
    isDisabled: {
        type: Boolean,
        default: false,
    },
});

// 定义 emits
const emit = defineEmits(['update:modelValue']);

// 响应式数据
const number = ref(0);
const uploadList = ref<any[]>([]);
const dialogImageUrl = ref('');
const dialogVisible = ref(false);
const hideUpload = ref(false);
const baseUrl = import.meta.env.VITE_APP_BASE_API as string;
const uploadImgUrl = `${baseUrl}/iot/tool/upload`; // 上传的图片服务器地址
const headers = {
    Authorization: `Bearer ${Session.get('token')}`,
};
const fileList = ref<any[]>([]);
// 类型断言：将 loadingInstance 的类型定义为 ElLoading.Service 或 null
const loadingInstance = ref<any | null>(null);
// 显示提示
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

// 监听 value 的变化，更新 fileList
watch(
    () => props.modelValue,
    (val) => {
        if (val) {           
            const list = Array.isArray(val) ? val : String(val).split(',');
            fileList.value = list.map((item) => {
                if (typeof item === 'string') {
                    if (!item.startsWith(baseUrl)) {
                        item = { name: `${baseUrl}${item}`, url: `${baseUrl}${item}` };
                    } else {
                        item = { name: item, url: item };
                    }
                }
                return item;
            });
        } else {
            fileList.value = [];
        }
    },
    { immediate: true, deep: true }
);

// 上传前处理
const handleBeforeUpload = (file: File) => {
    let isImg = false;
    if (props.fileType.length) {
        const fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
        isImg = props.fileType.some((type: any) => {
            if (file.type.includes(type)) return true;
            if (fileExtension && fileExtension.includes(type)) return true;
            return false;
        });
    } else {
        isImg = file.type.includes('image');
    }

    if (!isImg) {
        ElMessage.error(`文件格式不正确, 请上传 ${props.fileType.join('/')} 图片格式文件!`);
        return false;
    }
    if (props.fileSize) {
        const isLt = file.size / 1024 / 1024 < props.fileSize;
        if (!isLt) {
            ElMessage.error(`上传头像图片大小不能超过 ${props.fileSize} MB!`);
            return false;
        }
    }
    // 启动 loading
    loadingInstance.value = ElLoading.service({
        text: '正在上传图片，请稍候...',
        background: 'rgba(0, 0, 0, 0.7)', // 自定义背景色
    });
    number.value++;
    return true;
};

// 文件个数超出
const handleExceed = () => {
    ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
};

// 上传成功回调
const handleUploadSuccess = (res: any, file: File) => {
    if (res.code === 200) {
        uploadList.value.push({ name: res.fileName, url: res.fileName });
        uploadedSuccessfully();
    } else {
        number.value--;
        if (loadingInstance.value) {
            loadingInstance.value.close(); // 关闭 loading
        }
        ElMessage.error(res.msg);
        // this.$refs.imageUpload.handleRemove(file);
        uploadedSuccessfully();
    }
};

// 删除图片
const handleDelete = (file: any) => {
    const findex = fileList.value.map((f) => f.name).indexOf(file.name);
    if (findex > -1) {
        fileList.value.splice(findex, 1);
        emit('update:modelValue', listToString(fileList.value));
    }
};

// 上传失败
const handleUploadError = () => {
    ElMessage.error('上传图片失败，请重试');
    if (loadingInstance.value) {
        loadingInstance.value.close(); // 关闭 loading
    }
};

// 上传结束处理
const uploadedSuccessfully = () => {
    if (number.value > 0 && uploadList.value.length === number.value) {
        fileList.value = [...fileList.value, ...uploadList.value];
        fileList.value.forEach(item => {
            item.url = baseUrl + item.url
            item.name = baseUrl + item.name
        })
        uploadList.value = [];
        number.value = 0;
        emit('update:modelValue', listToString(fileList.value));
        if (loadingInstance.value) {
            loadingInstance.value.close(); // 关闭 loading
        }
    }
};

// 预览
const handlePictureCardPreview = (file: any) => {
    dialogImageUrl.value = file.url;
    dialogVisible.value = true;
};

// 对象转成指定字符串分隔
const listToString = (list: any[], separator = ',') => {
    let strs = '';
    separator = separator || ",";
    for (let i in list) {
        if (list[i].url) {
            strs += list[i].url.replace(baseUrl, "") + separator;

        }
    }
    return strs !== '' ? strs.substr(0, strs.length - 1) : '';
};
</script>

<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
    display: none;
}

:deep(.el-list-enter-active),
:deep(.el-list-leave-active) {
  transition: all 0s;
}

:deep(.el-list-enter),
:deep(.el-list-leave-active) {
  opacity: 0;
  transform: translateY(0);
}

:deep(.el-upload-list__item-thumbnail) {
  object-fit: cover !important;
}
</style>