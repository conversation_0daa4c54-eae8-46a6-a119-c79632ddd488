import { storeToRefs } from 'pinia';
import { useUserInfo } from '/@/stores/userInfo';

/**
 * 字符权限校验
 * @param {string[]} value 校验值
 * @returns {boolean} 是否拥有权限
 */
export function checkPermi(value: string[]): boolean {
    if (value && value.length > 0) {
        const userStore = useUserInfo();
        const { userInfos } = storeToRefs(userStore); // 解构获取响应式权限
        // 确保 permissions 是一个数组
        const permissions = Array.isArray(userInfos.value.permissions) ? userInfos.value.permissions : [];
        // const permissions = userInfos.value.permissions || [];
        const all_permission = "*:*:*"; // 通用权限标识

        const hasPermission = permissions.some((permission: any) => {
            return all_permission === permission || value.includes(permission);
        });

        if (!hasPermission) {
            return false;
        }
        return true;
    } else {
        console.error('Need roles! Like checkPermi="["system:user:add","system:user:edit"]"');
        return false;
    }
}

/**
 * 角色权限校验
 * @param {string[]} value 校验值
 * @returns {boolean} 是否拥有角色
 */
export function checkRole(value: string[]): boolean {
    if (value && value.length > 0) {
        const userStore = useUserInfo();
        const { userInfos } = storeToRefs(userStore); // 解构获取响应式角色
        // 确保 roles 是一个数组
        const roles = Array.isArray(userInfos.value.roles) ? userInfos.value.roles : [];
        const super_admin = "admin"; // 超级管理员角色

        const hasRole = roles.some((role) => {
            return super_admin === role || value.includes(role);
        });

        if (!hasRole) {
            return false;
        }
        return true;
    } else {
        console.error('Need roles! Like checkRole="["admin","editor"]"');
        return false;
    }
}
