<template>
  <div class="view-chart-wrapper" ref="xwin"></div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import * as echarts from 'echarts';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import chartOption from '@/assets/topo-data/chart-option.js';
import { getEchart } from '@/api/scada/echart';
import { safeInitECharts, fixEChartsColorIssues } from '@/utils/echarts-config';

// 定义组件名称
defineOptions({
  name: 'ChartWrapper'
});

// 定义 props，继承 BaseView 的 props
const props = defineProps({
  editMode: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
});

// 定义 emits
const emit = defineEmits(['refreshData', 'resize']);

// 响应式数据
const xwin = ref(null);
const echart = ref(null);
const timer = ref(null);

// 获取当前实例，用于访问全局属性
// const instance = getCurrentInstance();

// 计算属性
const echartRun = computed(() => {

  nextTick(() => {
    console.log(props.detail.dataBind)
    if (props.detail.dataBind?.echartOption && props.detail.dataBind?.echartRun > new Date().getTime() - 10000) {
      let funStr = chartOption.getFun(props.detail.dataBind.echartOption);
      try {
        let fun = eval('(' + funStr + ')');
        let echartData = {};
        if (props.detail.dataBind.echartData) {
          try {
            echartData = JSON.parse(props.detail.dataBind.echartData);
          } catch (error) {
            ElMessage({
              message: '请输入正确的json数据',
              type: 'warning',
            });
          }
        }
        let option = fun(echarts, echartData);
        loadData(option);
        onResize();
        ElMessage({ message: '运行成功', type: 'success' });
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log(error);
        ElMessage({
          message: '图表初始化失败，请检查代码视图！',
          type: 'warning',
        });
      }
    }
  });
  return (props.detail.dataBind?.echartOption || '') + (props.detail.dataBind?.echartRun || '');
});

// 监听器
watch(echartRun, (newValue, oldValue) => {
  // console.log('echartRun changed:', newValue);
});

// 生命周期 - mounted
onMounted(() => {
  if (props.editMode && props.detail.dataBind?.echartUrl) {
    // 获取刷新间隔，默认为60秒（1分钟）
    let echartSecond = props.detail.dataBind.echartSecond;
    if (!echartSecond) {
      echartSecond = 60 * 1000; // 默认1分钟自动刷新
    } else {
      echartSecond = echartSecond * 1000;
    }

    // 立即获取数据
    getEchartData(props.detail.dataBind.echartUrl);

    // 设置定时器，每1分钟自动刷新数据
    timer.value = setInterval(() => {
      getEchartData(props.detail.dataBind.echartUrl);
    }, echartSecond);
  } else {
    initEchart();

    // 即使没有URL，也设置1分钟的自动刷新来重新初始化图表
    timer.value = setInterval(() => {
      if (props.detail.dataBind?.echartOption) {
        initEchart();
      }
    }, 60 * 1000); // 1分钟
  }
});
// 方法定义
const loadData = (option:any) => {
  if (echart.value) {
    echart.value.dispose();
  }
  let view = xwin.value;
  // 使用安全的初始化方法
  echart.value = safeInitECharts(view) || echarts.init(view);

  // 增强图表选项，添加鼠标悬停显示数据值功能
  if (option && typeof option === 'object') {
    // 确保 series 存在且每个 series 都有完整的颜色配置
    if (option.series && Array.isArray(option.series)) {
      const defaultColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
      option.series = option.series.map((series: any, index: number) => {
        if (!series || typeof series !== 'object') {
          return {
            name: `系列${index + 1}`,
            type: 'bar',
            data: [],
            itemStyle: {
              color: defaultColors[index % defaultColors.length],
              borderColor: defaultColors[index % defaultColors.length],
              borderWidth: 0
            },
            lineStyle: {
              color: defaultColors[index % defaultColors.length]
            },
            areaStyle: series.type === 'line' && series.areaStyle ? {
              color: defaultColors[index % defaultColors.length],
              opacity: 0.3
            } : undefined
          };
        }

        const color = series.itemStyle?.color || defaultColors[index % defaultColors.length];

        // 确保每个 series 都有完整的颜色配置
        const enhancedSeries = {
          ...series,
          itemStyle: {
            color: color,
            borderColor: color,
            borderWidth: 0,
            ...series.itemStyle
          },
          lineStyle: {
            color: color,
            ...series.lineStyle
          }
        };

        // 为面积图添加 areaStyle
        if (series.type === 'line' && series.areaStyle) {
          enhancedSeries.areaStyle = {
            color: color,
            opacity: 0.3,
            ...series.areaStyle
          };
        }

        return enhancedSeries;
      });
    }

    // 设置全局颜色调色板
    if (!option.color) {
      option.color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'];
    }

    // 确保 tooltip 配置存在并启用，使用安全的配置
    option.tooltip = {
      trigger: 'axis',
      show: true,
      confine: true, // 限制在图表区域内
      backgroundColor: 'rgba(255,255,255,0.9)',
      borderColor: '#ccc',
      borderWidth: 1,
      textStyle: {
        color: '#333',
        fontSize: 12
      },
      formatter: function(params: any) {
        try {
          if (!params) return '';

          if (Array.isArray(params)) {
            let result = (params[0]?.name || '未知') + '<br/>';
            params.forEach((param, index) => {
              // 安全获取颜色，优先级：param.color > series.itemStyle.color > 全局color > 默认色
              let color = '#5470c6'; // 默认颜色
              if (param.color) {
                color = param.color;
              } else if (param.seriesIndex !== undefined && option.series?.[param.seriesIndex]?.itemStyle?.color) {
                color = option.series[param.seriesIndex].itemStyle.color;
              } else if (option.color && option.color[index % option.color.length]) {
                color = option.color[index % option.color.length];
              }

              const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              result += marker + (param.seriesName || '系列') + ': ' + (param.value !== undefined ? param.value : '无数据') + '<br/>';
            });
            return result;
          } else {
            // 单个参数的情况
            let color = '#5470c6';
            if (params.color) {
              color = params.color;
            } else if (params.seriesIndex !== undefined && option.series?.[params.seriesIndex]?.itemStyle?.color) {
              color = option.series[params.seriesIndex].itemStyle.color;
            } else if (option.color && option.color[0]) {
              color = option.color[0];
            }

            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
            return (params.name || '未知') + '<br/>' + marker + (params.seriesName || '系列') + ': ' + (params.value !== undefined ? params.value : '无数据');
          }
        } catch (error) {
          console.error('Tooltip formatter error:', error);
          return '数据显示错误';
        }
      },
      ...(option.tooltip || {})
    };

    // 确保 x 轴时间数据按升序排列
    if (option.xAxis && option.xAxis.type === 'category' && option.xAxis.data) {
      // 如果数据看起来像时间格式，进行排序
      const timePattern = /^\d{1,2}[月时:：-]\d{0,2}|^\d{4}[-/]\d{1,2}[-/]\d{1,2}|^\d{1,2}:\d{2}/;
      if (option.xAxis.data.some((item:any) => timePattern.test(String(item)))) {
        // 对时间数据进行排序（这里保持原有逻辑，因为可能已经是正确顺序）
        // eslint-disable-next-line no-console
        console.log('检测到时间数据，保持原有顺序');
      }
    }
  }

  // 安全地设置图表选项，捕获各种错误
  try {
    // 使用修复函数处理颜色问题
    const fixedOption = fixEChartsColorIssues(option);
    echart.value.setOption(fixedOption);
  } catch (chartError: any) {
    // eslint-disable-next-line no-console
    console.warn('图表设置失败，尝试修复配置:', chartError);

    // 创建一个安全的基础配置
    const safeOption = {
      title: option.title || { text: '图表' },
      color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'],
      tooltip: {
        trigger: 'axis',
        show: true,
        confine: true,
        formatter: function(params: any) {
          try {
            if (Array.isArray(params)) {
              let result = (params[0]?.name || '未知') + '<br/>';
              params.forEach((param, index) => {
                const color = param.color || safeOption.color[index % safeOption.color.length];
                const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
                result += marker + (param.seriesName || '系列') + ': ' + (param.value !== undefined ? param.value : '无数据') + '<br/>';
              });
              return result;
            } else {
              const color = params.color || safeOption.color[0];
              const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;
              return (params.name || '未知') + '<br/>' + marker + (params.seriesName || '系列') + ': ' + (params.value !== undefined ? params.value : '无数据');
            }
          } catch (error) {
            return '数据格式错误';
          }
        }
      },
      legend: option.legend || {},
      grid: option.grid || {},
      xAxis: option.xAxis || { type: 'category', data: [] },
      yAxis: option.yAxis || { type: 'value' },
      series: []
    };

    // 安全地处理 series
    if (option.series && Array.isArray(option.series)) {
      const defaultColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
      safeOption.series = option.series.map((series: any, index: number) => {
        if (!series || typeof series !== 'object') {
          return {
            name: `系列${index + 1}`,
            type: 'bar',
            data: [],
            itemStyle: { color: defaultColors[index % defaultColors.length] }
          };
        }

        // 移除可能导致问题的配置
        const safeSeries = {
          name: series.name || `系列${index + 1}`,
          type: series.type || 'bar',
          data: Array.isArray(series.data) ? series.data : [],
          itemStyle: {
            color: defaultColors[index % defaultColors.length],
            ...series.itemStyle
          }
        };

        // 移除极坐标相关配置
        if (series.coordinateSystem === 'polar') {
          safeSeries.coordinateSystem = 'cartesian2d';
        }

        return safeSeries;
      });
    } else {
      safeOption.series = [{
        name: '默认',
        type: 'bar',
        data: [],
        itemStyle: { color: '#5470c6' }
      }];
    }

    try {
      echart.value.setOption(safeOption);
      // eslint-disable-next-line no-console
      console.log('图表已使用安全配置修复');
    } catch (secondError: any) {
      // eslint-disable-next-line no-console
      console.error('图表修复仍然失败:', secondError);
      // 使用最基本的配置
      echart.value.setOption({
        title: { text: '图表配置错误' },
        xAxis: { type: 'category', data: ['错误'] },
        yAxis: { type: 'value' },
        series: [{
          name: '错误',
          type: 'bar',
          data: [0],
          itemStyle: { color: '#ff6b6b' }
        }]
      });
    }
  }
};

const onResize = () => {
  if (echart.value) {
    echart.value.resize();
  }
  // 触发父组件的 resize 事件
  emit('resize');
};

const initEchart = () => {
  if (!props.detail.dataBind.echartOption) {
    props.detail.dataBind.echartOption = chartOption.getOption();
    let funStr = chartOption.getFun(props.detail.dataBind.echartOption);
    let fun = eval('(' + funStr + ')');
    let echartData = {};
    if (props.detail.dataBind.echartData) {
      echartData = JSON.parse(props.detail.dataBind.echartData);
    }
    let option = fun(echarts, echartData);
    loadData(option);
    onResize();
  } else if (props.detail.dataBind.echartOption.indexOf('echartId-') > -1) {
    let id = props.detail.dataBind.echartOption.split('-')[1];
    getEchartDataById(id);
  } else {
    let funStr = chartOption.getFun(props.detail.dataBind.echartOption);
    let fun = eval('(' + funStr + ')');
    let echartData = {};
    if (props.detail.dataBind.echartData) {
      echartData = JSON.parse(props.detail.dataBind.echartData);
    }
    let option = fun(echarts, echartData);
    loadData(option);
    onResize();
  }
};

// 获取自定义echart详情
const getEchartDataById = (id:any) => {
  getEchart(id).then((res) => {
    res = res.data
    if (res.code === 200) {
      try {
        let funStr = chartOption.getFun(res.data.echartData);
        // 使用更安全的方式执行代码
        let fun;
        try {
          fun = new Function('return ' + funStr)();
        } catch (evalError) {
          // eslint-disable-next-line no-console
          console.error('函数创建失败，尝试使用 eval:', evalError);
          fun = eval('(' + funStr + ')');
        }

        let echartData = {};
        if (props.detail.dataBind?.echartData) {
          try {
            echartData = JSON.parse(props.detail.dataBind.echartData);
          } catch (parseError) {
            // eslint-disable-next-line no-console
            console.error('解析 echartData 失败:', parseError);
            ElMessage({
              message: '图表数据格式错误',
              type: 'warning',
            });
          }
        }

        let option = fun(echarts, echartData);
        if (option && typeof option === 'object') {
          loadData(option);
          onResize();
        } else {
          throw new Error('图表选项无效');
        }
      } catch (error: any) {
        // eslint-disable-next-line no-console
        // console.error('图表初始化失败:', error);
        // ElMessage({
        //   message: '图表初始化失败: ' + (error?.message || '未知错误'),
        //   type: 'error',
        // });
      }
    }
  }).catch((error) => {
    // eslint-disable-next-line no-console
    console.error('获取图表数据失败:', error);
    ElMessage({
      message: '获取图表数据失败',
      type: 'error',
    });
  });
};

const getEchartData = (dataUrl: string) => {
  axios({
    url: dataUrl,
    method: 'get',
  }).then((res) => {
    props.detail.dataBind.echartData = JSON.stringify(res.data);
    initEchart();
  }).catch((error: any) => {
    // eslint-disable-next-line no-console
    console.error('获取图表数据失败:', error);
    ElMessage({
      message: '获取图表数据失败',
      type: 'error',
    });
  });
};

// 生命周期 - beforeUnmount (Vue 3 中替代 beforeDestroy)
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>

<style>
.view-chart-wrapper {
  height: 100%;
  width: 100%;
  padding: 10px;
}
</style>
