<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="分组名称" prop="groupName">
                        <el-input v-model="state.tableData.param.groupName" clearable size="default"
                            placeholder="请输入分组名称" style="width: 240px" />
                    </el-form-item>
                    <!-- <el-form-item v-if="isAdmin" label="我的分组" style="margin: 0 20px 8px 0; ">
                        <el-switch style=" position: relative;  top: -4px;" size="large" v-model="myGroup"
                            @change="myGroupChange">
                        </el-switch>
                    </el-form-item> -->
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:template:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                        <el-button size="default" type="info" class="ml10" @click="toggleExpandAll">
                            <el-icon><ele-Sort /></el-icon>
                            展开/折叠
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table v-if="refreshTable" :data="state.tableData.data" v-loading="state.tableData.loading" border
                style="width: 100%" :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }"
                :default-expand-all="isExpandAll" row-key="groupId"
                :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
                <el-table-column label="分组名称" align="center" prop="groupName" width="300" />
                <el-table-column label="分组排序" align="center" prop="groupOrder" width="100" />
                <el-table-column label="创建时间" align="center" prop="createTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column v-if="isAdmin" label="所属用户" align="center" prop="userName" width="100" /> -->
                <el-table-column label="备注" align="left" header-align="center" prop="remark" width="150" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default" type="warning" @click="handleViewDevice(scope.row.groupId)"
                                v-auths="['iot:device:list']"><el-icon><ele-Search /></el-icon>查看设备</el-button>
                            <el-button size="default" type="success" @click="selectDevice(scope.row)"
                                v-auths="['iot:group:add']"><el-icon><ele-EditPen /></el-icon>添加设备</el-button>
                            <el-button size="default" type="primary" @click="onOpenEditDic('edit', scope.row)"
                                v-auths="['iot:group:query']"><el-icon><ele-View /></el-icon>查看</el-button>
                            <el-button size="default" type="danger" @click="onRowDel(scope.row)"
                                v-auths="['iot:group:remove']"><el-icon
                                    size="default"><ele-DeleteFilled /></el-icon>删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <!-- <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination> -->
        </el-card>
        <GroupDialog ref="GroupDialogRef" @refresh="getTableData()" />
        <GroupDeviceDialog ref="GroupDeviceDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { handleTree, parseTime } from '/@/utils/next'
import { delGroup, listGroup } from '/@/api/iot/group';
import { useUserInfo } from '/@/stores/userInfo';
import { useRouter } from 'vue-router';
const userInfoStore = useUserInfo();
const dictStore = useDictStore();  // 使用 Pinia store
const router = useRouter();
// 引入组件
const GroupDialog = defineAsyncComponent(() => import('/@/views/iot/group/dialog.vue'));
const GroupDeviceDialog = defineAsyncComponent(() => import('/@/views/iot/group/device-list.vue'));

// 定义变量内容
const GroupDialogRef = ref();
const GroupDeviceDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        // total: 0,
        loading: false,
        param: {
            // pageNum: 1,
            // pageSize: 10,
            groupName: undefined,//分类名称
            userId: undefined
        },
    },
});
// 是否管理员
// const isAdmin = ref(false)
const myGroup = ref(0)
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //groupId
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const statuslist = ref<TypeOption[]>([]);
// 是否展开，默认全部展开
const isExpandAll = ref(true)
// 重新渲染表格状态
const refreshTable = ref(true)
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listGroup(state.tableData.param);
        state.tableData.data = handleTree(response.data.data, "groupId") as any;
        console.log(state.tableData.data);
        
        // state.tableData.data = response.data.rows;
        // state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        // pageNum: 1,
        // pageSize: 10,
        groupName: undefined,
        userId: undefined
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        // statuslist.value = await dictStore.fetchDict('iot_yes_no')
        if (userInfoStore.userInfos.roles.indexOf('tenant') === -1 && userInfoStore.userInfos.roles.indexOf('general') === -1) {
            // isAdmin.value = true
        }
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 我的分组改变事件
const myGroupChange = () => {
    if (myGroup.value) {
        state.tableData.param.userId = userInfoStore.userInfos.userId
    } else {
        state.tableData.param.userId = undefined
    }
}
/** 查看设备按钮操作 */
const handleViewDevice = (groupId: any) => {
    router.push({
        path: '/iot/device',
        query: {
            t: Date.now(),
            groupId: groupId,
        },
    })
}
/** 选择设备 */
const selectDevice = (row: any) => {
    GroupDeviceDialogRef.value.openDialog(row);
}
/** 展开/折叠操作 */
const toggleExpandAll = () => {
    refreshTable.value = false;
    isExpandAll.value = !isExpandAll.value;
    nextTick(() => {
        refreshTable.value = true;
    });
}
// 打开新增产品分类弹窗
const onOpenAddDic = (type: string) => {
    GroupDialogRef.value.openDialog(type);
};
// 打开修改产品分类弹窗
const onOpenEditDic = (type: string, row: groupType | undefined) => {
    var groupId = ''
    if (!row) {
        groupId = ids.value
    } else {
        groupId = row.groupId
    }
    GroupDialogRef.value.openDialog(type, row, groupId);
};
// 删除产品分类
const onRowDel = (row: groupType) => {
    const groupIds = row.groupId || ids.value;
    ElMessageBox.confirm(`此操作将永久删除设备分类编号为：“${groupIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delGroup(groupIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// // 分页改变
// const onHandleSizeChange = (val: number) => {
//     state.tableData.param.pageSize = val;
//     getTableData();
// };
// // 分页改变
// const onHandleCurrentChange = (val: number) => {
//     state.tableData.param.pageNum = val;
//     getTableData();
// };
// 页面加载时
onMounted(() => {
    getTableData();
    // getdictdata()
});
</script>
