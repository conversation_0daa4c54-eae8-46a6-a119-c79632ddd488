<template>
	<div class="system-role-dialog-container">
		<el-dialog style="position: absolute; top: 100px" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" :width="state.dialog.assignmentShow ? '600px' : '700px'" append-to-body>
			<el-form ref="DialogFormRef" :model="state.ruleForm" size="default" label-width="90px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item label="角色名称" :prop="state.dialog.assignmentShow ? '' : 'roleName'">
							<el-input :disabled="state.dialog.assignmentShow" v-model="state.ruleForm.roleName"
								placeholder="请输入角色名称" clearable></el-input>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item label="权限字符" :prop="state.dialog.assignmentShow ? '' : 'roleKey'">
							<template #label>
								<div style="display:flex;align-items: center">
									<el-tooltip v-if="!state.dialog.assignmentShow"
										content="控制器中定义的权限字符，如：@PreAuthorize('@ss.hasRole('admin')')" placement="top">
										<el-icon><ele-QuestionFilled /></el-icon>
									</el-tooltip>
									<span style="width: 56px; margin-left: 5px">权限字符</span>
								</div>
							</template>
							<el-input :disabled="state.dialog.assignmentShow" v-model="state.ruleForm.roleKey"
								placeholder="请输入用户名"></el-input>
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item v-if="!state.dialog.assignmentShow" label="角色顺序" prop="roleSort">
							<el-input-number v-model="state.ruleForm.roleSort" :min="0" :max="999"
								controls-position="right" placeholder="请输入排序" class="w100" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
						<el-form-item v-if="!state.dialog.assignmentShow" label="角色状态">
							<el-radio-group v-model="state.ruleForm.status">
								<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue"
									:value="item.dictValue">{{
										item.dictLabel
									}}</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item v-if="state.dialog.assignmentShow" label="权限范围">
							<!-- @change="dataScopeSelectChange" -->
							<el-select v-model="state.ruleForm.dataScope">
								<el-option v-for="item in dataScopeOptions" :key="item.value" :label="item.label"
									:value="item.value"></el-option>
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item v-if="!state.dialog.assignmentShow" label="菜单权限">
							<el-checkbox v-model="menuExpand"
								@change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
							<el-checkbox v-model="menuNodeAll"
								@change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
							<el-checkbox v-model="state.ruleForm.menuCheckStrictly"
								@change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
						</el-form-item>
						<el-form-item v-if="!state.dialog.assignmentShow">
							<el-tree class="menu-data-tree" :data="state.menuData" show-checkbox ref="menuRef"
								node-key="id" :check-strictly="!state.ruleForm.menuCheckStrictly" empty-text="加载中，请稍候"
								:props="state.menuProps"></el-tree>
						</el-form-item>

					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item v-if="state.dialog.assignmentShow && state.ruleForm.dataScope == '2'"
							label="数据权限">
							<el-checkbox v-model="deptExpand"
								@change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
							<el-checkbox v-model="deptNodeAll"
								@change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
							<el-checkbox v-model="state.ruleForm.deptCheckStrictly"
								@change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
						</el-form-item>
						<el-form-item v-if="state.dialog.assignmentShow && state.ruleForm.dataScope == '2'">
							<el-tree style="" default-expand-all class="menu-data-tree" :data="state.deptData"
								show-checkbox ref="deptRef" node-key="id"
								:check-strictly="!state.ruleForm.deptCheckStrictly" empty-text="加载中，请稍候"
								:props="state.defaultProps"></el-tree>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item v-if="!state.dialog.assignmentShow" label="备注">
							<el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入角色描述"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>

				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button v-if="!state.dialog.assignmentShow" type="primary" @click="onSubmit(DialogFormRef)"
						size="default">{{
							state.dialog.submitTxt }}</el-button>
					<el-button v-else type="primary" @click="submitDataScope" size="default">确 定</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemRoleDialog">
import { nextTick, reactive, ref } from 'vue';
import { treeselect as menuTreeselect, roleMenuTreeselect } from "/@/api/system/menu"
import { ElMessage, ElTree, FormInstance } from 'element-plus';
import { addRole, deptTreeSelect, getRole, updateRole, dataScope } from '/@/api/system/role';
import { TreeKey, TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
// 定义变量内容
const DialogFormRef = ref<FormInstance>();
// 原始数据存储
const initialState = {
	ruleForm: {
		roleName: '', // 角色名称
		roleKey: '', // 角色标识
		roleSort: 0, // 排序
		status: '0', // 角色状态
		remark: '', // 角色描述
		roleId: undefined as string | undefined,
		deptCheckStrictly: true,
		menuCheckStrictly: true,
		menuIds: [] as any,
		deptIds: [] as any,
		dataScope: ''
	},
	menuData: [] as TreeType[],
	deptData: [] as TreeType[],
	menuProps: {
		children: 'children',
		label: 'label',
	},
	defaultProps: {
		children: 'children',
		label: 'label',
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
		assignmentShow: false
	},
};
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	menuData: [...initialState.menuData],
	deptData: [...initialState.deptData],
	menuProps: { ...initialState.menuProps },
	defaultProps: { ...initialState.defaultProps },
	dialog: { ...initialState.dialog },
});
// 校验规则
const rules = reactive({
	roleName: [
		{ required: true, message: "角色名称不能为空", trigger: "blur" }
	],
	roleKey: [
		{ required: true, message: "权限字符不能为空", trigger: "blur" }
	],
	roleSort: [
		{ required: true, message: "角色顺序不能为空", trigger: "blur" }
	]
})
const dataScopeOptions = ref([
	{ value: '1', label: '全部数据权限' },
	{ value: '2', label: '自定数据权限' },
	{ value: '3', label: '本部门数据权限' },
	{ value: '4', label: '本部门及以下数据权限' },
	{ value: '5', label: '仅本人数据权限' }
]);
// 菜单是否展开
const menuExpand = ref<boolean>(false);
// 菜单节点是否全选
const menuNodeAll = ref<boolean>(false);
// 部门是否展开
const deptExpand = ref<boolean>(true);
// 部门节点是否全选
const deptNodeAll = ref<boolean>(false);
// 定义树形结构的展开节点
const expandedKeys = ref([]);

interface statusOption {
	dictValue: string;
	dictLabel: string;
}
const statuslist = ref<statusOption[]>([]); //状态

// 对树组件的引用
const menuRef = ref<InstanceType<typeof ElTree> | null>(null);
const deptRef = ref<InstanceType<typeof ElTree> | null>(null);
// 打开弹窗
const openDialog = (type: string, row: RowRoleType, roleId: string) => {
	if (type === 'edit') {
		state.dialog.title = '修改角色';
		state.dialog.submitTxt = '修 改';
		state.dialog.assignmentShow = false
		// 表单反显
		getRole(roleId).then(res => {
			state.ruleForm = res.data.data
			// 根据传过来的roleId 得到当前roleId的树状图数据反显到页面上
			roleMenuTreeselect(roleId).then(res => {
				let checkedKeys = res.data.checkedKeys
				checkedKeys.forEach((v: TreeKey | TreeNodeData) => {
					nextTick(() => {
						// 确保 menuRef.value 不为空，然后调用 setChecked
						menuRef.value?.setChecked(v, true, false);
					});
				})
			})
		})

		if (!row) {
			state.ruleForm.roleId = roleId
		} else {
			state.ruleForm = row;
		}

	} else if (type == 'assignment') {
		state.dialog.title = '分配数据权限';
		state.dialog.assignmentShow = true
		/** 根据角色ID查询部门树结构 */
		deptTreeSelect(roleId).then(response => {
			// 初始化树形控件按数据
			state.deptData = response.data.depts
			// 默认展开树形控件
			nextTick(() => {
				handleCheckedTreeExpand(true, 'dept')
			})
			let checkedKeys = response.data.checkedKeys
			checkedKeys.forEach((v: TreeKey | TreeNodeData) => {
				nextTick(() => {
					// 确保 deptRef.value 不为空，然后调用 setChecked
					deptRef.value?.setChecked(v, true, false);
				});
			})
		})
		if (!row) {
			state.ruleForm.roleId = roleId
		} else {
			state.ruleForm = row;
		}
	} else {
		resetState();
		state.dialog.title = '新增角色';
		state.dialog.submitTxt = '新 增';
		state.dialog.assignmentShow = false
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	DialogFormRef.value.resetFields();
		// });
	}
	state.dialog.isShowDialog = true;
	getMenuData();

};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
	menuExpand.value = false
	menuNodeAll.value = false
	deptExpand.value = true
	deptNodeAll.value = false
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = (async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.roleId != undefined) {
				state.ruleForm.menuIds = getMenuAllCheckedKeys();
				updateRole(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				state.ruleForm.menuIds = getMenuAllCheckedKeys();
				addRole(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
	// closeDialog();
	// emit('refresh');
	// if (state.dialog.type === 'add') { }
});
// 提交部门数据
const submitDataScope = () => {
	if (state.ruleForm.roleId != undefined) {
		state.ruleForm.deptIds = getDeptAllCheckedKeys();
		dataScope(state.ruleForm).then(() => {
			emit('refresh');
			closeDialog();
			ElMessage.success('修改成功');
		});
	}
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 控制树节点的展开/折叠
const handleCheckedTreeExpand = (value: boolean, type: 'menu' | 'dept') => {
	// 菜单权限
	if (type === 'menu' && menuRef.value) {
		const treeList = state.menuData;
		for (const item of treeList) {
			const node = menuRef.value.store.nodesMap[item.id];
			if (node) {
				node.expanded = value;
			}
		}
	}
	// 部门权限
	else if (type === 'dept' && deptRef.value) {
		const treeList = state.deptData;
		for (const item of treeList) {
			const node = deptRef.value.store.nodesMap[item.id];
			if (node) {
				node.expanded = value;
			}
		}
		// const expandCollapseNodes = (nodes: any[], expand: boolean) => {
		// 	nodes.forEach(item => {
		// 		if (deptRef.value) {
		// 			const node = deptRef.value.store.nodesMap[item.id];
		// 			if (node) {
		// 				node.expanded = expand;
		// 			}
		// 			// 递归展开/折叠子节点
		// 			if (item.children && item.children.length) {
		// 				expandCollapseNodes(item.children, expand);
		// 			}
		// 		}

		// 	});
		// };

		// // 调用递归函数，展开或折叠所有节点
		// expandCollapseNodes(treeList, value);
	}
	// getMenuAllCheckedKeys()
};
// 递归获取所有节点的id
const getAllNodeIds = (nodes: any[]) => {
	let nodeIds: any[] = [];
	nodes.forEach((node: { id: any; children: string | any[]; }) => {
		nodeIds.push(node.id);
		if (node.children && node.children.length > 0) {
			nodeIds = nodeIds.concat(getAllNodeIds(node.children as any));
		}
	});
	return nodeIds;
};

// 控制树节点的全选/全不选
// const handleCheckedTreeNodeAll = (value: boolean, type: 'menu' | 'dept') => {
// 	console.log(value, 'value');
// 	if (type === 'menu' && menuRef.value) {
// 		menuRef.value.setCheckedNodes(value ? state.menuData : []);
// 	} else if (type === 'dept' && deptRef.value) {
// 		deptRef.value.setCheckedNodes(value ? deptOptions.value : []);
// 	}
// };
const handleCheckedTreeNodeAll = (value: boolean, type: 'menu' | 'dept') => {
	if (type === 'menu' && menuRef.value) {
		// 将 `state.menuData` 转换为符合 `Node` 类型的数据
		const transformToNode = (tree: TreeType[]): any[] => {
			return tree.map(node => ({
				...node,
				checked: value,  // 根据 `value` 全选或全不选
				indeterminate: false, // 设为 `false`，你可以根据需要调整
				children: node.children ? transformToNode(node.children) : [],  // 递归处理子节点
			}));
		};
		if (value) {
			menuRef.value.setCheckedNodes(transformToNode(state.menuData));  // 全选
		} else {
			menuRef.value.setCheckedNodes([]);  // 全不选
		}
		// getMenuAllCheckedKeys()
	}
	else if (type === 'dept' && deptRef.value) {
		// 将 `state.menuData` 转换为符合 `Node` 类型的数据
		const transformToNode = (tree: TreeType[]): any[] => {
			return tree.map(node => ({
				...node,
				checked: value,  // 根据 `value` 全选或全不选
				indeterminate: false, // 设为 `false`，你可以根据需要调整
				children: node.children ? transformToNode(node.children) : [],  // 递归处理子节点
			}));
		};

		if (value) {
			deptRef.value.setCheckedNodes(transformToNode(state.deptData));  // 全选
		} else {
			deptRef.value.setCheckedNodes([]);  // 全不选
		}
		// getDeptAllCheckedKeys()
	}


};

// 控制树节点的父子联动
const handleCheckedTreeConnect = (value: boolean, type: 'menu' | 'dept') => {
	if (type === 'menu') {
		state.ruleForm.menuCheckStrictly = value;
	} else if (type === 'dept') {
		state.ruleForm.deptCheckStrictly = value;
	}
	// getMenuAllCheckedKeys()
};
// 控制树节点的父子联动
const getMenuAllCheckedKeys = () => {
	if (menuRef.value) {
		// 获取完全选中的节点
		const checkedKeys = menuRef.value.getCheckedKeys();
		// 获取半选中的节点
		const halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
		checkedKeys.unshift(...halfCheckedKeys);
		return checkedKeys;
	}
	return [];
};
// 控制树节点的父子联动
const getDeptAllCheckedKeys = () => {
	if (deptRef.value) {
		// 获取完全选中的节点
		const checkedKeys = deptRef.value.getCheckedKeys();
		// 获取半选中的节点
		const halfCheckedKeys = deptRef.value.getHalfCheckedKeys();
		checkedKeys.unshift(...halfCheckedKeys);
		return checkedKeys;
	}
	return [];
};
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm };
	state.menuData = [...initialState.menuData];
	state.deptData = [...initialState.deptData];
	state.menuProps = { ...initialState.menuProps },
		state.defaultProps = { ...initialState.defaultProps },
		state.dialog = { ...initialState.dialog };
};

// /** 根据角色ID查询菜单树结构 */
// const getRoleMenuTreeselect = (roleId: string) => {
// 	return roleMenuTreeselect(roleId).then(response => {
// 		console.log(response, 'response');

// 		state.menuData = response.menus;
// 		return response;
// 	});
// }
// 获取菜单结构数据
const getMenuData = () => {
	getdictdata()
	menuTreeselect().then((response: { data: any; }) => {
		state.menuData = response.data.data;
	});

};


// 暴露变量
defineExpose({
	openDialog,
});
</script>

<style scoped lang="scss">
.system-role-dialog-container {
	.menu-data-tree {
		width: 100%;
		border: 1px solid var(--el-border-color);
		border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
		padding: 5px;
	}
}

/* 调整 form-item 标签布局 */
.el-form-item__label {
	display: flex !important;
	/* 确保 label 不会被其他样式覆盖 */
	align-items: center !important;
	/* 垂直居中对齐 */
	margin-right: 10px;
	/* 添加间距，避免 label 与 input 过于紧凑 */
}

/* 可选：控制 label 内部元素的布局 */
.el-form-item__label>div {
	display: flex;
	align-items: center;
}

.el-tooltip {
	margin-right: 5px;
	/* 添加图标和文本之间的间距 */
}

.el-form-item {
	display: flex;
	/* 使整个 form-item 元素为 flex 布局 */
	align-items: center;
	/* 确保表单项内容垂直居中 */
}

.el-tree {
	width: 500px;
	margin-top: 5px;
	border: 1px solid #e5e6e7;
	background: #ffffff none;
	border-radius: 4px;
}
</style>
