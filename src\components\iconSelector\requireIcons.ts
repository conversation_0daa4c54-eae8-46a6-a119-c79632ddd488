const importAllSVGs = async () => {
  const icons = [];

  const files = import.meta.glob('../../assets/icons/svg/*.svg'); // 使用 Vite 或现代 Webpack 支持的 import.meta.glob
  for (const path in files) {
    if (Object.hasOwnProperty.call(files, path)) {
      // 使用正则从路径中提取文件名，去掉扩展名
      const matchResult = path.match(/([^/]+)(?=\.svg$)/);      
      if (matchResult) {
        const iconName = matchResult[1]; // 获取 `-` 后面的部分作为文件名
        icons.push({ name: iconName}); // 存储图标名称和路径
      }
    }
  }
  return icons;


};

export default importAllSVGs;