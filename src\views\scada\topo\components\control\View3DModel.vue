<template>
  <div
      :class="editMode ? 'modelClass' : ''"
      :style="{
      height: editMode ? `${detail.style.position.h}px` : 'auto',
      backgroundColor: detail.style.backColor,
    }"
  >
    <iframe
        v-if="editMode"
        style="width: 100%"
        :src="detail.modelUrl"
        frameborder="0"
        :width="detail.style.position.w"
        :height="`${detail.style.position.h - 10}px`">
    </iframe>
    <img
        v-else
        :src="baseApi + detail.imageUrl"
        class="view-image"
        @dragstart.prevent
        @dragover.prevent
        @drop.prevent />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import ModelObj from './ModelObj.vue'; // 假设这是正确的相对路径
import ModelCollada from './ModelCollada.vue'; // 假设这是正确的相对路径
import ModelFbx from './ModelFbx.vue'; // 假设这是正确的相对路径

// 如果 BaseView 是一个组合函数或 mixin，这里应相应地引入并调用它
// import useBaseView from '@/composables/useBaseView';

const props = defineProps({
  detail: {
    type: Object,
    required: true
  },
  editMode: {
    type: Boolean,
    default: false
  }
});

const baseApi = import.meta.env.VITE_APP_BASE_API || '/dev-api'; // 在 Vite 中使用 import.meta.env

// 如果 BaseView 需要初始化逻辑，请在此处添加
// const { someValue, someMethod } = useBaseView(props);

onMounted(() => {
  // 初始化逻辑
});

</script>

<style lang="scss" scoped>
.view-image {
  height: 100%;
  width: 100%;
}
.modelClass {
  padding: 5px;
  overflow: hidden;
}
</style>