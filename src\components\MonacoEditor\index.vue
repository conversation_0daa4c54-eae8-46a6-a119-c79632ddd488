<!-- src/components/MonacoEditor.vue -->
<template>
  <div ref="container" class="monaco-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineProps, defineEmits, nextTick } from 'vue'
import * as monaco from 'monaco-editor'

const props = defineProps({
  modelValue: {
    type: String,
    required: true,
    default: ''
  },
  language: {
    type: String,
    default: 'javascript'
  },
  theme: {
    type: String,
    default: 'vs'
  },
  height: {
    type: String,
    default: '75vh'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const container = ref<HTMLElement | null>(null)
let editor: monaco.editor.IStandaloneCodeEditor | null = null

const initEditor = async () => {
  if (!container.value) return

  try {
    editor = monaco.editor.create(container.value, {
      value: props.modelValue || '',
      language: props.language,
      theme: props.theme,
      automaticLayout: true,
      scrollBeyondLastLine: false,
      minimap: { enabled: false },
      fontSize: 14,
      lineNumbers: 'on',
      roundedSelection: false,
      scrollbar: {
        vertical: 'auto',
        horizontal: 'auto',
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10,
        alwaysConsumeMouseWheel: false
      },
      wordWrap: 'on',
      mouseWheelZoom: false,
      smoothScrolling: false
    })

    editor.onDidChangeModelContent(() => {
      if (editor) {
        const value = editor.getValue()
        emit('update:modelValue', value)
        emit('change', value)
      }
    })
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Monaco Editor initialization failed:', error)
  }
}

onMounted(async () => {
  await nextTick()
  await initEditor()
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

watch(
    () => props.modelValue,
    (newVal) => {
      if (editor && editor.getValue() !== newVal) {
        editor.setValue(newVal || '')
      }
    }
)

watch(
    () => props.language,
    (newLang) => {
      if (editor) {
        const model = editor.getModel()
        if (model) {
          monaco.editor.setModelLanguage(model, newLang)
        }
      }
    }
)

// 暴露方法给父组件
const setValue = (value: string) => {
  if (editor) {
    editor.setValue(value)
  }
}

const getValue = () => {
  return editor ? editor.getValue() : ''
}

defineExpose({
  editor,
  setValue,
  getValue
})
</script>

<style scoped>
.monaco-container {
  width: 100%;
  height: 75vh;
  border: 1px solid #ccc;
  touch-action: none;
}

/* 修复Monaco Editor滚动问题 */
.monaco-container :deep(.monaco-scrollable-element) {
  touch-action: none;
}

.monaco-container :deep(.monaco-editor .overflow-guard) {
  touch-action: none;
}

.monaco-container :deep(.monaco-editor .monaco-scrollable-element > .scrollbar) {
  touch-action: none;
}
</style>