<template>
	<div class="system-dic-container layout-padding">
		<el-card shadow="hover" class="layout-padding-auto">
			<div class="system-user-search mb15">
				<el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
					<el-form-item label="字典名称">
						<el-input v-model="state.tableData.param.dictName" aria-label="First Name" placeholder="请输入字典名称"
							clearable style="width: 240px" />
					</el-form-item>
					<el-form-item label="字典类型">
						<el-input v-model="state.tableData.param.dictType" placeholder="请输入用户手机号码" clearable
							style="width: 240px" />
					</el-form-item>
					<el-form-item label="状态">
						<el-select v-model="state.tableData.param.status" placeholder="用户状态" clearable
							style="width: 240px">
							<el-option v-for="dict in statuslist" :key="dict.dictValue" :label="dict.dictLabel"
								:value="dict.dictValue" />
						</el-select>
					</el-form-item>
					<el-form-item label="创建时间">
						<el-date-picker v-model="dateRange" style="width: 240px" date-format="YYYY-MM-DD"
							value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期"
							end-placeholder="结束日期"></el-date-picker>
					</el-form-item>
					<el-form-item>
						<el-button size="default" type="primary" class="ml10" @click="getTableData">
							<el-icon>
								<ele-Search />
							</el-icon>
							查询
						</el-button>
						<el-button size="default" @click="resetQuery">
							<el-icon><ele-Refresh /></el-icon>
							重置
						</el-button>
					</el-form-item>
				</el-form>
				<el-row :gutter="10" class="mb8" :justify="'space-between'">
					<div>
						<el-button v-auths="['system:dict:add']" size="default" type="primary" class="ml5"
							@click="onOpenAddDic('add')">
							<el-icon><ele-Plus /></el-icon>
							新增
						</el-button>
						<el-button v-auths="['system:dict:edit']" size="default" type="success" class="ml10"
							:disabled="single" @click="onOpenEditDic('edit', undefined)">
							<el-icon><ele-EditPen /></el-icon>
							修改
						</el-button>
						<el-button v-auths="['system:dict:remove']" size="default" type="danger" class="ml10"
							:disabled="multiple" @click="onRowDel">
							<el-icon><ele-DeleteFilled /></el-icon>
							删除
						</el-button>
						<el-button v-auths="['system:dict:export']" size="default" type="warning" class="ml10"
							@click="handleExport">
							<el-icon><ele-Download /></el-icon>
							导出
						</el-button>
						<el-button v-auths="['system:dict:refresh']" size="default" type="info" class="ml10"
							@click="handleRefreshCache">
							<el-icon><ele-Refresh /></el-icon>
							刷新缓存
						</el-button>
					</div>
					<right-toolbar v-model:showSearch="showSearch" :showsearch="showSearch"
						@queryTable="getTableData"></right-toolbar>
				</el-row>
			</div>
			<el-table :data="state.tableData.data" v-loading="state.tableData.loading"
				@selection-change="handleSelectionChange" border style="width: 100%"
				:header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
				<el-table-column type="selection" width="55" align="center"></el-table-column>
				<el-table-column prop="dictId" label="字典编号" align="center"></el-table-column>
				<el-table-column label="字典名称" align="center" prop="dictName" :show-overflow-tooltip="true" />
				<el-table-column label="字典类型" align="center" :show-overflow-tooltip="true">
					<template #default="scope">
						<router-link :to="'/system/dict-data/index/' + scope.row.dictId" class="link-type">
							<span>{{ scope.row.dictType }}</span>
						</router-link>
					</template>
				</el-table-column>
				<el-table-column label="状态" align="center" prop="dictLabel">
					<template #default="scope">
						<DictTag :options="statuslist" :value="scope.row.status"></DictTag>
						<!-- <el-tag :type="scope.row.status == 0 ? '' : 'danger'">{{ scope.row.status == 0 ? '正常' :
							'停用'
							}}</el-tag> -->
					</template>
				</el-table-column>
				<el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
				<el-table-column label="创建时间" align="center" prop="createTime" width="180">
					<template #default="scope">
						<span>{{ (scope.row.createTime) }}</span>
					</template>
				</el-table-column>
				<el-table-column label="操作" align="center" class-name="small-padding fixed-width">
					<template #default="scope">
						<el-button size="default" text type="primary" icon="el-icon-edit"
							@click="onOpenEditDic('edit', scope.row)"
							v-auths="['system:dict:edit']"><el-icon><ele-EditPen /></el-icon>修改</el-button>
						<el-button size="default" text type="primary" icon="el-icon-delete" @click="onRowDel(scope.row)"
							v-auths="['system:dict:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
					</template>
				</el-table-column>
			</el-table>
			<el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
				style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
				v-model:current-page="state.tableData.param.pageNum" background
				v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
				:total="state.tableData.total">
			</el-pagination>
		</el-card>
		<DicDialog ref="dicDialogRef" @refresh="getTableData()" />
	</div>
</template>

<script setup lang="ts" name="systemDic">
import { defineAsyncComponent, reactive, onMounted, ref } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { addDateRange } from '/@/utils/next';
import { delType, listType, refreshCache } from '/@/api/system/dict/type';
import { download } from '/@/utils/request';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const DicDialog = defineAsyncComponent(() => import('/@/views/system/dict/dialog.vue'));

// 定义变量内容
const dicDialogRef = ref();
const state = reactive<SysDicState>({
	tableData: {
		data: [],
		total: 0,
		loading: false,
		param: {
			pageNum: 1,
			pageSize: 10,
			dictName: undefined,
			dictType: undefined,
			status: ''
		},
	},
});
const showSearch = ref(true)    // 显示搜索条件
interface statusOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}
const statuslist = ref<statusOption[]>([]); //状态列表
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref() //dictId
const dateRange = ref<[string, string]>(['', '']); //时间范围
// 初始化表格数据
const getTableData = async () => {
	state.tableData.loading = true;
	try {
		const data = addDateRange(state.tableData.param, dateRange.value)
		const response = await listType(data);
		state.tableData.data = response.data.rows;
		state.tableData.total = response.data.total;
	} catch (error) {
		console.error('Error fetching table data:', error);
	} finally {
		setTimeout(() => {
			state.tableData.loading = false;
		}, 500);
	}
	// state.tableData.loading = true;
	// const data = [];
	// for (let i = 0; i < 2; i++) {
	// 	data.push({
	// 		dicName: i === 0 ? '角色标识' : '用户性别',
	// 		fieldName: i === 0 ? 'SYS_ROLE' : 'SYS_UERINFO',
	// 		describe: i === 0 ? '这是角色字典' : '这是用户性别字典',
	// 		status: true,
	// 		createTime: new Date().toLocaleString(),
	// 		list: [],
	// 	});
	// }
	// state.tableData.data = data;
	// state.tableData.total = state.tableData.data.length;
	// setTimeout(() => {
	// 	state.tableData.loading = false;
	// }, 500);
};
/** 重置按钮操作 */
const resetQuery = () => {
	state.tableData.param = {
		pageNum: 1,
		pageSize: 10,
		dictName: undefined,
		dictType: undefined,
		status: ''
	}
}
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable')
		// const status = await getDicts('sys_normal_disable');
		// statuslist.value = status.data.data
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 多选框选中数据
const handleSelectionChange = (selection: any[]) => {
	ids.value = selection.map((item: { dictId: string; }) => item.dictId);
	console.log(ids.value);

	single.value = selection.length != 1;
	multiple.value = !selection.length;
}
// 打开新增字典弹窗
const onOpenAddDic = (type: string) => {
	dicDialogRef.value.openDialog(type);
};
// 打开修改字典弹窗
const onOpenEditDic = (type: string, row: RowDicType | undefined) => {
	var dictId = ''
	if (!row) {
		dictId = ids.value
	} else {
		dictId = row.dictId
	}
	dicDialogRef.value.openDialog(type, row, dictId);
};
// 删除字典
const onRowDel = (row: RowDicType) => {
	const dictIds = row.dictId || ids.value;
	ElMessageBox.confirm(`此操作将永久删除字典名称：“${dictIds}”的数据项，是否继续?`, '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			delType(dictIds).then(() => {
				getTableData();
				ElMessage.success('删除成功');
			})
		})
		.catch(() => { });
};
/** 导出按钮操作 */
const handleExport = () => {
	const exportParams = {
		pageNum: state.tableData.param.pageNum || 1,
		pageSize: state.tableData.param.pageSize || 10,
		dictName: state.tableData.param.dictName || '',
		dictType: state.tableData.param.dictType || '',
		status: state.tableData.param.status || '',
	};
	download('system/dict/type/export', {
		...state.tableData.param
	}, `type_${new Date().getTime()}.xlsx`)
}
// 刷新缓存处理函数
const handleRefreshCache = () => {
	try {
		// 调用刷新缓存的 API
		refreshCache().then(() => {
			ElMessage.success('刷新成功');
			// 清除字典缓存
			dictStore.clearDictCache();
		});

	} catch (error) {
		ElMessage.success('刷新失败');
		console.error('刷新缓存失败:', error);
	}
};
// 分页改变
const onHandleSizeChange = (val: number) => {
	state.tableData.param.pageSize = val;
	getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
	state.tableData.param.pageNum = val;
	getTableData();
};
// 页面加载时
onMounted(() => {
	getTableData();
	getdictdata()
});
</script>
