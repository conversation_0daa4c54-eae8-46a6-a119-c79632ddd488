<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="场景名称" prop="sceneName">
                        <el-input v-model="state.tableData.param.sceneName" clearable size="default"
                            placeholder="请输入场景名称" style="width: 240px" />
                    </el-form-item>

                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:script:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAddDic('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="场景名称" align="center" prop="sceneName" />
                <el-table-column label="状态" align="center" prop="enable">
                    <template #default="scope">
                        <el-tag type="success" size="small" v-if="scope.row.enable == 1">启动</el-tag>
                        <el-tag type="danger" size="small" v-if="scope.row.enable == 2">暂停</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="是否告警" align="center" prop="hasAlert">
                    <template #default="scope">
                        <span v-if="scope.row.hasAlert == 1">是</span>
                        <span v-if="scope.row.hasAlert == 2">否</span>
                    </template>
                </el-table-column>
                <el-table-column label="触发条件" align="center" prop="cond">
                    <template #default="scope">
                        <span v-if="scope.row.cond == 1">任意条件</span>
                        <span v-if="scope.row.cond == 2">所有条件</span>
                        <span v-if="scope.row.cond == 3">不满足条件</span>
                    </template>
                </el-table-column>
                <el-table-column label="执行方式" align="center" prop="executeMode">
                    <template #default="scope">
                        <span v-if="scope.row.executeMode == 1">串行</span>
                        <span v-if="scope.row.executeMode == 2">并行</span>
                    </template>
                </el-table-column>
                <el-table-column label="静默时间" align="center" prop="silentPeriod" width="">
                    <template #default="scope">
                        <span>{{ scope.row.silentPeriod }} 分钟</span>
                    </template>
                </el-table-column>
                <el-table-column label="延时执行" align="center" prop="executeDelay" width="">
                    <template #default="scope">
                        <span>{{ scope.row.executeDelay }} 秒钟</span>
                    </template>
                </el-table-column>

                <el-table-column label="创建时间" align="center" prop="createTime" width="">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default" type="primary" text @click="onOpenEditDic('edit', scope.row)"
                                v-auths="['iot:scene:query']"><el-icon size="small"><ele-View /></el-icon>查看</el-button>
                            <el-button size="default" type="primary" text @click="onRowDel(scope.row)"
                                v-auths="['iot:scene:remove']"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                            <el-button size="default" type="primary" text @click="handleRun(scope.row)"
                                v-auths="['iot:scene:run']"><el-icon>
                                    <CaretRight />
                                </el-icon>执行一次</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <SceneDialog ref="SceneDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { delScript, listScript } from '/@/api/iot/script';
import { parseTime } from '/@/utils/next'
import { delScene, listScene, runScene } from '/@/api/iot/scene';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const SceneDialog = defineAsyncComponent(() => import('/@/views/iot/scene/scene-dialog.vue'));

// 定义变量内容
const SceneDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            scriptPurpose: 1,
            sceneId: '' as any,
            sceneName: '' as any

        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //sceneId
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listScene(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        scriptPurpose: 1,
        sceneId: '' as any,
        sceneName: '' as any
    }
}
// 打开新增产品分类弹窗
const onOpenAddDic = (type: string) => {
    SceneDialogRef.value.openDialog(type);
};
// 打开修改产品分类弹窗
const onOpenAdd = (type: string, row: any | undefined) => {
    var sceneId = ''
    if (!row) {
        sceneId = ids.value
    } else {
        sceneId = row.sceneId
    }
    SceneDialogRef.value.openDialog(type, row, sceneId);
};
// 打开修改产品分类弹窗
const onOpenEditDic = (type: string, row: any | undefined) => {
    var sceneId = ''
    if (!row) {
        sceneId = ids.value
    } else {
        sceneId = row.sceneId
    }
    SceneDialogRef.value.openDialog(type, row, sceneId);
};
// 删除产品分类
const onRowDel = (row: any) => {
    const sceneIds = row.sceneId || ids.value;
    ElMessageBox.confirm(`是否确认删除场景联动编号为${sceneIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delScene(sceneIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 执行一次
const handleRun = (data: any) => {
    const params = { sceneId: data.sceneId };
    runScene(params).then((res) => {
        ElMessage.success(res.data.msg);
    });
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
});
</script>
