import request from '/@/utils/request'

// 定义查询公告列表时的请求参数类型
interface ListNoticeParams {
  pageNum?: number;
  pageSize?: number;
  [key: string]: any;  // 其他查询条件
}

// 定义公告对象的类型
interface Notice {
  noticeId: string;
  noticeTitle: string;
  noticeContent: string;
  createTime: string;
  updateTime?: string;
  status: number;
}

// 定义基础的 API 响应类型
interface ApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

// 查询公告列表
export function listNotice(query: ListNoticeParams): Promise<ApiResponse<any>> {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  })
}

// 查询公告详细
export function getNotice(noticeId: string): Promise<ApiResponse<any>> {
  return request({
    url: `/system/notice/${noticeId}`,
    method: 'get'
  })
}

// 新增公告
export function addNotice(data: any): Promise<ApiResponse<null>> {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  })
}

// 修改公告
export function updateNotice(data: any): Promise<ApiResponse<null>> {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  })
}

// 删除公告
export function delNotice(noticeId: string): Promise<ApiResponse<null>> {
  return request({
    url: `/system/notice/${noticeId}`,
    method: 'delete'
  })
}
