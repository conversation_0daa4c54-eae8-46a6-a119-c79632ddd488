import { reactive } from 'vue';  // 从 vue 中导入 reactive
import { mergeRecursive } from "/@/utils/next";
import DictMeta from './DictMeta';
import DictData from './DictData';

// Default options for the dictionary
const DEFAULT_DICT_OPTIONS = {
  types: [] as Array<any>,
};

/**
 * @classdesc 字典
 * @property {Object} label 标签对象，内部属性名为字典类型名称
 * @property {Object} dict 字段数组，内部属性名为字典类型名称
 * @property {Array.<DictMeta>} _dictMetas 字典元数据数组
 */
export default class Dict {
  owner: any = null;
  label: { [key: string]: { [key: string]: string } } = reactive({});  // 使用 reactive 来管理响应式
  type: { [key: string]: DictData[] } = reactive({});  // 使用 reactive 来管理响应式

  private _dictMetas: DictMeta[] = [];

  constructor() {}

  /**
   * 初始化字典
   * @param {Object | Array} options 字典选项
   * @returns {Promise<DictData[]>}
   */
  init(options: { types: Array<any> } | any[]): Promise<DictData[]> {
    if (options instanceof Array) {
      options = { types: options };
    }
    
    const opts = mergeRecursive(DEFAULT_DICT_OPTIONS, options);
    
    if (opts.types === undefined) {
      throw new Error('need dict types');
    }

    const ps: Array<Promise<DictData[]>> = [];
    this._dictMetas = opts.types.map((t:any) => DictMeta.parse(t));
    
    this._dictMetas.forEach(dictMeta => {
      const type = dictMeta.type;
      this.label[type] = {};  // 直接赋值
      this.type[type] = [];  // 直接赋值

      if (dictMeta.lazy) {
        return;
      }
      
      ps.push(loadDict(this, dictMeta));
    });

    return Promise.all(ps).then(() => {
      return ps.flat();
    });
  }

  /**
   * 重新加载字典
   * @param {String} type 字典类型
   * @returns {Promise<DictData[]>}
   */
  reloadDict(type: string): Promise<DictData[]> {
    const dictMeta = this._dictMetas.find(e => e.type === type);
    
    if (dictMeta === undefined) {
      return Promise.reject(`the dict meta of ${type} was not found`);
    }
    
    return loadDict(this, dictMeta);
  }
}

/**
 * 加载字典
 * @param {Dict} dict 字典
 * @param {DictMeta} dictMeta 字典元数据
 * @returns {Promise<DictData[]>}
 */
function loadDict(dict: Dict, dictMeta: DictMeta): Promise<DictData[]> {
  return dictMeta.request(dictMeta)
    .then((response: any) => {
      const type = dictMeta.type;
      let dicts = dictMeta.responseConverter(response, dictMeta);
      
      if (!(dicts instanceof Array)) {
        console.error('the return of responseConverter must be Array.<DictData>');
        dicts = [];
      } else if (dicts.filter(d => d instanceof DictData).length !== dicts.length) {
        console.error('the type of elements in dicts must be DictData');
        dicts = [];
      }
      
      dict.type[type].splice(0, Number.MAX_SAFE_INTEGER, ...dicts);
      dicts.forEach((d: { value: string | number; label: string; }) => {
        dict.label[type][d.value] = d.label;  // 直接赋值
      });
      
      return dicts;
    });
}
