// src/directives/dialogDrag.ts
export default {
  mounted(el:any) {
    const dialogEl = el.closest('.el-dialog');

    if (!dialogEl) return;

    const dragArea = el.querySelector('.el-dialog__header') || el;
    dragArea.style.cursor = 'move';

    let isDragging = false;
    let startX = 0, startY = 0;
    let initialX = 0, initialY = 0;

    const onMouseDown = (e:any) => {
      if (e.target.closest('input, textarea, .el-select')) return;
      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;
      const rect = dialogEl.getBoundingClientRect();
      initialX = rect.left;
      initialY = rect.top;
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    };

    const onMouseMove = (e:any) => {
      if (!isDragging) return;
      const dx = e.clientX - startX;
      const dy = e.clientY - startY;
      dialogEl.style.left = `${initialX + dx}px`;
      dialogEl.style.top = `${initialY + dy}px`;
    };

    const onMouseUp = () => {
      isDragging = false;
      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    };

    dragArea.addEventListener('mousedown', onMouseDown);
  },
};