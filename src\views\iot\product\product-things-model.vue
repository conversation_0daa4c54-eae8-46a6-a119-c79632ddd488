<template>
    <div style="padding-left: 20px">
        <el-row :gutter="10" class="mb8" :justify="'space-between'">
            <div>
                <el-button v-if="productInfo.status == 1" plain v-auths="['iot:model:add']" size="default"
                    type="primary" class="ml5" @click="handleAdd('add')">
                    <el-icon><ele-Plus /></el-icon>
                    新增
                </el-button>
                <el-button v-if="productInfo.status == 1" plain v-auths="['iot:model:import']" size="default"
                    type="success" class="ml10" @click="handleSelect">
                    <el-icon><ele-Upload /></el-icon>
                    导入通用物模型
                </el-button>
                <el-button v-auths="['iot:model:list']" plain size="default" type="warning" class="ml10"
                    @click="getTableData">
                    <el-icon><ele-Refresh /></el-icon>
                    刷新
                </el-button>
                <el-button size="default" plain type="info" class="ml10" @click="handleOpenThingsModel">
                    <el-icon><ele-View /></el-icon>
                    查看物模型
                </el-button>
                <el-link class="ml10" type="danger" style="padding-top: 5px" :underline="false">注意：标识符不能重复</el-link>
            </div>
        </el-row>

        <el-form size="default" v-if="state.tableData.queryParams.isModbus" style="margin-bottom: 20px;">
            <el-form-item label="请选择设备从机:">
                <div style="width: 250px;display: flex;">
                    <el-select v-model="slave.id" placeholder="请选择设备从机" @change="selectSlave">
                        <el-option v-for="slave in slaveList" :key="slave.slaveAddr"
                            :label="`${slave.slaveName}   (${slave.slaveAddr})`" :value="slave.slaveAddr"></el-option>
                    </el-select>
                    <el-button class="ml10" type="primary" plain size="default" @click="getGateway">网关物模型</el-button>
                </div>
            </el-form-item>
        </el-form>

        <el-table v-loading="state.tableData.loading" :data="state.tableData.modelList" border style="width: 100%"
            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }" size="small">
            <el-table-column label="名称" align="center" prop="modelName" width="230" />
            <el-table-column label="标识符" align="center" prop="identifier" />
            <el-table-column label="寄存器地址(10进制)" align="center" prop="regAddr"
                v-if="state.tableData.queryParams.isModbus" />
            <el-table-column label="图表展示" align="center" prop="" width="80">
                <template #default="scope">
                    <dictTag :options="statuslist" :value="scope.row.isChart" />
                </template>
            </el-table-column>
            <el-table-column label="实时监测" align="center" prop="" width="75">
                <template #default="scope">
                    <dictTag :options="statuslist" :value="scope.row.isMonitor" />
                </template>
            </el-table-column>
            <el-table-column label="只读" align="center" prop="" width="75">
                <template #default="scope">
                    <dictTag :options="statuslist" :value="scope.row.isReadonly" />
                </template>
            </el-table-column>
            <el-table-column label="历史存储" align="center" prop="" width="75">
                <template #default="scope">
                    <dictTag :options="statuslist" :value="scope.row.isHistory" />
                </template>
            </el-table-column>
            <el-table-column label="物模型类别" align="center" prop="type" width="100">
                <template #default="scope">
                    <dictTag :options="things_type_list" :value="scope.row.type" />
                </template>
            </el-table-column>
            <el-table-column label="数据类型" align="center" prop="datatype" width="80">
                <template #default="scope">
                    <dictTag :options="data_type_list" :value="scope.row.datatype" />
                </template>
            </el-table-column>
            <el-table-column label="数据定义" align="left" header-align="center" prop="specs" min-width="150"
                class-name="specsColor">
                <template #default="scope">
                    <div v-html="formatSpecsDisplay(scope.row.specs)"></div>
                </template>
            </el-table-column>
            <el-table-column label="计算公式" align="center" prop="formula" />
            <el-table-column label="排序" align="center" prop="modelOrder" width="80" />
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button size="small" text @click="handleUpdate('edit', scope.row)" type="primary"
                        v-auths="['iot:model:query']"
                        v-if="productInfo.status != 2"><el-icon><ele-View /></el-icon>查看</el-button>
                    <el-button size="small" text @click="handleDelete(scope.row)" type="primary"
                        v-auths="['iot:model:remove']"
                        v-if="productInfo.status != 2"><el-icon><ele-DeleteFilled /></el-icon>删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <!--物模型参数类型-->
        <ManipulateDialog ref="ManipulateDialogRef" @refresh="getTableData()" />
        <!-- 导入通用物模型对话框 -->
        <ImportDialog ref="ImportDialogRef" @refresh="getTableData()" />
        <!-- 物模型JSON -->
        <el-dialog :title="'物模型'" v-model="openThingsModel" width="600px" append-to-body>
            <div style="border: 1px solid #ccc; margin-top: -15px; height: 600px; overflow: scroll">
            </div>
            <template #footer>
                <el-button type="info" @click="handleCloseThingsModel">关 闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>


<script setup lang="ts">
import { reactive, ref, watch, defineAsyncComponent } from 'vue';
import { delTemplate, listTemplate } from '/@/api/iot/template';
import { listByPid } from '/@/api/iot/salve';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { ElMessage, ElMessageBox } from 'element-plus';
const dictStore = useDictStore();  // 使用 Pinia store
// 引入组件
const ManipulateDialog = defineAsyncComponent(() => import('/@/views/iot/product/manipulate-model.vue'));
const ImportDialog = defineAsyncComponent(() => import('/@/views/iot/product/product-select-template.vue'));
// 定义变量内容
const ManipulateDialogRef = ref();
const ImportDialogRef = ref();
// 定义 props
const props = defineProps({
    modelValue: {
        type: Object
    }
});
interface modeData {
    identifier: string;
    modelName: string;
    type: number;
    isChart: boolean;
    isMonitor: boolean;
    isHistory: boolean;
    isSharePerm: boolean;
    isReadonly: boolean;
    specs: string;

}
const state = reactive({
    tableData: {
        modelList: [] as modeData[],
        total: 0,
        loading: false,
        queryParams: {
            productId: 0,
            productName: '' as any,
            pageNum: 1,
            pageSize: 100,
            isModbus: '' as any,
            tempSlaveId: '' as any
        },
    },
});
//接收父组件得产品信息
const productInfo = ref({
    status: '' as any,
    productId: '' as any,
    productName: '' as any,
    isModbus: false,
})
const slave = ref({
    id: '' as any,
})
// 物模型
const thingsModel = ref({
    properties: [] as any[],
    functions: [] as any[],
    events: [] as any[],
})
const openThingsModel = ref(false)
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
interface SaveTypeOption {
    slaveAddr: string;
    slaveName: string;
}
const slaveList = ref<SaveTypeOption[]>([]);
const statuslist = ref<TypeOption[]>([]);
const things_type_list = ref<TypeOption[]>([]);
const data_type_list = ref<TypeOption[]>([]);
const ids = ref() //modelId
// 监听props的变化
watch(() => props.modelValue, (newVal) => {
    try {
        productInfo.value = newVal as any
        if (productInfo.value && productInfo.value.productId != 0) {
            state.tableData.queryParams.productId = productInfo.value.productId;
            state.tableData.queryParams.productName = productInfo.value.productName;
            state.tableData.queryParams.isModbus = productInfo.value.isModbus;
            getTableData();
            getdictdata()
            if (state.tableData.queryParams.isModbus) {
                getSlaveList();
            }
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
/** 查询产品物模型列表 */
const getTableData = async () => {
    try {
        state.tableData.loading = true;
        const response = await listTemplate(state.tableData.queryParams);
        state.tableData.total = response.data.total;
        state.tableData.modelList = response.data.rows;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('iot_yes_no')
        things_type_list.value = await dictStore.fetchDict('iot_things_type')
        data_type_list.value = await dictStore.fetchDict('iot_data_type')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/**根据产品id获取从机列表*/
const getSlaveList = () => {
    const params = {
        productId: state.tableData.queryParams.productId,
    };
    listByPid(params).then((response) => {

        slaveList.value = response.data.data;
        slave.value.id = slaveList.value[0].slaveAddr;
        state.tableData.queryParams.tempSlaveId = slaveList.value[0].slaveAddr;
        getTableData();
    });
}

/** 新增按钮操作 */
const handleAdd = (type: any) => {
    ManipulateDialogRef.value.openDialog(type, undefined, state.tableData.queryParams);
}
/** 选择物模型 */
const handleSelect = () => {
    ImportDialogRef.value.openDialog(state.tableData.queryParams);

}
/**查看物模型 */
const handleOpenThingsModel = () => {
    // 生成物模型
    thingsModel.value = {
        properties: [],
        functions: [],
        events: [],
    };
    for (var i = 0; i < state.tableData.modelList.length; i++) {
        let thingsItem = {
            id: '' as any,
            name: '' as any,
            isChart: '' as any,
            isMonitor: '' as any,
            isHistory: '' as any,
            isSharePerm: '' as any,
            isReadonly: '' as any,
            datatype: '' as any,
        };
        thingsItem.id = state.tableData.modelList[i].identifier;
        thingsItem.name = state.tableData.modelList[i].modelName;
        if (state.tableData.modelList[i].type == 1) {
            //属性
            thingsItem.isChart = state.tableData.modelList[i].isChart;
            thingsItem.isMonitor = state.tableData.modelList[i].isMonitor;
            thingsItem.isHistory = state.tableData.modelList[i].isHistory;
            thingsItem.isSharePerm = state.tableData.modelList[i].isSharePerm;
            thingsItem.isReadonly = state.tableData.modelList[i].isReadonly;
            thingsItem.datatype = JSON.parse(state.tableData.modelList[i].specs);
            thingsModel.value.properties.push(thingsItem);
        } else if (state.tableData.modelList[i].type == 2) {
            // 功能
            thingsItem.isHistory = state.tableData.modelList[i].isHistory;
            thingsItem.isSharePerm = state.tableData.modelList[i].isSharePerm;
            thingsItem.isReadonly = state.tableData.modelList[i].isReadonly;
            thingsItem.datatype = JSON.parse(state.tableData.modelList[i].specs);
            thingsModel.value.functions.push(thingsItem);
        } else if (state.tableData.modelList[i].type == 3) {
            // 事件
            thingsItem.isHistory = state.tableData.modelList[i].isHistory;
            thingsItem.isSharePerm = state.tableData.modelList[i].isSharePerm;
            thingsItem.isReadonly = state.tableData.modelList[i].isReadonly;
            thingsItem.datatype = JSON.parse(state.tableData.modelList[i].specs);
            thingsModel.value.events.push(thingsItem);
        }
    }

    openThingsModel.value = true;
}
/**关闭物模型 */
const handleCloseThingsModel = () => {
    openThingsModel.value = false;
}

/** 格式化显示数据定义 */
const formatSpecsDisplay = (json: string) => {
    let specs = JSON.parse(json);
    if (specs.type === 'integer' || specs.type === 'decimal' || specs.type === 'INT16' || specs.type === 'INT') {
        return (
            '<span style=\'width:50%;display:inline-block;\'>最大值：<span style="color:#F56C6C">' +
            specs.max +
            '</span></span>最小值：<span style="color:#F56C6C">' +
            specs.min +
            '</span><br /><span style=\'width:50%;display:inline-block;\'>步长：<span style="color:#F56C6C">' +
            specs.step +
            '</span></span>单位：<span style="color:#F56C6C">' +
            specs.unit
        );
    } else if (specs.type === 'string') {
        return '最大长度：<span style="color:#F56C6C">' + specs.maxLength + '</span>';
    } else if (specs.type === 'array') {
        return '<span style=\'width:50%;display:inline-block;\'>数组类型：<span style="color:#F56C6C">' + specs.arrayType + '</span></span>元素个数：<span style="color:#F56C6C">' + specs.arrayCount;
    } else if (specs.type === 'enum') {
        let items = '';
        for (let i = 0; i < specs.enumList.length; i++) {
            items = items + "<span style='width:50%;display:inline-block;'>" + specs.enumList[i].value + "：<span style='color:#F56C6C'>" + specs.enumList[i].text + '</span></span>';
            if (i > 0 && i % 2 != 0) {
                items = items + '<br />';
            }
        }
        return items;
    } else if (specs.type === 'bool') {
        return '<span style=\'width:50%;display:inline-block;\'>0：<span style="color:#F56C6C">' + specs.falseText + '</span></span>1：<span style="color:#F56C6C">' + specs.trueText;
    } else if (specs.type === 'object') {
        let items = '';
        for (let i = 0; i < specs.params.length; i++) {
            items = items + "<span style='width:50%;display:inline-block;'>" + specs.params[i].name + "：<span style='color:#F56C6C'>" + specs.params[i].datatype.type + '</span></span>';
            if (i > 0 && i % 2 != 0) {
                items = items + '<br />';
            }
        }
        return items;
    }
}

const selectSlave = () => {
    state.tableData.queryParams.tempSlaveId = slave.value.id;
    getTableData();
}
const getGateway = () => {
    state.tableData.queryParams.tempSlaveId = undefined;
    getTableData();
}
/** 修改按钮操作 */
const handleUpdate = (type: string, row: any | undefined) => {
    console.log(row, 'row');

    var modelId = ''
    if (!row) {
        modelId = ids.value
    } else {
        modelId = row.modelId
    }
    ManipulateDialogRef.value.openDialog(type, row, state.tableData.queryParams);
}
/** 删除按钮操作 */
const handleDelete = (row: { modelId: any; }) => {
    const modelIds = row.modelId;
    if (!state.tableData.queryParams.isModbus) {
        ElMessageBox.confirm('是否确认删除物模型编号为"' + modelIds + '"的数据项？', '系统提示', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
              delTemplate(modelIds).then(res => {
                    ElMessage.success('删除成功');
                    getTableData()
                });

            })
            .catch(() => { });
    } else {
        ElMessageBox.alert('采集点删除请在采集点模板修改');
    }
}

</script>
<style>
.specsColor {
    background-color: #fcfcfc;
}
</style>