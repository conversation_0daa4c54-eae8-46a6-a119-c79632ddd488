<template>
	<div class="system-menu-dialog-container">
		<el-dialog style="position: absolute; top: 100px; padding: 20px 30px" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" width="769px">
			<el-form ref="DialogFormRef" :model="state.ruleForm" size="default" label-width="80px" :rules="rules">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="上级菜单">
							<!-- <el-tree :data="state.menuData" :props="defaultProps" :expand-on-click-node="false">
								<template #default="{ node, data }">
									<span>{{ data.menuName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
</el-tree> -->
							<el-tree-select v-model="state.ruleForm.parentId" :props="defaultProps"
								:data="state.menuData" :render-after-expand="true" style="width: 240px"
								:show-count="true" @node-click="handleNodeClick">
								<template #default="{ node, data }">
									<span>{{ data.menuName }}</span>
									<span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
								</template>
							</el-tree-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="菜单类型" prop="menuType">
							<el-radio-group v-model="state.ruleForm.menuType">
								<el-radio value="M">目录</el-radio>
								<el-radio value="C">菜单</el-radio>
								<el-radio value="F">按钮</el-radio>
							</el-radio-group>
						</el-form-item>
					</el-col>
					<!-- 目录显示 -->
					<template v-if="state.ruleForm.menuType === 'M'">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
							<el-form-item label="菜单图标">
								<IconSelector v-model="state.ruleForm.icon" placeholder="请点击选择图标" />
							</el-form-item>
							<!-- <el-form-item label="菜单图标" prop="icon">
							<el-popover placement="bottom-start" width="460" trigger="click">
								<IconSelect ref="iconSelect" @selected="selected" />
								<el-input slot="reference" v-model="state.ruleForm.meta.icon" placeholder="点击选择图标"
									readonly>
									<svg-icon v-if="state.ruleForm.meta.icon" slot="prefix"
										:icon-class="state.ruleForm.meta.icon" class="el-input__icon"
										style="height: 32px;width: 16px;" />
									<i v-else slot="prefix" class="el-icon-search el-input__icon" />
								</el-input>
							</el-popover>
						</el-form-item> -->
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="菜单名称" prop="menuName">
								<el-input v-model="state.ruleForm.menuName" placeholder="格式：message.router.xxx"
									clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="显示排序" prop="orderNum">
								<el-input-number v-model="state.ruleForm.orderNum" controls-position="right"
									placeholder="请输入排序" class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="是否外链">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">是否外链</span>
									</div>
								</template>

								<el-radio-group v-model="state.ruleForm.isFrame">
									<el-radio :value="'0'">是</el-radio>
									<el-radio :value="'1'">否</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="路由地址" prop="path">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头"
											placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">路由地址</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.path" placeholder="路由中的 path 值" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="显示状态">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">显示状态</span>
									</div>
								</template>
								<el-radio-group v-model="state.ruleForm.visible">
									<el-radio :value="'0'">显示</el-radio>
									<el-radio :value="'1'">隐藏</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="菜单状态">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">菜单状态</span>
									</div>
								</template>
								<el-radio-group v-model="state.ruleForm.status" @change="onSelectIframeChange">
									<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue"
										:value="item.dictValue">{{
											item.dictLabel
										}}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
					</template>
					<!-- 菜单显示 -->
					<template v-if="state.ruleForm.menuType === 'C'">
						<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
							<el-form-item label="菜单图标">
								<IconSelector :modelValue="state.ruleForm.icon"
									@update:modelValue="handleModelValueUpdate" placeholder="请点击选择图标" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="菜单名称" prop="menuName">
								<el-input v-model="state.ruleForm.menuName" placeholder="格式：message.router.xxx"
									clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="显示排序" prop="orderNum">
								<el-input-number v-model="state.ruleForm.orderNum" controls-position="right"
									placeholder="请输入排序" class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="是否外链">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择是外链则路由地址需要以`http(s)://`开头" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">是否外链</span>
									</div>
								</template>

								<el-radio-group v-model="state.ruleForm.isFrame">
									<el-radio :value="'0'">是</el-radio>
									<el-radio :value="'1'">否</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="路由地址" prop="path">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头"
											placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">路由地址</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.path" placeholder="路由中的 path 值" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="组件路径">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="访问的组件路径，如：`system/user/index`，默认在`views`目录下"
											placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">组件路径</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.component" placeholder="组件路径" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="权限字符">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip
											content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
											placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">权限字符</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.perms" placeholder="请输入权限标识" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="路由参数">
								<template #label>
									<div style="display:flex; align-items: center">
										<el-tooltip content='访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`' placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">路由参数</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.name" placeholder="路由中的 name 值" clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="是否缓存">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">是否缓存</span>
									</div>
								</template>
								<el-radio-group v-model="state.ruleForm.isCache">
									<el-radio :value="'0'">缓存</el-radio>
									<el-radio :value="'1'">不缓存</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="显示状态">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择隐藏则路由将不会出现在侧边栏，但仍然可以访问" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">显示状态</span>
									</div>
								</template>
								<el-radio-group v-model="state.ruleForm.visible">
									<el-radio :value="'0'">显示</el-radio>
									<el-radio :value="'1'">隐藏</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="菜单状态">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip content="选择停用则路由将不会出现在侧边栏，也不能被访问" placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">菜单状态</span>
									</div>
								</template>
								<el-radio-group v-model="state.ruleForm.status" @change="onSelectIframeChange">
									<el-radio v-for="item in statuslist" :key="item.dictValue" :label="item.dictValue"
										:value="item.dictValue">{{
											item.dictLabel
										}}</el-radio>
								</el-radio-group>
							</el-form-item>
						</el-col>
					</template>
					<template v-if="state.ruleForm.menuType === 'F'">
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="菜单名称" prop="menuName">
								<el-input v-model="state.ruleForm.menuName" placeholder="格式：message.router.xxx"
									clearable></el-input>
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="显示排序" prop="orderNum">
								<el-input-number v-model="state.ruleForm.orderNum" controls-position="right"
									placeholder="请输入排序" class="w100" />
							</el-form-item>
						</el-col>
						<el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
							<el-form-item label="权限字符">
								<template #label>
									<div style="display:flex;align-items: center">
										<el-tooltip
											content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)"
											placement="top">
											<el-icon><ele-QuestionFilled /></el-icon>
										</el-tooltip>
										<span style="width: 56px; margin-left: 5px">权限字符</span>
									</div>
								</template>
								<el-input v-model="state.ruleForm.perms" placeholder="请输入权限标识" clearable></el-input>
							</el-form-item>
						</el-col>
					</template>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
						state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="systemMenuDialog">
import { defineAsyncComponent, reactive, onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useRoutesList } from '/@/stores/routesList';
import { handleTree } from '/@/utils/next';
import { addMenu, getMenu, listMenu, updateMenu } from '/@/api/system/menu';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store

const dictStore = useDictStore();  // 使用 Pinia store
// import { setBackEndControlRefreshRoutes } from "/@/router/backEnd";

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);
const defaultProps = reactive({
	children: "children",
	label: "menuName",
	value: 'menuId'
});
// 引入组件
const IconSelector = defineAsyncComponent(() => import('/@/components/iconSelector/index.vue'));

// 定义变量内容
const DialogFormRef = ref();
const stores = useRoutesList();
// 原始数据存储
const initialState = {
	// 参数请参考 `/src/router/route.ts` 中的 `dynamicRoutes` 路由菜单格式
	ruleForm: {
		// menuSuperior: 0, // 上级菜单
		menuType: 'M', // 菜单类型
		menuId: '' as any,
		name: '', // 路由名称
		component: '', // 组件路径
		componentAlias: '', // 组件路径别名
		orderNum: undefined, // 菜单排序
		path: '', // 路由路径
		redirect: '', // 路由重定向，有子集 children 时
		parentId: 0,	// 上级菜单
		menuName: '', // 菜单名称
		icon: '', // 菜单图标
		visible: '0', // 是否隐藏
		isCache: '0', // 是否缓存
		// isAffix: false, // 是否固定
		isLink: '', // 外链/内嵌时链接地址（http:xxx.com），开启外链条件，`1、isLink: 链接地址不为空`
		isFrame: '1', // 是否内嵌，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
		roles: '', // 权限标识，取角色管理
		status: '0',//菜单状态
		perms: '', // 菜单类型为按钮时，权限标识
	},
	menuData: [] as RouteItems, // 上级菜单数据
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
}
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	menuData: [...initialState.menuData],
	dialog: { ...initialState.dialog },
});
const handleModelValueUpdate = (value: string) => {
	console.log(value, 'value');
	state.ruleForm.icon = value;

};

// const state = reactive({
// 	// 参数请参考 `/src/router/route.ts` 中的 `dynamicRoutes` 路由菜单格式
// 	ruleForm: {
// 		// menuSuperior: 0, // 上级菜单
// 		menuType: 'M', // 菜单类型
// 		menuId: undefined as string | undefined,
// 		name: '', // 路由名称
// 		component: '', // 组件路径
// 		componentAlias: '', // 组件路径别名
// 		orderNum: 0, // 菜单排序
// 		path: '', // 路由路径
// 		redirect: '', // 路由重定向，有子集 children 时
// 		parentId: 0,	// 上级菜单
// 		menuName: '', // 菜单名称
// 		icon: '', // 菜单图标
// 		visible: '0', // 是否隐藏
// 		isCache: '0', // 是否缓存
// 		// isAffix: false, // 是否固定
// 		isLink: '', // 外链/内嵌时链接地址（http:xxx.com），开启外链条件，`1、isLink: 链接地址不为空`
// 		isFrame: '0', // 是否内嵌，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
// 		roles: '', // 权限标识，取角色管理
// 		status: '0',//菜单状态
// 		// meta: {
// 		// 	title: '', // 菜单名称
// 		// 	icon: '', // 菜单图标
// 		// 	isHide: true, // 是否隐藏
// 		// 	isKeepAlive: true, // 是否缓存
// 		// 	isAffix: false, // 是否固定
// 		// 	isLink: '', // 外链/内嵌时链接地址（http:xxx.com），开启外链条件，`1、isLink: 链接地址不为空`
// 		// 	isIframe: false, // 是否内嵌，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
// 		// 	roles: '', // 权限标识，取角色管理
// 		// 	status: '0'//菜单状态
// 		// },
// 		perms: '', // 菜单类型为按钮时，权限标识
// 	},
// 	menuData: [] as RouteItems, // 上级菜单数据
// 	dialog: {
// 		isShowDialog: false,
// 		type: '',
// 		title: '',
// 		submitTxt: '',
// 	},
// });
// 校验规则
const rules = reactive({
	menuName: [
		{ required: true, message: "菜单名称不能为空", trigger: "blur" }
	],
	orderNum: [
		{ required: true, message: "菜单顺序不能为空", trigger: "blur" }
	],
	path: [
		{ required: true, message: "路由地址不能为空", trigger: "blur" }
	]
})
interface statusOption {
	dictValue: string;
	dictLabel: string;
}
const statuslist = ref<statusOption[]>([]); //状态
// 搜索内容
let queryParams = reactive({
	menuName: undefined,
	status: ''
});
// 获取 pinia 中的路由
const getMenuData = () => {

	listMenu(queryParams).then(response => {
		state.menuData = [];
		const menu = [{ menuId: 0, menuName: '主类目', children: [] as any }];
		menu[0].children = handleTree(response.data.data, "menuId");
		state.menuData = menu;
	});

	// const arr: RouteItems = [];
	// routes.map((val: RouteItem) => {
	// 	val['title'] = val.meta?.title as string;
	// 	arr.push({ ...val });
	// 	if (val.children) getMenuData(val.children);
	// });
	// return arr;
};
// 点击节点时更新父节点
const handleNodeClick = (node: any, data: any) => {
	// 判断是否是父节点
	// if (!node.isLeaf) {
	// 如果是父节点，更新模型值
	state.ruleForm.parentId = data.data.menuId;
	// }
};
// 获取状态数据
const getdictdata = async () => {
	try {
		statuslist.value = await dictStore.fetchDict('sys_normal_disable')
		// 处理字典数据
	} catch (error) {
		console.error('获取字典数据失败:', error);
	}
};
// 打开弹窗
const openDialog = (type: string, row?: any) => {
	if (type === 'edit') {
		// 模拟数据，实际请走接口
		// row.menuType = 'menu';
		// row.menuSort = Math.floor(Math.random() * 100);
		getMenu(row.menuId).then(response => {

			console.log(response,row ,'response');

			// state.ruleForm = response.data.data;
			// this.open = true;
			// this.title = "修改菜单";
			state.ruleForm = JSON.parse(JSON.stringify(response.data.data));
		});
		
		state.dialog.title = '修改菜单';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();

		if (row) {
			state.ruleForm.parentId = row.menuId
		}
		state.dialog.title = '新增菜单';
		state.dialog.submitTxt = '新 增';
		// 清空表单，此项需加表单验证才能使用
		// nextTick(() => {
		// 	DialogFormRef.value.resetFields();
		// });
	}
	state.dialog.type = type;
	state.dialog.isShowDialog = true;
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 是否内嵌下拉改变
const onSelectIframeChange = () => {
	// if (state.ruleForm.isFrame) state.ruleForm.isLink = true;
	// else state.ruleForm.isLink = false;
};
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm }
	// state.menuData = [...initialState.menuData]
	state.dialog = { ...initialState.dialog }
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			// 提交数据
			if (state.ruleForm.menuId != '') {
				updateMenu(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {
				addMenu(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
	// if (state.dialog.type === 'add') { }
	// setBackEndControlRefreshRoutes() // 刷新菜单，未进行后端接口测试
};
// 页面加载时
onMounted(() => {
	getMenuData();
	getdictdata()
	// state.ruleForm.icon = ''; // 默认图标
});

// 暴露变量
defineExpose({
	openDialog,
});
</script>
