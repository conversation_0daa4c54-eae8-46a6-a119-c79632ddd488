<template>
  <div
    ref="container"
    @dblclick="fullscreenSwich"
    style="width:100%;height:100%;background-color: #000000;margin:0 auto;"
  ></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRoute } from 'vue-router'

// Props
const props = defineProps({
  videoUrl: String,
  error: Function,
  hasAudio: Boolean,
  height: [String, Number]
})

// Data
const playing = ref(false)
const isNotMute = ref(false)
const fullscreen = ref(false)
const loaded = ref(false)
const performance = ref('')
const kBps = ref(0)
const volume = ref(1)
const rotate = ref(0)
const vod = ref(true)
const forceNoOffscreen = ref(false)

// Refs
const container = ref(null)
const jessibucaInstance = ref(null)

const route = useRoute()

// Methods

function updatePlayerDomSize() {
  const dom = container.value
  if (!dom) return

  let width = dom.parentNode.clientWidth
  let height = (9 / 16) * width
  const clientHeight = Math.min(document.body.clientHeight, document.documentElement.clientHeight)

  if (height > clientHeight) {
    height = clientHeight
    width = (16 / 9) * height
  }

  dom.style.width = width + 'px'
  dom.style.height = height + 'px'
}

function createJessibuca(url) {
  const options = {
    container: container.value,
    autoWasm: true,
    background: '',
    controlAutoHide: false,
    debug: false,
    debugLevel: 'debug',
    decoder: '/js/jessibuca-pro/decoder-pro.js',
    forceNoOffscreen: true,
    hasAudio: props.hasAudio ?? true,
    hasVideo: true,
    heartTimeout: 5,
    heartTimeoutReplay: true,
    heartTimeoutReplayTimes: 3,
    hiddenAutoPause: false,
    hotKey: false,
    isFlv: false,
    isFullResize: false,
    isNotMute: isNotMute.value,
    isResize: false,
    keepScreenOn: false,
    loadingText: '请稍等, 视频加载中......',
    loadingTimeout: 10,
    loadingTimeoutReplay: true,
    loadingTimeoutReplayTimes: 3,
    openWebglAlignment: false,
    operateBtns: {
      fullscreen: true,
      zoom: true,
      ptz: false,
      play: true
    },
    recordType: 'webm',
    rotate: 0,
    showBandwidth: false,
    supportDblclickFullscreen: false,
    timeout: 10,
    useMSE: location.hostname !== 'localhost' && location.protocol !== 'https:',
    useOffscreen: false,
    useWCS: location.hostname === 'localhost' || location.protocol === 'https',
    useWebFullScreen: false,
    videoBuffer: 0,
    wasmDecodeAudioSyncVideo: true,
    wasmDecodeErrorReplay: true,
    wcsUseVideoRender: true
  }

  // 创建播放器
  jessibucaInstance.value = new window.JessibucaPro(options)

  const jessibuca = jessibucaInstance.value

  // 绑定事件监听
  jessibuca.on('load', () => {
    console.log('on load init')
  })

  jessibuca.on('log', msg => console.log('on log', msg))
  jessibuca.on('record', msg => console.log('on record:', msg))
  jessibuca.on('pause', () => {
    playing.value = false
    loaded.value = true
  })
  jessibuca.on('play', () => {
    playing.value = true
    loaded.value = true
  })
  jessibuca.on('fullscreen', msg => {
    fullscreen.value = msg
  })
  jessibuca.on('mute', msg => {
    isNotMute.value = !msg
  })
  jessibuca.on('audioInfo', msg => console.log('audioInfo', msg))

  let _ts = 0
  jessibuca.on('timeUpdate', ts => {
    _ts = ts
  })

  jessibuca.on('performance', perf => {
    let show = '卡顿'
    if (perf === 2) show = '非常流畅'
    else if (perf === 1) show = '流畅'
    performance.value = show
  })

  jessibuca.on('kBps', kbps => {
    kBps.value = Math.round(kbps)
  })

  // 开始播放
  jessibuca.play(url)
}

async function destroyJessibuca() {
  if (jessibucaInstance.value) {
    await jessibucaInstance.value.destroy()
    jessibucaInstance.value = null
    playing.value = false
    loaded.value = false
    performance.value = ''
  }
}

function play(url) {
  if (!url) return

  if (jessibucaInstance.value) {
    destroyJessibuca().then(() => {
      createJessibuca(url)
    })
  } else {
    createJessibuca(url)
  }
}

function pause() {
  if (jessibucaInstance.value) {
    jessibucaInstance.value.pause()
  }
  playing.value = false
}

function screenshot() {
  if (jessibucaInstance.value) {
    jessibucaInstance.value.screenshot()
  }
}

function mute() {
  if (jessibucaInstance.value) {
    jessibucaInstance.value.mute()
  }
}

function cancelMute() {
  if (jessibucaInstance.value) {
    jessibucaInstance.value.cancelMute()
  }
}

function fullscreenSwich() {
  const isFull = isFullscreen()
  if (jessibucaInstance.value) {
    jessibucaInstance.value.setFullscreen(!isFull)
    fullscreen.value = !isFull
  }
}

function isFullscreen() {
  return (
    document.fullscreenElement ||
    document.msFullscreenElement ||
    document.mozFullScreenElement ||
    document.webkitFullscreenElement ||
    false
  )
}

// Watchers
watch(
  () => props.videoUrl,
  url => {
    play(url)
  },
  { immediate: true }
)

// Lifecycle hooks
onMounted(() => {
  window.onerror = msg => {
    console.error(msg)
  }

  window.addEventListener('resize', updatePlayerDomSize)

  const paramUrl = decodeURIComponent(route.params.url || '')
  const finalUrl = props.videoUrl || paramUrl

  updatePlayerDomSize()
  play(finalUrl)
})

onUnmounted(() => {
  window.removeEventListener('resize', updatePlayerDomSize)
  destroyJessibuca()
})
</script>

<style scoped>

.buttons-box {
  width: 100%;
  height: 28px;
  background-color: rgba(43, 51, 63, 0.7);
  position: absolute;
  display: flex;
  left: 0;
  bottom: 0;
  user-select: none;
  z-index: 10;
}

.jessibuca-btn {
  width: 20px;
  color: rgb(255, 255, 255);
  line-height: 27px;
  margin: 0px 10px;
  padding: 0px 2px;
  cursor: pointer;
  text-align: center;
  font-size: 0.8rem !important;
}

.buttons-box-right {
  position: absolute;
  right: 0;
}
</style>