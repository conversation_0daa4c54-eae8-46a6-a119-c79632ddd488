<template>
	<div class="system-dic-dialog-container">
		<el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
			v-model="state.dialog.isShowDialog" width="500px">
			<el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
				size="default" label-width="140px">
				<el-row :gutter="35">
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item label="第三方平台名称" prop="platform">
							<el-select v-model="state.ruleForm.platform" placeholder="请选择第三方平台" clearable>
								<el-option v-for="plat in platformlist" :key="plat.dictValue" :label="plat.dictLabel"
									:value="plat.dictValue" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="20" :md="20" :lg="20" :xl="20" class="mb20">
						<el-form-item label="第三方平台状态" prop="status">
							<el-select v-model="state.ruleForm.status" placeholder="第三方平台状态" clearable>
								<el-option v-for="plat in platformstatuslist" :key="plat.dictValue"
									:label="plat.dictLabel" :value="plat.dictValue" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="第三方平台申请ID" prop="clientId">
							<el-input v-model="state.ruleForm.clientId" placeholder="请输入第三方平台申请Id" />
						</el-form-item>
					</el-col>

					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="第三方平台密钥" prop="secretKey">
							<el-input v-model="state.ruleForm.secretKey" placeholder="请输入第三方平台密钥" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="用户认证跳转地址" prop="redirectUri">
							<el-input v-model="state.ruleForm.redirectUri" placeholder="请输入用户认证后跳转地址" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="绑定注册登录URI" prop="bindUri">
							<el-input v-model="state.ruleForm.bindUri" placeholder="请输入绑定注册登录uri" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="跳转登录URI" prop="redirectLoginUri">
							<el-input v-model="state.ruleForm.redirectLoginUri" placeholder="请输入跳转登录uri" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="错误提示URI" prop="errorMsgUri">
							<el-input v-model="state.ruleForm.errorMsgUri" placeholder="请输入错误提示uri" />
						</el-form-item>
					</el-col>
					<el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
						<el-form-item label="备注">
							<el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容"
								maxlength="150"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<span class="dialog-footer">
					<el-button @click="onCancel" size="default">取 消</el-button>
					<el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
						state.dialog.submitTxt }}</el-button>
				</span>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addPlatform, getPlatform, updatePlatform } from '/@/api/iot/platform';

const dictStore = useDictStore();  // 使用 Pinia store

// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
	ruleForm: {
		platform: '', 
		secretKey: '', 
		redirectUri: '',
		bindUri: '', 
		redirectLoginUri:'',
		errorMsgUri:'',
		remark: '', 
		clientId: '',
		status: '',
		socialPlatformId:''
	},
	dialog: {
		isShowDialog: false,
		type: '',
		title: '',
		submitTxt: '',
	},
}
// 初始化 state
const state = reactive({
	ruleForm: { ...initialState.ruleForm },
	// deptData: [...initialState.deptData],
	dialog: { ...initialState.dialog },
});
interface platTypeOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}
interface platstatusTypeOption {
	dictValue: string;
	dictLabel: string;
	listClass: string;
	cssClass: string;
}
const platformlist = ref<platTypeOption[]>([]); //平台列表
const platformstatuslist = ref<platstatusTypeOption[]>([]); //平台状态列表
// 校验规则
const rules = reactive({
	platform: [{
		required: true,
		message: "第三方平台不能为空",
		trigger: "change"
	}],
	status: [{
		required: true,
		message: " 0:启用 ,1:禁用不能为空",
		trigger: "change"
	}],
	clientId: [{
		required: true,
		message: "第三方平台申请Id不能为空",
		trigger: "blur"
	}],
	secretKey: [{
		required: true,
		message: "第三方平台密钥不能为空",
		trigger: "blur"
	}],
	redirectUri: [{
		required: true,
		message: "用户认证后跳转地址不能为空",
		trigger: "blur"
	}],
	bindUri: [{
		required: true,
		message: "绑定注册登录uri,http://localhost/login?bindId=不能为空",
		trigger: "blur"
	}],
	redirectLoginUri: [{
		required: true,
		message: "跳转登录uri,http://localhost/login?loginId=不能为空",
		trigger: "blur"
	}],
	errorMsgUri: [{
		required: true,
		message: "错误提示uri,http://localhost/login?errorId=不能为空",
		trigger: "blur"
	}]

})
// 打开弹窗
const openDialog = (type: string, row: RowPlatType, socialPlatformId: string) => {
	if (type === 'edit') {
		resetState();
		if (row != undefined) {
			getPlatform(row.socialPlatformId).then(response => {
				state.ruleForm = response.data.data
			});
		} else {
			getPlatform(socialPlatformId).then(response => {
				state.ruleForm = response.data.data
			});
		}
		state.dialog.title = '修改参数';
		state.dialog.submitTxt = '修 改';
	} else {
		resetState();
		state.dialog.title = '添加第三方登录平台控制';
		state.dialog.submitTxt = '新 增';
	}
	state.dialog.isShowDialog = true;
	getdictdata()
};
// 清空弹框
const resetState = () => {
	state.ruleForm = { ...initialState.ruleForm }
	state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
	state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
	closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
		if (valid) {
			if (state.ruleForm.socialPlatformId != '') {
				updatePlatform(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('修改成功');
				});
			} else {

				addPlatform(state.ruleForm).then(response => {
					//  刷新页面
					emit('refresh');
					closeDialog();
					ElMessage.success('新增成功');
				});
			}

		} else {
			console.log('error submit!', fields)
		}
	})
};
// 获取状态数据
const getdictdata = async () => {
	try {
		platformlist.value = await dictStore.fetchDict('iot_social_platform')
		platformstatuslist.value = await dictStore.fetchDict('iot_social_platform_status')
		// 处理参数数据
	} catch (error) {
		console.error('获取参数数据失败:', error);
	}
};

// 暴露变量
defineExpose({
	openDialog,
});
</script>
