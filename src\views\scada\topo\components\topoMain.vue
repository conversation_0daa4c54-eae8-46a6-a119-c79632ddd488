<template>
  <div class="topo-main-wrap">
    <div class="content-wrap" ref="rulerToolBox">
      <vue-ruler-tool :parent="true" :is-scale-revise="true" ref="rulerTool" :is-hot-key="false">
        <div tabindex="0" id="surface-edit-layer" class="topo-layer" :class="{ 'topo-layer-view-selected': selectedIsLayer }" :style="layerStyle"
            @click="onLayerClick"
            @mouseup="onLayerMouseup($event)"
            @mousemove="onLayerMousemove($event)"
            @mousedown="onLayerMousedown($event)"
            @keyup.delete="removeItem"
            @dragover.prevent
            @drop="onDrop"
            @contextmenu.prevent="onContextmenu"
            @keydown.ctrl.c.stop="copyItem"
            @keydown.ctrl.v.stop="pasteItem"
            @keydown.ctrl.z.stop="revoke"
            @keydown.ctrl.y.stop="recovery"
        >
          <template v-for="(component, index) in configData.components" :key="index">
            <div :tabindex="0" class="topo-layer-view" :class="{ 'topo-layer-view-selected': selectedComponentMap[component.identifier] !== undefined }"
                v-show="component.style.visible ?? true"
                @click.stop="clickComponent(index, component, $event)"
                @mousedown.stop="controlMousedown(component, $event, index)"
                @keyup.delete="removeItem"
                @keydown.up.exact.prevent="moveItems('up')"
                @keydown.right.exact.prevent="moveItems('right')"
                @keydown.down.exact.prevent="moveItems('down')"
                @keydown.left.exact.prevent="moveItems('left')"
                @keydown.ctrl.c.stop="copyItem"
                @keydown.ctrl.v.stop="pasteItem"
                @keydown.ctrl.z.stop="revoke"
                @keydown.ctrl.y.stop="recovery"
                :style="{
                left: component.style.position.x + 'px',
                top: component.style.position.y + 'px',
                width: component.style.position.w + 'px',
                height: component.style.position.h + 'px',
                backgroundColor: component.type == 'flow-bar' || component.type == 'flow-bar-dynamic' ? 'transparent' : component.style.backColor,
                zIndex: component.style.zIndex,
                transform: component.style.transformType,
                opacity: component.style.opacity,
                borderRadius: component.style.borderRadius + 'px',
                boxShadow: '0 0 ' + component.style.boxShadowWidth + 'px 0 ' + component.style.boxShadowColor
              }" >
              <component :is="parseView(component)"
                         :detail="component"
                         :editMode="true"
                         :selected="!!selectedComponentMap[component.identifier]"
                         :ref="(el: MyComponentInstance) => setRef(el, index)"/>
              <div class="resize-left-top"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lt')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-left-center"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lc')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-left-bottom"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-lb')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-right-top"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rt')"
                  v-show="selectedComponentMap[component.identifier]"
              ></div>
              <div class="resize-right-center"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rc')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-right-bottom"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-rb')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-center-top"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-ct')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div class="resize-center-bottom"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-cb')"
                  v-show="selectedComponentMap[component.identifier]" ></div>
              <div v-for="handle in ['lt','lc','lb','rt','rc','rb','ct','cb']" :key="handle"
                  class="resize-handle resize-handle-" :class="handle"
                  @mousedown.stop="resizeMousedown(component, $event, index, 'resize-' + handle)"
                  v-show="selectedComponentMap[component.identifier]" ></div>
            </div>
          </template>
              <div class="topo-frame-selection"
                   :style="{ width: frameSelectionDiv.width + 'px', height: frameSelectionDiv.height + 'px', top: frameSelectionDiv.top + 'px', left: frameSelectionDiv.left + 'px' }">
              </div>
        </div>
      </vue-ruler-tool>
    </div>

    <div class="footer-wrap">
      <div class="left-wrap">
        <div>
          <el-tabs style="height: 42px" type="card" @tab-click="handleBottomTabClick">
            <el-tab-pane v-for="(page, index) in ztList" :key="page.id" :label="page.pageName" :name="page.guid">
              <template #label>
                <div>
                  <el-dropdown @command="handleBottomTabCommand">
                  <span :class="guid === page.guid ? 'dropdown-item-active' : 'dropdown-item'">
                    <el-icon><Calendar /></el-icon> {{ page.pageName }}
                  </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <div v-auths="['scada:center:edit']">
                          <el-dropdown-item :command="{ type: 'rename', index, page }">重命名</el-dropdown-item>
                        </div>
                        <div v-auths="['scada:center:remove']">
                          <el-dropdown-item :command="{ type: 'delete', index, page }">删除</el-dropdown-item>
                        </div>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="right-wrap">
        <el-button link @click="handleBottomTabAdd">
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>
    </div>
    <!-- 导入弹窗 -->
    <el-dialog
        v-model="uploadImport.open"
        :title="uploadImport.title"
        width="400px"
        append-to-body
        v-loading="importLoading"
        element-loading-text="拼命导入中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-upload
          ref="uploadImportRef"
          :limit="1"
          accept=".json"
          :headers="uploadImport.headers"
          :action="uploadImport.url"
          :disabled="uploadImport.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            提示：仅允许导入“Json”格式文件！
          </div>
          <div class="el-upload__tip" style="color: red">
            提示：导入界面后需重新绑定设备参数！
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确定</el-button>
          <el-button @click="uploadImport.open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 重命名弹窗 -->
    <el-dialog title="重命名" v-model="isRenameDialog" width="20%">
      <el-input v-model="renameValue" placeholder="请输入内容" clearable />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isRenameDialog = false">取消</el-button>
          <el-button type="primary" @click="handleTabRename">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {computed, nextTick, onBeforeUnmount, onMounted, reactive, ref, getCurrentInstance} from 'vue';
import {useRoute, useRouter} from 'vue-router';
import VueRulerTool from './ruler.vue';
import FileSaver from 'file-saver';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import {addCenter, delCenter, listCenter, updateCenter} from '/@/api/scada/center';
import { getByGuid, saveDetailData } from '/@/api/scada/topo';
import html2canvas from 'html2canvas';
import { useCounterStore } from '/@/stores/counterStore';
const counterStore = useCounterStore();
import { checkByRectCollisionDetection } from '/@/utils/topo/index';
import { deepCopy } from '/@/utils/index';
// import emitter from "/@/utils/mitt";
import {checkPermi} from "/@/utils/permission";
import uid from '/@/utils/uid';
// 示例数据
// const activeTab = ref('')
const route = useRoute();
const router = useRouter();
// 定义子组件向父组件传值/事件
// const props = defineProps([]);
const emit = defineEmits(['menuClick','recoveryFlagClick', 'revokeFlagClick', 'lockStatus']);
const baseApi = ref(import.meta.env.VITE_APP_BASE_API)
const moveItem = reactive({
    startX: 0,
    startY: 0,
})// 移动组件 相关变量
// resize组件 相关变量
const resizeItem = ref<{
  startPx: number;
  startPy: number;
  x: number;
  y: number;
  w: number;
  h: number;
}>({
  startPx: 0,
  startPy: 0,
  x: 0,
  y: 0,
  w: 0,
  h: 0,
});
// 添加图片数据
function addImageData(url:any) {
  const data = getJsonItem(url)
  configData.value.components.push(data)
  // 保存历史记录
  saveHistory()
}
// 随机数生成函数（保留原逻辑）
function randomNumBoth(min:number, max:number) {
  const range = max - min
  const rand = Math.random()
  const num = min + Math.round(rand * range)
  return num
}
function getJsonItem(url:any) {
  let newJson = {
    type: 'image',
    componentShow: ['动画', '单击', '组件颜色', '滤镜渲染', '组件填充', '参数绑定'],
    action: [],
    hdClassName: '',
    identifier: uid(),
    dataBind: {
      queryParam: {},
      imei: '',
      paramField: '',
      paramName: '',
      driveName: '',
      action: '',
      paramValue: '',
      redirectUrl: '',
      stateList: [],
    },
    dataAction: {
      imei: '',
      paramField: '',
      paramName: '',
      paramJudge: '',
      paramJudgeData: '',
      rotationSpeed: '中',
      translateList: [],
    },
    style: {
      position: {
        x: 200 + randomNumBoth(0, 30),
        y: 200 + randomNumBoth(0, 30),
        w: 100,
        h: 100,
      },
      backColor: 'transparent',
      foreColor: '',
      zIndex: 1,
      transform: 0,
      url: url,
      transformType: 'rotate(0deg)',
      isFilter: true,
    },
  };
  return newJson;
}
const loadingInstance = ref<any | null>(null);
const rulerTool = ref()
const deviceZtRow = ref({
    scadaData: [] as any
})
const id = ref() // 组态id
const guid = ref() // 组态标识
const zoom = ref(100) // 缩放大小
const configDataHis = ref([]) // 操作中的组态数据temp
const selectFlag = ref(0) // 选中组件标识
const importLoading = ref(false) //加载
const isMultiple = ref(false)//撤销恢复标志位
const operateFlag = ref(0) //撤销恢复标志位
const selectComponent = ref(null) // 选中组件
const flag = ref('') // 当前操作标志位
const curControl = ref() // 当前操作组件
const curIndex = ref(-1)
interface MyComponentInstance {
  onResize: () => void;
}
const compRefs = ref<(MyComponentInstance | null)[]>([]);



const componentRefs = ref<Record<number, HTMLElement>>({})

const setRef = (el: any, index: number) => {
  if (el) {
    componentRefs.value[index] = el
  }
}
const selectIndx = ref(null)
const newestConfigData = ref(null)// 操作中的组态数据temp

// 保存历史记录的函数
const saveHistory = () => {
    // 深拷贝当前配置数据
    const currentData = deepCopy(configData.value);


    // 如果当前不是在最新状态，需要清除后面的历史记录
    if (selectFlag.value < configDataHis.value.length) {
        configDataHis.value = configDataHis.value.slice(0, selectFlag.value);
    }
    // 添加新的历史记录
    configDataHis.value.push(currentData);
    // 限制历史记录数量，避免内存过大
    const maxHistorySize = 50;
    if (configDataHis.value.length > maxHistorySize) {
        configDataHis.value.shift(); // 移除最早的记录
        selectFlag.value = configDataHis.value.length; // 保持在最新位置
    } else {
        selectFlag.value = configDataHis.value.length; // 移动到最新位置
    }
    // 清除最新数据缓存
    newestConfigData.value = null;
    // 更新按钮状态 (false表示按钮可用，true表示按钮禁用)
    emit('revokeFlagClick', configDataHis.value.length <= 0); // 没有历史记录时禁用撤销按钮
    emit('recoveryFlagClick', true); // 保存历史记录后禁用恢复按钮
}
const uploadImport = reactive({
    open: false, // 是否显示弹出层（用户导入）
    title: '', // 弹出层标题（用户导入）
    isUploading: false, // 是否禁用上传
    // updateSupport: 0, // 是否更新已经存在的用户数据
  headers: {
    Authorization: "Bearer " + Session.get('token')
  },
    url: '', // 上传的地址
})
const uploadImportRef = ref()
const frameSelectionDiv = reactive({
    width: 0,
    height: 0,
    top: 10,
    left: 10,
    startX: 0,
    startY: 0,
    startPageX: 0,
    startPageY: 0,
})
//初始化卡尺
// const initRuler = () => {
//     nextTick(() => {
//         rulerTool.value.init();
//     });
// }
//调整视图大小
const handleZoom = (value: any) => {
    zoom.value = value;
}
//导入组态json
const handleImport = () => {
    uploadImport.title = '组态导入';
    uploadImport.open = true;
    uploadImport.url = baseApi.value + '/scada/center/importJson?guid=' + guid.value;
}

// 文件上传中处理
const handleFileUploadProgress = () => {
    uploadImport.isUploading = true;
}
// 文件上传成功处理
const handleFileSuccess = (response:any) => {
    uploadImport.open = false;
    uploadImport.isUploading = false;

    // 安全地清除文件列表
    if (uploadImportRef.value && uploadImportRef.value.clearFiles) {
        try {
            uploadImportRef.value.clearFiles();
        } catch (error) {
          // eslint-disable-next-line no-console
            console.warn('清除文件列表失败:', error);
        }
    }

    ElMessageBox.alert(response.msg, '导入结果', {
        dangerouslyUseHTMLString: true,
    });
    importLoading.value = false;
    getZtDetails();
}
// 提交上传文件
const submitFileForm = () => {
    if (!uploadImportRef.value) {
        ElMessage.error('上传组件未初始化，请重新打开导入对话框');
        return;
    }

    try {
        uploadImportRef.value.submit();
        importLoading.value = true;
    } catch (error) {
      // eslint-disable-next-line no-console
        console.error('文件上传失败:', error);
        ElMessage.error('文件上传失败，请重试');
        importLoading.value = false;
    }
}
//导出组态json
const handleDownLoad = () => {
    const data = JSON.stringify(deviceZtRow.value);
    const blob = new Blob([data], { type: '' });
    FileSaver.saveAs(blob, document.title + '.json');
}
//撤销
const revoke = () => {
    // 检查是否有历史记录可以撤销
    if (configDataHis.value.length === 0) {
        return;
    }
    if (selectFlag.value <= 0) {
        return;
    }
    // 如果当前是最新状态，先保存当前数据
    if (selectFlag.value === configDataHis.value.length) {
        newestConfigData.value = deepCopy(configData.value);
    }
    // 向前移动一步
    selectFlag.value = selectFlag.value - 1;
    // 加载对应的历史数据
    if (selectFlag.value >= 0 && selectFlag.value < configDataHis.value.length) {
        const historyData = configDataHis.value[selectFlag.value];
        // 清理颜色值并使用 loadDefaultTopoData 方法更新数据
        const cleanedData = deepCopy(historyData);
        cleanColorValues(cleanedData);
        counterStore.loadDefaultTopoData(cleanedData);

        // 使用 nextTick 确保更新完成
        nextTick(() => {
            // 清除当前选中的组件
            counterStore.clearSelectedComponent();
        });
    } else {
      // eslint-disable-next-line no-console
        console.log('撤销失败: selectFlag 超出范围');
    }

    // 更新按钮状态 (false表示按钮可用，true表示按钮禁用)
    emit('revokeFlagClick', selectFlag.value <= 0); // 没有历史记录时禁用撤销按钮
    emit('recoveryFlagClick', false); // 撤销后总是可以恢复
}

//恢复
const recovery = () => {
    // 检查是否有可恢复的操作
    if (selectFlag.value >= configDataHis.value.length && !newestConfigData.value) {
      emit('recoveryFlagClick', true);
        return;
    }
    // 向后移动一步
    selectFlag.value = selectFlag.value + 1;
    // 加载对应的数据
    if (selectFlag.value >= configDataHis.value.length) {
        // 恢复到最新状态
        if (newestConfigData.value) {
            // 清理颜色值并使用 loadDefaultTopoData 方法更新数据
            const cleanedData = deepCopy(newestConfigData.value);
            cleanColorValues(cleanedData);
            counterStore.loadDefaultTopoData(cleanedData);
            // 使用 nextTick 确保更新完成
            nextTick(() => {
                // 清除当前选中的组件
                counterStore.clearSelectedComponent();
              emit('recoveryFlagClick', true);
            });
        }
    } else {
        // 恢复到指定历史状态
        const historyData = configDataHis.value[selectFlag.value];
        // 清理颜色值并使用 loadDefaultTopoData 方法更新数据
        const cleanedData = deepCopy(historyData);
        cleanColorValues(cleanedData);
        counterStore.loadDefaultTopoData(cleanedData);
        // 使用 nextTick 确保更新完成
        nextTick(() => {
            // 清除当前选中的组件
            counterStore.clearSelectedComponent();
        });
    }
    // 更新按钮状态 (false表示按钮可用，true表示按钮禁用)
    emit('revokeFlagClick', false); // 恢复后总是可以撤销
    emit('recoveryFlagClick', selectFlag.value >= configDataHis.value.length && !newestConfigData.value); // 没有可恢复的操作时禁用恢复按钮
}
const { proxy } = getCurrentInstance() as any
const onContextmenu = (event: any) => {
    if (selectedComponentMap && selectedComponentMap[selectedComponents].type == 'VR') {
        return;
    }
    let isDisabled = false;
    if (selectedComponents.length > 0) {
        isDisabled = false;
    } else {
        isDisabled = true;
    }
  proxy.$contextmenu({
        items: [
            {
                label: '取消',
                icon: 'el-icon-circle-close',
                onClick: () => { },
            },
            {
                label: '复制',
                icon: 'el-icon-document-copy',
                disabled: isDisabled,
                onClick: () => {
                    copyItem();
                    pasteItem();
                },
            },
            {
                label: '删除',
                icon: 'el-icon-delete',
                disabled: isDisabled,
                onClick: () => {
                    removeItem();
                },
            },
            {
                label: '置顶',
                icon: 'el-icon-top',
                disabled: isDisabled,
                onClick: () => {
                    emit('menuClick', '置顶');
                },
            },
            {
                label: '置底',
                icon: 'el-icon-bottom',
                disabled: isDisabled,
                onClick: () => {
                    emit('menuClick', '置底');
                },
            },
            {
                label: '旋转',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '顺时针90°',
                        icon: 'el-icon-plus',
                        onClick: () => {
                            // console.log("顺时针90°");
                            emit('menuClick', '顺时针90°');
                        },
                    },
                    {
                        label: '逆时针90°',
                        icon: 'el-icon-minus',
                        onClick: () => {
                            // console.log("逆时针90°");
                            emit('menuClick', '逆时针90°');
                        },
                    },
                    {
                        label: '水平镜像',
                        icon: 'el-icon-d-arrow-left',
                        onClick: () => {
                            // console.log("水平镜像");
                            emit('menuClick', '水平镜像');
                        },
                    },
                    {
                        label: '垂直镜像',
                        icon: 'el-icon-d-arrow-right',
                        onClick: () => {
                            // console.log("垂直镜像");
                            emit('menuClick', '垂直镜像');
                        },
                    },
                    {
                        label: '自定义角度',
                        icon: 'el-icon-edit',
                        onClick: () => {
                            // console.log("自定义角度");
                            emit('menuClick', '自定义角度');
                        },
                    },
                ],
            },
            {
                label: '对齐',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '左对齐',
                        icon: 'el-icon-arrow-left',
                        onClick: () => {
                            alignClick('左对齐');
                        },
                    },
                    {
                        label: '右对齐',
                        icon: 'el-icon-arrow-right',
                        onClick: () => {
                            alignClick('右对齐');
                        },
                    },
                    {
                        label: '上对齐',
                        icon: 'el-icon-arrow-up',
                        onClick: () => {
                            alignClick('上对齐');
                        },
                    },
                    {
                        label: '下对齐',
                        icon: 'el-icon-arrow-down',
                        onClick: () => {
                            alignClick('下对齐');
                        },
                    },
                    {
                        label: '水平等间距',
                        icon: 'el-icon-sort',
                        onClick: () => {
                            alignClick('水平等间距');
                        },
                    },
                    {
                        label: '垂直等间距',
                        icon: 'el-icon-sort',
                        onClick: () => {
                            alignClick('垂直等间距');
                        },
                    },
                ],
            },
            {
                label: '组合',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '组合',
                        icon: 'el-icon-connection',
                        onClick: () => {
                            makeUpClick('组合');
                        },
                    },
                    {
                        label: '取消组合',
                        icon: 'el-icon-link',
                        onClick: () => {
                            makeUpClick('取消组合');
                        },
                    },
                ],
            },
            {
                label: '锁定',
                minWidth: 0,
                disabled: isDisabled,
                children: [
                    {
                        label: '锁定',
                        icon: 'el-icon-lock',
                        onClick: () => {
                            handleLock('锁定');
                        },
                    },
                    {
                        label: '解锁',
                        icon: 'el-icon-unlock',
                        onClick: () => {
                            handleLock('解锁');
                        },
                    },
                ],
            },
            {
                label: '图库',
                divided: true,
                icon: 'el-icon-picture-outline',
                onClick: () => {
                    emit('menuClick', '图库');
                },
            },
            {
                label: '预览',
                divided: true,
                icon: 'el-icon-view',
                onClick: () => {
                    emit('menuClick', '预览');
                },
            },
            {
                label: '保存',
                divided: true,
                icon: 'el-icon-star-off',
                onClick: () => {
                    emit('menuClick', '保存');
                },
            },
            {
                label: '重新加载',
                divided: true,
                icon: 'el-icon-refresh',
                onClick: () => {
                    router.go(0);
                },
            },
        ],
        event, // 鼠标事件信息
        customClass: 'custom-class', // 自定义菜单 class
        zIndex: 9999, // 菜单样式 z-index
        minWidth: 230, // 主菜单最小宽度
    });

    return true;
}
const controlMousedown = (component: any, event: any, index: any) => {
    if (event.ctrlKey) {
        return;
    }
    flag.value = 'move';
    curControl.value = component;
    clickItem(component, index);
    moveItem.startX = event.pageX;
    moveItem.startY = event.pageY;
    //记录初始信息--move
    for (const key in selectedComponentMap.value) {
      const component = selectedComponentMap.value[key];
        component.style.temp = {};
        component.style.temp.position = {};
        component.style.temp.position.x = component.style.position.x;
        component.style.temp.position.y = component.style.position.y;
        component.style.temp.position.w = component.style.position.w;
        component.style.temp.position.h = component.style.position.h;
    }
}
const isResizing = ref(false)
const handleMouseUp = () => {
  if (isResizing.value) {
    // 保存历史记录
    saveHistory()
  }
  isResizing.value = false
  window.removeEventListener('mousemove', handleMouseMove)
  window.removeEventListener('mouseup', handleMouseUp)
}
const handleMouseMove = (event: MouseEvent) => {
  if (!isResizing.value || !curControl.value) return

  const dx = event.clientX - resizeItem.value.startPx
  const dy = event.clientY - resizeItem.value.startPy

  const original = resizeItem.value
  let newX = original.x
  let newY = original.y
  let newW = original.w
  let newH = original.h

  switch (flag.value) {
    case 'resize-lt': // 左上
      newX = original.x + dx
      newY = original.y + dy
      newW = original.w - dx
      newH = original.h - dy
      break

    case 'resize-t': // 上
      newY = original.y + dy
      newH = original.h - dy
      break

    case 'resize-rt': // 右上
      newY = original.y + dy
      newH = original.h - dy
      newW = original.w + dx
      break

    case 'resize-r': // 右
      newW = original.w + dx
      break

    case 'resize-rb': // 右下（默认）
      newW = original.w + dx
      newH = original.h + dy
      break

    case 'resize-b': // 下
      newH = original.h + dy
      break

    case 'resize-lb': // 左下
      newX = original.x + dx
      newW = original.w - dx
      newH = original.h + dy
      break

    case 'resize-l': // 左
      newX = original.x + dx
      newW = original.w - dx
      break

    case 'resize-ct': // 中上
      newY = original.y + dy
      newH = original.h - dy
      break

    case 'resize-cb': // 中下
      newH = original.h + dy
      break

    case 'resize-rc': // 中右
      newW = original.w + dx
      break

    case 'resize-lc': // 中左
      newX = original.x + dx
      newW = original.w - dx
      break
  }

  // 最小尺寸限制
  newW = Math.max(newW, 20)
  newH = Math.max(newH, 20)

  // 更新组件样式
  curControl.value.style.position.x = newX
  curControl.value.style.position.y = newY
  curControl.value.style.position.w = newW
  curControl.value.style.position.h = newH
}
// resize 鼠标按下事件
const resizeMousedown = (component: any, event: MouseEvent, index: number, direction: string) => {
  flag.value = direction;
  curControl.value = component;
  curIndex.value = index;
  clickItem(component, index);
  // 更新 resizeItem 的值
  resizeItem.value = {
    startPx: event.clientX,
    startPy: event.clientY,
    x: component.style.position.x,
    y: component.style.position.y,
    w: component.style.position.w,
    h: component.style.position.h,
  };
  isResizing.value = true
  window.addEventListener('mousemove', handleMouseMove)
  window.addEventListener('mouseup', handleMouseUp)
};
const isRenameDialog = ref(false);
const renameId = ref('');
const renameValue = ref('');
interface CenterItem {
  id: number;
  guid: string;
  pageName: string;
}

const ztList = ref<CenterItem[]>([]);
// 处理 tab 组态功能操作
const handleBottomTabCommand = async (command:any) => {
  const { type, index, page } = command;
  const { id: pageId, guid: pageGuid, pageName } = page;

  if (type === 'rename') {
    isRenameDialog.value = true;
    renameId.value = pageId;
    renameValue.value = pageName;
  } else if (type === 'delete') {
    try {
      await ElMessageBox.confirm(
          '您是否要删除此页面，删除后将无法恢复！',
          '提示',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
      );
      // 显示加载中
      const loadingInstance = ElLoading.service({
        fullscreen: true,
        text: '正在删除，请稍候...'
      });
      try {
        const ress = await delCenter(pageId);
        const res = ress.data
        if (res.code === 200) {
          const flag = await getZtCenter(); // 获取最新列表
          if (flag && pageGuid === guid.value) {
            const prevItem = ztList.value[index - 1];
            if (prevItem) {
              guid.value = prevItem.guid;
              id.value = prevItem.id;

              await router.push({
                query: { ...router.currentRoute.value.query, id: id.value, guid: guid.value }
              });

              initZtTab();
            }
          }
          // 触发全局更新事件
          bus.emit('updateCenter')
        } else {
          ElMessageBox.alert(res.msg, '错误', { type: 'error' });
        }

        loadingInstance.close();
      } catch (err) {
        loadingInstance.close();
        // eslint-disable-next-line no-console
        console.error('删除失败:', err);
        ElMessageBox.alert('删除失败，请重试', '错误', { type: 'error' });
      }
    } catch (cancelError) {
      // 用户点击取消
      // eslint-disable-next-line no-console
      console.log('用户取消了删除操作');
    }
  }
};
const handleTabRename = () => {
  const params = {
    id: renameId.value,
    pageName: renameValue.value
  }

  updateCenter(params).then((res) => {
    if (res.data.code === 200) {
      getZtCenter()
    } else {
      ElMessage.error(res.msg)
    }
    isRenameDialog.value = false
  })
}
function onLayerMousemove(event: MouseEvent) {
  if (event.which !== 1) {
    flag.value = ''
    return
  }
  if (!flag.value) return

  if (flag.value.startsWith('resize')) {
    const dx = event.clientX - resizeItem.value.startPx
    const dy = event.clientY - resizeItem.value.startPy

    switch (flag.value) {
      case 'resize-lt':
        curControl.value!.style.position.x = resizeItem.value.x + dx
        curControl.value!.style.position.y = resizeItem.value.y + dy
        break
      case 'resize-lc':
        curControl.value!.style.position.x = resizeItem.value.x + dx
        break
      case 'resize-lb':
        curControl.value!.style.position.x = resizeItem.value.x + dx
        break
      case 'resize-rt':
        curControl.value!.style.position.y = resizeItem.value.y + dy
        break
      case 'resize-rc':
        break
      case 'resize-rb':
        break
      case 'resize-ct':
        curControl.value!.style.position.y = resizeItem.value.y + dy
        break
      case 'resize-cb':
        break
    }

    if (resizeItem.value.w + dx > 20) {
      curControl.value!.style.position.w = resizeItem.value.w + dx
    }
    if (resizeItem.value.h + dy > 20) {
      curControl.value!.style.position.h = resizeItem.value.h + dy
    }

    // 等待 DOM 更新后调用组件的 resize 方法
    setTimeout(() => {
      const componentRef = compRefs.value[curIndex.value]
      if (componentRef && typeof componentRef.onResize === 'function') {
        componentRef.onResize()
      }
    }, 0)
  } else if (flag.value === 'move') {
    const dx = event.pageX - moveItem.startX
    const dy = event.pageY - moveItem.startY

    Object.keys(selectedComponentMap.value).forEach((key) => {
      const component = selectedComponentMap.value[key]
      if (!component.isLock) {
        component.style.position.x = component.style.temp.position.x + dx
        component.style.position.y = component.style.temp.position.y + dy
      }
    })
  } else if (flag.value === 'frame_selection') {
    onFrameSelection(event)
  }
}
const onLayerMousedown = (event: any) => {
    flag.value = 'frame_selection';
    frameSelectionDiv.startX = event.offsetX;
    frameSelectionDiv.startY = event.offsetY;
    frameSelectionDiv.startPageX = event.pageX;
    frameSelectionDiv.startPageY = event.pageY;
    isMultiple.value = true;
}
const onLayerClick = () => {
    if (isMultiple.value == false) {
        clearSelected();
        selectComponent.value = null;
        setLayerSelected(true);
    } else {
        isMultiple.value = false;
    }
}
const onFrameSelection = (event: any) => {
    var dx = event.pageX - frameSelectionDiv.startPageX;
    var dy = event.pageY - frameSelectionDiv.startPageY;
    frameSelectionDiv.width = Math.abs(dx);
    frameSelectionDiv.height = Math.abs(dy);
    if (dx > 0 && dy > 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY;
        frameSelectionDiv.left = frameSelectionDiv.startX;
    } else if (dx > 0 && dy < 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY + dy;
        frameSelectionDiv.left = frameSelectionDiv.startX;
    } else if (dx < 0 && dy > 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY;
        frameSelectionDiv.left = frameSelectionDiv.startX + dx;
    } else if (dx < 0 && dy < 0) {
        frameSelectionDiv.top = frameSelectionDiv.startY + dy;
        frameSelectionDiv.left = frameSelectionDiv.startX + dx;
    }
    //判断各个组件是否在方框内
    var rect = {
        x: frameSelectionDiv.left,
        y: frameSelectionDiv.top,
        width: frameSelectionDiv.width,
        height: frameSelectionDiv.height,
    };
    var components = configData.value.components;
    components.forEach((component: any) => {
        var itemRect = {
            x: component.style.position.x,
            y: component.style.position.y,
            width: component.style.position.w,
            height: component.style.position.h,
        };
        if (checkByRectCollisionDetection(rect, itemRect)) {
            counterStore.addSelectedComponent(component);
        } else {
            counterStore.removeSelectedComponent(component);
        }
    });
    if (selectedComponents.length > 0) {
        setLayerSelected(false);
    } else {
        setLayerSelected(true);
    }
}
const onDrop = (event: any) => {
    event.preventDefault();
  try {
    const infoJson = event.dataTransfer.getData('my-info')
    const component = JSON.parse(infoJson)

    if (component.type === 'weather') {
      let isExist = false
      configData.value.components.forEach((comp: any) => {
        if (comp.type === 'weather') {
          isExist = true
        }
      })
      if (isExist) {
        ElMessage.warning('天气组件已存在，不能重复添加')
        return
      }
    }
    if (!checkAddComponent(component)) {
      return
    }

    let x = 0
    let y = 0
    if (event.target.id === 'surface-edit-layer') {
      x = event.offsetX
      y = event.offsetY
    } else {
      const layer = event.currentTarget
      const position = layer.getBoundingClientRect()
      x = event.clientX - position.left
      y = event.clientY - position.top
    }

    component.style = component.style || {}
    component.style.position = component.style.position || { x: 0, y: 0 }
    component.style.position.x = x
    component.style.position.y = y
    // 执行添加操作
    counterStore.execute({
      op: 'add',
      component
    })

    // 保存历史记录
    saveHistory()

    // 默认选中并点击
    clickItem(component, configData.value.components.length - 1)
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('onDrop error:', error)
    ElMessage.error('拖入组件时发生错误')
  }
}
const moveItems = (direction: any) => {
    var dx = 0,
        dy = 0;
    if (direction == 'up') {
        dy = -5;
    } else if (direction == 'right') {
        dx = 5;
    } else if (direction == 'down') {
        dy = 5;
    } else if (direction == 'left') {
        dx = -5;
    }
    counterStore.execute({
        op: 'move',
        dx: dx,
        dy: dy,
        items: Object.values(selectedComponentMap.value),
    });
    // 保存历史记录
    saveHistory();
}
import { useQuasar } from 'quasar'

const checkAddComponent = (info: any) => {
    if (info == null) {
      const $q = useQuasar()
      $q.notify({
            type: 'negative',
            position: 'bottom-right',
            message: 'This component not surpport.',
        });
        return false;
    }
    return true;
}
import topoUtil from '/@/utils/topo/topo-util';
// 导入所有组态组件 - 基于 topoBase.vue 的完整导入
// 基础组件
import ViewText from './control/ViewText.vue';
import View3DModel from './control/View3DModel.vue';
import ViewImage from './control/ViewImage.vue';
import ViewImageSwitch from './control/ViewImageSwitch.vue';
// Canvas 形状组件
import ViewCircular from './control/canvas/ViewCircular.vue';
import ViewLine from './control/canvas/ViewLine.vue';
import ViewLineArrow from './control/canvas/ViewLineArrow.vue';
import ViewBizierCurveArrow from './control/canvas/ViewBizierCurveArrow.vue';
import ViewRect from './control/canvas/ViewRect.vue';
import ViewTriangle from './control/canvas/ViewTriangle.vue';
// 图表组件
import ViewChart from './control/chart/ViewChart.vue';
import ViewChartPie from './control/chart/ViewChartPie.vue';
import ViewChartGauge from './control/chart/ViewChartGauge.vue';
import ViewChartWater from './control/chart/ViewChartWater.vue';
import ViewChartTemp from './control/chart/ViewChartTemp.vue';
import ViewChartMap from './control/chart/ViewChartMap.vue';
import ViewChartWrapper from './control/chart/ViewChartWrapper.vue';
// 媒体组件
import ViewVideo from './control/ViewVideo.vue';
import ViewVideoPlay from './control/ViewVideoPlay.vue';
import ViewVideoMp4 from './control/ViewVideoMp4.vue';
// 3D 组件
import ViewVR from './control/ViewVR.vue';
// 数据组件
import ViewFlowBarDynamic from './control/ViewFlowBarDynamic.vue';
// 功能组件
import ViewMap from './control/ViewMap.vue';
import ViewPanel from './control/ViewPanel.vue';
import ViewTimer from './control/ViewTimer.vue';
import ViewWeather from './control/ViewWeather.vue';
import ViewWarn from './control/ViewWarn.vue';
import ViewOrder from './control/ViewOrder.vue';
import ViewComponent from './control/ViewComponent.vue';
// 完整的组件映射表 - 基于 topoBase.vue 的完整映射
const componentMap: any = {
  // 基础组件
  'text': ViewText,
  // 'text-static': ViewTextStatic,
  'image': ViewImage,
  'imageSwitch': ViewImageSwitch,
  'image-switch': ViewImageSwitch,
  // switch: ViewSwitch,
  // 'knob-switch': ViewKnobSwitch,
  '3d-model': View3DModel,
  '3D-model': View3DModel,

  // Canvas 形状组件
  'circular': ViewCircular,
  'rect': ViewRect,
  'triangle': ViewTriangle,
  'line': ViewLine,
  'line-arrow': ViewLineArrow,
  // 'line-wave': ViewLineWave,
  'bizier-curve-arrow': ViewBizierCurveArrow,
  // dashed: ViewDashed,

  // 图表组件
  'chart-line': ViewChart,
  'chart-line-step': ViewChart,
  'chart-bar': ViewChart,
  'chart-pie': ViewChartPie,
  'chart-gauge': ViewChartGauge,
  'chart-water': ViewChartWater,
  'chart-temp': ViewChartTemp,
  'chart-map': ViewChartMap,
  'chart-wrapper': ViewChartWrapper,

  // SVG 组件
  // 'svg-image': ViewSvgImage,
  // 'svg-static': ViewSvgStatic,

  // 媒体组件
  'video': ViewVideo,
  'video-play': ViewVideoPlay,
  'video-mp4': ViewVideoMp4,

  // 3D 组件
  // threejs: ViewThreeJs,
  // 'relief-ball': ViewReliefBall,
  'VR': ViewVR,
  'vr': ViewVR,

  // 数据组件
  // 'real-data': ViewRealData,
  // history: ViewHistory,
  // 'flow-bar': ViewFlowBar,
  'flow-bar-dynamic': ViewFlowBarDynamic,

  // 功能组件
  'map': ViewMap,
  'panel': ViewPanel,
  'timer': ViewTimer,
  'weather': ViewWeather,
  'warn': ViewWarn,
  'order': ViewOrder,
  // 'luck-draw': ViewLuckDraw,
  'component': ViewComponent,
};

// 动态解析函数
const parseView = (component: any) => {
  const type = component.type;
  console.log(type)
  let viewComponent = componentMap[type];

  if (viewComponent) {
    return viewComponent;
  } else {
    // 如果没有找到组件，返回默认的组件名
    const viewName = topoUtil.parseViewName(component);

    // eslint-disable-next-line no-console
    console.warn(`[Topo] 未找到 ${type} 的组件映射，将使用默认组件名：${viewName}`);
    return viewName;
  }
};

const onLayerMouseup = (event: MouseEvent) => {
  if (flag.value?.startsWith('resize')) {
    const index = curIndex.value
    if (index !== null && compRefs.value[index]?.onResize) {
      compRefs.value[index]?.onResize()
    }
  } else if (flag.value === 'frame_selection') {
    onFrameSelection(event)
    frameSelectionDiv.width = 0
    frameSelectionDiv.height = 0
    frameSelectionDiv.top = 0
    frameSelectionDiv.left = 0
  } else if (flag.value === 'move') {
    const dx = event.pageX - moveItem.startX
    const dy = event.pageY - moveItem.startY

    for (const key in selectedComponentMap.value) {
      const component = selectedComponentMap.value[key]
      component.style.position.x -= dx
      component.style.position.y -= dy
    }
    counterStore.execute({
      op: 'move',
      dx: dx,
      dy: dy,
      items: Object.values(selectedComponentMap.value),
    });
    // 保存历史记录
    saveHistory();
  }
    flag.value = '';
}
const clickItem = (component: any, index: any) => {
    operateFlag.value = operateFlag.value + 1;
    selectFlag.value = operateFlag.value;
  // eslint-disable-next-line no-console
    console.log('单击组件:', deepCopy(component));
    sessionStorage.setItem('operateFlag' + operateFlag.value, JSON.stringify(deepCopy(component)));
    selectComponent.value = component;
    selectIndx.value = index;
    setLayerSelected(false);
    if (selectedComponentMap.value[component.identifier] == undefined) {
        counterStore.setSelectedComponent(component);
    } else {
        //如果已经选中，则不做任何处理
        counterStore.setSelectedComponent(component);
    }
    if (component.identifiers?.length) {
        component.identifiers.forEach((element: any) => {
            configData.value.components.forEach((ele: any) => {
                if (ele.identifier == element) {
                  selectedComponentMap.value[element] = ele
                }
            });
        });
    }
    emit('lockStatus', component.isLock);
}

//复制
const copyItem= () => {
  const items = []
  const map = selectedComponentMap.value
  for (const key in map) {
    if (map.hasOwnProperty(key)) {
      const item = deepCopy(map[key])
      items.push(item)
    }
  }
  counterStore.setCopySrcItems(items)
}
//粘贴
const pasteItem = () => {
  if (copySrcItems.value && Array.isArray(copySrcItems.value) && copySrcItems.value.length > 0) {
    counterStore.execute({
      op: 'copy-add',
      items: copySrcItems.value,
    })
    // 保存历史记录
    saveHistory()
  }
}

const removeItem = () => {
    //移除组件
    counterStore.execute({
        op: 'del',
    });
    // 保存历史记录
    saveHistory();
    setLayerSelected(true);
}
// 设置选中的组件
const clearSelected = () => {
    counterStore.clearSelectedComponent();
};
const setLayerSelected = (bool: any) => {
    counterStore.setLayerSelected(bool)
}
// 保存组态数据
const saveZtDatas = async (): Promise<boolean> => {
    try {
        // 模拟延迟，确保 loading 先显示
        await new Promise((resolve) => setTimeout(resolve, 500));

        const canvasBox = document.querySelector('#surface-edit-layer');
        if (!canvasBox) {
            throw new Error('Canvas element not found');
        }
        const options = {
            backgroundColor: null,
            useCORS: true,
        };
        const canvas = await html2canvas(canvasBox as any, options);
        const base64 = canvas.toDataURL('image/png');
        const json = JSON.stringify(configData.value);
        const params = {
            guid: guid.value,
            base64,
            scadaData: json,
        };
        // 调用保存数据 API
        const res = await saveDetailData(params);
        if (res.data.code === 200) {
            const resolution = `${configData.value.layer.width}x${configData.value.layer.height}`;
            const params2 = { id: id.value, pageResolution: resolution };

            // 调用更新中心数据 API
            const updateRes = await updateCenter(params2);
            if (updateRes.data.code === 200) {
                // 假设这里是触发 Vue 事件总线
              bus.emit('updateCenter')
                return true;
            } else {
                throw new Error('Failed to update center');
            }
        } else {
            throw new Error('Failed to save detail data');
        }
    } catch (error) {
      // eslint-disable-next-line no-console
        console.error(error);
        return false;
    }
};
import { useBus } from '/@/utils/useBus'
import {Session} from "/@/utils/storage";

const bus = useBus()
// 新增组态/Tab栏
function handleBottomTabAdd() {
  const params = {
    pageName: '未知页面' + Date.now(),
  }
  const loadingInstance = ElLoading.service({ fullscreen: true, text: '正在保存，请稍候...' })
  addCenter(params)
      .then((res:any) => {
        if (res.data.code === 200) {
          bus.emit('updateCenter') // 触发全局事件
          getZtCenter().then((flag) => {
            if (flag && ztList.value.length > 0) {
              const lastItem = ztList.value[ztList.value.length - 1]
              guid.value = lastItem.guid
              id.value = lastItem.id
              initZtTab()
            }
          })
        }
      })
      .catch((err:any) => {
        ElMessage.error('保存失败')
        // eslint-disable-next-line no-console
        console.error(err)
      })
      .finally(() => {
        loadingInstance.close()
      })
}
// 监听 Ctrl + S 事件
const handleCrtlS = (e: KeyboardEvent): void => {
    // 获取按键的键码
    const key = e.key === 'S' || e.key === 's';
    if (key && e.ctrlKey) {
        loadingInstance.value = ElLoading.service({
            text: '请稍候...',
        });

        saveZtDatas().then((flag) => {
            if (flag) {
                loadingInstance.value.close();
                ElMessage.success('保存成功');
            }
        });
        e.preventDefault(); // 阻止浏览器默认的保存行为
    }
};
// 点击组件
const clickComponent = (index: any, component: any, event: any) => {
    //点击了ctrl
    if (event.ctrlKey == true) {
        setLayerSelected(false);
        if (selectedComponentMap[component.identifier] == undefined) {
            counterStore.addSelectedComponent(component);
        } else {
            counterStore.removeSelectedComponent(component);
        }
    } else {
        //这里不再处理点击事件，改为鼠标左键
        //this.clickItem(component,index);
    }
}
// 清理颜色值，确保颜色属性始终是字符串
const cleanColorValues = (data: any) => {
    // 清理图层背景颜色
    if (data.layer && typeof data.layer.backColor === 'object') {
        data.layer.backColor = '';
    }

    // 清理组件颜色值
    if (data.components && Array.isArray(data.components)) {
        data.components.forEach((component: any) => {
            if (component.style) {
                // 清理背景颜色
                if (typeof component.style.backColor === 'object') {
                    component.style.backColor = 'transparent';
                }
                // 清理前景颜色
                if (typeof component.style.foreColor === 'object') {
                    component.style.foreColor = '';
                }
                // 清理边框颜色
                if (typeof component.style.borderColor === 'object') {
                    component.style.borderColor = '#ccccccff';
                }
            }
        });
    }
};

// 获取组态详情
const getZtDetails = () => {
    const guids = guid.value as any
    getByGuid(guids).then((res) => {
        deviceZtRow.value = res.data.data;
      // eslint-disable-next-line no-console
      console.log('组件JSON格式化', JSON.parse(deviceZtRow.value.scadaData));
        // document.title = deviceZtRow.value.pageName; // 修改title值
        if (deviceZtRow.value.scadaData) {
            let configData = JSON.parse(deviceZtRow.value.scadaData);
            // 清理可能的对象类型颜色值
            cleanColorValues(configData);
            counterStore.loadDefaultTopoData(configData)
        } else {
            let configData = { name: '--', layer: { backColor: '', backgroundImage: '', widthHeightRatio: '', width: 1920, height: 1080 }, components: [] };
            counterStore.loadDefaultTopoData(configData)
        }
    });
}
// 初始化组态
const initZtTab = () => {
    // console.log('初始化组态开始');
    getZtDetails(); // 获取组态详情
    configDataHis.value = [];
    selectFlag.value = 0;
    newestConfigData.value = null; // 清空最新数据缓存
    clearSelected(); // 清掉选择的组件
    setLayerSelected(true); // 默认切换回背景设置
    emit('recoveryFlagClick', true); // 初始化时没有可恢复的操作，禁用恢复按钮
    emit('revokeFlagClick', true); // 初始化时没有可撤销的操作，禁用撤销按钮
    // console.log('初始化组态完成');
}
// 获取所有组态数据
const getZtCenter = () => {
    return new Promise((resolve, reject) => {
        listCenter().then((res) => {
            if (res.data.code === 200) {
                ztList.value = res.data.rows;
                resolve(true);
            } else {
                reject(false);
            }
        });
    });
}
// 处理 Tab 点击事件
const handleBottomTabClick = async (tab:any) => {
  if (guid.value !== tab.paneName) {
    const hasPermission = await checkPermission()
    if (hasPermission) {
      try {
        await ElMessageBox.confirm(
            '要保存当前页面，并切换到其他页面吗？',
            '提示',
            {
              confirmButtonText: '保存',
              cancelButtonText: '取消',
              type: 'warning',
            }
        )
        await saveZtDatas()
        const loading = ElLoading.service({ fullscreen: true, text: '正在切换，请稍候...' })
        try {
          // 模拟异步加载
          await new Promise(resolve => setTimeout(resolve, 500))
        } finally {
          loading.close()
        }
        guid.value = tab.paneName
        const zt = ztList.value.find(item => item.guid === tab.paneName)
        if (zt) {
          id.value = zt.id
        }

        initZtTab()
      } catch (error) {
        // eslint-disable-next-line no-console
        console.log('用户取消操作或发生错误:', error)
      }
    }
  }
}
/**
 * 模拟权限检查
 */
const checkPermission = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const hasPermission = checkPermi(['scada:center:edit']);
      resolve(hasPermission);
    }, 1000);
  });
};

// 使用 computed 创建计算属性
const selectedComponents = computed(() => counterStore.selectedComponents,) as any;
const selectedComponentMap = computed(() => counterStore.selectedComponentMap,) as any;
const configData = computed(() => counterStore.topoData,) as any;
const selectedIsLayer = computed(() => counterStore.selectedIsLayer,) as any;
const copySrcItems = computed(() => counterStore.copySrcItems,) as any;
const layerStyle = computed(() => {
    const scale = zoom.value / 100;
    const styles = [`transform:scale(${scale})`];
    const layer = configData.value?.layer;
    if (layer?.backColor) {
      styles.push(`background-color: ${layer.backColor}`);
    }
    if (layer?.backgroundImage) {
      styles.push(`background-image: url("${layer.backgroundImage}")`);
    }
    if (layer?.width > 0) {
      styles.push(`width: ${layer.width}px`);
    }
    if (layer?.height > 0) {
      styles.push(`height: ${layer.height}px`);
    }
    styles.push('overflow:hidden');
    return styles.join(';');
});
// 页面加载时
onMounted(() => {
    id.value = route.query.id, // 组态id
        guid.value = route.query.guid, // 组态标识
        // eslint-disable-next-line no-console
        console.log(route.query, 'route.query');

    document.addEventListener('keydown', handleCrtlS, false); // crtl+s保存数据
    initZtTab(); // 初始组态数据
    getZtCenter(); // 获取buttonTabs列表
});
// 在组件卸载时移除事件监听
onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleCrtlS);
});
//预览
function screenPreview() {
  try {
    // 检查必要的参数
    if (!guid.value) {
      ElMessage.error('缺少组态标识，无法预览');
      return;
    }
    // 显示加载提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在准备预览...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    // 先保存当前数据
    saveZtDatas().then(() => {
      loading.close();
      // 构建预览页面路由
      const routeUrl = router.resolve({
        path: '/scada/topo/fullscreen',
        query: {
          id: router.currentRoute.value.query.id,
          guid: guid.value,
        },
      });
      // 打开新窗口预览
      const previewWindow = window.open(routeUrl.href, '_blank');
      // 检查是否成功打开窗口
      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置');
        return;
      }
      ElMessage.success('预览页面已打开');
    }).catch((error) => {
      loading.close();
      // eslint-disable-next-line no-console
      console.error('预览失败:', error);
      ElMessage.warning('保存数据失败，但仍可预览当前已保存的版本');

      // 即使保存失败也允许预览
      const routeUrl = router.resolve({
        path: '/scada/topo/fullscreen',
        query: {
          id: router.currentRoute.value.query.id,
          guid: guid.value,
        },
      });
      const previewWindow = window.open(routeUrl.href, '_blank');
      if (!previewWindow) {
        ElMessage.error('无法打开预览窗口，请检查浏览器弹窗设置');
      }
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('预览失败:', error);
    ElMessage.error('预览失败，请重试');
  }
}

//保存
const printData = async () => {
  loadingInstance.value = ElLoading.service({
    lock: true,
    text: '请稍候...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  try {
    const flag = await saveZtDatas()
    if (flag) {
      loadingInstance.value.close()
      showMessage('success', '保存成功')
    }
  } catch (error) {
    loadingInstance.value.close()
    showMessage('error', '保存失败')
    // eslint-disable-next-line no-console
    console.error(error)
  }
}
const showMessage = (type:any, message:any) => {
  ElMessage({
    type,
    message,
  })
}
interface ComponentType {
  identifier: string;
  identifiers?: string[];
  style?: Record<string, any>;
  [key: string]: any;
}
//组件组合
const makeUpClick = (command: '组合' | '取消组合') => {
  const componentList = Object.values(selectedComponentMap.value)as ComponentType[];
  if (command === '组合') {
    if (selectedComponents.value.length < 2) {
      showMessage('warning', '至少需要选择两个组件才能组合');
      return;
    }
    const ids = [...selectedComponents.value];
    componentList.forEach((component) => {
      component.identifiers = ids;
    });
  } else {
    componentList.forEach((component) => {
      component.identifiers = [];
    });
  }
  // eslint-disable-next-line no-console
  console.log("组件总数据", configData); // 假设 configData 是你从外部传入或引入的
  if (selectedComponents.value.length === 0) {
    showMessage('warning', '未选中组合组件');
  } else {
    counterStore.clearSelectedComponent(); // 假设 counterStore 是 Pinia store
    if (selectComponent.value && selectIndx.value !== null) {
      clickItem(selectComponent.value, selectIndx.value);
    }
    // 保存历史记录
    saveHistory();
  }
};

// 对齐方法
const alignClick = (command: '左对齐' | '右对齐' | '上对齐' | '下对齐' | '水平等间距' | '垂直等间距') => {
  const keys = Object.keys(selectedComponentMap.value);
  if (keys.length === 0) {
    showMessage('warning', '未选中任何组件');
    return;
  }
  switch (command) {
    case '左对齐': {
      let alignX = Infinity;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        if (component?.style.position.x < alignX) {
          alignX = component.style.position.x;
        }
      });
      keys.forEach(key => {
        selectedComponentMap.value[key].style.position.x = alignX;
      });
      break;
    }
    case '右对齐': {
      let alignX = -Infinity;
      let alignComponent: ComponentType | null = null;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        const x = component.style.position.x;
        if (x > alignX) {
          alignX = x;
          alignComponent = component;
        }
      });
      if (!alignComponent) return;
      const alignRight = alignX + alignComponent.style.position.w;
      keys.forEach(key => {
        const comp = selectedComponentMap.value[key];
        const newWidth = comp.style.position.w;
        comp.style.position.x = alignRight - newWidth;
      });
      break;
    }
    case '上对齐': {
      let alignY = Infinity;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        if (component?.style.position.y < alignY) {
          alignY = component.style.position.y;
        }
      });
      keys.forEach(key => {
        selectedComponentMap.value[key].style.position.y = alignY;
      });
      break;
    }
    case '下对齐': {
      let alignBottom = 0;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        const bottom = component.style.position.y + component.style.position.h;
        if (bottom > alignBottom) {
          alignBottom = bottom;
        }
      });
      keys.forEach(key => {
        const comp = selectedComponentMap.value[key];
        comp.style.position.y = alignBottom - comp.style.position.h;
      });
      break;
    }
    case '水平等间距': {
      if (selectedComponents.value.length < 2) {
        showMessage('warning', '至少选择两个组件才能进行等间距排列');
        return;
      }
      let minX = Infinity;
      let maxX = -Infinity;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        const x = component.style.position.x;
        minX = Math.min(minX, x);
        maxX = Math.max(maxX, x + component.style.position.w);
      });
      const totalSpace = maxX - minX;
      const spacing = totalSpace / (selectedComponents.value.length - 1);
      // 排序后依次排列
      const sorted = selectedComponents.value.map((id: string) => selectedComponentMap.value[id]);
      sorted.sort((a:any, b:any) => a.style.position.x - b.style.position.x);
      let currentX = minX;
      sorted.forEach((comp: any) => {
        comp.style.position.x = currentX;
        currentX += spacing;
      });
      break;
    }
    case '垂直等间距': {
      if (selectedComponents.value.length < 2) {
        showMessage('warning', '至少选择两个组件才能进行等间距排列');
        return;
      }
      let minY = Infinity;
      let maxY = -Infinity;
      selectedComponents.value.forEach((id: string) => {
        const component = selectedComponentMap.value[id];
        const y = component.style.position.y;
        minY = Math.min(minY, y);
        maxY = Math.max(maxY, y + component.style.position.h);
      });
      const totalSpace = maxY - minY;
      const spacing = totalSpace / (selectedComponents.value.length - 1);
      const sorted = selectedComponents.value.map((id: string) => selectedComponentMap.value[id]);
      sorted.sort((a:any, b:any) => a.style.position.y - b.style.position.y);
      let currentY = minY;
      sorted.forEach((comp: any) => {
        comp.style.position.y = currentY;
        currentY += spacing;
      });
      break;
    }
  }
  // 保存历史记录
  saveHistory();
};

const handleLock = (command: '锁定' | '解锁') => {
  const keys = Object.keys(selectedComponentMap.value);
  if (keys.length === 0) {
    // eslint-disable-next-line no-console
    console.warn('未选中任何组件');
    return;
  }
  if (command === '锁定') {
    keys.forEach((key: string) => {
      const component = selectedComponentMap.value[key];
      if (component) {
        component.isLock = true;
      }
    });
    emit('lockStatus', true);
  } else {
    keys.forEach((key: string) => {
      const component = selectedComponentMap.value[key];
      if (component) {
        component.isLock = false;
      }
    });
    emit('lockStatus', false);
  }
  // 保存历史记录
  saveHistory();
};

// 暴露方法给父组件调用
defineExpose({ addImageData ,screenPreview,printData,copyItem,pasteItem,removeItem,handleZoom,
  makeUpClick,alignClick,handleLock,revoke,recovery,saveHistory,handleDownLoad,handleImport})
</script>

<style lang="scss" scoped>
.topo-main-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    overflow-x: hidden;
    overflow-y: hidden;

    .content-wrap {
        height: calc(100% - 41px);
        width: 100%;

        .topo-layer {
            width: 100%;
            height: 100%;
            position: absolute;
            transform-origin: left top;
            overflow: auto;
            background-color: #ffffff;
            background-clip: padding-box;
            background-origin: padding-box;
            background-repeat: no-repeat;
            background-size: 100% 100%;

            .topo-frame-selection {
                background-color: #8787e7;
                opacity: 0.3;
                border: #0000ff solid 1px;
                position: absolute;
                z-index: 999;
            }

            .topo-layer-view-selected-line {
                outline: 1px solid #0cf;
            }

            .topo-layer-view {
                position: absolute;
                height: 100px;
                width: 100px;
                background-color: #999;
                cursor: move;
                // border: #ccc solid 1px;

                .resize-left-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    top: -5px;
                    cursor: nwse-resize;
                }

                .resize-left-center {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    top: 50%;
                    margin-top: -5px;
                    cursor: ew-resize;
                }

                .resize-left-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    left: -5px;
                    bottom: -5px;
                    cursor: nesw-resize;
                }

                .resize-right-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    top: -5px;
                    cursor: nesw-resize;
                }

                .resize-right-center {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    top: 50%;
                    margin-top: -5px;
                    cursor: ew-resize;
                }

                .resize-right-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    right: -5px;
                    bottom: -5px;
                    cursor: nwse-resize;
                }

                .resize-center-top {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    top: -5px;
                    left: 50%;
                    margin-left: -5px;
                    cursor: ns-resize;
                }

                .resize-center-bottom {
                    position: absolute;
                    height: 10px;
                    width: 10px;
                    border-radius: 50%;
                    background-color: #0cf;
                    border: 1px solid #0cf;
                    bottom: -5px;
                    left: 50%;
                    margin-left: -5px;
                    cursor: ns-resize;
                }
            }

            .topo-layer-view-selected {
                border: 1.5px solid #21cc96;
            }

            .topo-layer-view-selected:hover {
                border: 1.5px dashed #21cc96;
            }
        }

        .topo-layer::before,
        .topo-layer::after {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            content: '';
            background-repeat: repeat;
            pointer-events: none;
        }

        .topo-layer::before {
            background-image: linear-gradient(to right, black 1px, transparent 1px, transparent 10px), linear-gradient(to bottom, black 1px, transparent 1px, transparent 10px);
            background-size: 10px 10px;
            opacity: 0.05;
        }

        .topo-layer::after {
            background-image: linear-gradient(to right, black 1px, transparent 1px, transparent 50px), linear-gradient(to bottom, black 1px, transparent 1px, transparent 50px);
            background-size: 50px 50px;
            opacity: 0.1;
        }
    }

    .footer-wrap {
        position: relative;
        height: 42px;
        border-top: 1px solid #d8dce5;
        background-color: #f1f3f4;
        display: flex;
        flex-direction: row;
        align-items: center;

        .left-wrap {
            flex: 1;
            :deep(.el-tabs__nav) {
                border: unset;
                border-right: 1px solid #dfe4ed;
                border-radius: unset;
            }
            :deep(.is-focus) {
                box-shadow: unset;
                border-radius: unset;
            }

            .dropdown-item {
                font-size: 12px;
            }

            .dropdown-item-active {
                font-size: 12px;
                color: #409eff;
            }
        }

        .right-wrap {
            height: 42px;
            width: 42px;
            line-height: 42px;
            text-align: center;
            cursor: pointer;
            font-size: 18px;
            color: #78797a;
            font-weight: 700;
            margin-right: 5px;
        }
    }
}
</style>
<style scoped>
:deep(.custom-class .menu_item__available:hover),
:deep(.custom-class .menu_item_expand) {
  background: #ffecf2 !important;
  color: #ff4050 !important;
}
</style>
<style>
::-webkit-scrollbar-thumb {
    background-color: #e1e1e1;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #a5a2a2;
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    position: absolute;
}

::-webkit-scrollbar-track {
    background-color: #fff;
}
</style>