import request from '/@/utils/request';
import { AxiosResponse } from 'axios';

// 定义新闻分类类型
interface NewsCategory {
  id: string;
  name: string;
  description: string;
  // 根据实际情况可以补充更多属性
}

// 查询新闻分类列表
export function listNewsCategory(query: Record<string, any>): Promise<AxiosResponse<any>> {
  return request({
    url: '/iot/newsCategory/list',
    method: 'get',
    params: query,
  });
}

// 查询产品简短分类列表
export function listShortNewsCategory(): Promise<AxiosResponse<any>> {
  return request({
    url: '/iot/newsCategory/newsCategoryShortList',
    method: 'get',
  });
}

// 查询新闻分类详细
export function getNewsCategory(categoryId: string): Promise<AxiosResponse<any>> {
  return request({
    url: `/iot/newsCategory/${categoryId}`,
    method: 'get',
  });
}

// 新增新闻分类
export function addNewsCategory(data: any): Promise<AxiosResponse> {
  return request({
    url: '/iot/newsCategory',
    method: 'post',
    data: data,
  });
}

// 修改新闻分类
export function updateNewsCategory(data: any): Promise<AxiosResponse> {
  return request({
    url: '/iot/newsCategory',
    method: 'put',
    data: data,
  });
}

// 删除新闻分类
export function delNewsCategory(categoryId: string): Promise<AxiosResponse> {
  return request({
    url: `/iot/newsCategory/${categoryId}`,
    method: 'delete',
  });
}
