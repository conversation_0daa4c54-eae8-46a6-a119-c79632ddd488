<script setup>
import { ref, computed, onMounted } from 'vue';
import { animate } from '@/utils/topo/anime';

// 定义 props
const props = defineProps({
  editMode: {
    type: Boolean,
    default: false,
  },
  selected: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
});

// 计算属性：文字对齐方式
const textAlign = computed(() => {
  return props.detail.style?.textAlign ?? 'center';
});

// 计算属性：行高
const lineHeight = computed(() => {
  return props.detail.style?.lineHeight ?? props.detail.style.position?.h;
});

// 动画实例引用
const animateView = ref(null);

// 初始化动画
const animationInit = () => {
  console.log('开始初始化动画');
  const domId = document.getElementById(props.detail.identifier);
  let display = 'block';
  let rotate = [];
  let scale = [];
  let translates = [];
  let duration = 1000;
  let autoplay = false;
  let loop = true;

  // 设置动画时长
  if (props.detail.dataAction.duration) {
    duration = props.detail.dataAction.duration * 1000;
  } else if (props.detail.dataAction.rotationSpeed === '快') {
    duration = 500;
  } else if (props.detail.dataAction.rotationSpeed === '中') {
    duration = 1000;
  } else if (props.detail.dataAction.rotationSpeed === '慢') {
    duration = 1500;
  }

  // 旋转动画
  if (props.detail.dataBind?.xzAction) {
    rotate.push(360);
  } else {
    rotate.push(0);
  }

  // 缩放动画（闪烁）
  if (props.detail.dataBind?.ssAction) {
    scale.push(0.7, 1, 1.3, 1);
  } else {
    scale.push(1);
  }

  // 滑动动画
  if (props.detail.dataBind?.hdAction && props.detail.dataAction.translateList?.length > 0) {
    translates = props.detail.dataAction.translateList.map((element) => {
      if (element.direction === '竖直') {
        return { translateY: -element.position };
      } else {
        return { translateX: element.position };
      }
    });
  } else {
    translates.push({ translateX: 0 });
  }

  // 启动动画
  animateView.value = animate(domId, display, rotate, scale, translates, duration, autoplay, loop);
};

// mounted 生命周期
onMounted(() => {
  if (props.detail.componentShow?.includes('动画') && !props.editMode) {
    animationInit();
  }
});

// 定义 emit（如果后续需要触发事件）
const emit = defineEmits(['refreshData', 'resize']);

// 示例方法（暂未实现逻辑）
const refreshData = (val, sceneName) => {
  // 可以在这里处理数据刷新逻辑
  emit('refreshData', val, sceneName);
};

const onResize = () => {
  emit('resize');
};
</script>