<template>
    <div  style="padding-left:20px;">
        
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px">
                    <el-form-item label="告警名称" prop="alertName">
                        <el-input v-model="state.tableData.param.alertName" clearable size="default"
                            placeholder="请输入告警名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="告警级别" prop="alertLevel">
                        <el-select v-model="state.tableData.param.alertLevel" placeholder="请选择告警级别" clearable
                            size="default" style="width: 240px">
                            <el-option v-for="dict in alert_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="处理状态" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="请选择处理状态" clearable size="default"
                            style="width: 180px">
                            <el-option v-for="dict in process_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="告警名称" align="center" prop="alertName" />
                <el-table-column label="设备编号" align="center" prop="serialNumber" />
                <el-table-column label="设备名称" align="center" prop="deviceName" />
                <el-table-column label="告警级别" align="center" prop="alertLevel" width="120">
                    <template #default="scope">
                        <dict-tag :options="alert_level_list" :value="scope.row.alertLevel" />
                    </template>
                </el-table-column>
                <el-table-column label="告警时间" align="center" prop="createTime" width="170">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="数据" align="left" header-align="center" prop="detail">
                    <template #default="scope">
                        <div v-html="formatDetail(scope.row.detail)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="处理状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="process_status_list" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                    <template #default="scope">
                        <el-button size="default" text type="primary" @click="handleUpdate(scope.row)"
                            v-auths="['iot:alertLog:edit']"><el-icon>
                                <Edit />
                            </el-icon>处理</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>

        <!-- 添加或修改设备告警对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogData.tableData.dialog.title"
            v-model="dialogData.tableData.dialog.isShowDialog" width="600px" append-to-body>
            <el-form ref="DialogFormRef" :model="dialogData.tableData.ruleForm" :rules="rules" label-width="80px">
                <el-form-item label="处理结果" prop="remark">
                    <el-input v-model="dialogData.tableData.ruleForm.remark" type="textarea" placeholder="请输入内容"
                        :rows="8" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="cancel">取 消</el-button>
                <el-button type="primary" @click="SubmitForm(DialogFormRef)">确 定</el-button>

            </template>
        </el-dialog>

    </div>
</template>
<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick, watch } from 'vue';
import { ElMessageBox, ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { parseTime } from '/@/utils/next'
import { addAlertLog, getAlertLog, listAlertLog, updateAlertLog } from '/@/api/iot/alertLog';


const dictStore = useDictStore();  // 使用 Pinia store
// 定义 props
const props = defineProps({
    device: {
        type: Object
    }
})
// 引入组件
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
// 定义变量内容
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: '',
            alertName: '',
            alertLevel: '',
            serialNumber:''

        },
    },
});
const dialogData = reactive({
    tableData: {
        ruleForm: {
            remark: '',
            alertLogId: '' as any
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
})
// 校验规则
const rules = reactive({
    remark: [
        {
            required: true,
            message: '处理内容不能为空',
            trigger: 'blur',
        },
    ],

})
// 定义变量内容
const alert_level_list = ref<Option[]>([]);
const process_status_list = ref<Option[]>([]);
const DialogFormRef = ref();
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listAlertLog(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        alertName: '',
        alertLevel: '',
        serialNumber:''
    }
}
// 取消
const cancel = () => {
    dialogData.tableData.dialog.isShowDialog = false;
    dialogData.tableData.ruleForm.remark = '';
}
/** 修改按钮操作 */
const handleUpdate = (row: any) => {
    dialogData.tableData.ruleForm.remark = '';
    const alertLogId = row.alertLogId
    getAlertLog(alertLogId).then((response) => {
        dialogData.tableData.ruleForm = response.data.data;
        dialogData.tableData.dialog.isShowDialog = true;
        dialogData.tableData.dialog.title = '修改设备告警';
    });
}
// 提交
const SubmitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogData.tableData.ruleForm.alertLogId != '') {
                updateAlertLog(dialogData.tableData.ruleForm).then((res) => {
                    ElMessage.success('修改成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            } else {
                addAlertLog(dialogData.tableData.ruleForm).then((res) => {
                    ElMessage.success('新增成功');
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 获取状态数据
const getdictdata = async () => {
    try {
        alert_level_list.value = await dictStore.fetchDict('iot_alert_level')
        process_status_list.value = await dictStore.fetchDict('iot_process_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/**格式化显示物模型**/
const formatDetail = (json: any) => {
    if (json == null || json == '') {
        return;
    }
    
    let item = JSON.parse(json);
    let result = 'id：<span style="color:#F56C6C">' + item.id + '</span><br />';
    if (item.name) {
    result += 'name：<span style="color:#F56C6C">' + item.name + '</span><br />';
    }
    result = result + 'value：<span style="color:#F56C6C">' + item.value + '</span><br />';
    result = result + 'remark：<span style="color:#F56C6C">' + item.remark + '</span>';
    return result;
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 监听props的变化
watch(() => props.device, (newVal) => {
    console.log(newVal, 'newValnewValnewVal');

    try {
        if (newVal && newVal.deviceId != 0) {
            state.tableData.param.serialNumber = newVal.serialNumber;
            getTableData();
            getdictdata()
        }

    }
    catch (error) {
        console.error("Error in watcher callback:", error);
    }
}, { immediate: true });
</script>