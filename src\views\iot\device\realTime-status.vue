<template>
    <div class="running-status H100">
        <div>
            aaaaaaaaaaaa
        </div>

        <!-- <el-dialog title="服务调用" :visible.sync="dialogValue" label-width="200px">
            <el-form v-model="from" size="mini" style="height: 100%; padding: 0 20px">
                <el-form-item :label="from.name" label-width="180px">
                    <el-input v-model="from.shadow" type="number" @input="justicNumber()"
                        v-if="from.datatype.type == 'integer' || from.datatype.type == 'decimal' || from.datatype.type == 'string'"
                        style="width: 50%"></el-input>
                    <el-select v-if="from.datatype.type == 'enum'" v-model="from.shadow" @change="changeSelect()">
                        <el-option v-for="option in from.datatype.enumList" :key="option.value" :label="option.text"
                            :value="option.value"></el-option>
                    </el-select>
                    <el-switch v-if="from.datatype.type === 'bool'" v-model="from.shadow" active-value="1"
                        inactive-value="0" inline-prompt />
                    <span
                        v-if="(from.datatype.type == 'integer' || from.datatype.type == 'decimal') && from.datatype.type.unit && from.datatype.type.unit != 'un' && from.datatype.type.unit != '/'">（{{
                        from.unit }}）</span>
                    <div v-if="from.datatype.type == 'integer' || from.datatype.type == 'decimal'" class="range">
                        (数据范围:{{ from.datatype.max == 'null' ? (from.datatype.type == 'bool' ? 0 : '') :
                        from.datatype.min }} ~
                        {{ from.datatype.max == 'null' ? (from.datatype.type == 'bool' ? 1 : '') : from.datatype.max }})
                    </div>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogValue = false">取消</el-button>
                <el-button type="primary" @click="sendService" :loading="btnLoading" :disabled="!canSend">确认</el-button>
            </span>
        </el-dialog> -->
    </div>
</template>
<script setup lang="ts" name="">
import { reactive, ref } from 'vue';
// 定义 props
const props = defineProps({
    modelValue: {
        type: Object
    }
})
const params = reactive({
    serialNumber: undefined,
    type: 1,
    productId: undefined,
    slaveId: undefined,
    deviceId: undefined,


})
const deviceInfo = ref({}) // 设备信息
const serialNumber = ref('')
let slaveList = reactive([])
// 监听props的变化
// watch(() => props.modelValue, (newVal) => {
//     try {
//         if (newVal && newVal.serialNumber) {
//             params.serialNumber = newVal.serialNumber;
//             serialNumber.value = newVal.serialNumber;
//             params.productId = newVal.productId;
//             params.slaveId = newVal.slaveId;
//             params.deviceId = newVal.deviceId;
//             deviceInfo.value = newVal;
//             this.updateDeviceStatus(deviceInfo);
//             slaveList = newVal.subDeviceList;
//             this.getSlaveList(deviceInfo);
//             this.$busEvent.$on('updateData', (params) => {
//                 if (params.data && params.data[0].remark) {
//                     this.getDeviceFuncLog();
//                     params.data[0].ts = params.data[0].remark;
//                 }
//                 this.updateData(params);
//             });
//             this.$busEvent.$on('updateLog', (params) => {
//                 this.getDeviceFuncLog();
//             });
//             this.mqttCallback();
//         }

//     }
//     catch (error) {
//         console.error("Error in watcher callback:", error);
//     }
// }, { immediate: true });
</script>