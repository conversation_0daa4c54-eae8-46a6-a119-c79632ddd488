<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="800">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
                size="default" label-width="90px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="标题" prop="title">
                            <el-input v-model="state.ruleForm.title" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="作者" prop="author">
                            <el-input v-model="state.ruleForm.author" placeholder="请输入作者" />
                        </el-form-item>
                        <el-form-item label="摘要" prop="remark">
                            <el-input v-model="state.ruleForm.remark" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-row>
                            <el-col :span="8">
                                <el-form-item label="置顶" prop="isTop">
                                    <el-switch v-model="state.ruleForm.isTop" active-text="" inactive-text=""
                                        :active-value="1" :inactive-value="0"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="轮播" prop="isBanner">
                                    <el-switch v-model="state.ruleForm.isBanner" active-text="" inactive-text=""
                                        :active-value="1" :inactive-value="0"></el-switch>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="发布" prop="status">
                                    <el-switch v-model="state.ruleForm.status" active-text="" inactive-text=""
                                        :active-value="1" :inactive-value="0"></el-switch>
                                </el-form-item>
                            </el-col>
                        </el-row>

                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="咨询" prop="categoryId">
                            <el-select v-model="state.ruleForm.categoryId" placeholder="请选择咨询" @change="selectCategory">
                                <el-option v-for="category in categoryList" :key="category.id" :label="category.name"
                                    :value="category.id"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="图片">
                            <imageUpload ref="image-upload" v-model:model-value="state.ruleForm.imgUrl" :limit="1"
                                :fileSize="1"></imageUpload>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="内容">
                            <!-- <el-input v-model="state.ruleForm.noticeContent" type="textarea" placeholder="请输入内容"
                                maxlength="150"></el-input> -->
                            <Editor v-model="state.ruleForm.content" :min-height="192" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NewsDialogRef">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { listShortNewsCategory } from '/@/api/iot/newsCategory';
import { useUserInfo } from '/@/stores/userInfo';
import Editor from '/@/components/Editor/index.vue'
import imageUpload from '/@/components/ImageUpload/index.vue'
import { addNews, getNews, updateNews } from '/@/api/iot/news';
const userInfoStore = useUserInfo();
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        categoryName: '', // 新闻咨询标题
        title: '', // 新闻标题
        author: '',//作者
        remark: '', // 摘要
        isTop: '',//置顶
        isBanner: '',//轮播
        categoryId: '',//咨询
        content: '',//内容
        newsId: '',
        imgUrl: '',
        status: ''
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}

// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    dialog: { ...initialState.dialog },
});
const isAdmin = ref(false); // 用 ref 来定义响应式变量
interface statusOption {
    id: number;
    name: string;
}
const categoryList = ref<statusOption[]>([]);

// 校验规则
const rules = reactive({
    title: [{
        required: true,
        message: "标题不能为空",
        trigger: "blur"
    }],
    content: [{
        required: true,
        message: "内容不能为空",
        trigger: "blur"
    }],
    categoryId: [{
        required: true,
        message: "咨询不能为空",
        trigger: "blur"
    }],
    author: [{
        required: true,
        message: "作者不能为空",
        trigger: "blur"
    }],

})
// 打开弹窗
const openDialog = (type: string, newsId: string) => {
    if (type === 'edit') {
        getNews(newsId).then(response => {
            state.ruleForm = response.data.data

        });
        state.dialog.title = '修改新闻咨询';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '添加新闻咨询';
        state.dialog.submitTxt = '新 增';
    }
    getCategoryList()
    state.dialog.isShowDialog = true;
};
const getCategoryList = async () => {
    await userInfoStore.setUserInfos();

    // 检查 roles 中是否包含 "tenant" 或 "general"
    if (!userInfoStore.userInfos.roles.includes('tenant') && !userInfoStore.userInfos.roles.includes('general')) {
        isAdmin.value = true;
        // 获取咨询列表
        listShortNewsCategory().then(response => {
            categoryList.value = response.data.data;
        });
    }
}
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
/** 选择咨询 */
const selectCategory = (val: any) => {
    for (var i = 0; i < categoryList.value.length; i++) {
        if (categoryList.value[i].id == val) {
            state.ruleForm.categoryName = categoryList.value[i].name;
            return;
        }
    }
}
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    if (state.ruleForm.imgUrl == null || state.ruleForm.imgUrl == "") {
        ElMessage.error("请上传图片");
        return;
    }
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.newsId != '') {
                updateNews(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {
                addNews(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
