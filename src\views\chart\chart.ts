import request from '/@/utils/request';

// 设备监测范围
export function MonitorRang() {
	return request({
		url: '/deviceMonitor/scope',
		method: 'get',
	});
}

// 设备监测动态
export function MonitorDynamics() {
	return request({
		url: '/deviceMonitor/dynamics',
		method: 'get',
	});
}

// 设备故障分类对比
export function faultsMalfunction() {
	return request({
		url: '/deviceMonitor/faults/classification',
		method: 'get',
	});
}
// 采集齐全率分析
export function completenessRate() {
	return request({
		url: '/deviceMonitor/completenessRate',
		method: 'get',
	});
}
// 设备周故障对比
export function faultsWeek() {
	return request({
		url: '/deviceMonitor/faults/weeklyComparison',
		method: 'get',
	});
}
// 设备运行统计
export function runStatistics(data: any) {
	return request({
		url: `/deviceMonitor/operationStats`,
		method: 'get',
		params: data,
	});
}
// 查询所属单位列表
export function Affiliation() {
	return request({
		url: '/deviceMonitor/groupTree',
		method: 'get',		
	});
}
// 设备运维考核
export function assessEvaluation() {
	return request({
		url: '/deviceMonitor/maintenanceEvaluation',
		method: 'get',		
	});
}
// 设备故障区域排名
export function regionalRankings() {
	return request({
		url: '/deviceMonitor/faults/areaRanking',
		method: 'get',		
	});
}
// 设备故障清单
export function faultsList() {
	return request({
		url: '/deviceMonitor/faults/list',
		method: 'get',		
	});
}
