<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px" v-show="showSearch">
                    <el-form-item label="脚本标识" prop="scriptId">
                        <el-input v-model="state.tableData.param.scriptId" placeholder="请输入脚本标识" clearable />
                    </el-form-item>
                    <el-form-item label="脚本名" prop="scriptName">
                        <el-input v-model="state.tableData.param.scriptName" clearable size="default"
                            placeholder="请输入脚本名" style="width: 240px" />
                    </el-form-item>

                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                    <el-form-item style="float: right;">
                        <el-button plain v-auths="['iot:script:add']" size="default" type="primary" class="ml5"
                            @click="onOpenAdd('add')">
                            <el-icon><ele-Plus /></el-icon>
                            新增
                        </el-button>
                    </el-form-item>

                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="脚本名称" align="center" prop="scriptName" />
                <el-table-column label="所属产品" align="center" prop="productName" />
                <el-table-column label="脚本标识" align="center" prop="scriptId" width="180" />
                <el-table-column label="脚本事件" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="script_event_list" :value="scope.row.scriptEvent" size="small" />
                    </template>
                </el-table-column>
                <el-table-column label="脚本动作" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="script_action_list" :value="scope.row.scriptAction" size="small" />
                    </template>
                </el-table-column>
                <el-table-column label="脚本语言" align="center" prop="scriptLanguage" />
                <el-table-column label="状态" align="center" prop="enable" width="120">
                    <template #default="scope">
                        <el-tag v-if="scope.row.enable == 1" type="success" size="small">启动</el-tag>
                        <el-tag v-if="scope.row.enable == 0" type="danger" size="small">暂停</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="执行顺序" align="center" prop="scriptOrder" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <div style="display: flex;justify-content: center;">
                            <el-button size="default" type="primary" text @click="onOpenEdit('edit', scope.row)"
                                v-auths="['iot:script:query']"><el-icon
                                    size="small"><ele-View /></el-icon>查看</el-button>
                            <el-button size="default" type="primary" text @click="onRowDel(scope.row)"
                                v-auths="['iot:script:remove']"><el-icon
                                    size="large"><ele-DeleteFilled /></el-icon>删除</el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <ScriptDialog ref="ScriptDialogRef" @refresh="getTableData()" />
    </div>
</template>

<script setup lang="ts" name="">
import { defineAsyncComponent, reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessageBox, ElMessage } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { delScript, listScript } from '/@/api/iot/script';


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
const ScriptDialog = defineAsyncComponent(() => import('/@/views/iot/scene/dialog.vue'));

// 定义变量内容
const ScriptDialogRef = ref();
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            scriptPurpose: 1,
            scriptId: '' as any,
            scriptName: '' as any

        },
    },
});
const showSearch = ref(true)    // 显示搜索条件
const ids = ref() //scriptId
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const script_event_list = ref<TypeOption[]>([]);
const script_action_list = ref<TypeOption[]>([]);

// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listScript(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);

    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        scriptPurpose: 1,
        scriptId: '' as any,
        scriptName: '' as any
    }
}
// 获取状态数据
const getdictdata = async () => {
    try {
        script_event_list.value = await dictStore.fetchDict('rule_script_event')
        script_action_list.value = await dictStore.fetchDict('rule_script_action')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
// 打开新增产品分类弹窗
const onOpenAdd = (type: string) => {
    ScriptDialogRef.value.openDialog(type);
};
// 打开修改产品分类弹窗
const onOpenEdit= (type: string, row: any | undefined) => {
    var scriptId = ''
    if (!row) {
        scriptId = ids.value
    } else {
        scriptId = row.scriptId
    }
    ScriptDialogRef.value.openDialog(type, row, scriptId);
};
// 打开修改产品分类弹窗
const onOpenEditDic = (type: string, row: any | undefined) => {
    var scriptId = ''
    if (!row) {
        scriptId = ids.value
    } else {
        scriptId = row.scriptId
    }
    ScriptDialogRef.value.openDialog(type, row, scriptId);
};
// 删除产品分类
const onRowDel = (row: any) => {
    const scriptIds = row.scriptId || ids.value;
    ElMessageBox.confirm(`是否确认删除规则引擎脚本编号为：“${scriptIds}”的数据项，是否继续?`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
    })
        .then(() => {
            delScript(scriptIds).then(() => {
                getTableData();
                ElMessage.success('删除成功');
            })
        })
        .catch(() => { });
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata()
});
</script>
