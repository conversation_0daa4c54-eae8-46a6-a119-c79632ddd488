// src/api/deviceUser.ts
import request from '/@/utils/request';

// 查询设备用户列表
export function listDeviceUser(query: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser/list',
    method: 'get',
    params: query,
  });
}

// 查询设备用户详细
export function getDeviceUser(deviceId: string, userId: string): Promise<any> {
  return request({
    url: `/iot/deviceUser/${deviceId}/${userId}`,
    method: 'get',
  });
}

// 查询用户
export function shareUser(query: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser/shareUser',
    method: 'get',
    params: query,
  });
}

// 新增设备用户
export function addDeviceUser(data: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser',
    method: 'post',
    data: data,
  });
}

// 新增多个设备用户
export function addDeviceUsers(data: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser/addDeviceUsers',
    method: 'post',
    data: data,
  });
}

// 修改设备用户
export function updateDeviceUser(data: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser',
    method: 'put',
    data: data,
  });
}

// 删除设备用户
export function delDeviceUser(device: { [key: string]: any }): Promise<any> {
  return request({
    url: '/iot/deviceUser',
    method: 'delete',
    data: device,
  });
}
