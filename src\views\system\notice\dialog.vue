<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.dialog.title"
            v-model="state.dialog.isShowDialog" width="800">
            <el-form style="margin: 25px 0;" ref="DialogFormRef" :model="state.ruleForm" :rules="rules"
                size="default" label-width="90px">
                <el-row :gutter="35">
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="公告标题" prop="noticeTitle">
                            <el-input v-model="state.ruleForm.noticeTitle" placeholder="请输入公告标题" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12" class="mb20">
                        <el-form-item label="类型" prop="noticeType">
                            <el-select v-model="state.ruleForm.noticeType" placeholder="公告类型" clearable
                                style="width: 240px">
                                <el-option v-for="notice in noticeTypelist" :key="notice.dictValue"
                                    :label="notice.dictLabel" :value="notice.dictValue" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="系统内置">
                            <el-radio-group v-model="state.ruleForm.status">
                                <el-radio v-for="item in statuslist" :key="item.dictValue" :value="item.dictValue">{{
                                    item.dictLabel
                                }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24" class="mb20">
                        <el-form-item label="内容">
                                <Editor  v-model="state.ruleForm.noticeContent" :min-height="192"/>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="onSubmit(DialogFormRef)" size="default">{{
                        state.dialog.submitTxt }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="NoticeDialogRef">
import { reactive, ref } from 'vue';
import { ElMessage, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addNotice, getNotice, updateNotice } from '/@/api/system/notice';
import Editor from '/@/components/Editor/index.vue'

const dictStore = useDictStore();  // 使用 Pinia store
const content = ref('<p>这里是初始内容</p>')
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const initialState = {
    ruleForm: {
        noticeTitle: '', // 公告标题
        noticeType: '', // 公告类型
        status: '0', // 状态
        noticeContent: '', // 公告描述
        noticeId: ''
    },
    dialog: {
        isShowDialog: false,
        type: '',
        title: '',
        submitTxt: '',
    },
}



// 初始化 state
const state = reactive({
    ruleForm: { ...initialState.ruleForm },
    // deptData: [...initialState.deptData],
    dialog: { ...initialState.dialog },
});
const update = (value:any) => {
    state.ruleForm.noticeContent = value
}
interface noticeTypeOption {
    dictValue: string;
    dictLabel: string;
}
const noticeTypelist = ref<noticeTypeOption[]>([]); //状态列表
interface dictTypeOption {
    dictValue: string;
    dictLabel: string;
}
const statuslist = ref<dictTypeOption[]>([]); //状态
// 校验规则
const rules = reactive({
    noticeTitle: [
        { required: true, message: "公告标题不能为空", trigger: "blur" }
    ],
    noticeType: [
        { required: true, message: "公告类型不能为空", trigger: "change" }
    ]

})
// 打开弹窗
const openDialog = (type: string, row: RowNoticeType, noticeId: string) => {
    if (type === 'edit') {       
        if (row != undefined) {
            getNotice(row.noticeId).then(response => {
                state.ruleForm = response.data.data
                
            });
        } else {
            getNotice(noticeId).then(response => {
                state.ruleForm = response.data.data
                
            });
        }
        state.dialog.title = '修改公告';
        state.dialog.submitTxt = '修 改';
    } else {
        resetState();
        state.dialog.title = '新增公告';
        state.dialog.submitTxt = '新 增';
        // 清空表单，此项需加表单验证才能使用
        // nextTick(() => {
        // 	DialogFormRef.value.resetFields();
        // });
    }
    state.dialog.isShowDialog = true;
    getdictdata()
};
// 清空弹框
const resetState = () => {
    state.ruleForm = { ...initialState.ruleForm }
    state.dialog = { ...initialState.dialog }
};
// 关闭弹窗
const closeDialog = () => {
    state.dialog.isShowDialog = false;
};
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (state.ruleForm.noticeId != '') {
                updateNotice(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('修改成功');
                });
            } else {


                addNotice(state.ruleForm).then(response => {
                    //  刷新页面
                    emit('refresh');
                    closeDialog();
                    ElMessage.success('新增成功');
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};
// 获取状态数据
const getdictdata = async () => {
    try {
        statuslist.value = await dictStore.fetchDict('sys_notice_status')
        noticeTypelist.value = await dictStore.fetchDict('sys_notice_type')
        // 处理公告数据
    } catch (error) {
        console.error('获取公告数据失败:', error);
    }
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>
