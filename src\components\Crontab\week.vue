<template>
  <el-form size="small">
    <el-form-item>
      <el-radio v-model="radioValue" :label="1">
        周，允许的通配符[, - * ? / L #]
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio v-model="radioValue" :label="2">
        不指定
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio v-model="radioValue" :label="3">
        周期从星期
        <el-select clearable v-model="cycle01">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.value" :value="item.key"
            :disabled="item.key === 1">{{ item.value }}</el-option>
        </el-select>
        -
        <el-select clearable v-model="cycle02">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.value" :value="item.key"
            :disabled="item.key < cycle01 && item.key !== 1">{{ item.value }}</el-option>
        </el-select>
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio v-model="radioValue" :label="4">
        第
        <el-input-number v-model="average01" :min="1" :max="4" /> 周的星期
        <el-select clearable v-model="average02">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.value" :value="item.key">{{ item.value
            }}</el-option>
        </el-select>
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio v-model="radioValue" :label="5">
        本月最后一个星期
        <el-select clearable v-model="weekday">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.value" :value="item.key">{{ item.value
            }}</el-option>
        </el-select>
      </el-radio>
    </el-form-item>

    <el-form-item>
      <el-radio v-model="radioValue" :label="6">
        指定
        <el-select clearable v-model="checkboxList" placeholder="可多选" multiple style="width:100%">
          <el-option v-for="(item, index) in weekList" :key="index" :label="item.value" :value="String(item.key)">{{
            item.value }}</el-option> </el-select>
      </el-radio>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

interface WeekItem {
  key: any;
  value: string;
}

const radioValue = ref<any>(2);
const weekday = ref<any>(2);
const cycle01 = ref<any>(2);
const cycle02 = ref<any>(3);
const average01 = ref<any>(1);
const average02 = ref<any>(2);
const checkboxList = ref<any[]>([]);
// 定义emit事件
const emit = defineEmits<{
  (event: 'update', field: string, value: any, type: string): void;
}>();
const weekList: WeekItem[] = [
  { key: 2, value: '星期一' },
  { key: 3, value: '星期二' },
  { key: 4, value: '星期三' },
  { key: 5, value: '星期四' },
  { key: 6, value: '星期五' },
  { key: 7, value: '星期六' },
  { key: 1, value: '星期日' }
];

// Methods to handle computation
const checkNum = (value: any, min: any, max: any): any => {
  return Math.min(Math.max(value, min), max);
};

// Computed values
const cycleTotal = computed(() => {
  cycle01.value = checkNum(cycle01.value, 1, 7);
  cycle02.value = checkNum(cycle02.value, 1, 7);
  return `${cycle01.value}-${cycle02.value}`;
});

const averageTotal = computed(() => {
  average01.value = checkNum(average01.value, 1, 4);
  average02.value = checkNum(average02.value, 1, 7);
  return `${average02.value}#${average01.value}`;
});

const weekdayCheck = computed(() => {
  weekday.value = checkNum(weekday.value, 1, 7);
  return weekday.value;
});

const checkboxString = computed(() => {
  return checkboxList.value.length > 0 ? checkboxList.value.join() : '*';
});

// Watchers to emit updates
watch(radioValue, () => {
  if (radioValue.value !== 2) {
    emit('update', 'week', '?', 'week');
  }
  switch (radioValue.value) {
    case 1:
      emit('update', 'week', '*', 'week');
      break;
    case 2:
      emit('update', 'week', '?', 'week');
      break;
    case 3:
      emit('update', 'week', cycleTotal.value, 'week');
      break;
    case 4:
      emit('update', 'week', averageTotal.value, 'week');
      break;
    case 5:
      emit('update', 'week', `${weekdayCheck.value}L`, 'week');
      break;
    case 6:
      emit('update', 'week', checkboxString.value, 'week');
      break;
  }
});
watch(cycleTotal, () => {
  if (radioValue.value === 3) emit('update', 'week', cycleTotal.value, 'week');
});

watch(averageTotal, () => {
  if (radioValue.value === 4) emit('update', 'week', averageTotal.value, 'week');
});

watch(weekdayCheck, () => {
  if (radioValue.value === 5) emit('update', 'week', `${weekdayCheck.value}L`, 'week');
});

watch(checkboxString, () => {
  if (radioValue.value === 6) emit('update', 'week', checkboxString.value, 'week');
});

defineExpose({
  radioValue,
  cycle01,
  cycle02,
  checkboxString,
  average01,
  average02,
  checkboxList,
  weekday
});
</script>
