<template>
    <div style="padding: 6px">
        <el-card style="margin-bottom: 6px">
            <div style="display: flex; justify-content: flex-end;">
                <el-button  size="mini" @click="handleRefresh">
                    <el-icon>
                        <Refresh />
                    </el-icon>
                    刷新
                </el-button>
            </div>
            <el-row :gutter="120">
                <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
                    <h1 style="font-weight: bold;font-size: 25px;margin-bottom: 20px;">Mqtt 统计指标</h1>
                    <el-row :gutter="20" class="panel-group">
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 17px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-orange">
                                    <SvgIcon :name="'guide'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div>
                                        <div class="card-panel-text">发送消息</div>
                                        <count-to :start-val="0" :end-val="statics.send_total" :duration="3000"
                                            class="card-panel-num" />
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 18px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-green">
                                    <SvgIcon :name="'receiver'" :type="'menu'" :color="''" :size="30" />
                                </div>

                                <div class="card-panel-description">
                                    <div>
                                        <div class="card-panel-text">接收消息</div>
                                        <count-to :start-val="0" :end-val="statics.receive_total" :duration="3000"
                                            class="card-panel-num" />
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 17px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-orange">
                                    <SvgIcon :name="'authenticate'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">认证次数</div>
                                    <count-to :start-val="0" :end-val="statics.auth_total" :duration="1000"
                                        class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 18px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-green">
                                    <SvgIcon :name="'connect'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">连接次数</div>
                                    <count-to :start-val="0" :end-val="statics.connect_total" :duration="1000"
                                        class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 17px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-orange">
                                    <SvgIcon :name="'connect'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">订阅次数</div>
                                    <count-to :start-val="0" :end-val="statics.subscribe_total" :duration="2000"
                                        class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>

                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 17px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-green">
                                    <SvgIcon :name="'connect'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div>
                                        <div class="card-panel-text">今日接收</div>
                                        <count-to :start-val="0" :end-val="statics.today_received" :duration="3000"
                                            class="card-panel-num" />
                                    </div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="24" class="card-panel-col" style="margin-bottom: 17px">
                            <div class="card-panel">
                                <div class="card-panel-icon-wrapper icon-orange">
                                    <SvgIcon :name="'connect'" :type="'menu'" :color="''" :size="30" />
                                </div>
                                <div class="card-panel-description">
                                    <div class="card-panel-text">今日发送</div>
                                    <count-to :start-val="0" :end-val="statics.today_send" :duration="2000"
                                        class="card-panel-num" />
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </el-col>

                <el-col :xs="24" :sm="24" :md="24" :lg="15" :xl="15">
                    <div style="padding: 30px 0 85px">
                        <div ref="pieTotal" style="height: 230px"></div>
                    </div>
                    <div ref="statsChart" style="height: 275px; margin: 20px 0 40px 0"></div>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, Ref } from 'vue';
import { getNettyMqttStats, statisticNettyMqtt } from '/@/api/iot/netty';
import { CountTo } from 'vue3-count-to';
import * as echarts from 'echarts'; // 引入 echarts
import { ElLoading } from 'element-plus';
const pieTotal = ref()
const statsChart = ref()
const stats = ref({
    connection_count: 0,
    session_count: 0,
    subscription_count: 0,
    retain_count: 0,
    connection_total: 0,
    session_total: 0,
    subscription_total: 0,
    retain_total: 0,
})// mqtt状态数据
const statics = ref({
    send_total: 0,
    receive_total: 0,
    auth_total: 0,
    connect_total: 0,
    subscribe_total: 0,
    today_received: 0,
    today_send: 0,
})    // mqtt统计信息
/** 查询mqtt统计*/
const statisticMqtt = () => {
    try {
        statisticNettyMqtt().then((response) => {
            statics.value = response.data.data;
            totalMqtt();
        });
    } catch (error) {
        console.log(error);
    }
}
/** 查询mqtt状态数据*/
const getMqttStats = () => {
    try {
        getNettyMqttStats().then((response) => {
            stats.value = response.data.data;
            drawStats();
        });
    } catch (error) {
        console.log(error);
    }
}
const fullscreenLoading = ref(false)
// 刷新按钮 
const handleRefresh = () => {
    statisticMqtt();
    getMqttStats();
   const loading = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  setTimeout(() => {
    loading.close()
  }, 500)

}
// 绘制mqtt饼图
const totalMqtt = () => {
    if (pieTotal.value) {
        // 初始化echarts实例
        const myChart = echarts.init(pieTotal.value);
        // 显示加载动画
        myChart.showLoading();
        const option = {
            title: {
                text: 'Mqtt消息',
                left: 'left',
                textStyle: {
                    fontSize: 16,
                },
            },
            tooltip: {
                trigger: 'item',
            },
            legend: {
                orient: 'vertical',
                left: 'right',
            },
            color: ['#E6A23C', '#F56C6C', '#DDD'],
            series: [
                {
                    name: 'Mqtt消息 %',
                    type: 'pie',
                    radius: '55%',
                    label: {
                        show: true,
                    },
                    labelLine: {
                        normal: {
                            position: 'inner',
                            show: false,
                        },
                    },
                    data: [
                        {
                            value: statics.value.send_total,
                            name: '发送消息总数',
                        },
                        {
                            value: statics.value.receive_total,
                            name: '接收消息总数',
                        },
                    ],
                },
            ],
        };
        myChart.setOption(option);
        setTimeout(() => {
            myChart.hideLoading();
        }, 500);
    }
}
/** 绘制mqtt状态统计 */
const drawStats = () => {
    // 基于准备好的dom，初始化echarts实例
    const myChart = echarts.init(statsChart.value);
    // 显示加载动画
    myChart.showLoading();
    var option;
    option = {
        title: {
            text: 'Mqtt 状态数据',
            textStyle: {
                fontSize: 18,
                color: '#000',
                fontWeight: 800,
            },
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow',
            },
        },
        legend: {},
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true,
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
        },
        yAxis: {
            type: 'category',
            axisLabel: {
                fontSize: 14,
            },
            data: ['连接数量', '会话数量', '订阅数量', '路由数量', '保留消息'],
        },
        series: [
            {
                name: '当前数量',
                type: 'bar',
                data: [stats.value.connection_count, stats.value.session_count, stats.value.subscription_count, stats.value.retain_count, stats.value.retain_count],
                itemStyle: {
                    color: '#67C23A',
                },
            },
            {
                name: '累计总数',
                type: 'bar',
                data: [stats.value.connection_total, stats.value.session_total, stats.value.subscription_total, stats.value.retain_total, stats.value.retain_total],
                itemStyle: {
                    color: '#409EFF',
                },
            },
        ],
    };

    option && myChart.setOption(option);
    setTimeout(() => {
        myChart.hideLoading();
    }, 500);
}
// 页面加载时
onMounted(() => {
    statisticMqtt();
    getMqttStats();
});
</script>
<style lang="scss" scoped>
.panel-group {
    .card-panel-col {
        margin-bottom: 10px;
    }

    .card-panel {
        height: 68px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        color: #666;
        border: 1px solid #eee;
        border-radius: 5px;
        //box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.08);
        background-color: #fff;

        &:hover {
            .card-panel-icon-wrapper {
                color: #fff;
            }

            .icon-blue {
                background: #36a3f7;
            }

            .icon-green {
                background: #34bfa3;
            }

            .icon-red {
                background: #f56c6c;
            }

            .icon-orange {
                background: #e6a23c;
            }
        }

        .icon-blue {
            color: #36a3f7;
        }

        .icon-green {
            color: #34bfa3;
        }

        .icon-red {
            color: #f56c6c;
        }

        .icon-orange {
            color: #e6a23c;
        }

        .card-panel-icon-wrapper {
            float: left;
            margin: 8px;
            padding: 10px 12px;
            transition: all 0.38s ease-out;
            border-radius: 6px;
        }

        .card-panel-icon {
            float: left;
            font-size: 30px;
        }

        .card-panel-description {
            float: right;
            font-weight: bold;
            margin: 15px;
            margin-left: 0px;

            .card-panel-text {
                line-height: 14px;
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                margin-bottom: 12px;
                text-align: right;
            }

            .card-panel-num {
                font-size: 18px;
            }
        }
    }
}
</style>
