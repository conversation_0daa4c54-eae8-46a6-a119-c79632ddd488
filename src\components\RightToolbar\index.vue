<template>
  <div class="top-right-btn" :style="style">
    <el-row>
      <el-tooltip class="item" effect="dark" :content="showsearch ? '隐藏搜索' : '显示搜索'" placement="top" v-if="search">
        <el-button size="default" circle @click="toggleSearch">
          <el-icon><ele-Search /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="刷新" placement="top">
        <el-button size="default" circle @click="refresh">
          <el-icon><ele-Refresh /></el-icon>
        </el-button>
      </el-tooltip>
      <el-tooltip class="item" effect="dark" content="显隐列" placement="top" v-if="columns.length>0">
        <el-button size="default" circle @click="showColumn">
          <el-icon><ele-Menu /></el-icon>
        </el-button>
      </el-tooltip>
    </el-row>
    <el-dialog :title="title" append-to-body v-model="open">
      <el-transfer :titles="['显示', '隐藏']" :data="columns" v-model="value" @change="dataChange"></el-transfer>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, PropType } from 'vue';
import { ElButton, ElTooltip, ElDialog, ElTransfer, ElRow } from 'element-plus';

interface Column {
  key: string | number;
  label: string;
  visible: boolean;
}

export default defineComponent({
  name: 'RightToolbar',
  components: {
    ElButton,
    ElTooltip,
    ElDialog,
    ElTransfer,
    ElRow
  },
  props: {
    showsearch: {
      type: Boolean,
      default: true
    },
    columns: {
      type: Array as PropType<Column[]>,
      default: () => []
    },
    search: {
      type: Boolean,
      default: true
    },
    gutter: {
      type: Number,
      default: 10
    }
  },
  setup(props, { emit }) {
    const value = ref<(string | number)[]>([]); // value 类型改为 (string | number)[] 以兼容 TransferKey[]
    const open = ref(false);
    const title = ref('显示/隐藏');

    // 计算 style
    const style = computed(() => {
      const ret: { marginRight?: string } = {};
      if (props.gutter) {
        ret.marginRight = `${props.gutter / 2}px`;
      }
      return ret;
    });

    // 初始化显隐列
    watch(
      () => props.columns,
      (columns) => {
        // 初始化时，将 visible 为 false 的列的索引添加到 value 数组
        value.value = columns
          .filter((col) => !col.visible)
          .map((_, index) => index.toString()); // 使用 string 类型的索引
      },
      { immediate: true }
    );

    // 切换搜索显示
    const toggleSearch = () => {
      emit('update:showSearch', !props.showsearch);
    };

    // 刷新操作
    const refresh = () => {
      emit('queryTable');
    };

    // 显隐列变化，更新 columns 的 visible 状态
    const dataChange = (value: (string | number)[], direction: string, movedKeys: (string | number)[]) => {
      for (let item in props.columns) {
        const key = props.columns[item].key;
        props.columns[item].visible = !value.includes(key);
      }
    };

    // 显示显隐列对话框
    const showColumn = () => {
      open.value = true;
      console.log(props.columns, 'columns');

    };

    return {
      value,
      open,
      title,
      style,
      toggleSearch,
      refresh,
      dataChange,
      showColumn
    };
  }
});
</script>

<style lang="scss" scoped>
:deep(.child) .el-transfer__button {
  border-radius: 50%;
  padding: 12px;
  display: block;
  margin-left: 0px;
}

:deep(.child) .el-transfer__button:first-child {
  margin-bottom: 10px;
}
</style>