import { mergeRecursive } from "/@/utils/next";
import DictOptions from './DictOptions';

/**
 * @classdesc 字典元数据
 * @property {String} type 类型
 * @property {Function} request 请求
 * @property {String} label 标签字段
 * @property {String} value 值字段
 */
interface DictMetaOptions {
  type: string;
  request: Function;
  responseConverter?: Function;
  labelField: string;
  valueField: string;
  lazy?: boolean;
}

export default class DictMeta {
  type: string;
  request: Function;
  responseConverter?: Function;
  labelField: string;
  valueField: string;
  lazy: boolean;

  constructor(options: DictMetaOptions) {
    this.type = options.type;
    this.request = options.request;
    this.responseConverter = options.responseConverter;
    this.labelField = options.labelField;
    this.valueField = options.valueField;
    this.lazy = options.lazy === true;
  }

  /**
   * 解析字典元数据
   * @param {Object} options
   * @returns {DictMeta}
   */
  static parse(options: string | DictMetaOptions): DictMeta {
    let opts: DictMetaOptions | null = null;

    if (typeof options === 'string') {
      opts = DictOptions.metas[options] || {};
      opts.type = options;
    } else if (typeof options === 'object') {
      opts = options;
    }

    if (opts === null) {
      throw new Error('Invalid options provided to parse function');
    }

    opts = mergeRecursive(DictOptions.metas['*'], opts);
    return new DictMeta(opts);
  }
}
