// src/api/category.ts
import request from '/@/utils/request'

// 定义请求参数的类型（如果有特定的请求参数结构的话，可以根据需要调整）
interface QueryParams {
  [key: string]: string | number | boolean;
}

// 定义产品分类的数据类型
interface Category {
  id: number;
  name: string;
  description: string;
  // 你可以根据实际需要添加其他属性
}

// 查询产品分类列表
export function listCategory(query: any) {
  return request({
    url: '/iot/iotcategory/list',
    method: 'get',
    params: query
  })
}

// 查询产品简短分类列表
export function listShortCategory() {
  return request({
    url: '/iot/iotcategory/shortlist',
    method: 'get',
  })
}

// 查询产品分类详细
export function getCategory(categoryId: any) {
  return request({
    url: `/iot/iotcategory/${categoryId}`,
    method: 'get'
  })
}

// 新增产品分类
export function addCategory(data: any) {
  return request({
    url: '/iot/iotcategory',
    method: 'post',
    data: data
  })
}

// 修改产品分类
export function updateCategory(data: any) {
  return request({
    url: '/iot/iotcategory',
    method: 'put',
    data: data
  })
}

// 删除产品分类
export function delCategory(categoryId: number) {
  return request({
    url: `/iot/iotcategory/${categoryId}`,
    method: 'delete'
  })
}
