<template>
    <div class="system-dic-dialog-container">
        <el-dialog style="position: absolute; top: 100px;" :title="state.tableData.title" v-model="isShowDialog"
            width="800">
            <el-form ref="queryForm" :inline="true" label-width="68px">
                <el-form-item label="设备名称" prop="deviceName">
                    <el-input v-model="state.tableData.queryParams.deviceName" placeholder="请输入设备名称" clearable
                        size="default" @keyup.enter.native="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" size="default" @click="handleQuery">
                        <el-icon>
                            <ele-Search />
                        </el-icon>
                        搜索
                    </el-button>
                </el-form-item>
            </el-form>
            <el-table v-loading="state.tableData.loading" :data="state.tableData.data" @select="handleSelectionChange"
                @select-all="handleSelectionAll" ref="multipleTable" size="default" border>
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="设备名称" align="center" prop="deviceName" />
                <el-table-column label="设备编号" align="center" prop="serialNumber" />
                <el-table-column label="产品名称" align="center" prop="productName" />
                <el-table-column label="设备类型" align="center">
                    <template #default="scope">
                        <el-tag type="success" v-if="scope.row.isOwner == 0">分享</el-tag>
                        <el-tag type="primary" v-else>拥有</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="设备状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="typelist" :value="scope.row.status" />
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination size="small" @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.queryParams.pageNum" background
                v-model:page-size="state.tableData.queryParams.pageSize"
                layout="total, sizes, prev, pager, next, jumper" :total="state.tableData.total">
            </el-pagination>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="onCancel" size="default">取 消</el-button>
                    <el-button type="primary" @click="handleDeviceSelected" size="default">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts" name="NoticeDialogRef">
import { nextTick, reactive, ref } from 'vue';
import { ElMessage, ElTable, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import { addGroup, getDeviceIds, getGroup, updateDeviceGroups, updateGroup } from '/@/api/iot/group';
import { listDeviceByGroup } from '/@/api/iot/device';
const dictStore = useDictStore();  // 使用 Pinia store
// 定义子组件向父组件传值/事件
const emit = defineEmits(['refresh']);

// 定义变量内容
const DialogFormRef = ref();
const multipleTable = ref<InstanceType<typeof ElTable> | null>(null);
// 初始化 state
const state = reactive({
    tableData: {
        data: [] as any,
        total: 0,
        loading: false,
        title: '',
        queryParams: {
            pageNum: 1,
            pageSize: 10,
            deviceName: null,
            productId: null,
            productName: null,
            userId: null,
            userName: null,
            tenantId: null,
            tenantName: null,
            serialNumber: null,
            status: null,
            networkAddress: null,
            activeTime: null,
            params:{}
        },
    },
});
const isShowDialog = ref(false)
// 非单个禁用
const single = ref(true)
// 非多个禁用
const multiple = ref(true)
const ids = ref<number[]>([]); //groupId
// 定义一个 interface 来描述 deviceGroup 的结构
interface DeviceGroup {
    userId: any;  // 或者是 number，取决于你的数据类型
    groupId: any; // 或者是 number，取决于你的数据类型
    deviceIds: any;
    // 如果有其他属性，可以在这里添加
}
let deviceGroup = reactive<DeviceGroup>({ userId: '', groupId: '', deviceIds: '' })
interface TypeOption {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
const typelist = ref<TypeOption[]>([]);
// 打开弹窗
const openDialog = (row: any,) => {
    isShowDialog.value = true;
    deviceGroup = row
    // 获取分组下的设备
    state.tableData.queryParams.userId = deviceGroup.userId;
    state.tableData.queryParams.pageNum = 1;
    getDeviceIds(deviceGroup.groupId).then(response => {
        ids.value = response.data.data;
        getTableData();
        getdictdata()
        isShowDialog.value = true;
    });
};
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    state.tableData.queryParams.params = {};
    try {
        const response = await listDeviceByGroup(state.tableData.queryParams);
        state.tableData.data = response.data.rows;
        state.tableData.total = response.data.total;
        // 设置分组关联的设备选中
        state.tableData.data.forEach((row: { deviceId: number; }) => {
            nextTick(() => {
                if (ids.value.includes(row.deviceId)) {
                    multipleTable.value?.toggleRowSelection(row, true)
                }
            })
        });
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
// 获取状态数据
const getdictdata = async () => {
    try {
        typelist.value = await dictStore.fetchDict('iot_device_status')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/** 搜索按钮操作 */
const handleQuery = () => {
    state.tableData.queryParams.pageNum = 1;
    getTableData();
}
// 关闭弹窗
const closeDialog = () => {
    isShowDialog.value = false;
};
// 多选框选中数据
const handleSelectionChange = (selection: any[], row: { deviceId: any; }) => {
    console.log(selection, row);

    // 设备ID是否存在于原始设备ID数组中
    let index = ids.value.indexOf(row.deviceId);
    // 是否选中
    let value = selection.indexOf(row);
    
    if (index == -1 && value != -1) {
        // 不存在且选中
        ids.value.push(row.deviceId);
    } else if (index != -1 && value == -1) {
        // 存在且取消选中
        ids.value.splice(index, 1);
    }
    
}
// 全选事件处理
const handleSelectionAll = (selection: any[]) => {
    for (let i = 0; i < state.tableData.data.length; i++) {
        // 设备ID是否存在于原始设备ID数组中
        let index = ids.value.indexOf(state.tableData.data[i].deviceId);
        // 是否选中
        let value = selection.indexOf(state.tableData.data[i]);
        if (index == -1 && value != -1) {
            // 不存在且选中
            ids.value.push(state.tableData.data[i].deviceId);
        } else if (index != -1 && value == -1) {
            // 存在且取消选中
            ids.value.splice(index, 1);
        }
    }
}
// 取消
const onCancel = () => {
    closeDialog();
};
// 提交
const handleDeviceSelected = () => {
    deviceGroup.deviceIds = ids.value;
    updateDeviceGroups(deviceGroup).then(response => {
        ElMessage.success("更新分组下的设备成功");
        isShowDialog.value = false;
    })
};
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.queryParams.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.queryParams.pageNum = val;
    getTableData();
};

// 暴露变量
defineExpose({
    openDialog,
});
</script>